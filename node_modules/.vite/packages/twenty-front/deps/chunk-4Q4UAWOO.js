import {
  isMacOS
} from "./chunk-LHBPKJEO.js";
import {
  computed,
  createElementBlock,
  defineComponent,
  guardReactiveProps,
  i2 as i,
  normalizeProps,
  openBlock,
  toDisplayString,
  unref
} from "./chunk-NPL3RUXR.js";

// node_modules/@scalar/api-client/dist/components/ScalarHotkey.vue.js
var b = defineComponent({
  __name: "ScalarHotkey",
  props: {
    hotkey: {},
    modifier: {}
  },
  setup(t) {
    const e = t, { cx: i2 } = i(), o = computed(() => e.modifier || "meta"), n = computed(() => `${o.value === "meta" ? isMacOS() ? "⌘" : "^" : o.value} ${e.hotkey}`);
    return (d, y) => (openBlock(), createElementBlock("div", normalizeProps(guardReactiveProps(
      unref(i2)(
        "border-b-3 inline-block overflow-hidden rounded border-1/2 text-xxs rounded-b px-1 font-medium uppercase"
      )
    )), toDisplayString(n.value), 17));
  }
});

export {
  b
};
//# sourceMappingURL=chunk-4Q4UAWOO.js.map
