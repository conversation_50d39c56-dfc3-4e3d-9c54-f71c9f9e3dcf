{"version": 3, "sources": ["../../../../extract-files/public/ReactNativeFile.js", "../../../../extract-files/public/isExtractableFile.js", "../../../../extract-files/public/extractFiles.js", "../../../../apollo-upload-client/public/formDataAppendFile.js", "../../../../apollo-upload-client/public/isExtractableFile.js", "../../../../apollo-upload-client/public/createUploadLink.js", "../../../../apollo-upload-client/public/ReactNativeFile.js", "../../../../apollo-upload-client/public/index.mjs"], "sourcesContent": ["'use strict';\n\n/**\n * Used to mark a\n * [React Native `File` substitute]{@link ReactNativeFileSubstitute}\n * in an object tree for [`extractFiles`]{@link extractFiles}. It’s too risky to\n * assume all objects with `uri`, `type` and `name` properties are files to\n * extract.\n * @kind class\n * @name ReactNativeFile\n * @param {ReactNativeFileSubstitute} file A [React Native](https://reactnative.dev) [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File) substitute.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { ReactNativeFile } from 'extract-files';\n * ```\n *\n * ```js\n * import ReactNativeFile from 'extract-files/public/ReactNativeFile.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { ReactNativeFile } = require('extract-files');\n * ```\n *\n * ```js\n * const ReactNativeFile = require('extract-files/public/ReactNativeFile.js');\n * ```\n * @example <caption>An extractable file in [React Native](https://reactnative.dev).</caption>\n * ```js\n * const file = new ReactNativeFile({\n *   uri: uriFromCameraRoll,\n *   name: 'a.jpg',\n *   type: 'image/jpeg',\n * });\n * ```\n */\nmodule.exports = class ReactNativeFile {\n  constructor({ uri, name, type }) {\n    this.uri = uri;\n    this.name = name;\n    this.type = type;\n  }\n};\n", "'use strict';\n\nconst ReactNativeFile = require('./ReactNativeFile.js');\n\n/**\n * Checks if a value is an [extractable file]{@link ExtractableFile}.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an [extractable file]{@link ExtractableFile}.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from 'extract-files';\n * ```\n *\n * ```js\n * import isExtractableFile from 'extract-files/public/isExtractableFile.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require('extract-files');\n * ```\n *\n * ```js\n * const isExtractableFile = require('extract-files/public/isExtractableFile.js');\n * ```\n */\nmodule.exports = function isExtractableFile(value) {\n  return (\n    (typeof File !== 'undefined' && value instanceof File) ||\n    (typeof Blob !== 'undefined' && value instanceof Blob) ||\n    value instanceof ReactNativeFile\n  );\n};\n", "'use strict';\n\nconst defaultIsExtractableFile = require('./isExtractableFile.js');\n\n/**\n * Clones a value, recursively extracting\n * [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File),\n * [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob) and\n * [`ReactNativeFile`]{@link ReactNativeFile} instances with their\n * [object paths]{@link ObjectPath}, replacing them with `null`.\n * [`FileList`](https://developer.mozilla.org/en-US/docs/Web/API/Filelist) instances\n * are treated as [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File)\n * instance arrays.\n * @kind function\n * @name extractFiles\n * @param {*} value Value (typically an object tree) to extract files from.\n * @param {ObjectPath} [path=''] Prefix for object paths for extracted files.\n * @param {ExtractableFileMatcher} [isExtractableFile=isExtractableFile] The function used to identify extractable files.\n * @returns {ExtractFilesResult} Result.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { extractFiles } from 'extract-files';\n * ```\n *\n * ```js\n * import extractFiles from 'extract-files/public/extractFiles.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { extractFiles } = require('extract-files');\n * ```\n *\n * ```js\n * const extractFiles = require('extract-files/public/extractFiles.js');\n * ```\n * @example <caption>Extract files from an object.</caption>\n * For the following:\n *\n * ```js\n * const file1 = new File(['1'], '1.txt', { type: 'text/plain' });\n * const file2 = new File(['2'], '2.txt', { type: 'text/plain' });\n * const value = {\n *   a: file1,\n *   b: [file1, file2],\n * };\n *\n * const { clone, files } = extractFiles(value, 'prefix');\n * ```\n *\n * `value` remains the same.\n *\n * `clone` is:\n *\n * ```json\n * {\n *   \"a\": null,\n *   \"b\": [null, null]\n * }\n * ```\n *\n * `files` is a [`Map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map) instance containing:\n *\n * | Key     | Value                        |\n * | :------ | :--------------------------- |\n * | `file1` | `['prefix.a', 'prefix.b.0']` |\n * | `file2` | `['prefix.b.1']`             |\n */\nmodule.exports = function extractFiles(\n  value,\n  path = '',\n  isExtractableFile = defaultIsExtractableFile\n) {\n  // Map of extracted files and their object paths within the input value.\n  const files = new Map();\n\n  // Map of arrays and objects recursed within the input value and their clones,\n  // for reusing clones of values that are referenced multiple times within the\n  // input value.\n  const clones = new Map();\n\n  /**\n   * Recursively clones the value, extracting files.\n   * @kind function\n   * @name extractFiles~recurse\n   * @param {*} value Value to extract files from.\n   * @param {ObjectPath} path Prefix for object paths for extracted files.\n   * @param {Set} recursed Recursed arrays and objects for avoiding infinite recursion of circular references within the input value.\n   * @returns {*} Clone of the value with files replaced with `null`.\n   * @ignore\n   */\n  function recurse(value, path, recursed) {\n    let clone = value;\n\n    if (isExtractableFile(value)) {\n      clone = null;\n\n      const filePaths = files.get(value);\n\n      filePaths ? filePaths.push(path) : files.set(value, [path]);\n    } else {\n      const isList =\n        Array.isArray(value) ||\n        (typeof FileList !== 'undefined' && value instanceof FileList);\n      const isObject = value && value.constructor === Object;\n\n      if (isList || isObject) {\n        const hasClone = clones.has(value);\n\n        if (hasClone) clone = clones.get(value);\n        else {\n          clone = isList ? [] : {};\n\n          clones.set(value, clone);\n        }\n\n        if (!recursed.has(value)) {\n          const pathPrefix = path ? `${path}.` : '';\n          const recursedDeeper = new Set(recursed).add(value);\n\n          if (isList) {\n            let index = 0;\n\n            for (const item of value) {\n              const itemClone = recurse(\n                item,\n                pathPrefix + index++,\n                recursedDeeper\n              );\n\n              if (!hasClone) clone.push(itemClone);\n            }\n          } else\n            for (const key in value) {\n              const propertyClone = recurse(\n                value[key],\n                pathPrefix + key,\n                recursedDeeper\n              );\n\n              if (!hasClone) clone[key] = propertyClone;\n            }\n        }\n      }\n    }\n\n    return clone;\n  }\n\n  return {\n    clone: recurse(value, path, new Set()),\n    files,\n  };\n};\n", "\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.formDataAppendFile` that uses the standard\n * [`FormData.append`](https://developer.mozilla.org/en-US/docs/Web/API/FormData/append)\n * method.\n * @kind function\n * @name formDataAppendFile\n * @type {FormDataFileAppender}\n * @param {FormData} formData [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance to append the specified file to.\n * @param {string} fieldName Field name for the file.\n * @param {*} file File to append.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { formDataAppendFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import formDataAppendFile from \"apollo-upload-client/public/formDataAppendFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { formDataAppendFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const formDataAppendFile = require(\"apollo-upload-client/public/formDataAppendFile.js\");\n * ```\n */\nmodule.exports = function formDataAppendFile(formData, fieldName, file) {\n  formData.append(fieldName, file, file.name);\n};\n", "\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.isExtractableFile`.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an extractable file.\n * @see [`extract-files` `isExtractableFile` docs](https://github.com/jaydenseric/extract-files#function-isextractablefile).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import isExtractableFile from \"apollo-upload-client/public/isExtractableFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const isExtractableFile = require(\"apollo-upload-client/public/isExtractableFile.js\");\n * ```\n */\nmodule.exports = require(\"extract-files/public/isExtractableFile.js\");\n", "\"use strict\";\n\nconst { ApolloLink, Observable } = require(\"@apollo/client/core\");\nconst {\n  createSignalIfSupported,\n  fallbackHttpConfig,\n  parseAndCheckHttpResponse,\n  rewriteURIForGET,\n  selectHttpOptionsAndBody,\n  selectURI,\n  serializeFetchParameter,\n} = require(\"@apollo/client/link/http\");\nconst extractFiles = require(\"extract-files/public/extractFiles.js\");\nconst formDataAppendFile = require(\"./formDataAppendFile.js\");\nconst isExtractableFile = require(\"./isExtractableFile.js\");\n\n/**\n * Creates a\n * [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link)\n * for [Apollo Client](https://apollographql.com/docs/react) that fetches a\n * [GraphQL multipart request](https://github.com/jaydenseric/graphql-multipart-request-spec)\n * if the GraphQL variables contain files (by default\n * [`FileList`](https://developer.mozilla.org/en-US/docs/Web/API/FileList),\n * [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File),\n * [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob), or\n * [`ReactNativeFile`](#class-reactnativefile) instances), or else fetches a\n * regular\n * [GraphQL POST or GET request](https://apollographql.com/docs/apollo-server/requests)\n * (depending on the config and GraphQL operation).\n *\n * Some of the options are similar to the\n * [`createHttpLink` options](https://apollographql.com/docs/react/api/link/apollo-link-http/#httplink-constructor-options).\n * @see [GraphQL multipart request spec](https://github.com/jaydenseric/graphql-multipart-request-spec).\n * @kind function\n * @name createUploadLink\n * @param {object} options Options.\n * @param {string} [options.uri=\"/graphql\"] GraphQL endpoint URI.\n * @param {boolean} [options.useGETForQueries] Should GET be used to fetch queries, if there are no files to upload.\n * @param {ExtractableFileMatcher} [options.isExtractableFile=isExtractableFile] Customizes how files are matched in the GraphQL operation for extraction.\n * @param {class} [options.FormData] [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) implementation to use, defaulting to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) global.\n * @param {FormDataFileAppender} [options.formDataAppendFile=formDataAppendFile] Customizes how extracted files are appended to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance.\n * @param {Function} [options.fetch] [`fetch`](https://fetch.spec.whatwg.org) implementation to use, defaulting to the [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch) global.\n * @param {FetchOptions} [options.fetchOptions] [`fetch` options]{@link FetchOptions}; overridden by upload requirements.\n * @param {string} [options.credentials] Overrides `options.fetchOptions.credentials`.\n * @param {object} [options.headers] Merges with and overrides `options.fetchOptions.headers`.\n * @param {boolean} [options.includeExtensions=false] Toggles sending `extensions` fields to the GraphQL server.\n * @returns {ApolloLink} A [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { createUploadLink } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { createUploadLink } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const createUploadLink = require(\"apollo-upload-client/public/createUploadLink.js\");\n * ```\n * @example <caption>A basic Apollo Client setup.</caption>\n * ```js\n * import { ApolloClient, InMemoryCache } from \"@apollo/client\";\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n *\n * const client = new ApolloClient({\n *   cache: new InMemoryCache(),\n *   link: createUploadLink(),\n * });\n * ```\n */\nmodule.exports = function createUploadLink({\n  uri: fetchUri = \"/graphql\",\n  useGETForQueries,\n  isExtractableFile: customIsExtractableFile = isExtractableFile,\n  FormData: CustomFormData,\n  formDataAppendFile: customFormDataAppendFile = formDataAppendFile,\n  fetch: customFetch,\n  fetchOptions,\n  credentials,\n  headers,\n  includeExtensions,\n} = {}) {\n  const linkConfig = {\n    http: { includeExtensions },\n    options: fetchOptions,\n    credentials,\n    headers,\n  };\n\n  return new ApolloLink((operation) => {\n    const context = operation.getContext();\n    const {\n      // Apollo Studio client awareness `name` and `version` can be configured\n      // via `ApolloClient` constructor options:\n      // https://apollographql.com/docs/studio/client-awareness/#using-apollo-server-and-apollo-client\n      clientAwareness: { name, version } = {},\n      headers,\n    } = context;\n\n    const contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: {\n        // Client awareness headers can be overridden by context `headers`.\n        ...(name && { \"apollographql-client-name\": name }),\n        ...(version && { \"apollographql-client-version\": version }),\n        ...headers,\n      },\n    };\n\n    const { options, body } = selectHttpOptionsAndBody(\n      operation,\n      fallbackHttpConfig,\n      linkConfig,\n      contextConfig\n    );\n\n    const { clone, files } = extractFiles(body, \"\", customIsExtractableFile);\n\n    let uri = selectURI(operation, fetchUri);\n\n    if (files.size) {\n      // Automatically set by `fetch` when the `body` is a `FormData` instance.\n      delete options.headers[\"content-type\"];\n\n      // GraphQL multipart request spec:\n      // https://github.com/jaydenseric/graphql-multipart-request-spec\n\n      const RuntimeFormData = CustomFormData || FormData;\n\n      const form = new RuntimeFormData();\n\n      form.append(\"operations\", serializeFetchParameter(clone, \"Payload\"));\n\n      const map = {};\n      let i = 0;\n      files.forEach((paths) => {\n        map[++i] = paths;\n      });\n      form.append(\"map\", JSON.stringify(map));\n\n      i = 0;\n      files.forEach((paths, file) => {\n        customFormDataAppendFile(form, ++i, file);\n      });\n\n      options.body = form;\n    } else {\n      if (\n        useGETForQueries &&\n        // If the operation contains some mutations GET shouldn’t be used.\n        !operation.query.definitions.some(\n          (definition) =>\n            definition.kind === \"OperationDefinition\" &&\n            definition.operation === \"mutation\"\n        )\n      )\n        options.method = \"GET\";\n\n      if (options.method === \"GET\") {\n        const { newURI, parseError } = rewriteURIForGET(uri, body);\n        if (parseError)\n          // Apollo’s `HttpLink` uses `fromError` for this, but it’s not\n          // exported from `@apollo/client/link/http`.\n          return new Observable((observer) => {\n            observer.error(parseError);\n          });\n        uri = newURI;\n      } else options.body = serializeFetchParameter(clone, \"Payload\");\n    }\n\n    const { controller } = createSignalIfSupported();\n\n    if (controller) {\n      if (options.signal)\n        // Respect the user configured abort controller signal.\n        options.signal.aborted\n          ? // Signal already aborted, so immediately abort.\n            controller.abort()\n          : // Signal not already aborted, so setup a listener to abort when it\n            // does.\n            options.signal.addEventListener(\n              \"abort\",\n              () => {\n                controller.abort();\n              },\n              {\n                // Prevent a memory leak if the user configured abort controller\n                // is long lasting, or controls multiple things.\n                once: true,\n              }\n            );\n\n      options.signal = controller.signal;\n    }\n\n    const runtimeFetch = customFetch || fetch;\n\n    return new Observable((observer) => {\n      // Used to track if the observable is being cleaned up.\n      let cleaningUp;\n\n      runtimeFetch(uri, options)\n        .then((response) => {\n          // Forward the response on the context.\n          operation.setContext({ response });\n          return response;\n        })\n        .then(parseAndCheckHttpResponse(operation))\n        .then((result) => {\n          observer.next(result);\n          observer.complete();\n        })\n        .catch((error) => {\n          // If the observable is being cleaned up, there is no need to call\n          // next or error because there are no more subscribers. An error after\n          // cleanup begins is likely from the cleanup function aborting the\n          // fetch.\n          if (!cleaningUp) {\n            // For errors such as an invalid fetch URI there will be no GraphQL\n            // result with errors or data to forward.\n            if (error.result && error.result.errors && error.result.data)\n              observer.next(error.result);\n\n            observer.error(error);\n          }\n        });\n\n      // Cleanup function.\n      return () => {\n        cleaningUp = true;\n\n        // Abort fetch. It’s ok to signal an abort even when not fetching.\n        if (controller) controller.abort();\n      };\n    });\n  });\n};\n", "\"use strict\";\n\n/**\n * Used to mark\n * [React Native `File` substitutes]{@link ReactNativeFileSubstitute} as it’s\n * too risky to assume all objects with `uri`, `type` and `name` properties are\n * extractable files.\n * @kind class\n * @name ReactNativeFile\n * @param {ReactNativeFileSubstitute} file A [React Native](https://reactnative.dev) [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File) substitute.\n * @see [`extract-files` `ReactNativeFile` docs](https://github.com/jaydenseric/extract-files#class-reactnativefile).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { ReactNativeFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import ReactNativeFile from \"apollo-upload-client/public/ReactNativeFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { ReactNativeFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const ReactNativeFile = require(\"apollo-upload-client/public/ReactNativeFile.js\");\n * ```\n * @example <caption>A file in [React Native](https://reactnative.dev) that can be used in query or mutation variables.</caption>\n * ```js\n * const file = new ReactNativeFile({\n *   uri: uriFromCameraRoll,\n *   name: \"a.jpg\",\n *   type: \"image/jpeg\",\n * });\n * ```\n */\nmodule.exports = require(\"extract-files/public/ReactNativeFile.js\");\n", "export { default as createUploadLink } from \"./createUploadLink.js\";\nexport { default as formDataAppendFile } from \"./formDataAppendFile.js\";\nexport { default as isExtractableFile } from \"./isExtractableFile.js\";\nexport { default as ReactNativeFile } from \"./ReactNativeFile.js\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAoCA,WAAO,UAAU,MAAM,gBAAgB;AAAA,MACrC,YAAY,EAAE,KAAK,MAAM,KAAK,GAAG;AAC/B,aAAK,MAAM;AACX,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA;AAAA;;;AC1CA;AAAA;AAAA;AAEA,QAAM,kBAAkB;AA0BxB,WAAO,UAAU,SAAS,kBAAkB,OAAO;AACjD,aACG,OAAO,SAAS,eAAe,iBAAiB,QAChD,OAAO,SAAS,eAAe,iBAAiB,QACjD,iBAAiB;AAAA,IAErB;AAAA;AAAA;;;AClCA;AAAA;AAAA;AAEA,QAAM,2BAA2B;AAiEjC,WAAO,UAAU,SAAS,aACxB,OACA,OAAO,IACP,oBAAoB,0BACpB;AAEA,YAAM,QAAQ,oBAAI,IAAI;AAKtB,YAAM,SAAS,oBAAI,IAAI;AAYvB,eAAS,QAAQA,QAAOC,OAAM,UAAU;AACtC,YAAI,QAAQD;AAEZ,YAAI,kBAAkBA,MAAK,GAAG;AAC5B,kBAAQ;AAER,gBAAM,YAAY,MAAM,IAAIA,MAAK;AAEjC,sBAAY,UAAU,KAAKC,KAAI,IAAI,MAAM,IAAID,QAAO,CAACC,KAAI,CAAC;AAAA,QAC5D,OAAO;AACL,gBAAM,SACJ,MAAM,QAAQD,MAAK,KAClB,OAAO,aAAa,eAAeA,kBAAiB;AACvD,gBAAM,WAAWA,UAASA,OAAM,gBAAgB;AAEhD,cAAI,UAAU,UAAU;AACtB,kBAAM,WAAW,OAAO,IAAIA,MAAK;AAEjC,gBAAI,SAAU,SAAQ,OAAO,IAAIA,MAAK;AAAA,iBACjC;AACH,sBAAQ,SAAS,CAAC,IAAI,CAAC;AAEvB,qBAAO,IAAIA,QAAO,KAAK;AAAA,YACzB;AAEA,gBAAI,CAAC,SAAS,IAAIA,MAAK,GAAG;AACxB,oBAAM,aAAaC,QAAO,GAAGA,KAAI,MAAM;AACvC,oBAAM,iBAAiB,IAAI,IAAI,QAAQ,EAAE,IAAID,MAAK;AAElD,kBAAI,QAAQ;AACV,oBAAI,QAAQ;AAEZ,2BAAW,QAAQA,QAAO;AACxB,wBAAM,YAAY;AAAA,oBAChB;AAAA,oBACA,aAAa;AAAA,oBACb;AAAA,kBACF;AAEA,sBAAI,CAAC,SAAU,OAAM,KAAK,SAAS;AAAA,gBACrC;AAAA,cACF;AACE,2BAAW,OAAOA,QAAO;AACvB,wBAAM,gBAAgB;AAAA,oBACpBA,OAAM,GAAG;AAAA,oBACT,aAAa;AAAA,oBACb;AAAA,kBACF;AAEA,sBAAI,CAAC,SAAU,OAAM,GAAG,IAAI;AAAA,gBAC9B;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,OAAO,QAAQ,OAAO,MAAM,oBAAI,IAAI,CAAC;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACxJA;AAAA;AAAA;AA8BA,WAAO,UAAU,SAAS,mBAAmB,UAAU,WAAW,MAAM;AACtE,eAAS,OAAO,WAAW,MAAM,KAAK,IAAI;AAAA,IAC5C;AAAA;AAAA;;;AChCA,IAAAE,6BAAA;AAAA;AAAA;AA4BA,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAAA;AAEA,QAAM,EAAE,YAAY,WAAW,IAAI;AACnC,QAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAM,eAAe;AACrB,QAAM,qBAAqB;AAC3B,QAAM,oBAAoB;AA4D1B,WAAO,UAAU,SAAS,iBAAiB;AAAA,MACzC,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,mBAAmB,0BAA0B;AAAA,MAC7C,UAAU;AAAA,MACV,oBAAoB,2BAA2B;AAAA,MAC/C,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,CAAC,GAAG;AACN,YAAM,aAAa;AAAA,QACjB,MAAM,EAAE,kBAAkB;AAAA,QAC1B,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAEA,aAAO,IAAI,WAAW,CAAC,cAAc;AACnC,cAAM,UAAU,UAAU,WAAW;AACrC,cAAM;AAAA;AAAA;AAAA;AAAA,UAIJ,iBAAiB,EAAE,MAAM,QAAQ,IAAI,CAAC;AAAA,UACtC,SAAAC;AAAA,QACF,IAAI;AAEJ,cAAM,gBAAgB;AAAA,UACpB,MAAM,QAAQ;AAAA,UACd,SAAS,QAAQ;AAAA,UACjB,aAAa,QAAQ;AAAA,UACrB,SAAS;AAAA;AAAA,YAEP,GAAI,QAAQ,EAAE,6BAA6B,KAAK;AAAA,YAChD,GAAI,WAAW,EAAE,gCAAgC,QAAQ;AAAA,YACzD,GAAGA;AAAA,UACL;AAAA,QACF;AAEA,cAAM,EAAE,SAAS,KAAK,IAAI;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,cAAM,EAAE,OAAO,MAAM,IAAI,aAAa,MAAM,IAAI,uBAAuB;AAEvE,YAAI,MAAM,UAAU,WAAW,QAAQ;AAEvC,YAAI,MAAM,MAAM;AAEd,iBAAO,QAAQ,QAAQ,cAAc;AAKrC,gBAAM,kBAAkB,kBAAkB;AAE1C,gBAAM,OAAO,IAAI,gBAAgB;AAEjC,eAAK,OAAO,cAAc,wBAAwB,OAAO,SAAS,CAAC;AAEnE,gBAAM,MAAM,CAAC;AACb,cAAI,IAAI;AACR,gBAAM,QAAQ,CAAC,UAAU;AACvB,gBAAI,EAAE,CAAC,IAAI;AAAA,UACb,CAAC;AACD,eAAK,OAAO,OAAO,KAAK,UAAU,GAAG,CAAC;AAEtC,cAAI;AACJ,gBAAM,QAAQ,CAAC,OAAO,SAAS;AAC7B,qCAAyB,MAAM,EAAE,GAAG,IAAI;AAAA,UAC1C,CAAC;AAED,kBAAQ,OAAO;AAAA,QACjB,OAAO;AACL,cACE;AAAA,UAEA,CAAC,UAAU,MAAM,YAAY;AAAA,YAC3B,CAAC,eACC,WAAW,SAAS,yBACpB,WAAW,cAAc;AAAA,UAC7B;AAEA,oBAAQ,SAAS;AAEnB,cAAI,QAAQ,WAAW,OAAO;AAC5B,kBAAM,EAAE,QAAQ,WAAW,IAAI,iBAAiB,KAAK,IAAI;AACzD,gBAAI;AAGF,qBAAO,IAAI,WAAW,CAAC,aAAa;AAClC,yBAAS,MAAM,UAAU;AAAA,cAC3B,CAAC;AACH,kBAAM;AAAA,UACR,MAAO,SAAQ,OAAO,wBAAwB,OAAO,SAAS;AAAA,QAChE;AAEA,cAAM,EAAE,WAAW,IAAI,wBAAwB;AAE/C,YAAI,YAAY;AACd,cAAI,QAAQ;AAEV,oBAAQ,OAAO;AAAA;AAAA,cAEX,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA,cAGjB,QAAQ,OAAO;AAAA,gBACb;AAAA,gBACA,MAAM;AACJ,6BAAW,MAAM;AAAA,gBACnB;AAAA,gBACA;AAAA;AAAA;AAAA,kBAGE,MAAM;AAAA,gBACR;AAAA,cACF;AAAA;AAEN,kBAAQ,SAAS,WAAW;AAAA,QAC9B;AAEA,cAAM,eAAe,eAAe;AAEpC,eAAO,IAAI,WAAW,CAAC,aAAa;AAElC,cAAI;AAEJ,uBAAa,KAAK,OAAO,EACtB,KAAK,CAAC,aAAa;AAElB,sBAAU,WAAW,EAAE,SAAS,CAAC;AACjC,mBAAO;AAAA,UACT,CAAC,EACA,KAAK,0BAA0B,SAAS,CAAC,EACzC,KAAK,CAAC,WAAW;AAChB,qBAAS,KAAK,MAAM;AACpB,qBAAS,SAAS;AAAA,UACpB,CAAC,EACA,MAAM,CAAC,UAAU;AAKhB,gBAAI,CAAC,YAAY;AAGf,kBAAI,MAAM,UAAU,MAAM,OAAO,UAAU,MAAM,OAAO;AACtD,yBAAS,KAAK,MAAM,MAAM;AAE5B,uBAAS,MAAM,KAAK;AAAA,YACtB;AAAA,UACF,CAAC;AAGH,iBAAO,MAAM;AACX,yBAAa;AAGb,gBAAI,WAAY,YAAW,MAAM;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA;;;AClPA,IAAAC,2BAAA;AAAA;AAAA;AAoCA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB,8BAA4C;AAC5C,gCAA8C;AAC9C,+BAA6C;AAC7C,6BAA2C;", "names": ["value", "path", "require_isExtractableFile", "headers", "require_ReactNativeFile"]}