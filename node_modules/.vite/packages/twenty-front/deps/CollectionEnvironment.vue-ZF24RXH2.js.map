{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/CollectionEnvironment.vue2.js"], "sourcesContent": ["import { defineComponent as e, openBlock as n, createBlock as o, withCtx as r, createTextVNode as i } from \"vue\";\nimport m from \"../../components/ViewLayout/ViewLayoutSection.vue.js\";\nconst s = /* @__PURE__ */ e({\n  __name: \"CollectionEnvironment\",\n  setup(_) {\n    return (l, t) => (n(), o(m, null, {\n      title: r(() => t[0] || (t[0] = [\n        i(\"Environments\")\n      ])),\n      _: 1\n    }));\n  }\n});\nexport {\n  s as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAMA,IAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG,MAAM;AAAA,MAChC,OAAO,QAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,QAC7B,gBAAE,cAAc;AAAA,MAClB,EAAE;AAAA,MACF,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": ["_"]}