import {
  w
} from "./chunk-OD3MJMNF.js";
import {
  useRouter
} from "./chunk-Z6VJM5ZM.js";
import {
  s
} from "./chunk-MFP3SY2Q.js";
import {
  $,
  E2 as E,
  Fragment,
  c,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  f,
  guardReactiveProps,
  i2 as i,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onMounted,
  openBlock,
  p,
  ref,
  renderSlot,
  resolveComponent,
  toDisplayString,
  unref,
  vModelText,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-NPL3RUXR.js";

// node_modules/@scalar/api-client/dist/components/Sidebar/SidebarList.vue.js
var s2 = {};
var l = { class: "gap-1/2 flex flex-col px-3 pb-[75px]" };
function n(e, f2) {
  return openBlock(), createElementBlock("ul", l, [
    renderSlot(e.$slots, "default")
  ]);
}
var _ = s(s2, [["render", n]]);

// node_modules/@scalar/api-client/dist/components/Sidebar/SidebarListElementActions.vue.js
var E2 = { class: "absolute right-1 flex opacity-0 group-hover:opacity-100" };
var R = defineComponent({
  __name: "SidebarListElementActions",
  props: {
    variable: {},
    warningMessage: {},
    isCopyable: { type: Boolean },
    isDeletable: { type: Boolean },
    isRenameable: { type: Boolean }
  },
  emits: ["delete", "rename"],
  setup(d, { emit: p2 }) {
    const b2 = p2, o = ref({ action: "None", name: "" }), s3 = E(), { copyToClipboard: v2 } = p();
    function f2(e) {
      o.value = { action: e, name: d.variable.name }, s3.show();
    }
    function c2() {
      s3.hide(), o.value = { action: "None", name: "" };
    }
    function y(e) {
      b2("delete", e), c2();
    }
    return (e, t) => (openBlock(), createElementBlock(Fragment, null, [
      createBaseVNode("div", E2, [
        e.isCopyable ? (openBlock(), createElementBlock("button", {
          key: 0,
          class: "text-c-3 hover:bg-b-3 hover:text-c-1 rounded p-[5px]",
          type: "button",
          onClick: t[0] || (t[0] = (u) => unref(v2)(e.variable.name))
        }, [
          createVNode(unref(c), {
            class: "h-3 w-3",
            icon: "Clipboard"
          })
        ])) : createCommentVNode("", true),
        e.isRenameable ? (openBlock(), createElementBlock("button", {
          key: 1,
          class: "text-c-3 hover:bg-b-3 hover:text-c-1 rounded p-[5px]",
          type: "button",
          onClick: t[1] || (t[1] = (u) => b2("rename", e.variable.uid))
        }, [
          createVNode(unref(c), {
            class: "h-3 w-3",
            icon: "Edit"
          })
        ])) : createCommentVNode("", true),
        !e.variable.isDefault && e.isDeletable ? (openBlock(), createElementBlock("button", {
          key: 2,
          class: "text-c-3 hover:bg-b-3 hover:text-c-1 rounded p-1",
          type: "button",
          onClick: t[2] || (t[2] = withModifiers((u) => f2(
            "Delete"
            /* Delete */
          ), ["prevent"]))
        }, [
          createVNode(unref(c), {
            class: "h-3.5 w-3.5",
            icon: "Close"
          })
        ])) : createCommentVNode("", true)
      ]),
      createVNode(unref(f), {
        size: "sm",
        state: unref(s3),
        title: `${o.value.action} ${o.value.name}`
      }, {
        default: withCtx(() => [
          o.value.action === "Delete" ? (openBlock(), createBlock(w, {
            key: 0,
            variableName: o.value.name,
            warningMessage: e.warningMessage,
            onClose: c2,
            onDelete: t[3] || (t[3] = (u) => y(e.variable.uid))
          }, null, 8, ["variableName", "warningMessage"])) : createCommentVNode("", true)
        ]),
        _: 1
      }, 8, ["state", "title"])
    ], 64));
  }
});

// node_modules/@scalar/api-client/dist/components/Sidebar/SidebarListElement.vue2.js
var E3 = { class: "empty-variable-name line-clamp-1 break-all text-sm group-hover:pr-5" };
var K = defineComponent({
  __name: "SidebarListElement",
  props: {
    variable: {},
    warningMessage: {},
    to: {},
    isDeletable: { type: Boolean },
    isCopyable: { type: Boolean },
    isRenameable: { type: Boolean }
  },
  emits: ["delete", "colorModal", "rename"],
  setup(p2, { emit: u }) {
    const n2 = p2, a2 = u, r = useRouter(), c2 = (e) => {
      e.metaKey ? window.open(r.resolve(n2.to).href, "_blank") : r.push(n2.to);
    }, d = (e) => {
      a2("delete", e);
    }, v2 = (e) => {
      a2("colorModal", e);
    }, f2 = (e) => {
      a2("rename", e);
    };
    return (e, o) => {
      const g = resolveComponent("router-link");
      return openBlock(), createElementBlock("li", null, [
        createVNode(g, {
          class: normalizeClass(["text-c-2 hover:bg-b-2 group relative flex h-8 items-center gap-1.5 rounded py-1 pr-1.5 font-medium no-underline", [e.variable.color ? "pl-1" : "pl-1.5"]]),
          exactActiveClass: "bg-b-2 text-c-1",
          role: "button",
          to: e.to,
          onClick: o[1] || (o[1] = withModifiers((t) => c2(t), ["prevent"]))
        }, {
          default: withCtx(() => [
            e.variable.color ? (openBlock(), createElementBlock("button", {
              key: 0,
              class: "hover:bg-b-3 rounded p-1.5",
              type: "button",
              onClick: o[0] || (o[0] = (t) => v2(e.variable.uid))
            }, [
              createBaseVNode("div", {
                class: "h-2.5 w-2.5 rounded-xl",
                style: normalizeStyle({ backgroundColor: e.variable.color })
              }, null, 4)
            ])) : createCommentVNode("", true),
            e.variable.icon ? (openBlock(), createBlock(unref(c), {
              key: 1,
              class: "text-sidebar-c-2 size-3.5 stroke-[2.25]",
              icon: e.variable.icon
            }, null, 8, ["icon"])) : createCommentVNode("", true),
            createBaseVNode("span", E3, toDisplayString(e.variable.name), 1),
            createVNode(R, {
              isCopyable: !!e.isCopyable,
              isDeletable: !!e.isDeletable,
              isRenameable: !!e.isRenameable,
              variable: { ...e.variable, isDefault: e.variable.isDefault ?? false },
              warningMessage: e.warningMessage,
              onDelete: d,
              onRename: f2
            }, null, 8, ["isCopyable", "isDeletable", "isRenameable", "variable", "warningMessage"])
          ]),
          _: 1
        }, 8, ["class", "to"])
      ]);
    };
  }
});

// node_modules/@scalar/api-client/dist/components/Sidebar/SidebarListElement.vue.js
var a = s(K, [["__scopeId", "data-v-245380f1"]]);

// node_modules/@scalar/api-client/dist/components/CommandPalette/CommandActionForm.vue.js
var v = { class: "flex gap-2" };
var w2 = { class: "flex max-h-8 flex-1" };
var h = defineComponent({
  __name: "CommandActionForm",
  props: {
    loading: {},
    disabled: { type: Boolean, default: false }
  },
  emits: ["submit", "cancel", "back"],
  setup(B) {
    const { cx: i2 } = i();
    return (e, o) => (openBlock(), createElementBlock("form", {
      class: "flex w-full flex-col gap-3",
      onKeydown: o[0] || (o[0] = withKeys(withModifiers(() => {
      }, ["stop"]), ["enter"])),
      onSubmit: o[1] || (o[1] = withModifiers((C) => e.$emit("submit"), ["prevent", "stop"]))
    }, [
      createBaseVNode("div", normalizeProps(guardReactiveProps(unref(i2)("relative flex min-h-20 flex-col rounded"))), [
        renderSlot(e.$slots, "default")
      ], 16),
      createBaseVNode("div", v, [
        createBaseVNode("div", w2, [
          renderSlot(e.$slots, "options")
        ]),
        createVNode(unref($), {
          class: "max-h-8 p-0 px-3 text-xs",
          disabled: e.disabled,
          loading: e.loading,
          type: "submit"
        }, {
          default: withCtx(() => [
            renderSlot(e.$slots, "submit", {}, () => [
              o[2] || (o[2] = createTextVNode("Continue"))
            ])
          ]),
          _: 3
        }, 8, ["disabled", "loading"])
      ])
    ], 32));
  }
});

// node_modules/@scalar/api-client/dist/components/CommandPalette/CommandActionInput.vue.js
var E4 = ["placeholder"];
var b = defineComponent({
  inheritAttrs: false,
  __name: "CommandActionInput",
  props: {
    modelValue: {},
    placeholder: {},
    autofocus: { type: Boolean }
  },
  emits: ["update:modelValue", "onDelete"],
  setup(i2, { emit: p2 }) {
    const n2 = i2, r = p2, a2 = ref(null);
    onMounted(
      () => nextTick(() => {
        var e;
        n2.autofocus || (e = a2.value) == null || e.focus();
      })
    );
    const l2 = computed({
      get: () => n2.modelValue ?? "",
      set: (e) => r("update:modelValue", e)
    });
    function d(e) {
      var u;
      if (e.shiftKey || !e.target) return;
      e.preventDefault();
      const t = e.target, o = new Event("submit", { cancelable: true });
      (u = t.form) == null || u.dispatchEvent(o);
    }
    function m(e) {
      l2.value === "" && (e.preventDefault(), e.stopPropagation(), r("onDelete", e));
    }
    return (e, t) => withDirectives((openBlock(), createElementBlock("textarea", mergeProps({
      ref_key: "input",
      ref: a2,
      "onUpdate:modelValue": t[0] || (t[0] = (o) => l2.value = o),
      class: "min-h-8 w-full flex-1 resize-none border-none py-1.5 pl-8 text-sm outline-none",
      placeholder: n2.placeholder ?? "",
      wrap: "hard"
    }, e.$attrs, {
      onKeydown: [
        t[1] || (t[1] = withKeys((o) => m(o), ["delete"])),
        t[2] || (t[2] = withKeys((o) => d(o), ["enter"]))
      ]
    }), null, 16, E4)), [
      [vModelText, l2.value]
    ]);
  }
});

export {
  _,
  a,
  h,
  b
};
//# sourceMappingURL=chunk-4EBVZBV7.js.map
