import {
  isMacOS
} from "./chunk-LHBPKJEO.js";

// node_modules/@scalar/api-client/dist/libs/hot-keys.js
var f = [
  "Escape",
  "ArrowDown",
  "ArrowUp",
  "Enter",
  "F1",
  "F2",
  "F3",
  "F4",
  "F5",
  "F6",
  "F7",
  "F8",
  "F9",
  "F10",
  "F11",
  "F12"
];
var d = {
  Escape: { event: "closeModal" },
  Enter: { event: "executeRequest", modifiers: ["default"] },
  b: { event: "toggleSidebar", modifiers: ["default"] },
  k: { event: "openCommandPalette", modifiers: ["default"] },
  l: { event: "focusAddressBar", modifiers: ["default"] }
};
var c = (e) => {
  if (!(e.target instanceof HTMLElement)) return false;
  const t = e.target;
  return t.tagName === "INPUT" ? !f.includes(e.key) : !!(t.tagName === "TEXTAREA" || t.getAttribute("contenteditable") || t.contentEditable === "true");
};
var l = {
  Alt: "altKey",
  Control: "ctrlKey",
  Shift: "shiftKey",
  Meta: "metaKey"
};
var u = (e) => e.map((t) => t === "default" ? isMacOS() ? "metaKey" : "ctrlKey" : l[t]);
var E = (e, t, { hotKeys: o = d, modifiers: n = ["default"] } = {}) => {
  const i = e.key === " " ? "Space" : e.key, r = o[i];
  r && (i === "Escape" ? t.emit({ [r.event]: e }) : u(r.modifiers || n).every((s) => e[s] === true) ? t.emit({ [r.event]: e }) : !c(e) && r.modifiers === void 0 && t.emit({ [r.event]: e }));
};

export {
  u,
  E
};
//# sourceMappingURL=chunk-PMU7JHTD.js.map
