import {
  _
} from "./chunk-TACAB4LM.js";
import {
  createBlock,
  createTextVNode,
  defineComponent,
  openBlock,
  withCtx
} from "./chunk-NPL3RUXR.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-4DUSDZ2B.js";
import "./chunk-BVF5FUF3.js";
import "./chunk-YRBF5NWE.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-IX2KTD5L.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-URP2UYTW.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-MR4E537R.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CAP4CFCM.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Collection/CollectionCookies.vue2.js
var c = defineComponent({
  __name: "CollectionCookies",
  setup(l) {
    return (m, e) => (openBlock(), createBlock(_, null, {
      title: withCtx(() => e[0] || (e[0] = [
        createTextVNode("Cookies")
      ])),
      _: 1
    }));
  }
});
export {
  c as default
};
//# sourceMappingURL=CollectionCookies.vue-GPCQASBB.js.map
