import {
  _ as _3,
  a as a2,
  b,
  h
} from "./chunk-4EBVZBV7.js";
import {
  C
} from "./chunk-3UB6XEFM.js";
import {
  g
} from "./chunk-WA7SXFY3.js";
import {
  x
} from "./chunk-VJ7NQHUB.js";
import "./chunk-4Q4UAWOO.js";
import {
  d
} from "./chunk-VWRBV6HI.js";
import {
  u
} from "./chunk-5HYEUVPS.js";
import {
  a
} from "./chunk-OD3MJMNF.js";
import {
  m as m2
} from "./chunk-IPQF6MZF.js";
import {
  m
} from "./chunk-LHBPKJEO.js";
import {
  _ as _2
} from "./chunk-TG5SUGVT.js";
import "./chunk-OFOUQLHF.js";
import {
  useRoute,
  useRouter
} from "./chunk-Z6VJM5ZM.js";
import {
  F,
  a as a3,
  je
} from "./chunk-MFP3SY2Q.js";
import {
  _
} from "./chunk-TACAB4LM.js";
import {
  $,
  E2 as E,
  Fragment,
  P,
  c,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineComponent,
  f,
  i3 as i,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  openBlock,
  ref,
  renderList,
  toDisplayString,
  unref,
  vModelText,
  vShow,
  watch,
  withCtx,
  withDirectives,
  withModifiers
} from "./chunk-NPL3RUXR.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-4DUSDZ2B.js";
import "./chunk-BVF5FUF3.js";
import "./chunk-YRBF5NWE.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-IX2KTD5L.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-URP2UYTW.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-MR4E537R.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CAP4CFCM.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-O3EO7ESF.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-WESWXL2S.js";
import "./chunk-2S2EUIOI.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Environment/EnvironmentColors.vue.js
var R = ["onClick"];
var T = ["placeholder"];
var U = defineComponent({
  __name: "EnvironmentColors",
  props: {
    activeColor: {},
    selector: { type: Boolean, default: false }
  },
  emits: ["select"],
  setup(x2, { emit: E2 }) {
    const e = x2, z = E2, o = ref(""), C2 = ref(null), a4 = ref(false), u2 = ref(false), k = [
      { color: "#8E8E8E" },
      { color: "#EF0006" },
      { color: "#EDBE20" },
      { color: "#069061" },
      { color: "#FB892C" },
      { color: "#0082D0" },
      { color: "#5203D1" },
      { color: "#FFC0CB" }
    ], b3 = computed(() => e.activeColor && !k.some((l) => l.color === e.activeColor) || o.value ? `background-color: ${e.activeColor || o.value};` : "background: linear-gradient(to right, rgb(235, 87, 87), rgb(242, 201, 76), rgb(76, 183, 130), rgb(78, 167, 252), rgb(250, 96, 122));"), y = () => {
      a4.value = !a4.value, e.selector && (u2.value = false), nextTick(() => {
        C2.value && C2.value.focus();
      });
    };
    watch(o, (l) => {
      l && !l.startsWith("#") && (o.value = `#${l}`), a4.value = true;
    });
    const B = () => {
      e.selector && (u2.value = !u2.value);
    }, g2 = (l) => {
      z("select", l), e.selector && (u2.value = false);
    };
    return (l, i2) => (openBlock(), createElementBlock("div", null, [
      a4.value ? createCommentVNode("", true) : (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        e.selector && !u2.value ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(["flex cursor-pointer items-center justify-center rounded-full", e.selector ? "h-4 w-4" : "h-5 w-5"]),
          style: normalizeStyle({ backgroundColor: l.activeColor }),
          onClick: B
        }, [
          l.activeColor ? (openBlock(), createBlock(unref(c), {
            key: 0,
            class: normalizeClass(["text-c-btn", e.selector && "p-0.5"]),
            icon: "Checkmark",
            size: "xs"
          }, null, 8, ["class"])) : createCommentVNode("", true)
        ], 6)) : createCommentVNode("", true),
        u2.value || !e.selector ? (openBlock(), createElementBlock("div", {
          key: 1,
          class: normalizeClass(["color-selector flex flex-row items-center justify-between gap-1.5 space-x-1", e.selector ? "h-4" : "min-h-10 min-w-[296px]"])
        }, [
          (openBlock(), createElementBlock(Fragment, null, renderList(k, (r) => createBaseVNode("div", {
            key: r.color,
            class: normalizeClass(["flex cursor-pointer items-center justify-center rounded-full", e.selector ? "h-4 w-4" : "h-5 w-5"]),
            style: normalizeStyle({ backgroundColor: r.color }),
            onClick: (L) => g2(r.color)
          }, [
            l.activeColor === r.color && !o.value ? (openBlock(), createBlock(unref(c), {
              key: 0,
              class: normalizeClass(["text-c-btn", e.selector && "p-0.5"]),
              icon: "Checkmark",
              size: "xs"
            }, null, 8, ["class"])) : createCommentVNode("", true)
          ], 14, R)), 64)),
          i2[2] || (i2[2] = createBaseVNode("hr", { class: "border-ghost h-5 w-0.5 border-l" }, null, -1)),
          createBaseVNode("label", {
            class: normalizeClass(["z-10 flex cursor-pointer flex-row items-center justify-center gap-2 rounded-full", e.selector ? "h-4 w-4" : "h-5 w-5"]),
            style: normalizeStyle(b3.value),
            onClick: y
          }, [
            !a4.value && (l.activeColor === o.value || l.activeColor && !k.some((r) => r.color === l.activeColor)) ? (openBlock(), createBlock(unref(c), {
              key: 0,
              class: "text-c-btn",
              icon: "Checkmark",
              size: "xs"
            })) : createCommentVNode("", true)
          ], 6)
        ], 2)) : createCommentVNode("", true)
      ], 64)),
      a4.value ? (openBlock(), createElementBlock("div", {
        key: 1,
        class: normalizeClass(["color-selector flex flex-1 items-center gap-2 rounded", e.selector ? "h-4" : "min-h-10"])
      }, [
        createBaseVNode("span", {
          class: normalizeClass(["absolute rounded-full border border-dashed", e.selector ? "h-4 w-4" : "h-5 w-5"])
        }, null, 2),
        createBaseVNode("span", {
          class: normalizeClass(["z-[1] rounded-full", e.selector ? "h-4 w-4" : "h-5 w-5"]),
          style: normalizeStyle(b3.value)
        }, null, 6),
        withDirectives(createBaseVNode("input", {
          ref_key: "customColorInputRef",
          ref: C2,
          "onUpdate:modelValue": i2[0] || (i2[0] = (r) => o.value = r),
          class: "w-full flex-1 border-transparent text-sm outline-none",
          placeholder: l.activeColor || "#000000",
          type: "text",
          onInput: i2[1] || (i2[1] = (r) => g2(o.value))
        }, null, 40, T), [
          [vModelText, o.value]
        ]),
        createBaseVNode("button", {
          class: "text-c-3 hover:bg-b-2 rounded-lg p-1.5",
          type: "button",
          onClick: y
        }, [
          createVNode(unref(c), {
            icon: "Checkmark",
            size: "xs"
          })
        ])
      ], 2)) : createCommentVNode("", true)
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Environment/EnvironmentColorModal.vue.js
var b2 = { class: "flex flex-col gap-4" };
var M = defineComponent({
  __name: "EnvironmentColorModal",
  props: {
    state: {},
    selectedColor: {}
  },
  emits: ["cancel", "submit"],
  setup(r, { emit: a4 }) {
    const s = r, o = a4, e = ref(""), c2 = (t) => {
      e.value = t;
    }, i2 = () => {
      o("submit", e.value), e.value = "";
    };
    return (t, l) => (openBlock(), createBlock(unref(f), {
      size: "xxs",
      state: t.state,
      title: "Edit Environment Color"
    }, {
      default: withCtx(() => [
        createBaseVNode("div", b2, [
          createVNode(U, {
            activeColor: e.value || s.selectedColor,
            class: "w-full p-1",
            onSelect: c2
          }, null, 8, ["activeColor"]),
          createVNode(a, {
            onCancel: l[0] || (l[0] = (E2) => o("cancel")),
            onSubmit: i2
          })
        ])
      ]),
      _: 1
    }, 8, ["state"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Environment/EnvironmentModal.vue.js
var P2 = { class: "flex items-start gap-2" };
var K = defineComponent({
  __name: "EnvironmentModal",
  props: {
    state: {},
    activeWorkspaceCollections: {},
    collectionId: {}
  },
  emits: ["cancel", "submit"],
  setup(x2, { emit: g2 }) {
    const n = x2, f2 = g2, { events: E2 } = je(), r = ref(""), c2 = ref("#8E8E8E"), d2 = computed(() => [
      ...n.activeWorkspaceCollections.filter((t) => {
        var e;
        return ((e = t.info) == null ? void 0 : e.title) !== "Drafts";
      }).map((t) => {
        var e;
        return {
          id: t.uid,
          label: ((e = t.info) == null ? void 0 : e.title) ?? "Untitled Collection"
        };
      })
    ]), l = ref(
      d2.value.find((t) => t.id === n.collectionId)
    ), { toast: S } = i(), V = (t) => {
      c2.value = t;
    };
    watch(
      () => n.state.open,
      (t) => {
        t && (r.value = "", c2.value = "#8E8E8E", n.collectionId ? l.value = d2.value.find(
          (e) => e.id === n.collectionId
        ) : l.value = void 0);
      }
    );
    const k = () => {
      var t, e, o, b3;
      if (!((t = l.value) != null && t.id)) {
        S("Please select a collection before adding an environment.", "error");
        return;
      }
      f2("submit", {
        name: r.value,
        color: c2.value,
        type: ((e = l.value) == null ? void 0 : e.id) === "global" ? "global" : "collection",
        collectionId: ((o = l.value) == null ? void 0 : o.id) !== "global" ? (b3 = l.value) == null ? void 0 : b3.id : void 0
      });
    }, y = () => {
      n.state.hide(), E2.commandPalette.emit({ commandName: "Create Collection" });
    };
    return (t, e) => (openBlock(), createBlock(unref(f), {
      bodyClass: "border-t-0 rounded-t-lg",
      size: "xs",
      state: t.state
    }, {
      default: withCtx(() => [
        createVNode(h, {
          disabled: !l.value,
          onCancel: e[2] || (e[2] = (o) => f2("cancel")),
          onSubmit: k
        }, {
          options: withCtx(() => [
            createVNode(unref(P), {
              modelValue: l.value,
              "onUpdate:modelValue": e[1] || (e[1] = (o) => l.value = o),
              options: d2.value,
              placeholder: "Select Type"
            }, {
              default: withCtx(() => [
                d2.value.length > 0 ? (openBlock(), createBlock(unref($), {
                  key: 0,
                  class: "hover:bg-b-2 max-h-8 w-fit justify-between gap-1 p-2 text-xs",
                  variant: "outlined"
                }, {
                  default: withCtx(() => [
                    createBaseVNode("span", {
                      class: normalizeClass(l.value ? "text-c-1" : "text-c-3")
                    }, toDisplayString(l.value ? l.value.label : "Select Collection"), 3),
                    createVNode(unref(c), {
                      class: "text-c-3",
                      icon: "ChevronDown",
                      size: "xs"
                    })
                  ]),
                  _: 1
                })) : (openBlock(), createBlock(unref($), {
                  key: 1,
                  class: "hover:bg-b-2 max-h-8 justify-between gap-1 p-2 text-xs",
                  variant: "outlined",
                  onClick: y
                }, {
                  default: withCtx(() => e[3] || (e[3] = [
                    createBaseVNode("span", { class: "text-c-1" }, "Create Collection", -1)
                  ])),
                  _: 1
                }))
              ]),
              _: 1
            }, 8, ["modelValue", "options"])
          ]),
          submit: withCtx(() => e[4] || (e[4] = [
            createTextVNode(" Add Environment ")
          ])),
          default: withCtx(() => [
            createBaseVNode("div", P2, [
              createVNode(U, {
                activeColor: c2.value,
                class: "peer",
                selector: "",
                onSelect: V
              }, null, 8, ["activeColor"]),
              createVNode(b, {
                modelValue: r.value,
                "onUpdate:modelValue": e[0] || (e[0] = (o) => r.value = o),
                class: "-mt-[.5px] !p-0 peer-has-[.color-selector]:hidden",
                placeholder: "Environment name"
              }, null, 8, ["modelValue"])
            ])
          ]),
          _: 1
        }, 8, ["disabled"])
      ]),
      _: 1
    }, 8, ["state"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Environment/Environment.vue2.js
var De = { class: "flex-1" };
var Ue = ["onClick"];
var We = { class: "flex h-5 max-w-[14px] items-center justify-center" };
var ln = defineComponent({
  __name: "Environment",
  setup(ze) {
    const C2 = useRouter(), E2 = useRoute(), {
      activeWorkspace: x2,
      activeEnvironment: j,
      activeWorkspaceCollections: u2,
      activeEnvVariables: H
    } = F(), { events: N, workspaceMutators: P3, collectionMutators: k } = je(), { collapsedSidebarFolders: y, toggleSidebarFolder: _4 } = m(), w = E(), I = E(), M2 = E(), R2 = ref(null), s = ref("default"), D = ref(""), b3 = ref(void 0), d2 = ref(void 0), S = ref(void 0), { toast: U2 } = i(), q = (e) => JSON.parse(e);
    function W(e, o, n) {
      var t;
      e && (o.uid === n ? U2(
        `Environment name already used in ${(t = o.info) == null ? void 0 : t.title}`,
        "error"
      ) : U2("Environment name already used in another collection", "error"));
    }
    function Q(e) {
      u2.value.some(
        (n) => {
          const t = Object.keys(
            n["x-scalar-environments"] || {}
          ).includes(e.name);
          return W(t, n, e.collectionId), t;
        }
      ) || (e.collectionId && (k.addEnvironment(
        e.name,
        {
          variables: {},
          color: e.color
        },
        e.collectionId
      ), y[e.collectionId] || _4(e.collectionId), C2.push({
        name: "environment.collection",
        params: {
          [a3.Collection]: e.collectionId,
          [a3.Environment]: e.name
        }
      })), I.hide());
    }
    function X(e) {
      var o, n;
      if (j) {
        const t = q(e);
        if (s.value === "default")
          P3.edit(
            (o = x2.value) == null ? void 0 : o.uid,
            "environments",
            t
          );
        else {
          const a4 = u2.value.find(
            (l) => {
              var f2;
              return (f2 = l["x-scalar-environments"]) == null ? void 0 : f2[s.value ?? ""];
            }
          );
          if ((n = a4 == null ? void 0 : a4["x-scalar-environments"]) != null && n[s.value ?? ""]) {
            const l = a4["x-scalar-environments"][s.value ?? ""];
            l && (l.variables = t, k.edit(
              a4.uid,
              "x-scalar-environments",
              a4["x-scalar-environments"]
            ));
          }
        }
      }
    }
    const $2 = (e) => {
      b3.value = e, I.show();
    }, Y = (e, o) => {
      d2.value = e, b3.value = o, S.value = e, M2.show();
    }, Z = (e) => {
      var o, n, t;
      R2.value = e, D.value = ((t = (n = (o = u2.value.find(
        (a4) => {
          var l;
          return (l = a4["x-scalar-environments"]) == null ? void 0 : l[e];
        }
      )) == null ? void 0 : o["x-scalar-environments"]) == null ? void 0 : n[e]) == null ? void 0 : t.color) ?? "", w.show();
    }, ee = (e) => {
      const o = R2.value;
      typeof o == "string" && (u2.value.some(
        (t) => {
          var a4;
          return (a4 = t["x-scalar-environments"]) == null ? void 0 : a4[o];
        }
      ) && u2.value.forEach((t) => {
        var a4;
        (a4 = t["x-scalar-environments"]) != null && a4[o] && (t["x-scalar-environments"][o].color = e, k.edit(
          t.uid,
          "x-scalar-environments",
          t["x-scalar-environments"]
        ));
      }), w.hide());
    };
    function ne(e) {
      u2.value.forEach((n) => {
        k.removeEnvironment(e, n.uid);
      });
      const o = u2.value.flatMap(
        (n) => Object.keys(n["x-scalar-environments"] || {})
      );
      if (o.length > 0) {
        const n = o[o.length - 1];
        if (!n) return;
        const t = u2.value.find(
          (a4) => Object.keys(a4["x-scalar-environments"] || {}).includes(
            n
          )
        );
        s.value = n, C2.push({
          name: "environment.collection",
          params: {
            collectionId: t == null ? void 0 : t.uid,
            environmentId: n
          }
        }), t && !y[t.uid] && _4(t.uid);
      } else
        s.value = "default", C2.push({
          name: "environment",
          params: { environment: "default" }
        });
    }
    const te = () => s.value === "default" ? "Global Environment" : s.value, oe = () => {
      var e, o, n, t;
      return s.value === "default" ? JSON.stringify((e = x2.value) == null ? void 0 : e.environments, null, 2) : JSON.stringify(
        (t = (n = (o = u2.value.find(
          (a4) => {
            var l;
            return (l = a4["x-scalar-environments"]) == null ? void 0 : l[s.value ?? ""];
          }
        )) == null ? void 0 : o["x-scalar-environments"]) == null ? void 0 : n[s.value ?? ""]) == null ? void 0 : t.variables,
        null,
        2
      );
    }, ae = (e) => y[e], z = (e) => {
      e != null && e.createNew && E2.name === "environment" && $2();
    };
    watch(
      () => [E2.params[a3.Collection], E2.params[a3.Environment]],
      ([e, o]) => {
        e ? s.value = o : s.value = "default";
      }
    ), onMounted(() => {
      s.value = E2.params[a3.Environment] || "default", N.hotKeys.on(z);
      const e = E2.params[a3.Collection];
      e && !y[e] && _4(e);
    }), onBeforeUnmount(() => N.hotKeys.off(z));
    const le = (e, o, n) => {
      var a4, l;
      const t = n ? {
        name: "environment.collection",
        params: {
          [a3.Workspace]: (a4 = x2.value) == null ? void 0 : a4.uid,
          [a3.Collection]: n,
          [a3.Environment]: o
        }
      } : {
        name: "environment.default",
        params: {
          [a3.Workspace]: (l = x2.value) == null ? void 0 : l.uid,
          [a3.Environment]: o
        }
      };
      e.metaKey ? window.open(C2.resolve(t).href, "_blank") : C2.push(t);
    };
    function re() {
      d2.value = void 0, b3.value = void 0, S.value = void 0, M2.hide();
    }
    function se(e) {
      u2.value.some(
        (n) => {
          const t = Object.keys(
            n["x-scalar-environments"] || {}
          ).includes(e);
          return W(
            t,
            n,
            b3.value
          ), t;
        }
      ) || (e && d2.value !== "default" && u2.value.forEach((n) => {
        var t;
        if ((t = n["x-scalar-environments"]) != null && t[d2.value ?? ""]) {
          const a4 = n["x-scalar-environments"][d2.value ?? ""];
          a4 && (delete n["x-scalar-environments"][d2.value ?? ""], n["x-scalar-environments"][e] = a4, k.edit(
            n.uid,
            "x-scalar-environments",
            n["x-scalar-environments"]
          ));
        }
      }), e && s.value === d2.value && (s.value = e), d2.value = void 0, b3.value = void 0, S.value = void 0, M2.hide());
    }
    return (e, o) => (openBlock(), createBlock(u, null, {
      default: withCtx(() => [
        createVNode(m2, { title: "Collections" }, {
          content: withCtx(() => [
            createBaseVNode("div", De, [
              createVNode(_3, null, {
                default: withCtx(() => [
                  (openBlock(), createBlock(a2, {
                    key: "default",
                    class: "text-xs",
                    isCopyable: false,
                    to: {
                      name: "environment.default",
                      params: {
                        [unref(a3).Environment]: "default"
                      }
                    },
                    type: "environment",
                    variable: {
                      name: "Global Environment",
                      uid: "default",
                      icon: "Globe",
                      isDefault: true
                    }
                  }, null, 8, ["to"])),
                  (openBlock(true), createElementBlock(Fragment, null, renderList(unref(u2), (n) => {
                    var t;
                    return openBlock(), createElementBlock("li", {
                      key: n.uid,
                      class: "gap-1/2 flex flex-col"
                    }, [
                      createBaseVNode("button", {
                        class: "hover:bg-b-2 group flex w-full items-center gap-1.5 break-words rounded p-1.5 text-left text-sm font-medium",
                        type: "button",
                        onClick: (a4) => unref(_4)(n.uid)
                      }, [
                        createBaseVNode("span", We, [
                          createVNode(unref(d), {
                            class: "text-sidebar-c-2 size-3.5 min-w-3.5 stroke-2 group-hover:hidden",
                            src: n["x-scalar-icon"] || "interface-content-folder"
                          }, null, 8, ["src"]),
                          createBaseVNode("div", {
                            class: normalizeClass({
                              "rotate-90": unref(y)[n.uid]
                            })
                          }, [
                            createVNode(unref(c), {
                              class: "text-c-3 hover:text-c-1 hidden text-sm group-hover:block",
                              icon: "ChevronRight",
                              size: "md"
                            })
                          ], 2)
                        ]),
                        createTextVNode(" " + toDisplayString(((t = n.info) == null ? void 0 : t.title) ?? ""), 1)
                      ], 8, Ue),
                      withDirectives(createBaseVNode("div", {
                        class: normalizeClass({
                          "before:bg-border before:z-1 relative mb-[.5px] before:pointer-events-none before:absolute before:left-3 before:top-0 before:h-[calc(100%_+_.5px)] before:w-[.5px] last:mb-0 last:before:h-full": Object.keys(n["x-scalar-environments"] || {}).length > 0
                        })
                      }, [
                        (openBlock(true), createElementBlock(Fragment, null, renderList(n["x-scalar-environments"], (a4, l) => (openBlock(), createBlock(a2, {
                          key: l,
                          class: "text-xs [&>a]:pl-5",
                          collectionId: n.uid,
                          isCopyable: false,
                          isDeletable: true,
                          isRenameable: true,
                          to: {
                            name: "collection.environment",
                            params: {
                              [unref(a3).Collection]: n.uid,
                              [unref(a3).Environment]: l
                            }
                          },
                          type: "environment",
                          variable: {
                            name: l,
                            uid: l,
                            color: a4.color ?? "#8E8E8E",
                            isDefault: false
                          },
                          warningMessage: "Are you sure you want to delete this environment?",
                          onClick: withModifiers((f2) => le(f2, l, n.uid), ["prevent"]),
                          onColorModal: (f2) => Z(l),
                          onDelete: (f2) => ne(l),
                          onRename: (f2) => Y(l, n.uid)
                        }, null, 8, ["collectionId", "to", "variable", "onClick", "onColorModal", "onDelete", "onRename"]))), 128)),
                        Object.keys(n["x-scalar-environments"] || {}).length === 0 ? (openBlock(), createBlock(unref($), {
                          key: 0,
                          class: "text-c-1 hover:bg-b-2 flex h-8 w-full justify-start gap-1.5 py-0 pl-6 text-xs",
                          variant: "ghost",
                          onClick: (a4) => $2(n.uid)
                        }, {
                          default: withCtx(() => [
                            createVNode(unref(c), {
                              icon: "Add",
                              size: "sm"
                            }),
                            o[2] || (o[2] = createBaseVNode("span", null, "Add Environment", -1))
                          ]),
                          _: 2
                        }, 1032, ["onClick"])) : createCommentVNode("", true)
                      ], 2), [
                        [vShow, ae(n.uid)]
                      ])
                    ]);
                  }), 128))
                ]),
                _: 1
              })
            ])
          ]),
          button: withCtx(() => [
            createVNode(g, {
              click: $2,
              hotkey: "N"
            }, {
              title: withCtx(() => o[3] || (o[3] = [
                createTextVNode(" Add Environment ")
              ])),
              _: 1
            })
          ]),
          _: 1
        }),
        createVNode(x, { class: "flex-1" }, {
          default: withCtx(() => [
            createVNode(_, null, createSlots({
              default: withCtx(() => [
                s.value && unref(x2) ? (openBlock(), createBlock(_2, {
                  key: 0,
                  class: "py-2 pl-px pr-2 md:px-4",
                  envVariables: unref(H),
                  environment: unref(j),
                  isCopyable: "",
                  language: "json",
                  lineNumbers: "",
                  lint: "",
                  modelValue: oe(),
                  workspace: unref(x2),
                  "onUpdate:modelValue": X
                }, null, 8, ["envVariables", "environment", "modelValue", "workspace"])) : createCommentVNode("", true)
              ]),
              _: 2
            }, [
              s.value ? {
                name: "title",
                fn: withCtx(() => [
                  createBaseVNode("span", null, toDisplayString(te()), 1)
                ]),
                key: "0"
              } : void 0
            ]), 1024)
          ]),
          _: 1
        }),
        createVNode(M, {
          selectedColor: D.value,
          state: unref(w),
          onCancel: o[0] || (o[0] = (n) => unref(w).hide()),
          onSubmit: ee
        }, null, 8, ["selectedColor", "state"]),
        createVNode(K, {
          activeWorkspaceCollections: unref(u2),
          collectionId: b3.value,
          state: unref(I),
          onCancel: o[1] || (o[1] = (n) => unref(I).hide()),
          onSubmit: Q
        }, null, 8, ["activeWorkspaceCollections", "collectionId", "state"]),
        createVNode(unref(f), {
          size: "xxs",
          state: unref(M2),
          title: `Edit ${d2.value}`
        }, {
          default: withCtx(() => [
            createVNode(C, {
              name: S.value ?? "",
              onClose: re,
              onEdit: se
            }, null, 8, ["name"])
          ]),
          _: 1
        }, 8, ["state", "title"])
      ]),
      _: 1
    }));
  }
});
export {
  ln as default
};
//# sourceMappingURL=Environment.vue-XDRXEUDH.js.map
