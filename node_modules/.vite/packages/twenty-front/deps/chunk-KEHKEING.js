import {
  s
} from "./chunk-MFP3SY2Q.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createElementBlock,
  defineComponent,
  normalizeClass,
  normalizeStyle,
  openBlock,
  renderList,
  toDisplayString
} from "./chunk-NPL3RUXR.js";

// node_modules/@scalar/api-client/dist/components/ScalarAsciiArt.vue2.js
var p = 500;
var o = 100;
var B = defineComponent({
  __name: "ScalarAsciiArt",
  props: {
    art: {},
    animate: { type: Boolean }
  },
  setup(u) {
    const d = u, n = computed(() => d.art.split(`
`)), g = (a, s2) => {
      var e, t, r, c;
      return {
        animationDuration: `${a * o}ms, ${p}ms`,
        animationTimingFunction: `steps(${a}), step-end`,
        animationDelay: `${s2 * o}ms, 0ms`,
        animationIterationCount: `1, ${((((e = n.value) == null ? void 0 : e.length) ?? 0) + (((c = (r = n.value) == null ? void 0 : r[((t = n.value) == null ? void 0 : t.length) - 1]) == null ? void 0 : c.length) ?? 0) + 5) * o / p}`
      };
    };
    return (a, s2) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["ascii-art font-code flex flex-col items-start text-[6px] leading-[7px]", { "ascii-art-animate": a.animate }])
    }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(n.value, (e, t) => (openBlock(), createElementBlock("span", {
        key: t,
        class: "inline-block",
        style: normalizeStyle({ width: `calc(${e.length + 1}ch)` })
      }, [
        createBaseVNode("span", {
          class: "inline-block whitespace-pre overflow-hidden",
          style: normalizeStyle(g(e.length, t))
        }, toDisplayString(e), 5)
      ], 4))), 128))
    ], 2));
  }
});

// node_modules/@scalar/api-client/dist/components/ScalarAsciiArt.vue.js
var i = s(B, [["__scopeId", "data-v-ea0a9794"]]);

export {
  i
};
//# sourceMappingURL=chunk-KEHKEING.js.map
