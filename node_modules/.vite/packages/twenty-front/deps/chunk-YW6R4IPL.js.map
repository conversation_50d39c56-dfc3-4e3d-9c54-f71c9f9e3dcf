{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/Form/Form.vue.js"], "sourcesContent": ["import { defineComponent as v, useId as b, openBlock as l, createBlock as r, createSlots as c, withCtx as o, createElementVNode as n, unref as t, createElementBlock as m, Fragment as y, renderList as h, normalizeClass as V, createVNode as p, toDisplayString as f, createCommentVNode as g, renderSlot as w } from \"vue\";\nimport { ScalarIcon as E } from \"@scalar/components\";\nimport C from \"../DataTable/DataTable.vue.js\";\nimport S from \"../DataTable/DataTableInput.vue.js\";\nimport U from \"../DataTable/DataTableRow.vue.js\";\nimport $ from \"../ViewLayout/ViewLayoutSection.vue.js\";\nimport { useActiveEntities as B } from \"../../store/active-entities.js\";\nconst D = { key: 0 }, F = { class: \"custom-scroll flex flex-1 flex-col gap-1.5\" }, I = [\"for\"], N = { class: \"bg-b-2 flex-center border-l px-2\" }, q = /* @__PURE__ */ v({\n  __name: \"Form\",\n  props: {\n    title: {},\n    options: {},\n    data: {},\n    onUpdate: { type: Function }\n  },\n  setup(z) {\n    const { activeEnvVariables: u, activeEnvironment: k, activeWorkspace: s } = B(), i = b();\n    return (e, T) => (l(), r($, null, c({\n      default: o(() => [\n        n(\"div\", F, [\n          Object.keys(e.data).length > 0 && t(s) ? (l(), r(C, {\n            key: 0,\n            columns: [\"\"]\n          }, {\n            default: o(() => [\n              (l(!0), m(y, null, h(e.options, (a, d) => (l(), r(U, {\n                key: d,\n                class: V({ \"border-t\": d === 0 })\n              }, {\n                default: o(() => [\n                  p(S, {\n                    id: t(i),\n                    envVariables: t(u),\n                    environment: t(k),\n                    modelValue: e.data[a.key] ?? \"\",\n                    placeholder: a.placeholder,\n                    workspace: t(s),\n                    \"onUpdate:modelValue\": (_) => e.onUpdate(a.key, _)\n                  }, c({\n                    default: o(() => [\n                      n(\"label\", { for: t(i) }, f(a.label), 9, I)\n                    ]),\n                    _: 2\n                  }, [\n                    a.key === \"description\" ? {\n                      name: \"icon\",\n                      fn: o(() => [\n                        n(\"div\", N, [\n                          p(t(E), {\n                            icon: \"Markdown\",\n                            size: \"lg\"\n                          })\n                        ])\n                      ]),\n                      key: \"0\"\n                    } : void 0\n                  ]), 1032, [\"id\", \"envVariables\", \"environment\", \"modelValue\", \"placeholder\", \"workspace\", \"onUpdate:modelValue\"])\n                ]),\n                _: 2\n              }, 1032, [\"class\"]))), 128))\n            ]),\n            _: 1\n          })) : g(\"\", !0)\n        ])\n      ]),\n      _: 2\n    }, [\n      e.title || e.$slots.title ? {\n        name: \"title\",\n        fn: o(() => [\n          e.title ? (l(), m(\"span\", D, f(e.title), 1)) : w(e.$slots, \"title\", { key: 1 })\n        ]),\n        key: \"0\"\n      } : void 0\n    ]), 1024));\n  }\n});\nexport {\n  q as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,IAAI,EAAE,KAAK,EAAE;AAAnB,IAAsBA,KAAI,EAAE,OAAO,6CAA6C;AAAhF,IAAmF,IAAI,CAAC,KAAK;AAA7F,IAAgG,IAAI,EAAE,OAAO,mCAAmC;AAAhJ,IAAmJ,IAAoB,gBAAE;AAAA,EACvK,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO,CAAC;AAAA,IACR,SAAS,CAAC;AAAA,IACV,MAAM,CAAC;AAAA,IACP,UAAU,EAAE,MAAM,SAAS;AAAA,EAC7B;AAAA,EACA,MAAM,GAAG;AACP,UAAM,EAAE,oBAAoB,GAAG,mBAAmB,GAAG,iBAAiB,EAAE,IAAI,EAAE,GAAGC,KAAI,MAAE;AACvF,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG,MAAM,YAAE;AAAA,MAClC,SAAS,QAAE,MAAM;AAAA,QACf,gBAAE,OAAOD,IAAG;AAAA,UACV,OAAO,KAAK,EAAE,IAAI,EAAE,SAAS,KAAK,MAAE,CAAC,KAAK,UAAE,GAAG,YAAE,GAAG;AAAA,YAClD,KAAK;AAAA,YACL,SAAS,CAAC,EAAE;AAAA,UACd,GAAG;AAAA,YACD,SAAS,QAAE,MAAM;AAAA,eACd,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAE,EAAE,SAAS,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG;AAAA,gBACnD,KAAK;AAAA,gBACL,OAAO,eAAE,EAAE,YAAY,MAAM,EAAE,CAAC;AAAA,cAClC,GAAG;AAAA,gBACD,SAAS,QAAE,MAAM;AAAA,kBACf,YAAE,GAAG;AAAA,oBACH,IAAI,MAAEC,EAAC;AAAA,oBACP,cAAc,MAAE,CAAC;AAAA,oBACjB,aAAa,MAAE,CAAC;AAAA,oBAChB,YAAY,EAAE,KAAK,EAAE,GAAG,KAAK;AAAA,oBAC7B,aAAa,EAAE;AAAA,oBACf,WAAW,MAAE,CAAC;AAAA,oBACd,uBAAuB,CAACC,OAAM,EAAE,SAAS,EAAE,KAAKA,EAAC;AAAA,kBACnD,GAAG,YAAE;AAAA,oBACH,SAAS,QAAE,MAAM;AAAA,sBACf,gBAAE,SAAS,EAAE,KAAK,MAAED,EAAC,EAAE,GAAG,gBAAE,EAAE,KAAK,GAAG,GAAG,CAAC;AAAA,oBAC5C,CAAC;AAAA,oBACD,GAAG;AAAA,kBACL,GAAG;AAAA,oBACD,EAAE,QAAQ,gBAAgB;AAAA,sBACxB,MAAM;AAAA,sBACN,IAAI,QAAE,MAAM;AAAA,wBACV,gBAAE,OAAO,GAAG;AAAA,0BACV,YAAE,MAAE,CAAC,GAAG;AAAA,4BACN,MAAM;AAAA,4BACN,MAAM;AAAA,0BACR,CAAC;AAAA,wBACH,CAAC;AAAA,sBACH,CAAC;AAAA,sBACD,KAAK;AAAA,oBACP,IAAI;AAAA,kBACN,CAAC,GAAG,MAAM,CAAC,MAAM,gBAAgB,eAAe,cAAc,eAAe,aAAa,qBAAqB,CAAC;AAAA,gBAClH,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG;AAAA,YAC5B,CAAC;AAAA,YACD,GAAG;AAAA,UACL,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG;AAAA,MACD,EAAE,SAAS,EAAE,OAAO,QAAQ;AAAA,QAC1B,MAAM;AAAA,QACN,IAAI,QAAE,MAAM;AAAA,UACV,EAAE,SAAS,UAAE,GAAG,mBAAE,QAAQ,GAAG,gBAAE,EAAE,KAAK,GAAG,CAAC,KAAK,WAAE,EAAE,QAAQ,SAAS,EAAE,KAAK,EAAE,CAAC;AAAA,QAChF,CAAC;AAAA,QACD,KAAK;AAAA,MACP,IAAI;AAAA,IACN,CAAC,GAAG,IAAI;AAAA,EACV;AACF,CAAC;", "names": ["F", "i", "_"]}