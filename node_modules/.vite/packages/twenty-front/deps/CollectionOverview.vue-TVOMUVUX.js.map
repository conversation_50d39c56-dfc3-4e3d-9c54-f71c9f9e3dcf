{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/components/MarkdownInput.vue2.js", "../../../../@scalar/api-client/dist/views/Collection/components/MarkdownInput.vue.js", "../../../../@scalar/api-client/dist/views/Collection/CollectionOverview.vue2.js"], "sourcesContent": ["import { defineComponent as g, ref as m, watch as w, nextTick as h, openBlock as o, createElementBlock as i, createElementVNode as n, createBlock as u, unref as s, withCtx as v, createVNode as d, createCommentVNode as a, Fragment as f } from \"vue\";\nimport { ScalarButton as c, ScalarIcon as b, ScalarMarkdown as x } from \"@scalar/components\";\nimport y from \"../../../components/CodeInput/CodeInput.vue.js\";\nconst z = { class: \"flex h-full w-full flex-col gap-2 pt-8\" }, C = { class: \"flex min-h-8 items-center justify-between gap-2 pl-1.5\" }, I = { class: \"has-[:focus-visible]:bg-b-1 z-1 group relative flex flex-col rounded-lg\" }, B = { class: \"h-full min-h-[calc(1rem*4)]\" }, $ = {\n  key: 1,\n  class: \"text-c-3 flex h-full items-center justify-center rounded-lg border p-4\"\n}, D = /* @__PURE__ */ g({\n  __name: \"MarkdownInput\",\n  props: {\n    modelValue: {},\n    environment: {},\n    envVariables: {},\n    workspace: {}\n  },\n  emits: [\"update:modelValue\"],\n  setup(E, { emit: k }) {\n    const V = k, t = m(\"preview\"), p = m(null);\n    return w(t, (l) => {\n      l === \"edit\" && h(() => {\n        var e;\n        (e = p.value) == null || e.focus();\n      });\n    }), (l, e) => (o(), i(\"div\", z, [\n      n(\"div\", C, [\n        e[6] || (e[6] = n(\"h3\", { class: \"font-bold\" }, \"Description\", -1)),\n        t.value === \"preview\" ? (o(), u(s(c), {\n          key: 0,\n          class: \"text-c-2 hover:text-c-1 flex items-center gap-2\",\n          type: \"button\",\n          size: \"sm\",\n          variant: \"outlined\",\n          onClick: e[0] || (e[0] = (r) => t.value = \"edit\")\n        }, {\n          default: v(() => [\n            d(s(b), {\n              icon: \"Pencil\",\n              size: \"sm\",\n              thickness: \"1.5\"\n            }),\n            e[5] || (e[5] = n(\"span\", null, \"Edit\", -1))\n          ]),\n          _: 1\n        })) : a(\"\", !0)\n      ]),\n      n(\"div\", I, [\n        n(\"div\", B, [\n          t.value === \"preview\" ? (o(), i(f, { key: 0 }, [\n            l.modelValue ? (o(), i(f, { key: 0 }, [\n              l.modelValue ? (o(), u(s(x), {\n                key: 0,\n                withImages: \"\",\n                class: \"hover:border-b-3 h-full rounded border border-transparent p-1.5\",\n                value: l.modelValue,\n                onDblclick: e[1] || (e[1] = (r) => t.value = \"edit\")\n              }, null, 8, [\"value\"])) : a(\"\", !0),\n              e[7] || (e[7] = n(\"div\", { class: \"brightness-lifted -z-1 bg-b-1 absolute inset-0 hidden rounded group-hover:block group-has-[:focus-visible]:hidden\" }, null, -1))\n            ], 64)) : (o(), i(\"div\", $, [\n              d(s(c), {\n                class: \"hover:bg-b-2 hover:text-c-1 text-c-2 flex items-center gap-2\",\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: e[2] || (e[2] = (r) => t.value = \"edit\")\n              }, {\n                default: v(() => [\n                  d(s(b), {\n                    icon: \"Pencil\",\n                    size: \"sm\",\n                    thickness: \"1.5\"\n                  }),\n                  e[8] || (e[8] = n(\"span\", null, \"Write a description\", -1))\n                ]),\n                _: 1\n              })\n            ]))\n          ], 64)) : a(\"\", !0),\n          t.value === \"edit\" ? (o(), u(y, {\n            key: 1,\n            ref_key: \"codeInputRef\",\n            ref: p,\n            class: \"h-full border px-0.5 py-0\",\n            modelValue: l.modelValue,\n            environment: l.environment,\n            envVariables: l.envVariables,\n            workspace: l.workspace,\n            onBlur: e[3] || (e[3] = (r) => t.value = \"preview\"),\n            \"onUpdate:modelValue\": e[4] || (e[4] = (r) => V(\"update:modelValue\", r))\n          }, null, 8, [\"modelValue\", \"environment\", \"envVariables\", \"workspace\"])) : a(\"\", !0)\n        ])\n      ])\n    ]));\n  }\n});\nexport {\n  D as default\n};\n", "import o from \"./MarkdownInput.vue2.js\";\n/* empty css                   */\nimport t from \"../../../_virtual/_plugin-vue_export-helper.js\";\nconst m = /* @__PURE__ */ t(o, [[\"__scopeId\", \"data-v-139eac35\"]]);\nexport {\n  m as default\n};\n", "import { defineComponent as u, openBlock as c, createBlock as l, withCtx as v, unref as e, createCommentVNode as d } from \"vue\";\nimport f from \"../../components/ViewLayout/ViewLayoutSection.vue.js\";\nimport { useActiveEntities as _ } from \"../../store/active-entities.js\";\nimport k from \"./components/MarkdownInput.vue.js\";\nimport { useWorkspace as V } from \"../../store/store.js\";\nconst M = /* @__PURE__ */ u({\n  __name: \"CollectionOverview\",\n  setup(C) {\n    const {\n      activeCollection: o,\n      activeEnvironment: t,\n      activeEnvVariables: s,\n      activeWorkspace: n\n    } = _(), { collectionMutators: m } = V(), p = (i) => {\n      o.value && m.edit(o.value.uid, \"info.description\", i);\n    };\n    return (i, w) => (c(), l(f, null, {\n      default: v(() => {\n        var r, a;\n        return [\n          e(t) && e(n) ? (c(), l(k, {\n            key: 0,\n            environment: e(t),\n            envVariables: e(s),\n            workspace: e(n),\n            modelValue: ((a = (r = e(o)) == null ? void 0 : r.info) == null ? void 0 : a.description) ?? \"\",\n            \"onUpdate:modelValue\": p\n          }, null, 8, [\"environment\", \"envVariables\", \"workspace\", \"modelValue\"])) : d(\"\", !0)\n        ];\n      }),\n      _: 1\n    }));\n  }\n});\nexport {\n  M as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,IAAI,EAAE,OAAO,yCAAyC;AAA5D,IAA+D,IAAI,EAAE,OAAO,yDAAyD;AAArI,IAAwI,IAAI,EAAE,OAAO,0EAA0E;AAA/N,IAAkO,IAAI,EAAE,OAAO,8BAA8B;AAA7Q,IAAgRA,KAAI;AAAA,EAClR,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY,CAAC;AAAA,IACb,aAAa,CAAC;AAAA,IACd,cAAc,CAAC;AAAA,IACf,WAAW,CAAC;AAAA,EACd;AAAA,EACA,OAAO,CAAC,mBAAmB;AAAA,EAC3B,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAI,IAAE,SAAS,GAAG,IAAI,IAAE,IAAI;AACzC,WAAO,MAAE,GAAG,CAAC,MAAM;AACjB,YAAM,UAAU,SAAE,MAAM;AACtB,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,MAAM;AAAA,MACnC,CAAC;AAAA,IACH,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,MAC9B,gBAAE,OAAO,GAAG;AAAA,QACV,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,MAAM,EAAE,OAAO,YAAY,GAAG,eAAe,EAAE;AAAA,QACjE,EAAE,UAAU,aAAa,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,UACpC,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,QAC5C,GAAG;AAAA,UACD,SAAS,QAAE,MAAM;AAAA,YACf,YAAE,MAAE,CAAC,GAAG;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,WAAW;AAAA,YACb,CAAC;AAAA,YACD,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC5C,CAAC;AAAA,UACD,GAAG;AAAA,QACL,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAChB,CAAC;AAAA,MACD,gBAAE,OAAO,GAAG;AAAA,QACV,gBAAE,OAAO,GAAG;AAAA,UACV,EAAE,UAAU,aAAa,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,YAC7C,EAAE,cAAc,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,cACpC,EAAE,cAAc,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,gBAC3B,KAAK;AAAA,gBACL,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,OAAO,EAAE;AAAA,gBACT,YAAY,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,cAC/C,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,cAClC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,OAAO,EAAE,OAAO,oHAAoH,GAAG,MAAM,EAAE;AAAA,YACnK,GAAG,EAAE,MAAM,UAAE,GAAG,mBAAE,OAAOA,IAAG;AAAA,cAC1B,YAAE,MAAE,CAAC,GAAG;AAAA,gBACN,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,MAAM;AAAA,gBACN,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,cAC5C,GAAG;AAAA,gBACD,SAAS,QAAE,MAAM;AAAA,kBACf,YAAE,MAAE,CAAC,GAAG;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,WAAW;AAAA,kBACb,CAAC;AAAA,kBACD,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,QAAQ,MAAM,uBAAuB,EAAE;AAAA,gBAC3D,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,CAAC;AAAA,YACH,CAAC;AAAA,UACH,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,UAClB,EAAE,UAAU,UAAU,UAAE,GAAG,YAAEC,IAAG;AAAA,YAC9B,KAAK;AAAA,YACL,SAAS;AAAA,YACT,KAAK;AAAA,YACL,OAAO;AAAA,YACP,YAAY,EAAE;AAAA,YACd,aAAa,EAAE;AAAA,YACf,cAAc,EAAE;AAAA,YAChB,WAAW,EAAE;AAAA,YACb,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,YACzC,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,qBAAqB,CAAC;AAAA,UACxE,GAAG,MAAM,GAAG,CAAC,cAAc,eAAe,gBAAgB,WAAW,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACrF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;;;ACxFD,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;;;ACEjE,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAMC,IAAG;AACP,UAAM;AAAA,MACJ,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,oBAAoBC;AAAA,MACpB,iBAAiB;AAAA,IACnB,IAAI,EAAE,GAAG,EAAE,oBAAoBC,GAAE,IAAI,GAAE,GAAG,IAAI,CAAC,MAAM;AACnD,QAAE,SAASA,GAAE,KAAK,EAAE,MAAM,KAAK,oBAAoB,CAAC;AAAA,IACtD;AACA,WAAO,CAAC,GAAGC,QAAO,UAAE,GAAG,YAAE,GAAG,MAAM;AAAA,MAChC,SAAS,QAAE,MAAM;AACf,YAAI,GAAG;AACP,eAAO;AAAA,UACL,MAAE,CAAC,KAAK,MAAE,CAAC,KAAK,UAAE,GAAG,YAAE,GAAG;AAAA,YACxB,KAAK;AAAA,YACL,aAAa,MAAE,CAAC;AAAA,YAChB,cAAc,MAAEF,EAAC;AAAA,YACjB,WAAW,MAAE,CAAC;AAAA,YACd,cAAc,KAAK,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,SAAS,OAAO,SAAS,EAAE,gBAAgB;AAAA,YAC7F,uBAAuB;AAAA,UACzB,GAAG,MAAM,GAAG,CAAC,eAAe,gBAAgB,aAAa,YAAY,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACrF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": ["$", "_", "C", "s", "m", "w"]}