{"version": 3, "sources": ["../../../../node_modules/codemirror/keymap/sublime.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// A rough approximation of Sublime Text's keybindings\n// Depends on addon/search/searchcursor.js and optionally addon/dialog/dialogs.js\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../lib/codemirror\"), require(\"../addon/search/searchcursor\"), require(\"../addon/edit/matchbrackets\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../lib/codemirror\", \"../addon/search/searchcursor\", \"../addon/edit/matchbrackets\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var cmds = CodeMirror.commands;\n  var Pos = CodeMirror.Pos;\n\n  // This is not exactly <PERSON>lime's algorithm. I couldn't make heads or tails of that.\n  function findPosSubword(doc, start, dir) {\n    if (dir < 0 && start.ch == 0) return doc.clipPos(Pos(start.line - 1));\n    var line = doc.getLine(start.line);\n    if (dir > 0 && start.ch >= line.length) return doc.clipPos(Pos(start.line + 1, 0));\n    var state = \"start\", type, startPos = start.ch;\n    for (var pos = startPos, e = dir < 0 ? 0 : line.length, i = 0; pos != e; pos += dir, i++) {\n      var next = line.charAt(dir < 0 ? pos - 1 : pos);\n      var cat = next != \"_\" && CodeMirror.isWordChar(next) ? \"w\" : \"o\";\n      if (cat == \"w\" && next.toUpperCase() == next) cat = \"W\";\n      if (state == \"start\") {\n        if (cat != \"o\") { state = \"in\"; type = cat; }\n        else startPos = pos + dir\n      } else if (state == \"in\") {\n        if (type != cat) {\n          if (type == \"w\" && cat == \"W\" && dir < 0) pos--;\n          if (type == \"W\" && cat == \"w\" && dir > 0) { // From uppercase to lowercase\n            if (pos == startPos + 1) { type = \"w\"; continue; }\n            else pos--;\n          }\n          break;\n        }\n      }\n    }\n    return Pos(start.line, pos);\n  }\n\n  function moveSubword(cm, dir) {\n    cm.extendSelectionsBy(function(range) {\n      if (cm.display.shift || cm.doc.extend || range.empty())\n        return findPosSubword(cm.doc, range.head, dir);\n      else\n        return dir < 0 ? range.from() : range.to();\n    });\n  }\n\n  cmds.goSubwordLeft = function(cm) { moveSubword(cm, -1); };\n  cmds.goSubwordRight = function(cm) { moveSubword(cm, 1); };\n\n  cmds.scrollLineUp = function(cm) {\n    var info = cm.getScrollInfo();\n    if (!cm.somethingSelected()) {\n      var visibleBottomLine = cm.lineAtHeight(info.top + info.clientHeight, \"local\");\n      if (cm.getCursor().line >= visibleBottomLine)\n        cm.execCommand(\"goLineUp\");\n    }\n    cm.scrollTo(null, info.top - cm.defaultTextHeight());\n  };\n  cmds.scrollLineDown = function(cm) {\n    var info = cm.getScrollInfo();\n    if (!cm.somethingSelected()) {\n      var visibleTopLine = cm.lineAtHeight(info.top, \"local\")+1;\n      if (cm.getCursor().line <= visibleTopLine)\n        cm.execCommand(\"goLineDown\");\n    }\n    cm.scrollTo(null, info.top + cm.defaultTextHeight());\n  };\n\n  cmds.splitSelectionByLine = function(cm) {\n    var ranges = cm.listSelections(), lineRanges = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var from = ranges[i].from(), to = ranges[i].to();\n      for (var line = from.line; line <= to.line; ++line)\n        if (!(to.line > from.line && line == to.line && to.ch == 0))\n          lineRanges.push({anchor: line == from.line ? from : Pos(line, 0),\n                           head: line == to.line ? to : Pos(line)});\n    }\n    cm.setSelections(lineRanges, 0);\n  };\n\n  cmds.singleSelectionTop = function(cm) {\n    var range = cm.listSelections()[0];\n    cm.setSelection(range.anchor, range.head, {scroll: false});\n  };\n\n  cmds.selectLine = function(cm) {\n    var ranges = cm.listSelections(), extended = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      extended.push({anchor: Pos(range.from().line, 0),\n                     head: Pos(range.to().line + 1, 0)});\n    }\n    cm.setSelections(extended);\n  };\n\n  function insertLine(cm, above) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    cm.operation(function() {\n      var len = cm.listSelections().length, newSelection = [], last = -1;\n      for (var i = 0; i < len; i++) {\n        var head = cm.listSelections()[i].head;\n        if (head.line <= last) continue;\n        var at = Pos(head.line + (above ? 0 : 1), 0);\n        cm.replaceRange(\"\\n\", at, null, \"+insertLine\");\n        cm.indentLine(at.line, null, true);\n        newSelection.push({head: at, anchor: at});\n        last = head.line + 1;\n      }\n      cm.setSelections(newSelection);\n    });\n    cm.execCommand(\"indentAuto\");\n  }\n\n  cmds.insertLineAfter = function(cm) { return insertLine(cm, false); };\n\n  cmds.insertLineBefore = function(cm) { return insertLine(cm, true); };\n\n  function wordAt(cm, pos) {\n    var start = pos.ch, end = start, line = cm.getLine(pos.line);\n    while (start && CodeMirror.isWordChar(line.charAt(start - 1))) --start;\n    while (end < line.length && CodeMirror.isWordChar(line.charAt(end))) ++end;\n    return {from: Pos(pos.line, start), to: Pos(pos.line, end), word: line.slice(start, end)};\n  }\n\n  cmds.selectNextOccurrence = function(cm) {\n    var from = cm.getCursor(\"from\"), to = cm.getCursor(\"to\");\n    var fullWord = cm.state.sublimeFindFullWord == cm.doc.sel;\n    if (CodeMirror.cmpPos(from, to) == 0) {\n      var word = wordAt(cm, from);\n      if (!word.word) return;\n      cm.setSelection(word.from, word.to);\n      fullWord = true;\n    } else {\n      var text = cm.getRange(from, to);\n      var query = fullWord ? new RegExp(\"\\\\b\" + text + \"\\\\b\") : text;\n      var cur = cm.getSearchCursor(query, to);\n      var found = cur.findNext();\n      if (!found) {\n        cur = cm.getSearchCursor(query, Pos(cm.firstLine(), 0));\n        found = cur.findNext();\n      }\n      if (!found || isSelectedRange(cm.listSelections(), cur.from(), cur.to())) return\n      cm.addSelection(cur.from(), cur.to());\n    }\n    if (fullWord)\n      cm.state.sublimeFindFullWord = cm.doc.sel;\n  };\n\n  cmds.skipAndSelectNextOccurrence = function(cm) {\n    var prevAnchor = cm.getCursor(\"anchor\"), prevHead = cm.getCursor(\"head\");\n    cmds.selectNextOccurrence(cm);\n    if (CodeMirror.cmpPos(prevAnchor, prevHead) != 0) {\n      cm.doc.setSelections(cm.doc.listSelections()\n          .filter(function (sel) {\n            return sel.anchor != prevAnchor || sel.head != prevHead;\n          }));\n    }\n  }\n\n  function addCursorToSelection(cm, dir) {\n    var ranges = cm.listSelections(), newRanges = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      var newAnchor = cm.findPosV(\n          range.anchor, dir, \"line\", range.anchor.goalColumn);\n      var newHead = cm.findPosV(\n          range.head, dir, \"line\", range.head.goalColumn);\n      newAnchor.goalColumn = range.anchor.goalColumn != null ?\n          range.anchor.goalColumn : cm.cursorCoords(range.anchor, \"div\").left;\n      newHead.goalColumn = range.head.goalColumn != null ?\n          range.head.goalColumn : cm.cursorCoords(range.head, \"div\").left;\n      var newRange = {anchor: newAnchor, head: newHead};\n      newRanges.push(range);\n      newRanges.push(newRange);\n    }\n    cm.setSelections(newRanges);\n  }\n  cmds.addCursorToPrevLine = function(cm) { addCursorToSelection(cm, -1); };\n  cmds.addCursorToNextLine = function(cm) { addCursorToSelection(cm, 1); };\n\n  function isSelectedRange(ranges, from, to) {\n    for (var i = 0; i < ranges.length; i++)\n      if (CodeMirror.cmpPos(ranges[i].from(), from) == 0 &&\n          CodeMirror.cmpPos(ranges[i].to(), to) == 0) return true\n    return false\n  }\n\n  var mirror = \"(){}[]\";\n  function selectBetweenBrackets(cm) {\n    var ranges = cm.listSelections(), newRanges = []\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], pos = range.head, opening = cm.scanForBracket(pos, -1);\n      if (!opening) return false;\n      for (;;) {\n        var closing = cm.scanForBracket(pos, 1);\n        if (!closing) return false;\n        if (closing.ch == mirror.charAt(mirror.indexOf(opening.ch) + 1)) {\n          var startPos = Pos(opening.pos.line, opening.pos.ch + 1);\n          if (CodeMirror.cmpPos(startPos, range.from()) == 0 &&\n              CodeMirror.cmpPos(closing.pos, range.to()) == 0) {\n            opening = cm.scanForBracket(opening.pos, -1);\n            if (!opening) return false;\n          } else {\n            newRanges.push({anchor: startPos, head: closing.pos});\n            break;\n          }\n        }\n        pos = Pos(closing.pos.line, closing.pos.ch + 1);\n      }\n    }\n    cm.setSelections(newRanges);\n    return true;\n  }\n\n  cmds.selectScope = function(cm) {\n    selectBetweenBrackets(cm) || cm.execCommand(\"selectAll\");\n  };\n  cmds.selectBetweenBrackets = function(cm) {\n    if (!selectBetweenBrackets(cm)) return CodeMirror.Pass;\n  };\n\n  function puncType(type) {\n    return !type ? null : /\\bpunctuation\\b/.test(type) ? type : undefined\n  }\n\n  cmds.goToBracket = function(cm) {\n    cm.extendSelectionsBy(function(range) {\n      var next = cm.scanForBracket(range.head, 1, puncType(cm.getTokenTypeAt(range.head)));\n      if (next && CodeMirror.cmpPos(next.pos, range.head) != 0) return next.pos;\n      var prev = cm.scanForBracket(range.head, -1, puncType(cm.getTokenTypeAt(Pos(range.head.line, range.head.ch + 1))));\n      return prev && Pos(prev.pos.line, prev.pos.ch + 1) || range.head;\n    });\n  };\n\n  cmds.swapLineUp = function(cm) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    var ranges = cm.listSelections(), linesToMove = [], at = cm.firstLine() - 1, newSels = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], from = range.from().line - 1, to = range.to().line;\n      newSels.push({anchor: Pos(range.anchor.line - 1, range.anchor.ch),\n                    head: Pos(range.head.line - 1, range.head.ch)});\n      if (range.to().ch == 0 && !range.empty()) --to;\n      if (from > at) linesToMove.push(from, to);\n      else if (linesToMove.length) linesToMove[linesToMove.length - 1] = to;\n      at = to;\n    }\n    cm.operation(function() {\n      for (var i = 0; i < linesToMove.length; i += 2) {\n        var from = linesToMove[i], to = linesToMove[i + 1];\n        var line = cm.getLine(from);\n        cm.replaceRange(\"\", Pos(from, 0), Pos(from + 1, 0), \"+swapLine\");\n        if (to > cm.lastLine())\n          cm.replaceRange(\"\\n\" + line, Pos(cm.lastLine()), null, \"+swapLine\");\n        else\n          cm.replaceRange(line + \"\\n\", Pos(to, 0), null, \"+swapLine\");\n      }\n      cm.setSelections(newSels);\n      cm.scrollIntoView();\n    });\n  };\n\n  cmds.swapLineDown = function(cm) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    var ranges = cm.listSelections(), linesToMove = [], at = cm.lastLine() + 1;\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var range = ranges[i], from = range.to().line + 1, to = range.from().line;\n      if (range.to().ch == 0 && !range.empty()) from--;\n      if (from < at) linesToMove.push(from, to);\n      else if (linesToMove.length) linesToMove[linesToMove.length - 1] = to;\n      at = to;\n    }\n    cm.operation(function() {\n      for (var i = linesToMove.length - 2; i >= 0; i -= 2) {\n        var from = linesToMove[i], to = linesToMove[i + 1];\n        var line = cm.getLine(from);\n        if (from == cm.lastLine())\n          cm.replaceRange(\"\", Pos(from - 1), Pos(from), \"+swapLine\");\n        else\n          cm.replaceRange(\"\", Pos(from, 0), Pos(from + 1, 0), \"+swapLine\");\n        cm.replaceRange(line + \"\\n\", Pos(to, 0), null, \"+swapLine\");\n      }\n      cm.scrollIntoView();\n    });\n  };\n\n  cmds.toggleCommentIndented = function(cm) {\n    cm.toggleComment({ indent: true });\n  }\n\n  cmds.joinLines = function(cm) {\n    var ranges = cm.listSelections(), joined = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], from = range.from();\n      var start = from.line, end = range.to().line;\n      while (i < ranges.length - 1 && ranges[i + 1].from().line == end)\n        end = ranges[++i].to().line;\n      joined.push({start: start, end: end, anchor: !range.empty() && from});\n    }\n    cm.operation(function() {\n      var offset = 0, ranges = [];\n      for (var i = 0; i < joined.length; i++) {\n        var obj = joined[i];\n        var anchor = obj.anchor && Pos(obj.anchor.line - offset, obj.anchor.ch), head;\n        for (var line = obj.start; line <= obj.end; line++) {\n          var actual = line - offset;\n          if (line == obj.end) head = Pos(actual, cm.getLine(actual).length + 1);\n          if (actual < cm.lastLine()) {\n            cm.replaceRange(\" \", Pos(actual), Pos(actual + 1, /^\\s*/.exec(cm.getLine(actual + 1))[0].length));\n            ++offset;\n          }\n        }\n        ranges.push({anchor: anchor || head, head: head});\n      }\n      cm.setSelections(ranges, 0);\n    });\n  };\n\n  cmds.duplicateLine = function(cm) {\n    cm.operation(function() {\n      var rangeCount = cm.listSelections().length;\n      for (var i = 0; i < rangeCount; i++) {\n        var range = cm.listSelections()[i];\n        if (range.empty())\n          cm.replaceRange(cm.getLine(range.head.line) + \"\\n\", Pos(range.head.line, 0));\n        else\n          cm.replaceRange(cm.getRange(range.from(), range.to()), range.from());\n      }\n      cm.scrollIntoView();\n    });\n  };\n\n\n  function sortLines(cm, caseSensitive, direction) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    var ranges = cm.listSelections(), toSort = [], selected;\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      if (range.empty()) continue;\n      var from = range.from().line, to = range.to().line;\n      while (i < ranges.length - 1 && ranges[i + 1].from().line == to)\n        to = ranges[++i].to().line;\n      if (!ranges[i].to().ch) to--;\n      toSort.push(from, to);\n    }\n    if (toSort.length) selected = true;\n    else toSort.push(cm.firstLine(), cm.lastLine());\n\n    cm.operation(function() {\n      var ranges = [];\n      for (var i = 0; i < toSort.length; i += 2) {\n        var from = toSort[i], to = toSort[i + 1];\n        var start = Pos(from, 0), end = Pos(to);\n        var lines = cm.getRange(start, end, false);\n        if (caseSensitive)\n          lines.sort(function(a, b) { return a < b ? -direction : a == b ? 0 : direction; });\n        else\n          lines.sort(function(a, b) {\n            var au = a.toUpperCase(), bu = b.toUpperCase();\n            if (au != bu) { a = au; b = bu; }\n            return a < b ? -direction : a == b ? 0 : direction;\n          });\n        cm.replaceRange(lines, start, end);\n        if (selected) ranges.push({anchor: start, head: Pos(to + 1, 0)});\n      }\n      if (selected) cm.setSelections(ranges, 0);\n    });\n  }\n\n  cmds.sortLines = function(cm) { sortLines(cm, true, 1); };\n  cmds.reverseSortLines = function(cm) { sortLines(cm, true, -1); };\n  cmds.sortLinesInsensitive = function(cm) { sortLines(cm, false, 1); };\n  cmds.reverseSortLinesInsensitive = function(cm) { sortLines(cm, false, -1); };\n\n  cmds.nextBookmark = function(cm) {\n    var marks = cm.state.sublimeBookmarks;\n    if (marks) while (marks.length) {\n      var current = marks.shift();\n      var found = current.find();\n      if (found) {\n        marks.push(current);\n        return cm.setSelection(found.from, found.to);\n      }\n    }\n  };\n\n  cmds.prevBookmark = function(cm) {\n    var marks = cm.state.sublimeBookmarks;\n    if (marks) while (marks.length) {\n      marks.unshift(marks.pop());\n      var found = marks[marks.length - 1].find();\n      if (!found)\n        marks.pop();\n      else\n        return cm.setSelection(found.from, found.to);\n    }\n  };\n\n  cmds.toggleBookmark = function(cm) {\n    var ranges = cm.listSelections();\n    var marks = cm.state.sublimeBookmarks || (cm.state.sublimeBookmarks = []);\n    for (var i = 0; i < ranges.length; i++) {\n      var from = ranges[i].from(), to = ranges[i].to();\n      var found = ranges[i].empty() ? cm.findMarksAt(from) : cm.findMarks(from, to);\n      for (var j = 0; j < found.length; j++) {\n        if (found[j].sublimeBookmark) {\n          found[j].clear();\n          for (var k = 0; k < marks.length; k++)\n            if (marks[k] == found[j])\n              marks.splice(k--, 1);\n          break;\n        }\n      }\n      if (j == found.length)\n        marks.push(cm.markText(from, to, {sublimeBookmark: true, clearWhenEmpty: false}));\n    }\n  };\n\n  cmds.clearBookmarks = function(cm) {\n    var marks = cm.state.sublimeBookmarks;\n    if (marks) for (var i = 0; i < marks.length; i++) marks[i].clear();\n    marks.length = 0;\n  };\n\n  cmds.selectBookmarks = function(cm) {\n    var marks = cm.state.sublimeBookmarks, ranges = [];\n    if (marks) for (var i = 0; i < marks.length; i++) {\n      var found = marks[i].find();\n      if (!found)\n        marks.splice(i--, 0);\n      else\n        ranges.push({anchor: found.from, head: found.to});\n    }\n    if (ranges.length)\n      cm.setSelections(ranges, 0);\n  };\n\n  function modifyWordOrSelection(cm, mod) {\n    cm.operation(function() {\n      var ranges = cm.listSelections(), indices = [], replacements = [];\n      for (var i = 0; i < ranges.length; i++) {\n        var range = ranges[i];\n        if (range.empty()) { indices.push(i); replacements.push(\"\"); }\n        else replacements.push(mod(cm.getRange(range.from(), range.to())));\n      }\n      cm.replaceSelections(replacements, \"around\", \"case\");\n      for (var i = indices.length - 1, at; i >= 0; i--) {\n        var range = ranges[indices[i]];\n        if (at && CodeMirror.cmpPos(range.head, at) > 0) continue;\n        var word = wordAt(cm, range.head);\n        at = word.from;\n        cm.replaceRange(mod(word.word), word.from, word.to);\n      }\n    });\n  }\n\n  cmds.smartBackspace = function(cm) {\n    if (cm.somethingSelected()) return CodeMirror.Pass;\n\n    cm.operation(function() {\n      var cursors = cm.listSelections();\n      var indentUnit = cm.getOption(\"indentUnit\");\n\n      for (var i = cursors.length - 1; i >= 0; i--) {\n        var cursor = cursors[i].head;\n        var toStartOfLine = cm.getRange({line: cursor.line, ch: 0}, cursor);\n        var column = CodeMirror.countColumn(toStartOfLine, null, cm.getOption(\"tabSize\"));\n\n        // Delete by one character by default\n        var deletePos = cm.findPosH(cursor, -1, \"char\", false);\n\n        if (toStartOfLine && !/\\S/.test(toStartOfLine) && column % indentUnit == 0) {\n          var prevIndent = new Pos(cursor.line,\n            CodeMirror.findColumn(toStartOfLine, column - indentUnit, indentUnit));\n\n          // Smart delete only if we found a valid prevIndent location\n          if (prevIndent.ch != cursor.ch) deletePos = prevIndent;\n        }\n\n        cm.replaceRange(\"\", deletePos, cursor, \"+delete\");\n      }\n    });\n  };\n\n  cmds.delLineRight = function(cm) {\n    cm.operation(function() {\n      var ranges = cm.listSelections();\n      for (var i = ranges.length - 1; i >= 0; i--)\n        cm.replaceRange(\"\", ranges[i].anchor, Pos(ranges[i].to().line), \"+delete\");\n      cm.scrollIntoView();\n    });\n  };\n\n  cmds.upcaseAtCursor = function(cm) {\n    modifyWordOrSelection(cm, function(str) { return str.toUpperCase(); });\n  };\n  cmds.downcaseAtCursor = function(cm) {\n    modifyWordOrSelection(cm, function(str) { return str.toLowerCase(); });\n  };\n\n  cmds.setSublimeMark = function(cm) {\n    if (cm.state.sublimeMark) cm.state.sublimeMark.clear();\n    cm.state.sublimeMark = cm.setBookmark(cm.getCursor());\n  };\n  cmds.selectToSublimeMark = function(cm) {\n    var found = cm.state.sublimeMark && cm.state.sublimeMark.find();\n    if (found) cm.setSelection(cm.getCursor(), found);\n  };\n  cmds.deleteToSublimeMark = function(cm) {\n    var found = cm.state.sublimeMark && cm.state.sublimeMark.find();\n    if (found) {\n      var from = cm.getCursor(), to = found;\n      if (CodeMirror.cmpPos(from, to) > 0) { var tmp = to; to = from; from = tmp; }\n      cm.state.sublimeKilled = cm.getRange(from, to);\n      cm.replaceRange(\"\", from, to);\n    }\n  };\n  cmds.swapWithSublimeMark = function(cm) {\n    var found = cm.state.sublimeMark && cm.state.sublimeMark.find();\n    if (found) {\n      cm.state.sublimeMark.clear();\n      cm.state.sublimeMark = cm.setBookmark(cm.getCursor());\n      cm.setCursor(found);\n    }\n  };\n  cmds.sublimeYank = function(cm) {\n    if (cm.state.sublimeKilled != null)\n      cm.replaceSelection(cm.state.sublimeKilled, null, \"paste\");\n  };\n\n  cmds.showInCenter = function(cm) {\n    var pos = cm.cursorCoords(null, \"local\");\n    cm.scrollTo(null, (pos.top + pos.bottom) / 2 - cm.getScrollInfo().clientHeight / 2);\n  };\n\n  function getTarget(cm) {\n    var from = cm.getCursor(\"from\"), to = cm.getCursor(\"to\");\n    if (CodeMirror.cmpPos(from, to) == 0) {\n      var word = wordAt(cm, from);\n      if (!word.word) return;\n      from = word.from;\n      to = word.to;\n    }\n    return {from: from, to: to, query: cm.getRange(from, to), word: word};\n  }\n\n  function findAndGoTo(cm, forward) {\n    var target = getTarget(cm);\n    if (!target) return;\n    var query = target.query;\n    var cur = cm.getSearchCursor(query, forward ? target.to : target.from);\n\n    if (forward ? cur.findNext() : cur.findPrevious()) {\n      cm.setSelection(cur.from(), cur.to());\n    } else {\n      cur = cm.getSearchCursor(query, forward ? Pos(cm.firstLine(), 0)\n                                              : cm.clipPos(Pos(cm.lastLine())));\n      if (forward ? cur.findNext() : cur.findPrevious())\n        cm.setSelection(cur.from(), cur.to());\n      else if (target.word)\n        cm.setSelection(target.from, target.to);\n    }\n  };\n  cmds.findUnder = function(cm) { findAndGoTo(cm, true); };\n  cmds.findUnderPrevious = function(cm) { findAndGoTo(cm,false); };\n  cmds.findAllUnder = function(cm) {\n    var target = getTarget(cm);\n    if (!target) return;\n    var cur = cm.getSearchCursor(target.query);\n    var matches = [];\n    var primaryIndex = -1;\n    while (cur.findNext()) {\n      matches.push({anchor: cur.from(), head: cur.to()});\n      if (cur.from().line <= target.from.line && cur.from().ch <= target.from.ch)\n        primaryIndex++;\n    }\n    cm.setSelections(matches, primaryIndex);\n  };\n\n\n  var keyMap = CodeMirror.keyMap;\n  keyMap.macSublime = {\n    \"Cmd-Left\": \"goLineStartSmart\",\n    \"Shift-Tab\": \"indentLess\",\n    \"Shift-Ctrl-K\": \"deleteLine\",\n    \"Alt-Q\": \"wrapLines\",\n    \"Ctrl-Left\": \"goSubwordLeft\",\n    \"Ctrl-Right\": \"goSubwordRight\",\n    \"Ctrl-Alt-Up\": \"scrollLineUp\",\n    \"Ctrl-Alt-Down\": \"scrollLineDown\",\n    \"Cmd-L\": \"selectLine\",\n    \"Shift-Cmd-L\": \"splitSelectionByLine\",\n    \"Esc\": \"singleSelectionTop\",\n    \"Cmd-Enter\": \"insertLineAfter\",\n    \"Shift-Cmd-Enter\": \"insertLineBefore\",\n    \"Cmd-D\": \"selectNextOccurrence\",\n    \"Shift-Cmd-Space\": \"selectScope\",\n    \"Shift-Cmd-M\": \"selectBetweenBrackets\",\n    \"Cmd-M\": \"goToBracket\",\n    \"Cmd-Ctrl-Up\": \"swapLineUp\",\n    \"Cmd-Ctrl-Down\": \"swapLineDown\",\n    \"Cmd-/\": \"toggleCommentIndented\",\n    \"Cmd-J\": \"joinLines\",\n    \"Shift-Cmd-D\": \"duplicateLine\",\n    \"F5\": \"sortLines\",\n    \"Shift-F5\": \"reverseSortLines\",\n    \"Cmd-F5\": \"sortLinesInsensitive\",\n    \"Shift-Cmd-F5\": \"reverseSortLinesInsensitive\",\n    \"F2\": \"nextBookmark\",\n    \"Shift-F2\": \"prevBookmark\",\n    \"Cmd-F2\": \"toggleBookmark\",\n    \"Shift-Cmd-F2\": \"clearBookmarks\",\n    \"Alt-F2\": \"selectBookmarks\",\n    \"Backspace\": \"smartBackspace\",\n    \"Cmd-K Cmd-D\": \"skipAndSelectNextOccurrence\",\n    \"Cmd-K Cmd-K\": \"delLineRight\",\n    \"Cmd-K Cmd-U\": \"upcaseAtCursor\",\n    \"Cmd-K Cmd-L\": \"downcaseAtCursor\",\n    \"Cmd-K Cmd-Space\": \"setSublimeMark\",\n    \"Cmd-K Cmd-A\": \"selectToSublimeMark\",\n    \"Cmd-K Cmd-W\": \"deleteToSublimeMark\",\n    \"Cmd-K Cmd-X\": \"swapWithSublimeMark\",\n    \"Cmd-K Cmd-Y\": \"sublimeYank\",\n    \"Cmd-K Cmd-C\": \"showInCenter\",\n    \"Cmd-K Cmd-G\": \"clearBookmarks\",\n    \"Cmd-K Cmd-Backspace\": \"delLineLeft\",\n    \"Cmd-K Cmd-1\": \"foldAll\",\n    \"Cmd-K Cmd-0\": \"unfoldAll\",\n    \"Cmd-K Cmd-J\": \"unfoldAll\",\n    \"Ctrl-Shift-Up\": \"addCursorToPrevLine\",\n    \"Ctrl-Shift-Down\": \"addCursorToNextLine\",\n    \"Cmd-F3\": \"findUnder\",\n    \"Shift-Cmd-F3\": \"findUnderPrevious\",\n    \"Alt-F3\": \"findAllUnder\",\n    \"Shift-Cmd-[\": \"fold\",\n    \"Shift-Cmd-]\": \"unfold\",\n    \"Cmd-I\": \"findIncremental\",\n    \"Shift-Cmd-I\": \"findIncrementalReverse\",\n    \"Cmd-H\": \"replace\",\n    \"F3\": \"findNext\",\n    \"Shift-F3\": \"findPrev\",\n    \"fallthrough\": \"macDefault\"\n  };\n  CodeMirror.normalizeKeyMap(keyMap.macSublime);\n\n  keyMap.pcSublime = {\n    \"Shift-Tab\": \"indentLess\",\n    \"Shift-Ctrl-K\": \"deleteLine\",\n    \"Alt-Q\": \"wrapLines\",\n    \"Ctrl-T\": \"transposeChars\",\n    \"Alt-Left\": \"goSubwordLeft\",\n    \"Alt-Right\": \"goSubwordRight\",\n    \"Ctrl-Up\": \"scrollLineUp\",\n    \"Ctrl-Down\": \"scrollLineDown\",\n    \"Ctrl-L\": \"selectLine\",\n    \"Shift-Ctrl-L\": \"splitSelectionByLine\",\n    \"Esc\": \"singleSelectionTop\",\n    \"Ctrl-Enter\": \"insertLineAfter\",\n    \"Shift-Ctrl-Enter\": \"insertLineBefore\",\n    \"Ctrl-D\": \"selectNextOccurrence\",\n    \"Shift-Ctrl-Space\": \"selectScope\",\n    \"Shift-Ctrl-M\": \"selectBetweenBrackets\",\n    \"Ctrl-M\": \"goToBracket\",\n    \"Shift-Ctrl-Up\": \"swapLineUp\",\n    \"Shift-Ctrl-Down\": \"swapLineDown\",\n    \"Ctrl-/\": \"toggleCommentIndented\",\n    \"Ctrl-J\": \"joinLines\",\n    \"Shift-Ctrl-D\": \"duplicateLine\",\n    \"F9\": \"sortLines\",\n    \"Shift-F9\": \"reverseSortLines\",\n    \"Ctrl-F9\": \"sortLinesInsensitive\",\n    \"Shift-Ctrl-F9\": \"reverseSortLinesInsensitive\",\n    \"F2\": \"nextBookmark\",\n    \"Shift-F2\": \"prevBookmark\",\n    \"Ctrl-F2\": \"toggleBookmark\",\n    \"Shift-Ctrl-F2\": \"clearBookmarks\",\n    \"Alt-F2\": \"selectBookmarks\",\n    \"Backspace\": \"smartBackspace\",\n    \"Ctrl-K Ctrl-D\": \"skipAndSelectNextOccurrence\",\n    \"Ctrl-K Ctrl-K\": \"delLineRight\",\n    \"Ctrl-K Ctrl-U\": \"upcaseAtCursor\",\n    \"Ctrl-K Ctrl-L\": \"downcaseAtCursor\",\n    \"Ctrl-K Ctrl-Space\": \"setSublimeMark\",\n    \"Ctrl-K Ctrl-A\": \"selectToSublimeMark\",\n    \"Ctrl-K Ctrl-W\": \"deleteToSublimeMark\",\n    \"Ctrl-K Ctrl-X\": \"swapWithSublimeMark\",\n    \"Ctrl-K Ctrl-Y\": \"sublimeYank\",\n    \"Ctrl-K Ctrl-C\": \"showInCenter\",\n    \"Ctrl-K Ctrl-G\": \"clearBookmarks\",\n    \"Ctrl-K Ctrl-Backspace\": \"delLineLeft\",\n    \"Ctrl-K Ctrl-1\": \"foldAll\",\n    \"Ctrl-K Ctrl-0\": \"unfoldAll\",\n    \"Ctrl-K Ctrl-J\": \"unfoldAll\",\n    \"Ctrl-Alt-Up\": \"addCursorToPrevLine\",\n    \"Ctrl-Alt-Down\": \"addCursorToNextLine\",\n    \"Ctrl-F3\": \"findUnder\",\n    \"Shift-Ctrl-F3\": \"findUnderPrevious\",\n    \"Alt-F3\": \"findAllUnder\",\n    \"Shift-Ctrl-[\": \"fold\",\n    \"Shift-Ctrl-]\": \"unfold\",\n    \"Ctrl-I\": \"findIncremental\",\n    \"Shift-Ctrl-I\": \"findIncrementalReverse\",\n    \"Ctrl-H\": \"replace\",\n    \"F3\": \"findNext\",\n    \"Shift-F3\": \"findPrev\",\n    \"fallthrough\": \"pcDefault\"\n  };\n  CodeMirror.normalizeKeyMap(keyMap.pcSublime);\n\n  var mac = keyMap.default == keyMap.macDefault;\n  keyMap.sublime = mac ? keyMap.macSublime : keyMap.pcSublime;\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,GAAC,SAAS,KAAK;AAEX,QAAIA,kBAA4B,GAAEC,oBAAAA,GAAyCC,qBAAsC,CAAA;EAKpH,GAAE,SAAS,YAAY;AAGtB,QAAI,OAAO,WAAW;AACtB,QAAI,MAAM,WAAW;AAGrB,aAAS,eAAe,KAAK,OAAO,KAAK;AACvC,UAAI,MAAM,KAAK,MAAM,MAAM,EAAG,QAAO,IAAI,QAAQ,IAAI,MAAM,OAAO,CAAC,CAAC;AACpE,UAAI,OAAO,IAAI,QAAQ,MAAM,IAAI;AACjC,UAAI,MAAM,KAAK,MAAM,MAAM,KAAK,OAAQ,QAAO,IAAI,QAAQ,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC;AACjF,UAAI,QAAQ,SAAS,MAAM,WAAW,MAAM;AAC5C,eAAS,MAAM,UAAU,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,GAAG,OAAO,GAAG,OAAO,KAAK,KAAK;AACxF,YAAI,OAAO,KAAK,OAAO,MAAM,IAAI,MAAM,IAAI,GAAG;AAC9C,YAAI,MAAM,QAAQ,OAAO,WAAW,WAAW,IAAI,IAAI,MAAM;AAC7D,YAAI,OAAO,OAAO,KAAK,YAAW,KAAM,KAAM,OAAM;AACpD,YAAI,SAAS,SAAS;AACpB,cAAI,OAAO,KAAK;AAAE,oBAAQ;AAAM,mBAAO;UAAM,MACxC,YAAW,MAAM;QAC9B,WAAiB,SAAS,MAAM;AACxB,cAAI,QAAQ,KAAK;AACf,gBAAI,QAAQ,OAAO,OAAO,OAAO,MAAM,EAAG;AAC1C,gBAAI,QAAQ,OAAO,OAAO,OAAO,MAAM,GAAG;AACxC,kBAAI,OAAO,WAAW,GAAG;AAAE,uBAAO;AAAK;cAAW,MAC7C;YACN;AACD;UACD;QACF;MACF;AACD,aAAO,IAAI,MAAM,MAAM,GAAG;IAC3B;AAED,aAAS,YAAY,IAAI,KAAK;AAC5B,SAAG,mBAAmB,SAAS,OAAO;AACpC,YAAI,GAAG,QAAQ,SAAS,GAAG,IAAI,UAAU,MAAM,MAAO;AACpD,iBAAO,eAAe,GAAG,KAAK,MAAM,MAAM,GAAG;;AAE7C,iBAAO,MAAM,IAAI,MAAM,KAAI,IAAK,MAAM,GAAA;MAC9C,CAAK;IACF;AAED,SAAK,gBAAgB,SAAS,IAAI;AAAE,kBAAY,IAAI,EAAE;IAAA;AACtD,SAAK,iBAAiB,SAAS,IAAI;AAAE,kBAAY,IAAI,CAAC;IAAA;AAEtD,SAAK,eAAe,SAAS,IAAI;AAC/B,UAAI,OAAO,GAAG,cAAA;AACd,UAAI,CAAC,GAAG,kBAAA,GAAqB;AAC3B,YAAI,oBAAoB,GAAG,aAAa,KAAK,MAAM,KAAK,cAAc,OAAO;AAC7E,YAAI,GAAG,UAAA,EAAY,QAAQ;AACzB,aAAG,YAAY,UAAU;MAC5B;AACD,SAAG,SAAS,MAAM,KAAK,MAAM,GAAG,kBAAiB,CAAE;IACvD;AACE,SAAK,iBAAiB,SAAS,IAAI;AACjC,UAAI,OAAO,GAAG,cAAA;AACd,UAAI,CAAC,GAAG,kBAAA,GAAqB;AAC3B,YAAI,iBAAiB,GAAG,aAAa,KAAK,KAAK,OAAO,IAAE;AACxD,YAAI,GAAG,UAAA,EAAY,QAAQ;AACzB,aAAG,YAAY,YAAY;MAC9B;AACD,SAAG,SAAS,MAAM,KAAK,MAAM,GAAG,kBAAiB,CAAE;IACvD;AAEE,SAAK,uBAAuB,SAAS,IAAI;AACvC,UAAI,SAAS,GAAG,eAAc,GAAI,aAAa,CAAA;AAC/C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,OAAO,CAAC,EAAE,KAAI,GAAI,KAAK,OAAO,CAAC,EAAE,GAAA;AAC5C,iBAAS,OAAO,KAAK,MAAM,QAAQ,GAAG,MAAM,EAAE;AAC5C,cAAI,EAAE,GAAG,OAAO,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,MAAM;AACvD,uBAAW,KAAK;cAAC,QAAQ,QAAQ,KAAK,OAAO,OAAO,IAAI,MAAM,CAAC;cAC9C,MAAM,QAAQ,GAAG,OAAO,KAAK,IAAI,IAAI;YAAC,CAAC;MAC7D;AACD,SAAG,cAAc,YAAY,CAAC;IAClC;AAEE,SAAK,qBAAqB,SAAS,IAAI;AACrC,UAAI,QAAQ,GAAG,eAAgB,EAAC,CAAC;AACjC,SAAG,aAAa,MAAM,QAAQ,MAAM,MAAM,EAAC,QAAQ,MAAK,CAAC;IAC7D;AAEE,SAAK,aAAa,SAAS,IAAI;AAC7B,UAAI,SAAS,GAAG,eAAc,GAAI,WAAW,CAAA;AAC7C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC;AACpB,iBAAS,KAAK;UAAC,QAAQ,IAAI,MAAM,KAAI,EAAG,MAAM,CAAC;UAChC,MAAM,IAAI,MAAM,GAAE,EAAG,OAAO,GAAG,CAAC;QAAC,CAAC;MAClD;AACD,SAAG,cAAc,QAAQ;IAC7B;AAEE,aAAS,WAAW,IAAI,OAAO;AAC7B,UAAI,GAAG,WAAA,EAAc,QAAO,WAAW;AACvC,SAAG,UAAU,WAAW;AACtB,YAAI,MAAM,GAAG,eAAA,EAAiB,QAAQ,eAAe,CAAE,GAAE,OAAO;AAChE,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,OAAO,GAAG,eAAgB,EAAC,CAAC,EAAE;AAClC,cAAI,KAAK,QAAQ,KAAM;AACvB,cAAI,KAAK,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI,CAAC;AAC3C,aAAG,aAAa,MAAM,IAAI,MAAM,aAAa;AAC7C,aAAG,WAAW,GAAG,MAAM,MAAM,IAAI;AACjC,uBAAa,KAAK,EAAC,MAAM,IAAI,QAAQ,GAAE,CAAC;AACxC,iBAAO,KAAK,OAAO;QACpB;AACD,WAAG,cAAc,YAAY;MACnC,CAAK;AACD,SAAG,YAAY,YAAY;IAC5B;AAED,SAAK,kBAAkB,SAAS,IAAI;AAAE,aAAO,WAAW,IAAI,KAAK;IAAA;AAEjE,SAAK,mBAAmB,SAAS,IAAI;AAAE,aAAO,WAAW,IAAI,IAAI;IAAA;AAEjE,aAAS,OAAO,IAAI,KAAK;AACvB,UAAI,QAAQ,IAAI,IAAI,MAAM,OAAO,OAAO,GAAG,QAAQ,IAAI,IAAI;AAC3D,aAAO,SAAS,WAAW,WAAW,KAAK,OAAO,QAAQ,CAAC,CAAC,EAAG,GAAE;AACjE,aAAO,MAAM,KAAK,UAAU,WAAW,WAAW,KAAK,OAAO,GAAG,CAAC,EAAG,GAAE;AACvE,aAAO,EAAC,MAAM,IAAI,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG,GAAG,MAAM,KAAK,MAAM,OAAO,GAAG,EAAC;IACzF;AAED,SAAK,uBAAuB,SAAS,IAAI;AACvC,UAAI,OAAO,GAAG,UAAU,MAAM,GAAG,KAAK,GAAG,UAAU,IAAI;AACvD,UAAI,WAAW,GAAG,MAAM,uBAAuB,GAAG,IAAI;AACtD,UAAI,WAAW,OAAO,MAAM,EAAE,KAAK,GAAG;AACpC,YAAI,OAAO,OAAO,IAAI,IAAI;AAC1B,YAAI,CAAC,KAAK,KAAM;AAChB,WAAG,aAAa,KAAK,MAAM,KAAK,EAAE;AAClC,mBAAW;MACjB,OAAW;AACL,YAAI,OAAO,GAAG,SAAS,MAAM,EAAE;AAC/B,YAAI,QAAQ,WAAW,IAAI,OAAO,QAAQ,OAAO,KAAK,IAAI;AAC1D,YAAI,MAAM,GAAG,gBAAgB,OAAO,EAAE;AACtC,YAAI,QAAQ,IAAI,SAAA;AAChB,YAAI,CAAC,OAAO;AACV,gBAAM,GAAG,gBAAgB,OAAO,IAAI,GAAG,UAAS,GAAI,CAAC,CAAC;AACtD,kBAAQ,IAAI,SAAA;QACb;AACD,YAAI,CAAC,SAAS,gBAAgB,GAAG,eAAgB,GAAE,IAAI,KAAA,GAAQ,IAAI,GAAE,CAAE,EAAG;AAC1E,WAAG,aAAa,IAAI,KAAM,GAAE,IAAI,GAAE,CAAE;MACrC;AACD,UAAI;AACF,WAAG,MAAM,sBAAsB,GAAG,IAAI;IAC5C;AAEE,SAAK,8BAA8B,SAAS,IAAI;AAC9C,UAAI,aAAa,GAAG,UAAU,QAAQ,GAAG,WAAW,GAAG,UAAU,MAAM;AACvE,WAAK,qBAAqB,EAAE;AAC5B,UAAI,WAAW,OAAO,YAAY,QAAQ,KAAK,GAAG;AAChD,WAAG,IAAI,cAAc,GAAG,IAAI,eAAgB,EACvC,OAAO,SAAU,KAAK;AACrB,iBAAO,IAAI,UAAU,cAAc,IAAI,QAAQ;QAChD,CAAA,CAAC;MACP;IACF;AAED,aAAS,qBAAqB,IAAI,KAAK;AACrC,UAAI,SAAS,GAAG,eAAc,GAAI,YAAY,CAAA;AAC9C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,YAAY,GAAG;UACf,MAAM;UAAQ;UAAK;UAAQ,MAAM,OAAO;QAAU;AACtD,YAAI,UAAU,GAAG;UACb,MAAM;UAAM;UAAK;UAAQ,MAAM,KAAK;QAAU;AAClD,kBAAU,aAAa,MAAM,OAAO,cAAc,OAC9C,MAAM,OAAO,aAAa,GAAG,aAAa,MAAM,QAAQ,KAAK,EAAE;AACnE,gBAAQ,aAAa,MAAM,KAAK,cAAc,OAC1C,MAAM,KAAK,aAAa,GAAG,aAAa,MAAM,MAAM,KAAK,EAAE;AAC/D,YAAI,WAAW,EAAC,QAAQ,WAAW,MAAM,QAAO;AAChD,kBAAU,KAAK,KAAK;AACpB,kBAAU,KAAK,QAAQ;MACxB;AACD,SAAG,cAAc,SAAS;IAC3B;AACD,SAAK,sBAAsB,SAAS,IAAI;AAAE,2BAAqB,IAAI,EAAE;IAAA;AACrE,SAAK,sBAAsB,SAAS,IAAI;AAAE,2BAAqB,IAAI,CAAC;IAAA;AAEpE,aAAS,gBAAgB,QAAQ,MAAM,IAAI;AACzC,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AACjC,YAAI,WAAW,OAAO,OAAO,CAAC,EAAE,KAAM,GAAE,IAAI,KAAK,KAC7C,WAAW,OAAO,OAAO,CAAC,EAAE,GAAE,GAAI,EAAE,KAAK,EAAG,QAAO;AACzD,aAAO;IACR;AAED,QAAI,SAAS;AACb,aAAS,sBAAsB,IAAI;AACjC,UAAI,SAAS,GAAG,eAAgB,GAAE,YAAY,CAAE;AAChD,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC,GAAG,MAAM,MAAM,MAAM,UAAU,GAAG,eAAe,KAAK,EAAE;AAC5E,YAAI,CAAC,QAAS,QAAO;AACrB,mBAAS;AACP,cAAI,UAAU,GAAG,eAAe,KAAK,CAAC;AACtC,cAAI,CAAC,QAAS,QAAO;AACrB,cAAI,QAAQ,MAAM,OAAO,OAAO,OAAO,QAAQ,QAAQ,EAAE,IAAI,CAAC,GAAG;AAC/D,gBAAI,WAAW,IAAI,QAAQ,IAAI,MAAM,QAAQ,IAAI,KAAK,CAAC;AACvD,gBAAI,WAAW,OAAO,UAAU,MAAM,KAAM,CAAA,KAAK,KAC7C,WAAW,OAAO,QAAQ,KAAK,MAAM,GAAE,CAAE,KAAK,GAAG;AACnD,wBAAU,GAAG,eAAe,QAAQ,KAAK,EAAE;AAC3C,kBAAI,CAAC,QAAS,QAAO;YACjC,OAAiB;AACL,wBAAU,KAAK,EAAC,QAAQ,UAAU,MAAM,QAAQ,IAAG,CAAC;AACpD;YACD;UACF;AACD,gBAAM,IAAI,QAAQ,IAAI,MAAM,QAAQ,IAAI,KAAK,CAAC;QAC/C;MACF;AACD,SAAG,cAAc,SAAS;AAC1B,aAAO;IACR;AAED,SAAK,cAAc,SAAS,IAAI;AAC9B,4BAAsB,EAAE,KAAK,GAAG,YAAY,WAAW;IAC3D;AACE,SAAK,wBAAwB,SAAS,IAAI;AACxC,UAAI,CAAC,sBAAsB,EAAE,EAAG,QAAO,WAAW;IACtD;AAEE,aAAS,SAAS,MAAM;AACtB,aAAO,CAAC,OAAO,OAAO,kBAAkB,KAAK,IAAI,IAAI,OAAO;IAC7D;AAED,SAAK,cAAc,SAAS,IAAI;AAC9B,SAAG,mBAAmB,SAAS,OAAO;AACpC,YAAI,OAAO,GAAG,eAAe,MAAM,MAAM,GAAG,SAAS,GAAG,eAAe,MAAM,IAAI,CAAC,CAAC;AACnF,YAAI,QAAQ,WAAW,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,EAAG,QAAO,KAAK;AACtE,YAAI,OAAO,GAAG,eAAe,MAAM,MAAM,IAAI,SAAS,GAAG,eAAe,IAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;AACjH,eAAO,QAAQ,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,CAAC,KAAK,MAAM;MAClE,CAAK;IACL;AAEE,SAAK,aAAa,SAAS,IAAI;AAC7B,UAAI,GAAG,WAAA,EAAc,QAAO,WAAW;AACvC,UAAI,SAAS,GAAG,eAAgB,GAAE,cAAc,CAAE,GAAE,KAAK,GAAG,UAAW,IAAG,GAAG,UAAU,CAAA;AACvF,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC,GAAG,OAAO,MAAM,KAAM,EAAC,OAAO,GAAG,KAAK,MAAM,GAAE,EAAG;AACrE,gBAAQ,KAAK;UAAC,QAAQ,IAAI,MAAM,OAAO,OAAO,GAAG,MAAM,OAAO,EAAE;UAClD,MAAM,IAAI,MAAM,KAAK,OAAO,GAAG,MAAM,KAAK,EAAE;QAAC,CAAC;AAC5D,YAAI,MAAM,GAAI,EAAC,MAAM,KAAK,CAAC,MAAM,MAAA,EAAS,GAAE;AAC5C,YAAI,OAAO,GAAI,aAAY,KAAK,MAAM,EAAE;iBAC/B,YAAY,OAAQ,aAAY,YAAY,SAAS,CAAC,IAAI;AACnE,aAAK;MACN;AACD,SAAG,UAAU,WAAW;AACtB,iBAASC,KAAI,GAAGA,KAAI,YAAY,QAAQA,MAAK,GAAG;AAC9C,cAAIC,QAAO,YAAYD,EAAC,GAAGE,MAAK,YAAYF,KAAI,CAAC;AACjD,cAAI,OAAO,GAAG,QAAQC,KAAI;AAC1B,aAAG,aAAa,IAAI,IAAIA,OAAM,CAAC,GAAG,IAAIA,QAAO,GAAG,CAAC,GAAG,WAAW;AAC/D,cAAIC,MAAK,GAAG,SAAU;AACpB,eAAG,aAAa,OAAO,MAAM,IAAI,GAAG,SAAA,CAAU,GAAG,MAAM,WAAW;;AAElE,eAAG,aAAa,OAAO,MAAM,IAAIA,KAAI,CAAC,GAAG,MAAM,WAAW;QAC7D;AACD,WAAG,cAAc,OAAO;AACxB,WAAG,eAAc;MACvB,CAAK;IACL;AAEE,SAAK,eAAe,SAAS,IAAI;AAC/B,UAAI,GAAG,WAAA,EAAc,QAAO,WAAW;AACvC,UAAI,SAAS,GAAG,eAAc,GAAI,cAAc,CAAA,GAAI,KAAK,GAAG,SAAU,IAAG;AACzE,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,QAAQ,OAAO,CAAC,GAAG,OAAO,MAAM,GAAI,EAAC,OAAO,GAAG,KAAK,MAAM,KAAI,EAAG;AACrE,YAAI,MAAM,GAAI,EAAC,MAAM,KAAK,CAAC,MAAM,MAAA,EAAS;AAC1C,YAAI,OAAO,GAAI,aAAY,KAAK,MAAM,EAAE;iBAC/B,YAAY,OAAQ,aAAY,YAAY,SAAS,CAAC,IAAI;AACnE,aAAK;MACN;AACD,SAAG,UAAU,WAAW;AACtB,iBAASF,KAAI,YAAY,SAAS,GAAGA,MAAK,GAAGA,MAAK,GAAG;AACnD,cAAIC,QAAO,YAAYD,EAAC,GAAGE,MAAK,YAAYF,KAAI,CAAC;AACjD,cAAI,OAAO,GAAG,QAAQC,KAAI;AAC1B,cAAIA,SAAQ,GAAG,SAAU;AACvB,eAAG,aAAa,IAAI,IAAIA,QAAO,CAAC,GAAG,IAAIA,KAAI,GAAG,WAAW;;AAEzD,eAAG,aAAa,IAAI,IAAIA,OAAM,CAAC,GAAG,IAAIA,QAAO,GAAG,CAAC,GAAG,WAAW;AACjE,aAAG,aAAa,OAAO,MAAM,IAAIC,KAAI,CAAC,GAAG,MAAM,WAAW;QAC3D;AACD,WAAG,eAAc;MACvB,CAAK;IACL;AAEE,SAAK,wBAAwB,SAAS,IAAI;AACxC,SAAG,cAAc,EAAE,QAAQ,KAAM,CAAA;IAClC;AAED,SAAK,YAAY,SAAS,IAAI;AAC5B,UAAI,SAAS,GAAG,eAAc,GAAI,SAAS,CAAA;AAC3C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC,GAAG,OAAO,MAAM,KAAA;AACpC,YAAI,QAAQ,KAAK,MAAM,MAAM,MAAM,GAAI,EAAC;AACxC,eAAO,IAAI,OAAO,SAAS,KAAK,OAAO,IAAI,CAAC,EAAE,KAAM,EAAC,QAAQ;AAC3D,gBAAM,OAAO,EAAE,CAAC,EAAE,GAAE,EAAG;AACzB,eAAO,KAAK,EAAC,OAAc,KAAU,QAAQ,CAAC,MAAM,MAAA,KAAW,KAAI,CAAC;MACrE;AACD,SAAG,UAAU,WAAW;AACtB,YAAI,SAAS,GAAGC,UAAS,CAAA;AACzB,iBAASH,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,cAAI,MAAM,OAAOA,EAAC;AAClB,cAAI,SAAS,IAAI,UAAU,IAAI,IAAI,OAAO,OAAO,QAAQ,IAAI,OAAO,EAAE,GAAG;AACzE,mBAAS,OAAO,IAAI,OAAO,QAAQ,IAAI,KAAK,QAAQ;AAClD,gBAAI,SAAS,OAAO;AACpB,gBAAI,QAAQ,IAAI,IAAK,QAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,EAAE,SAAS,CAAC;AACrE,gBAAI,SAAS,GAAG,SAAA,GAAY;AAC1B,iBAAG,aAAa,KAAK,IAAI,MAAM,GAAG,IAAI,SAAS,GAAG,OAAO,KAAK,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;AAChG,gBAAE;YACH;UACF;AACDG,kBAAO,KAAK,EAAC,QAAQ,UAAU,MAAM,KAAU,CAAC;QACjD;AACD,WAAG,cAAcA,SAAQ,CAAC;MAChC,CAAK;IACL;AAEE,SAAK,gBAAgB,SAAS,IAAI;AAChC,SAAG,UAAU,WAAW;AACtB,YAAI,aAAa,GAAG,eAAc,EAAG;AACrC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAI,QAAQ,GAAG,eAAgB,EAAC,CAAC;AACjC,cAAI,MAAM,MAAO;AACf,eAAG,aAAa,GAAG,QAAQ,MAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;;AAE3E,eAAG,aAAa,GAAG,SAAS,MAAM,KAAI,GAAI,MAAM,GAAE,CAAE,GAAG,MAAM,KAAM,CAAA;QACtE;AACD,WAAG,eAAc;MACvB,CAAK;IACL;AAGE,aAAS,UAAU,IAAI,eAAe,WAAW;AAC/C,UAAI,GAAG,WAAA,EAAc,QAAO,WAAW;AACvC,UAAI,SAAS,GAAG,eAAgB,GAAE,SAAS,CAAE,GAAE;AAC/C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,MAAK,EAAI;AACnB,YAAI,OAAO,MAAM,KAAA,EAAO,MAAM,KAAK,MAAM,GAAI,EAAC;AAC9C,eAAO,IAAI,OAAO,SAAS,KAAK,OAAO,IAAI,CAAC,EAAE,KAAM,EAAC,QAAQ;AAC3D,eAAK,OAAO,EAAE,CAAC,EAAE,GAAE,EAAG;AACxB,YAAI,CAAC,OAAO,CAAC,EAAE,GAAE,EAAG,GAAI;AACxB,eAAO,KAAK,MAAM,EAAE;MACrB;AACD,UAAI,OAAO,OAAQ,YAAW;UACzB,QAAO,KAAK,GAAG,UAAW,GAAE,GAAG,SAAQ,CAAE;AAE9C,SAAG,UAAU,WAAW;AACtB,YAAIA,UAAS,CAAA;AACb,iBAASH,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK,GAAG;AACzC,cAAIC,QAAO,OAAOD,EAAC,GAAGE,MAAK,OAAOF,KAAI,CAAC;AACvC,cAAI,QAAQ,IAAIC,OAAM,CAAC,GAAG,MAAM,IAAIC,GAAE;AACtC,cAAI,QAAQ,GAAG,SAAS,OAAO,KAAK,KAAK;AACzC,cAAI;AACF,kBAAM,KAAK,SAAS,GAAG,GAAG;AAAE,qBAAO,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI;YAAY,CAAA;;AAEjF,kBAAM,KAAK,SAAS,GAAG,GAAG;AACxB,kBAAI,KAAK,EAAE,YAAW,GAAI,KAAK,EAAE,YAAA;AACjC,kBAAI,MAAM,IAAI;AAAE,oBAAI;AAAI,oBAAI;cAAK;AACjC,qBAAO,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI;YACrD,CAAW;AACH,aAAG,aAAa,OAAO,OAAO,GAAG;AACjC,cAAI,SAAUC,SAAO,KAAK,EAAC,QAAQ,OAAO,MAAM,IAAID,MAAK,GAAG,CAAC,EAAC,CAAC;QAChE;AACD,YAAI,SAAU,IAAG,cAAcC,SAAQ,CAAC;MAC9C,CAAK;IACF;AAED,SAAK,YAAY,SAAS,IAAI;AAAE,gBAAU,IAAI,MAAM,CAAC;IAAA;AACrD,SAAK,mBAAmB,SAAS,IAAI;AAAE,gBAAU,IAAI,MAAM,EAAE;IAAA;AAC7D,SAAK,uBAAuB,SAAS,IAAI;AAAE,gBAAU,IAAI,OAAO,CAAC;IAAA;AACjE,SAAK,8BAA8B,SAAS,IAAI;AAAE,gBAAU,IAAI,OAAO,EAAE;IAAA;AAEzE,SAAK,eAAe,SAAS,IAAI;AAC/B,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,MAAO,QAAO,MAAM,QAAQ;AAC9B,YAAI,UAAU,MAAM,MAAA;AACpB,YAAI,QAAQ,QAAQ,KAAA;AACpB,YAAI,OAAO;AACT,gBAAM,KAAK,OAAO;AAClB,iBAAO,GAAG,aAAa,MAAM,MAAM,MAAM,EAAE;QAC5C;MACF;IACL;AAEE,SAAK,eAAe,SAAS,IAAI;AAC/B,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,MAAO,QAAO,MAAM,QAAQ;AAC9B,cAAM,QAAQ,MAAM,IAAK,CAAA;AACzB,YAAI,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,KAAA;AACpC,YAAI,CAAC;AACH,gBAAM,IAAG;;AAET,iBAAO,GAAG,aAAa,MAAM,MAAM,MAAM,EAAE;MAC9C;IACL;AAEE,SAAK,iBAAiB,SAAS,IAAI;AACjC,UAAI,SAAS,GAAG,eAAA;AAChB,UAAI,QAAQ,GAAG,MAAM,qBAAqB,GAAG,MAAM,mBAAmB,CAAA;AACtE,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,OAAO,CAAC,EAAE,KAAI,GAAI,KAAK,OAAO,CAAC,EAAE,GAAA;AAC5C,YAAI,QAAQ,OAAO,CAAC,EAAE,MAAO,IAAG,GAAG,YAAY,IAAI,IAAI,GAAG,UAAU,MAAM,EAAE;AAC5E,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,MAAM,CAAC,EAAE,iBAAiB;AAC5B,kBAAM,CAAC,EAAE,MAAA;AACT,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,kBAAI,MAAM,CAAC,KAAK,MAAM,CAAC;AACrB,sBAAM,OAAO,KAAK,CAAC;AACvB;UACD;QACF;AACD,YAAI,KAAK,MAAM;AACb,gBAAM,KAAK,GAAG,SAAS,MAAM,IAAI,EAAC,iBAAiB,MAAM,gBAAgB,MAAK,CAAC,CAAC;MACnF;IACL;AAEE,SAAK,iBAAiB,SAAS,IAAI;AACjC,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,MAAO,UAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,OAAM,CAAC,EAAE,MAAK;AAChE,YAAM,SAAS;IACnB;AAEE,SAAK,kBAAkB,SAAS,IAAI;AAClC,UAAI,QAAQ,GAAG,MAAM,kBAAkB,SAAS,CAAA;AAChD,UAAI,MAAO,UAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAChD,YAAI,QAAQ,MAAM,CAAC,EAAE,KAAI;AACzB,YAAI,CAAC;AACH,gBAAM,OAAO,KAAK,CAAC;;AAEnB,iBAAO,KAAK,EAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,GAAE,CAAC;MACnD;AACD,UAAI,OAAO;AACT,WAAG,cAAc,QAAQ,CAAC;IAChC;AAEE,aAAS,sBAAsB,IAAI,KAAK;AACtC,SAAG,UAAU,WAAW;AACtB,YAAI,SAAS,GAAG,eAAc,GAAI,UAAU,CAAE,GAAE,eAAe,CAAA;AAC/D,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,MAAM,MAAA,GAAS;AAAE,oBAAQ,KAAK,CAAC;AAAG,yBAAa,KAAK,EAAE;UAAI,MACzD,cAAa,KAAK,IAAI,GAAG,SAAS,MAAM,KAAI,GAAI,MAAM,GAAI,CAAA,CAAC,CAAC;QAClE;AACD,WAAG,kBAAkB,cAAc,UAAU,MAAM;AACnD,iBAAS,IAAI,QAAQ,SAAS,GAAG,IAAI,KAAK,GAAG,KAAK;AAChD,cAAI,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAC7B,cAAI,MAAM,WAAW,OAAO,MAAM,MAAM,EAAE,IAAI,EAAG;AACjD,cAAI,OAAO,OAAO,IAAI,MAAM,IAAI;AAChC,eAAK,KAAK;AACV,aAAG,aAAa,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,KAAK,EAAE;QACnD;MACP,CAAK;IACF;AAED,SAAK,iBAAiB,SAAS,IAAI;AACjC,UAAI,GAAG,kBAAiB,EAAI,QAAO,WAAW;AAE9C,SAAG,UAAU,WAAW;AACtB,YAAI,UAAU,GAAG,eAAA;AACjB,YAAI,aAAa,GAAG,UAAU,YAAY;AAE1C,iBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,cAAI,SAAS,QAAQ,CAAC,EAAE;AACxB,cAAI,gBAAgB,GAAG,SAAS,EAAC,MAAM,OAAO,MAAM,IAAI,EAAC,GAAG,MAAM;AAClE,cAAI,SAAS,WAAW,YAAY,eAAe,MAAM,GAAG,UAAU,SAAS,CAAC;AAGhF,cAAI,YAAY,GAAG,SAAS,QAAQ,IAAI,QAAQ,KAAK;AAErD,cAAI,iBAAiB,CAAC,KAAK,KAAK,aAAa,KAAK,SAAS,cAAc,GAAG;AAC1E,gBAAI,aAAa,IAAI;cAAI,OAAO;cAC9B,WAAW,WAAW,eAAe,SAAS,YAAY,UAAU;YAAC;AAGvE,gBAAI,WAAW,MAAM,OAAO,GAAI,aAAY;UAC7C;AAED,aAAG,aAAa,IAAI,WAAW,QAAQ,SAAS;QACjD;MACP,CAAK;IACL;AAEE,SAAK,eAAe,SAAS,IAAI;AAC/B,SAAG,UAAU,WAAW;AACtB,YAAI,SAAS,GAAG,eAAA;AAChB,iBAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AACtC,aAAG,aAAa,IAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,OAAO,CAAC,EAAE,GAAE,EAAG,IAAI,GAAG,SAAS;AAC3E,WAAG,eAAc;MACvB,CAAK;IACL;AAEE,SAAK,iBAAiB,SAAS,IAAI;AACjC,4BAAsB,IAAI,SAAS,KAAK;AAAE,eAAO,IAAI,YAAW;MAAG,CAAE;IACzE;AACE,SAAK,mBAAmB,SAAS,IAAI;AACnC,4BAAsB,IAAI,SAAS,KAAK;AAAE,eAAO,IAAI,YAAW;MAAG,CAAE;IACzE;AAEE,SAAK,iBAAiB,SAAS,IAAI;AACjC,UAAI,GAAG,MAAM,YAAa,IAAG,MAAM,YAAY,MAAA;AAC/C,SAAG,MAAM,cAAc,GAAG,YAAY,GAAG,UAAS,CAAE;IACxD;AACE,SAAK,sBAAsB,SAAS,IAAI;AACtC,UAAI,QAAQ,GAAG,MAAM,eAAe,GAAG,MAAM,YAAY,KAAA;AACzD,UAAI,MAAO,IAAG,aAAa,GAAG,UAAS,GAAI,KAAK;IACpD;AACE,SAAK,sBAAsB,SAAS,IAAI;AACtC,UAAI,QAAQ,GAAG,MAAM,eAAe,GAAG,MAAM,YAAY,KAAA;AACzD,UAAI,OAAO;AACT,YAAI,OAAO,GAAG,UAAS,GAAI,KAAK;AAChC,YAAI,WAAW,OAAO,MAAM,EAAE,IAAI,GAAG;AAAE,cAAI,MAAM;AAAI,eAAK;AAAM,iBAAO;QAAM;AAC7E,WAAG,MAAM,gBAAgB,GAAG,SAAS,MAAM,EAAE;AAC7C,WAAG,aAAa,IAAI,MAAM,EAAE;MAC7B;IACL;AACE,SAAK,sBAAsB,SAAS,IAAI;AACtC,UAAI,QAAQ,GAAG,MAAM,eAAe,GAAG,MAAM,YAAY,KAAA;AACzD,UAAI,OAAO;AACT,WAAG,MAAM,YAAY,MAAA;AACrB,WAAG,MAAM,cAAc,GAAG,YAAY,GAAG,UAAS,CAAE;AACpD,WAAG,UAAU,KAAK;MACnB;IACL;AACE,SAAK,cAAc,SAAS,IAAI;AAC9B,UAAI,GAAG,MAAM,iBAAiB;AAC5B,WAAG,iBAAiB,GAAG,MAAM,eAAe,MAAM,OAAO;IAC/D;AAEE,SAAK,eAAe,SAAS,IAAI;AAC/B,UAAI,MAAM,GAAG,aAAa,MAAM,OAAO;AACvC,SAAG,SAAS,OAAO,IAAI,MAAM,IAAI,UAAU,IAAI,GAAG,cAAe,EAAC,eAAe,CAAC;IACtF;AAEE,aAAS,UAAU,IAAI;AACrB,UAAI,OAAO,GAAG,UAAU,MAAM,GAAG,KAAK,GAAG,UAAU,IAAI;AACvD,UAAI,WAAW,OAAO,MAAM,EAAE,KAAK,GAAG;AACpC,YAAI,OAAO,OAAO,IAAI,IAAI;AAC1B,YAAI,CAAC,KAAK,KAAM;AAChB,eAAO,KAAK;AACZ,aAAK,KAAK;MACX;AACD,aAAO,EAAC,MAAY,IAAQ,OAAO,GAAG,SAAS,MAAM,EAAE,GAAG,KAAU;IACrE;AAED,aAAS,YAAY,IAAI,SAAS;AAChC,UAAI,SAAS,UAAU,EAAE;AACzB,UAAI,CAAC,OAAQ;AACb,UAAI,QAAQ,OAAO;AACnB,UAAI,MAAM,GAAG,gBAAgB,OAAO,UAAU,OAAO,KAAK,OAAO,IAAI;AAErE,UAAI,UAAU,IAAI,SAAU,IAAG,IAAI,aAAY,GAAI;AACjD,WAAG,aAAa,IAAI,KAAM,GAAE,IAAI,GAAE,CAAE;MAC1C,OAAW;AACL,cAAM,GAAG,gBAAgB,OAAO,UAAU,IAAI,GAAG,UAAW,GAAE,CAAC,IACrB,GAAG,QAAQ,IAAI,GAAG,SAAQ,CAAE,CAAC,CAAC;AACxE,YAAI,UAAU,IAAI,SAAQ,IAAK,IAAI,aAAc;AAC/C,aAAG,aAAa,IAAI,KAAM,GAAE,IAAI,GAAE,CAAE;iBAC7B,OAAO;AACd,aAAG,aAAa,OAAO,MAAM,OAAO,EAAE;MACzC;IACL;AACE,SAAK,YAAY,SAAS,IAAI;AAAE,kBAAY,IAAI,IAAI;IAAA;AACpD,SAAK,oBAAoB,SAAS,IAAI;AAAE,kBAAY,IAAG,KAAK;IAAA;AAC5D,SAAK,eAAe,SAAS,IAAI;AAC/B,UAAI,SAAS,UAAU,EAAE;AACzB,UAAI,CAAC,OAAQ;AACb,UAAI,MAAM,GAAG,gBAAgB,OAAO,KAAK;AACzC,UAAI,UAAU,CAAA;AACd,UAAI,eAAe;AACnB,aAAO,IAAI,SAAA,GAAY;AACrB,gBAAQ,KAAK,EAAC,QAAQ,IAAI,KAAA,GAAQ,MAAM,IAAI,GAAI,EAAA,CAAC;AACjD,YAAI,IAAI,KAAI,EAAG,QAAQ,OAAO,KAAK,QAAQ,IAAI,KAAM,EAAC,MAAM,OAAO,KAAK;AACtE;MACH;AACD,SAAG,cAAc,SAAS,YAAY;IAC1C;AAGE,QAAI,SAAS,WAAW;AACxB,WAAO,aAAa;MAClB,YAAY;MACZ,aAAa;MACb,gBAAgB;MAChB,SAAS;MACT,aAAa;MACb,cAAc;MACd,eAAe;MACf,iBAAiB;MACjB,SAAS;MACT,eAAe;MACf,OAAO;MACP,aAAa;MACb,mBAAmB;MACnB,SAAS;MACT,mBAAmB;MACnB,eAAe;MACf,SAAS;MACT,eAAe;MACf,iBAAiB;MACjB,SAAS;MACT,SAAS;MACT,eAAe;MACf,MAAM;MACN,YAAY;MACZ,UAAU;MACV,gBAAgB;MAChB,MAAM;MACN,YAAY;MACZ,UAAU;MACV,gBAAgB;MAChB,UAAU;MACV,aAAa;MACb,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,mBAAmB;MACnB,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,uBAAuB;MACvB,eAAe;MACf,eAAe;MACf,eAAe;MACf,iBAAiB;MACjB,mBAAmB;MACnB,UAAU;MACV,gBAAgB;MAChB,UAAU;MACV,eAAe;MACf,eAAe;MACf,SAAS;MACT,eAAe;MACf,SAAS;MACT,MAAM;MACN,YAAY;MACZ,eAAe;IACnB;AACE,eAAW,gBAAgB,OAAO,UAAU;AAE5C,WAAO,YAAY;MACjB,aAAa;MACb,gBAAgB;MAChB,SAAS;MACT,UAAU;MACV,YAAY;MACZ,aAAa;MACb,WAAW;MACX,aAAa;MACb,UAAU;MACV,gBAAgB;MAChB,OAAO;MACP,cAAc;MACd,oBAAoB;MACpB,UAAU;MACV,oBAAoB;MACpB,gBAAgB;MAChB,UAAU;MACV,iBAAiB;MACjB,mBAAmB;MACnB,UAAU;MACV,UAAU;MACV,gBAAgB;MAChB,MAAM;MACN,YAAY;MACZ,WAAW;MACX,iBAAiB;MACjB,MAAM;MACN,YAAY;MACZ,WAAW;MACX,iBAAiB;MACjB,UAAU;MACV,aAAa;MACb,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,qBAAqB;MACrB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,yBAAyB;MACzB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,eAAe;MACf,iBAAiB;MACjB,WAAW;MACX,iBAAiB;MACjB,UAAU;MACV,gBAAgB;MAChB,gBAAgB;MAChB,UAAU;MACV,gBAAgB;MAChB,UAAU;MACV,MAAM;MACN,YAAY;MACZ,eAAe;IACnB;AACE,eAAW,gBAAgB,OAAO,SAAS;AAE3C,QAAI,MAAM,OAAO,WAAW,OAAO;AACnC,WAAO,UAAU,MAAM,OAAO,aAAa,OAAO;EACpD,CAAC;;;;;;;;", "names": ["require$$0", "require$$1", "require$$2", "i", "from", "to", "ranges"]}