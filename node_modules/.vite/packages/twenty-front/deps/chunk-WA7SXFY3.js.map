{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/Sidebar/SidebarButton.vue.js"], "sourcesContent": ["import { defineComponent as i, openBlock as o, createBlock as e, unref as r, withCtx as u, renderSlot as p, createCommentVNode as s } from \"vue\";\nimport { ScalarButton as m } from \"@scalar/components\";\nimport d from \"../ScalarHotkey.vue.js\";\nimport { useLayout as k } from \"../../hooks/useLayout.js\";\nconst g = /* @__PURE__ */ i({\n  __name: \"SidebarButton\",\n  props: {\n    click: { type: Function },\n    hotkey: {}\n  },\n  setup(n) {\n    const a = n, { layout: c } = k(), l = () => {\n      a.click();\n    };\n    return (t, h) => (o(), e(r(m), {\n      class: \"bg-b-1 text-c-1 hover:bg-b-2 border-1/2 group relative h-auto w-auto px-2 py-1 md:w-full md:p-1.5\",\n      icon: \"Plus\",\n      variant: \"outlined\",\n      onClick: l\n    }, {\n      default: u(() => [\n        p(t.$slots, \"title\"),\n        t.hotkey && r(c) === \"desktop\" ? (o(), e(d, {\n          key: 0,\n          class: \"text-c-2 add-item-hotkey absolute right-2 hidden group-hover:opacity-80 md:block\",\n          hotkey: t.hotkey\n        }, null, 8, [\"hotkey\"])) : s(\"\", !0)\n      ]),\n      _: 3\n    }));\n  }\n});\nexport {\n  g as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO,EAAE,MAAM,SAAS;AAAA,IACxB,QAAQ,CAAC;AAAA,EACX;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC1C,QAAE,MAAM;AAAA,IACV;AACA,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,MAC7B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,IACX,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,WAAE,EAAE,QAAQ,OAAO;AAAA,QACnB,EAAE,UAAU,MAAE,CAAC,MAAM,aAAa,UAAE,GAAG,YAAE,GAAG;AAAA,UAC1C,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ,EAAE;AAAA,QACZ,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACrC,CAAC;AAAA,MACD,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": []}