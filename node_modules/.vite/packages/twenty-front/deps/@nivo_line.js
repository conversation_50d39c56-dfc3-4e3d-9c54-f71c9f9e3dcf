import {
  require_prop_types
} from "./chunk-IWRIPP2N.js";
import {
  cubehelix,
  cubehelixLong,
  number_default,
  rgb,
  rgbBasis,
  round_default,
  string_default,
  value_default
} from "./chunk-MQHIVPAP.js";
import {
  require_Set,
  require_SetCache,
  require_Stack,
  require_Symbol,
  require_Uint8Array,
  require_arrayLikeKeys,
  require_arrayMap,
  require_arrayPush,
  require_baseAssignValue,
  require_baseFor,
  require_baseForOwn,
  require_baseGet,
  require_baseGetTag,
  require_baseIsEqual,
  require_baseIteratee,
  require_baseUnary,
  require_cacheHas,
  require_castPath,
  require_defineProperty,
  require_eq,
  require_get,
  require_hasIn,
  require_identity,
  require_isArguments,
  require_isArray,
  require_isArrayLike,
  require_isBuffer,
  require_isFunction,
  require_isIndex,
  require_isObject,
  require_isObjectLike,
  require_isPrototype,
  require_isSymbol,
  require_isTypedArray,
  require_nodeUtil,
  require_overArg,
  require_root,
  require_setToArray,
  require_toKey,
  require_toString
} from "./chunk-SDSH2LOF.js";
import "./chunk-AQJSMIPF.js";
import {
  require_jsx_runtime
} from "./chunk-VV4ISGQI.js";
import {
  require_react_dom
} from "./chunk-VZQMTKPA.js";
import {
  require_react
} from "./chunk-LABDTKBP.js";
import {
  __commonJS,
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/lodash/_assignMergeValue.js
var require_assignMergeValue = __commonJS({
  "node_modules/lodash/_assignMergeValue.js"(exports, module) {
    var baseAssignValue = require_baseAssignValue();
    var eq = require_eq();
    function assignMergeValue(object, key, value) {
      if (value !== void 0 && !eq(object[key], value) || value === void 0 && !(key in object)) {
        baseAssignValue(object, key, value);
      }
    }
    module.exports = assignMergeValue;
  }
});

// node_modules/lodash/_cloneBuffer.js
var require_cloneBuffer = __commonJS({
  "node_modules/lodash/_cloneBuffer.js"(exports, module) {
    var root = require_root();
    var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
    var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
    var moduleExports = freeModule && freeModule.exports === freeExports;
    var Buffer = moduleExports ? root.Buffer : void 0;
    var allocUnsafe = Buffer ? Buffer.allocUnsafe : void 0;
    function cloneBuffer(buffer, isDeep) {
      if (isDeep) {
        return buffer.slice();
      }
      var length = buffer.length, result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);
      buffer.copy(result);
      return result;
    }
    module.exports = cloneBuffer;
  }
});

// node_modules/lodash/_cloneArrayBuffer.js
var require_cloneArrayBuffer = __commonJS({
  "node_modules/lodash/_cloneArrayBuffer.js"(exports, module) {
    var Uint8Array = require_Uint8Array();
    function cloneArrayBuffer(arrayBuffer) {
      var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
      new Uint8Array(result).set(new Uint8Array(arrayBuffer));
      return result;
    }
    module.exports = cloneArrayBuffer;
  }
});

// node_modules/lodash/_cloneTypedArray.js
var require_cloneTypedArray = __commonJS({
  "node_modules/lodash/_cloneTypedArray.js"(exports, module) {
    var cloneArrayBuffer = require_cloneArrayBuffer();
    function cloneTypedArray(typedArray, isDeep) {
      var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;
      return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
    }
    module.exports = cloneTypedArray;
  }
});

// node_modules/lodash/_copyArray.js
var require_copyArray = __commonJS({
  "node_modules/lodash/_copyArray.js"(exports, module) {
    function copyArray(source, array3) {
      var index3 = -1, length = source.length;
      array3 || (array3 = Array(length));
      while (++index3 < length) {
        array3[index3] = source[index3];
      }
      return array3;
    }
    module.exports = copyArray;
  }
});

// node_modules/lodash/_baseCreate.js
var require_baseCreate = __commonJS({
  "node_modules/lodash/_baseCreate.js"(exports, module) {
    var isObject = require_isObject();
    var objectCreate = Object.create;
    var baseCreate = /* @__PURE__ */ function() {
      function object() {
      }
      return function(proto) {
        if (!isObject(proto)) {
          return {};
        }
        if (objectCreate) {
          return objectCreate(proto);
        }
        object.prototype = proto;
        var result = new object();
        object.prototype = void 0;
        return result;
      };
    }();
    module.exports = baseCreate;
  }
});

// node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "node_modules/lodash/_getPrototype.js"(exports, module) {
    var overArg = require_overArg();
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    module.exports = getPrototype;
  }
});

// node_modules/lodash/_initCloneObject.js
var require_initCloneObject = __commonJS({
  "node_modules/lodash/_initCloneObject.js"(exports, module) {
    var baseCreate = require_baseCreate();
    var getPrototype = require_getPrototype();
    var isPrototype = require_isPrototype();
    function initCloneObject(object) {
      return typeof object.constructor == "function" && !isPrototype(object) ? baseCreate(getPrototype(object)) : {};
    }
    module.exports = initCloneObject;
  }
});

// node_modules/lodash/isArrayLikeObject.js
var require_isArrayLikeObject = __commonJS({
  "node_modules/lodash/isArrayLikeObject.js"(exports, module) {
    var isArrayLike = require_isArrayLike();
    var isObjectLike = require_isObjectLike();
    function isArrayLikeObject(value) {
      return isObjectLike(value) && isArrayLike(value);
    }
    module.exports = isArrayLikeObject;
  }
});

// node_modules/lodash/isPlainObject.js
var require_isPlainObject = __commonJS({
  "node_modules/lodash/isPlainObject.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var getPrototype = require_getPrototype();
    var isObjectLike = require_isObjectLike();
    var objectTag = "[object Object]";
    var funcProto = Function.prototype;
    var objectProto = Object.prototype;
    var funcToString = funcProto.toString;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var objectCtorString = funcToString.call(Object);
    function isPlainObject(value) {
      if (!isObjectLike(value) || baseGetTag(value) != objectTag) {
        return false;
      }
      var proto = getPrototype(value);
      if (proto === null) {
        return true;
      }
      var Ctor = hasOwnProperty.call(proto, "constructor") && proto.constructor;
      return typeof Ctor == "function" && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;
    }
    module.exports = isPlainObject;
  }
});

// node_modules/lodash/_safeGet.js
var require_safeGet = __commonJS({
  "node_modules/lodash/_safeGet.js"(exports, module) {
    function safeGet(object, key) {
      if (key === "constructor" && typeof object[key] === "function") {
        return;
      }
      if (key == "__proto__") {
        return;
      }
      return object[key];
    }
    module.exports = safeGet;
  }
});

// node_modules/lodash/_assignValue.js
var require_assignValue = __commonJS({
  "node_modules/lodash/_assignValue.js"(exports, module) {
    var baseAssignValue = require_baseAssignValue();
    var eq = require_eq();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function assignValue(object, key, value) {
      var objValue = object[key];
      if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
        baseAssignValue(object, key, value);
      }
    }
    module.exports = assignValue;
  }
});

// node_modules/lodash/_copyObject.js
var require_copyObject = __commonJS({
  "node_modules/lodash/_copyObject.js"(exports, module) {
    var assignValue = require_assignValue();
    var baseAssignValue = require_baseAssignValue();
    function copyObject(source, props, object, customizer) {
      var isNew = !object;
      object || (object = {});
      var index3 = -1, length = props.length;
      while (++index3 < length) {
        var key = props[index3];
        var newValue = customizer ? customizer(object[key], source[key], key, object, source) : void 0;
        if (newValue === void 0) {
          newValue = source[key];
        }
        if (isNew) {
          baseAssignValue(object, key, newValue);
        } else {
          assignValue(object, key, newValue);
        }
      }
      return object;
    }
    module.exports = copyObject;
  }
});

// node_modules/lodash/_nativeKeysIn.js
var require_nativeKeysIn = __commonJS({
  "node_modules/lodash/_nativeKeysIn.js"(exports, module) {
    function nativeKeysIn(object) {
      var result = [];
      if (object != null) {
        for (var key in Object(object)) {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = nativeKeysIn;
  }
});

// node_modules/lodash/_baseKeysIn.js
var require_baseKeysIn = __commonJS({
  "node_modules/lodash/_baseKeysIn.js"(exports, module) {
    var isObject = require_isObject();
    var isPrototype = require_isPrototype();
    var nativeKeysIn = require_nativeKeysIn();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function baseKeysIn(object) {
      if (!isObject(object)) {
        return nativeKeysIn(object);
      }
      var isProto = isPrototype(object), result = [];
      for (var key in object) {
        if (!(key == "constructor" && (isProto || !hasOwnProperty.call(object, key)))) {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = baseKeysIn;
  }
});

// node_modules/lodash/keysIn.js
var require_keysIn = __commonJS({
  "node_modules/lodash/keysIn.js"(exports, module) {
    var arrayLikeKeys = require_arrayLikeKeys();
    var baseKeysIn = require_baseKeysIn();
    var isArrayLike = require_isArrayLike();
    function keysIn(object) {
      return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);
    }
    module.exports = keysIn;
  }
});

// node_modules/lodash/toPlainObject.js
var require_toPlainObject = __commonJS({
  "node_modules/lodash/toPlainObject.js"(exports, module) {
    var copyObject = require_copyObject();
    var keysIn = require_keysIn();
    function toPlainObject(value) {
      return copyObject(value, keysIn(value));
    }
    module.exports = toPlainObject;
  }
});

// node_modules/lodash/_baseMergeDeep.js
var require_baseMergeDeep = __commonJS({
  "node_modules/lodash/_baseMergeDeep.js"(exports, module) {
    var assignMergeValue = require_assignMergeValue();
    var cloneBuffer = require_cloneBuffer();
    var cloneTypedArray = require_cloneTypedArray();
    var copyArray = require_copyArray();
    var initCloneObject = require_initCloneObject();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isArrayLikeObject = require_isArrayLikeObject();
    var isBuffer = require_isBuffer();
    var isFunction = require_isFunction();
    var isObject = require_isObject();
    var isPlainObject = require_isPlainObject();
    var isTypedArray = require_isTypedArray();
    var safeGet = require_safeGet();
    var toPlainObject = require_toPlainObject();
    function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {
      var objValue = safeGet(object, key), srcValue = safeGet(source, key), stacked = stack.get(srcValue);
      if (stacked) {
        assignMergeValue(object, key, stacked);
        return;
      }
      var newValue = customizer ? customizer(objValue, srcValue, key + "", object, source, stack) : void 0;
      var isCommon = newValue === void 0;
      if (isCommon) {
        var isArr = isArray(srcValue), isBuff = !isArr && isBuffer(srcValue), isTyped = !isArr && !isBuff && isTypedArray(srcValue);
        newValue = srcValue;
        if (isArr || isBuff || isTyped) {
          if (isArray(objValue)) {
            newValue = objValue;
          } else if (isArrayLikeObject(objValue)) {
            newValue = copyArray(objValue);
          } else if (isBuff) {
            isCommon = false;
            newValue = cloneBuffer(srcValue, true);
          } else if (isTyped) {
            isCommon = false;
            newValue = cloneTypedArray(srcValue, true);
          } else {
            newValue = [];
          }
        } else if (isPlainObject(srcValue) || isArguments(srcValue)) {
          newValue = objValue;
          if (isArguments(objValue)) {
            newValue = toPlainObject(objValue);
          } else if (!isObject(objValue) || isFunction(objValue)) {
            newValue = initCloneObject(srcValue);
          }
        } else {
          isCommon = false;
        }
      }
      if (isCommon) {
        stack.set(srcValue, newValue);
        mergeFunc(newValue, srcValue, srcIndex, customizer, stack);
        stack["delete"](srcValue);
      }
      assignMergeValue(object, key, newValue);
    }
    module.exports = baseMergeDeep;
  }
});

// node_modules/lodash/_baseMerge.js
var require_baseMerge = __commonJS({
  "node_modules/lodash/_baseMerge.js"(exports, module) {
    var Stack = require_Stack();
    var assignMergeValue = require_assignMergeValue();
    var baseFor = require_baseFor();
    var baseMergeDeep = require_baseMergeDeep();
    var isObject = require_isObject();
    var keysIn = require_keysIn();
    var safeGet = require_safeGet();
    function baseMerge(object, source, srcIndex, customizer, stack) {
      if (object === source) {
        return;
      }
      baseFor(source, function(srcValue, key) {
        stack || (stack = new Stack());
        if (isObject(srcValue)) {
          baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);
        } else {
          var newValue = customizer ? customizer(safeGet(object, key), srcValue, key + "", object, source, stack) : void 0;
          if (newValue === void 0) {
            newValue = srcValue;
          }
          assignMergeValue(object, key, newValue);
        }
      }, keysIn);
    }
    module.exports = baseMerge;
  }
});

// node_modules/lodash/_apply.js
var require_apply = __commonJS({
  "node_modules/lodash/_apply.js"(exports, module) {
    function apply(func, thisArg, args) {
      switch (args.length) {
        case 0:
          return func.call(thisArg);
        case 1:
          return func.call(thisArg, args[0]);
        case 2:
          return func.call(thisArg, args[0], args[1]);
        case 3:
          return func.call(thisArg, args[0], args[1], args[2]);
      }
      return func.apply(thisArg, args);
    }
    module.exports = apply;
  }
});

// node_modules/lodash/_overRest.js
var require_overRest = __commonJS({
  "node_modules/lodash/_overRest.js"(exports, module) {
    var apply = require_apply();
    var nativeMax = Math.max;
    function overRest(func, start2, transform) {
      start2 = nativeMax(start2 === void 0 ? func.length - 1 : start2, 0);
      return function() {
        var args = arguments, index3 = -1, length = nativeMax(args.length - start2, 0), array3 = Array(length);
        while (++index3 < length) {
          array3[index3] = args[start2 + index3];
        }
        index3 = -1;
        var otherArgs = Array(start2 + 1);
        while (++index3 < start2) {
          otherArgs[index3] = args[index3];
        }
        otherArgs[start2] = transform(array3);
        return apply(func, this, otherArgs);
      };
    }
    module.exports = overRest;
  }
});

// node_modules/lodash/constant.js
var require_constant = __commonJS({
  "node_modules/lodash/constant.js"(exports, module) {
    function constant2(value) {
      return function() {
        return value;
      };
    }
    module.exports = constant2;
  }
});

// node_modules/lodash/_baseSetToString.js
var require_baseSetToString = __commonJS({
  "node_modules/lodash/_baseSetToString.js"(exports, module) {
    var constant2 = require_constant();
    var defineProperty = require_defineProperty();
    var identity4 = require_identity();
    var baseSetToString = !defineProperty ? identity4 : function(func, string) {
      return defineProperty(func, "toString", {
        "configurable": true,
        "enumerable": false,
        "value": constant2(string),
        "writable": true
      });
    };
    module.exports = baseSetToString;
  }
});

// node_modules/lodash/_shortOut.js
var require_shortOut = __commonJS({
  "node_modules/lodash/_shortOut.js"(exports, module) {
    var HOT_COUNT = 800;
    var HOT_SPAN = 16;
    var nativeNow = Date.now;
    function shortOut(func) {
      var count3 = 0, lastCalled = 0;
      return function() {
        var stamp = nativeNow(), remaining = HOT_SPAN - (stamp - lastCalled);
        lastCalled = stamp;
        if (remaining > 0) {
          if (++count3 >= HOT_COUNT) {
            return arguments[0];
          }
        } else {
          count3 = 0;
        }
        return func.apply(void 0, arguments);
      };
    }
    module.exports = shortOut;
  }
});

// node_modules/lodash/_setToString.js
var require_setToString = __commonJS({
  "node_modules/lodash/_setToString.js"(exports, module) {
    var baseSetToString = require_baseSetToString();
    var shortOut = require_shortOut();
    var setToString = shortOut(baseSetToString);
    module.exports = setToString;
  }
});

// node_modules/lodash/_baseRest.js
var require_baseRest = __commonJS({
  "node_modules/lodash/_baseRest.js"(exports, module) {
    var identity4 = require_identity();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function baseRest(func, start2) {
      return setToString(overRest(func, start2, identity4), func + "");
    }
    module.exports = baseRest;
  }
});

// node_modules/lodash/_isIterateeCall.js
var require_isIterateeCall = __commonJS({
  "node_modules/lodash/_isIterateeCall.js"(exports, module) {
    var eq = require_eq();
    var isArrayLike = require_isArrayLike();
    var isIndex = require_isIndex();
    var isObject = require_isObject();
    function isIterateeCall(value, index3, object) {
      if (!isObject(object)) {
        return false;
      }
      var type = typeof index3;
      if (type == "number" ? isArrayLike(object) && isIndex(index3, object.length) : type == "string" && index3 in object) {
        return eq(object[index3], value);
      }
      return false;
    }
    module.exports = isIterateeCall;
  }
});

// node_modules/lodash/_createAssigner.js
var require_createAssigner = __commonJS({
  "node_modules/lodash/_createAssigner.js"(exports, module) {
    var baseRest = require_baseRest();
    var isIterateeCall = require_isIterateeCall();
    function createAssigner(assigner) {
      return baseRest(function(object, sources) {
        var index3 = -1, length = sources.length, customizer = length > 1 ? sources[length - 1] : void 0, guard = length > 2 ? sources[2] : void 0;
        customizer = assigner.length > 3 && typeof customizer == "function" ? (length--, customizer) : void 0;
        if (guard && isIterateeCall(sources[0], sources[1], guard)) {
          customizer = length < 3 ? void 0 : customizer;
          length = 1;
        }
        object = Object(object);
        while (++index3 < length) {
          var source = sources[index3];
          if (source) {
            assigner(object, source, index3, customizer);
          }
        }
        return object;
      });
    }
    module.exports = createAssigner;
  }
});

// node_modules/lodash/merge.js
var require_merge = __commonJS({
  "node_modules/lodash/merge.js"(exports, module) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var merge3 = createAssigner(function(object, source, srcIndex) {
      baseMerge(object, source, srcIndex);
    });
    module.exports = merge3;
  }
});

// node_modules/lodash/_baseSet.js
var require_baseSet = __commonJS({
  "node_modules/lodash/_baseSet.js"(exports, module) {
    var assignValue = require_assignValue();
    var castPath = require_castPath();
    var isIndex = require_isIndex();
    var isObject = require_isObject();
    var toKey = require_toKey();
    function baseSet(object, path2, value, customizer) {
      if (!isObject(object)) {
        return object;
      }
      path2 = castPath(path2, object);
      var index3 = -1, length = path2.length, lastIndex = length - 1, nested = object;
      while (nested != null && ++index3 < length) {
        var key = toKey(path2[index3]), newValue = value;
        if (key === "__proto__" || key === "constructor" || key === "prototype") {
          return object;
        }
        if (index3 != lastIndex) {
          var objValue = nested[key];
          newValue = customizer ? customizer(objValue, key, nested) : void 0;
          if (newValue === void 0) {
            newValue = isObject(objValue) ? objValue : isIndex(path2[index3 + 1]) ? [] : {};
          }
        }
        assignValue(nested, key, newValue);
        nested = nested[key];
      }
      return object;
    }
    module.exports = baseSet;
  }
});

// node_modules/lodash/set.js
var require_set = __commonJS({
  "node_modules/lodash/set.js"(exports, module) {
    var baseSet = require_baseSet();
    function set2(object, path2, value) {
      return object == null ? object : baseSet(object, path2, value);
    }
    module.exports = set2;
  }
});

// node_modules/lodash/isString.js
var require_isString = __commonJS({
  "node_modules/lodash/isString.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isArray = require_isArray();
    var isObjectLike = require_isObjectLike();
    var stringTag = "[object String]";
    function isString(value) {
      return typeof value == "string" || !isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag;
    }
    module.exports = isString;
  }
});

// node_modules/lodash/last.js
var require_last = __commonJS({
  "node_modules/lodash/last.js"(exports, module) {
    function last(array3) {
      var length = array3 == null ? 0 : array3.length;
      return length ? array3[length - 1] : void 0;
    }
    module.exports = last;
  }
});

// node_modules/lodash/_baseFindIndex.js
var require_baseFindIndex = __commonJS({
  "node_modules/lodash/_baseFindIndex.js"(exports, module) {
    function baseFindIndex(array3, predicate, fromIndex, fromRight) {
      var length = array3.length, index3 = fromIndex + (fromRight ? 1 : -1);
      while (fromRight ? index3-- : ++index3 < length) {
        if (predicate(array3[index3], index3, array3)) {
          return index3;
        }
      }
      return -1;
    }
    module.exports = baseFindIndex;
  }
});

// node_modules/lodash/_baseIsNaN.js
var require_baseIsNaN = __commonJS({
  "node_modules/lodash/_baseIsNaN.js"(exports, module) {
    function baseIsNaN(value) {
      return value !== value;
    }
    module.exports = baseIsNaN;
  }
});

// node_modules/lodash/_strictIndexOf.js
var require_strictIndexOf = __commonJS({
  "node_modules/lodash/_strictIndexOf.js"(exports, module) {
    function strictIndexOf(array3, value, fromIndex) {
      var index3 = fromIndex - 1, length = array3.length;
      while (++index3 < length) {
        if (array3[index3] === value) {
          return index3;
        }
      }
      return -1;
    }
    module.exports = strictIndexOf;
  }
});

// node_modules/lodash/_baseIndexOf.js
var require_baseIndexOf = __commonJS({
  "node_modules/lodash/_baseIndexOf.js"(exports, module) {
    var baseFindIndex = require_baseFindIndex();
    var baseIsNaN = require_baseIsNaN();
    var strictIndexOf = require_strictIndexOf();
    function baseIndexOf(array3, value, fromIndex) {
      return value === value ? strictIndexOf(array3, value, fromIndex) : baseFindIndex(array3, baseIsNaN, fromIndex);
    }
    module.exports = baseIndexOf;
  }
});

// node_modules/lodash/_arrayIncludes.js
var require_arrayIncludes = __commonJS({
  "node_modules/lodash/_arrayIncludes.js"(exports, module) {
    var baseIndexOf = require_baseIndexOf();
    function arrayIncludes(array3, value) {
      var length = array3 == null ? 0 : array3.length;
      return !!length && baseIndexOf(array3, value, 0) > -1;
    }
    module.exports = arrayIncludes;
  }
});

// node_modules/lodash/_arrayIncludesWith.js
var require_arrayIncludesWith = __commonJS({
  "node_modules/lodash/_arrayIncludesWith.js"(exports, module) {
    function arrayIncludesWith(array3, value, comparator) {
      var index3 = -1, length = array3 == null ? 0 : array3.length;
      while (++index3 < length) {
        if (comparator(value, array3[index3])) {
          return true;
        }
      }
      return false;
    }
    module.exports = arrayIncludesWith;
  }
});

// node_modules/lodash/_baseDifference.js
var require_baseDifference = __commonJS({
  "node_modules/lodash/_baseDifference.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var arrayMap = require_arrayMap();
    var baseUnary = require_baseUnary();
    var cacheHas = require_cacheHas();
    var LARGE_ARRAY_SIZE = 200;
    function baseDifference(array3, values, iteratee, comparator) {
      var index3 = -1, includes = arrayIncludes, isCommon = true, length = array3.length, result = [], valuesLength = values.length;
      if (!length) {
        return result;
      }
      if (iteratee) {
        values = arrayMap(values, baseUnary(iteratee));
      }
      if (comparator) {
        includes = arrayIncludesWith;
        isCommon = false;
      } else if (values.length >= LARGE_ARRAY_SIZE) {
        includes = cacheHas;
        isCommon = false;
        values = new SetCache(values);
      }
      outer:
        while (++index3 < length) {
          var value = array3[index3], computed = iteratee == null ? value : iteratee(value);
          value = comparator || value !== 0 ? value : 0;
          if (isCommon && computed === computed) {
            var valuesIndex = valuesLength;
            while (valuesIndex--) {
              if (values[valuesIndex] === computed) {
                continue outer;
              }
            }
            result.push(value);
          } else if (!includes(values, computed, comparator)) {
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseDifference;
  }
});

// node_modules/lodash/without.js
var require_without = __commonJS({
  "node_modules/lodash/without.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var without = baseRest(function(array3, values) {
      return isArrayLikeObject(array3) ? baseDifference(array3, values) : [];
    });
    module.exports = without;
  }
});

// node_modules/lodash/_basePickBy.js
var require_basePickBy = __commonJS({
  "node_modules/lodash/_basePickBy.js"(exports, module) {
    var baseGet = require_baseGet();
    var baseSet = require_baseSet();
    var castPath = require_castPath();
    function basePickBy(object, paths, predicate) {
      var index3 = -1, length = paths.length, result = {};
      while (++index3 < length) {
        var path2 = paths[index3], value = baseGet(object, path2);
        if (predicate(value, path2)) {
          baseSet(result, castPath(path2, object), value);
        }
      }
      return result;
    }
    module.exports = basePickBy;
  }
});

// node_modules/lodash/_basePick.js
var require_basePick = __commonJS({
  "node_modules/lodash/_basePick.js"(exports, module) {
    var basePickBy = require_basePickBy();
    var hasIn = require_hasIn();
    function basePick(object, paths) {
      return basePickBy(object, paths, function(value, path2) {
        return hasIn(object, path2);
      });
    }
    module.exports = basePick;
  }
});

// node_modules/lodash/_isFlattenable.js
var require_isFlattenable = __commonJS({
  "node_modules/lodash/_isFlattenable.js"(exports, module) {
    var Symbol3 = require_Symbol();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var spreadableSymbol = Symbol3 ? Symbol3.isConcatSpreadable : void 0;
    function isFlattenable(value) {
      return isArray(value) || isArguments(value) || !!(spreadableSymbol && value && value[spreadableSymbol]);
    }
    module.exports = isFlattenable;
  }
});

// node_modules/lodash/_baseFlatten.js
var require_baseFlatten = __commonJS({
  "node_modules/lodash/_baseFlatten.js"(exports, module) {
    var arrayPush = require_arrayPush();
    var isFlattenable = require_isFlattenable();
    function baseFlatten(array3, depth, predicate, isStrict, result) {
      var index3 = -1, length = array3.length;
      predicate || (predicate = isFlattenable);
      result || (result = []);
      while (++index3 < length) {
        var value = array3[index3];
        if (depth > 0 && predicate(value)) {
          if (depth > 1) {
            baseFlatten(value, depth - 1, predicate, isStrict, result);
          } else {
            arrayPush(result, value);
          }
        } else if (!isStrict) {
          result[result.length] = value;
        }
      }
      return result;
    }
    module.exports = baseFlatten;
  }
});

// node_modules/lodash/flatten.js
var require_flatten = __commonJS({
  "node_modules/lodash/flatten.js"(exports, module) {
    var baseFlatten = require_baseFlatten();
    function flatten(array3) {
      var length = array3 == null ? 0 : array3.length;
      return length ? baseFlatten(array3, 1) : [];
    }
    module.exports = flatten;
  }
});

// node_modules/lodash/_flatRest.js
var require_flatRest = __commonJS({
  "node_modules/lodash/_flatRest.js"(exports, module) {
    var flatten = require_flatten();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function flatRest(func) {
      return setToString(overRest(func, void 0, flatten), func + "");
    }
    module.exports = flatRest;
  }
});

// node_modules/lodash/pick.js
var require_pick = __commonJS({
  "node_modules/lodash/pick.js"(exports, module) {
    var basePick = require_basePick();
    var flatRest = require_flatRest();
    var pick = flatRest(function(object, paths) {
      return object == null ? {} : basePick(object, paths);
    });
    module.exports = pick;
  }
});

// node_modules/lodash/isEqual.js
var require_isEqual = __commonJS({
  "node_modules/lodash/isEqual.js"(exports, module) {
    var baseIsEqual = require_baseIsEqual();
    function isEqual2(value, other) {
      return baseIsEqual(value, other);
    }
    module.exports = isEqual2;
  }
});

// node_modules/lodash/noop.js
var require_noop = __commonJS({
  "node_modules/lodash/noop.js"(exports, module) {
    function noop2() {
    }
    module.exports = noop2;
  }
});

// node_modules/lodash/_createSet.js
var require_createSet = __commonJS({
  "node_modules/lodash/_createSet.js"(exports, module) {
    var Set2 = require_Set();
    var noop2 = require_noop();
    var setToArray = require_setToArray();
    var INFINITY = 1 / 0;
    var createSet = !(Set2 && 1 / setToArray(new Set2([, -0]))[1] == INFINITY) ? noop2 : function(values) {
      return new Set2(values);
    };
    module.exports = createSet;
  }
});

// node_modules/lodash/_baseUniq.js
var require_baseUniq = __commonJS({
  "node_modules/lodash/_baseUniq.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var cacheHas = require_cacheHas();
    var createSet = require_createSet();
    var setToArray = require_setToArray();
    var LARGE_ARRAY_SIZE = 200;
    function baseUniq(array3, iteratee, comparator) {
      var index3 = -1, includes = arrayIncludes, length = array3.length, isCommon = true, result = [], seen = result;
      if (comparator) {
        isCommon = false;
        includes = arrayIncludesWith;
      } else if (length >= LARGE_ARRAY_SIZE) {
        var set2 = iteratee ? null : createSet(array3);
        if (set2) {
          return setToArray(set2);
        }
        isCommon = false;
        includes = cacheHas;
        seen = new SetCache();
      } else {
        seen = iteratee ? [] : result;
      }
      outer:
        while (++index3 < length) {
          var value = array3[index3], computed = iteratee ? iteratee(value) : value;
          value = comparator || value !== 0 ? value : 0;
          if (isCommon && computed === computed) {
            var seenIndex = seen.length;
            while (seenIndex--) {
              if (seen[seenIndex] === computed) {
                continue outer;
              }
            }
            if (iteratee) {
              seen.push(computed);
            }
            result.push(value);
          } else if (!includes(seen, computed, comparator)) {
            if (seen !== result) {
              seen.push(computed);
            }
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseUniq;
  }
});

// node_modules/lodash/uniq.js
var require_uniq = __commonJS({
  "node_modules/lodash/uniq.js"(exports, module) {
    var baseUniq = require_baseUniq();
    function uniq(array3) {
      return array3 && array3.length ? baseUniq(array3) : [];
    }
    module.exports = uniq;
  }
});

// node_modules/lodash/uniqBy.js
var require_uniqBy = __commonJS({
  "node_modules/lodash/uniqBy.js"(exports, module) {
    var baseIteratee = require_baseIteratee();
    var baseUniq = require_baseUniq();
    function uniqBy(array3, iteratee) {
      return array3 && array3.length ? baseUniq(array3, baseIteratee(iteratee, 2)) : [];
    }
    module.exports = uniqBy;
  }
});

// node_modules/lodash/_createBaseEach.js
var require_createBaseEach = __commonJS({
  "node_modules/lodash/_createBaseEach.js"(exports, module) {
    var isArrayLike = require_isArrayLike();
    function createBaseEach(eachFunc, fromRight) {
      return function(collection, iteratee) {
        if (collection == null) {
          return collection;
        }
        if (!isArrayLike(collection)) {
          return eachFunc(collection, iteratee);
        }
        var length = collection.length, index3 = fromRight ? length : -1, iterable = Object(collection);
        while (fromRight ? index3-- : ++index3 < length) {
          if (iteratee(iterable[index3], index3, iterable) === false) {
            break;
          }
        }
        return collection;
      };
    }
    module.exports = createBaseEach;
  }
});

// node_modules/lodash/_baseEach.js
var require_baseEach = __commonJS({
  "node_modules/lodash/_baseEach.js"(exports, module) {
    var baseForOwn = require_baseForOwn();
    var createBaseEach = require_createBaseEach();
    var baseEach = createBaseEach(baseForOwn);
    module.exports = baseEach;
  }
});

// node_modules/lodash/_baseMap.js
var require_baseMap = __commonJS({
  "node_modules/lodash/_baseMap.js"(exports, module) {
    var baseEach = require_baseEach();
    var isArrayLike = require_isArrayLike();
    function baseMap(collection, iteratee) {
      var index3 = -1, result = isArrayLike(collection) ? Array(collection.length) : [];
      baseEach(collection, function(value, key, collection2) {
        result[++index3] = iteratee(value, key, collection2);
      });
      return result;
    }
    module.exports = baseMap;
  }
});

// node_modules/lodash/_baseSortBy.js
var require_baseSortBy = __commonJS({
  "node_modules/lodash/_baseSortBy.js"(exports, module) {
    function baseSortBy(array3, comparer) {
      var length = array3.length;
      array3.sort(comparer);
      while (length--) {
        array3[length] = array3[length].value;
      }
      return array3;
    }
    module.exports = baseSortBy;
  }
});

// node_modules/lodash/_compareAscending.js
var require_compareAscending = __commonJS({
  "node_modules/lodash/_compareAscending.js"(exports, module) {
    var isSymbol = require_isSymbol();
    function compareAscending(value, other) {
      if (value !== other) {
        var valIsDefined = value !== void 0, valIsNull = value === null, valIsReflexive = value === value, valIsSymbol = isSymbol(value);
        var othIsDefined = other !== void 0, othIsNull = other === null, othIsReflexive = other === other, othIsSymbol = isSymbol(other);
        if (!othIsNull && !othIsSymbol && !valIsSymbol && value > other || valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol || valIsNull && othIsDefined && othIsReflexive || !valIsDefined && othIsReflexive || !valIsReflexive) {
          return 1;
        }
        if (!valIsNull && !valIsSymbol && !othIsSymbol && value < other || othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol || othIsNull && valIsDefined && valIsReflexive || !othIsDefined && valIsReflexive || !othIsReflexive) {
          return -1;
        }
      }
      return 0;
    }
    module.exports = compareAscending;
  }
});

// node_modules/lodash/_compareMultiple.js
var require_compareMultiple = __commonJS({
  "node_modules/lodash/_compareMultiple.js"(exports, module) {
    var compareAscending = require_compareAscending();
    function compareMultiple(object, other, orders) {
      var index3 = -1, objCriteria = object.criteria, othCriteria = other.criteria, length = objCriteria.length, ordersLength = orders.length;
      while (++index3 < length) {
        var result = compareAscending(objCriteria[index3], othCriteria[index3]);
        if (result) {
          if (index3 >= ordersLength) {
            return result;
          }
          var order = orders[index3];
          return result * (order == "desc" ? -1 : 1);
        }
      }
      return object.index - other.index;
    }
    module.exports = compareMultiple;
  }
});

// node_modules/lodash/_baseOrderBy.js
var require_baseOrderBy = __commonJS({
  "node_modules/lodash/_baseOrderBy.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseGet = require_baseGet();
    var baseIteratee = require_baseIteratee();
    var baseMap = require_baseMap();
    var baseSortBy = require_baseSortBy();
    var baseUnary = require_baseUnary();
    var compareMultiple = require_compareMultiple();
    var identity4 = require_identity();
    var isArray = require_isArray();
    function baseOrderBy(collection, iteratees, orders) {
      if (iteratees.length) {
        iteratees = arrayMap(iteratees, function(iteratee) {
          if (isArray(iteratee)) {
            return function(value) {
              return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);
            };
          }
          return iteratee;
        });
      } else {
        iteratees = [identity4];
      }
      var index3 = -1;
      iteratees = arrayMap(iteratees, baseUnary(baseIteratee));
      var result = baseMap(collection, function(value, key, collection2) {
        var criteria = arrayMap(iteratees, function(iteratee) {
          return iteratee(value);
        });
        return { "criteria": criteria, "index": ++index3, "value": value };
      });
      return baseSortBy(result, function(object, other) {
        return compareMultiple(object, other, orders);
      });
    }
    module.exports = baseOrderBy;
  }
});

// node_modules/lodash/sortBy.js
var require_sortBy = __commonJS({
  "node_modules/lodash/sortBy.js"(exports, module) {
    var baseFlatten = require_baseFlatten();
    var baseOrderBy = require_baseOrderBy();
    var baseRest = require_baseRest();
    var isIterateeCall = require_isIterateeCall();
    var sortBy = baseRest(function(collection, iteratees) {
      if (collection == null) {
        return [];
      }
      var length = iteratees.length;
      if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {
        iteratees = [];
      } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {
        iteratees = [iteratees[0]];
      }
      return baseOrderBy(collection, baseFlatten(iteratees, 1), []);
    });
    module.exports = sortBy;
  }
});

// node_modules/lodash/_baseIsDate.js
var require_baseIsDate = __commonJS({
  "node_modules/lodash/_baseIsDate.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var dateTag = "[object Date]";
    function baseIsDate(value) {
      return isObjectLike(value) && baseGetTag(value) == dateTag;
    }
    module.exports = baseIsDate;
  }
});

// node_modules/lodash/isDate.js
var require_isDate = __commonJS({
  "node_modules/lodash/isDate.js"(exports, module) {
    var baseIsDate = require_baseIsDate();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsDate = nodeUtil && nodeUtil.isDate;
    var isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;
    module.exports = isDate;
  }
});

// node_modules/lodash/uniqueId.js
var require_uniqueId = __commonJS({
  "node_modules/lodash/uniqueId.js"(exports, module) {
    var toString = require_toString();
    var idCounter = 0;
    function uniqueId(prefix2) {
      var id = ++idCounter;
      return toString(prefix2) + id;
    }
    module.exports = uniqueId;
  }
});

// node_modules/@nivo/line/dist/nivo-line.es.js
var import_react21 = __toESM(require_react());

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_react16 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@nivo/tooltip/dist/nivo-tooltip.es.js
var import_react15 = __toESM(require_react());

// node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs
var updateQueue = makeQueue();
var raf = (fn3) => schedule(fn3, updateQueue);
var writeQueue = makeQueue();
raf.write = (fn3) => schedule(fn3, writeQueue);
var onStartQueue = makeQueue();
raf.onStart = (fn3) => schedule(fn3, onStartQueue);
var onFrameQueue = makeQueue();
raf.onFrame = (fn3) => schedule(fn3, onFrameQueue);
var onFinishQueue = makeQueue();
raf.onFinish = (fn3) => schedule(fn3, onFinishQueue);
var timeouts = [];
raf.setTimeout = (handler, ms) => {
  const time2 = raf.now() + ms;
  const cancel = () => {
    const i6 = timeouts.findIndex((t8) => t8.cancel == cancel);
    if (~i6)
      timeouts.splice(i6, 1);
    pendingCount -= ~i6 ? 1 : 0;
  };
  const timeout = { time: time2, handler, cancel };
  timeouts.splice(findTimeout(time2), 0, timeout);
  pendingCount += 1;
  start();
  return timeout;
};
var findTimeout = (time2) => ~(~timeouts.findIndex((t8) => t8.time > time2) || ~timeouts.length);
raf.cancel = (fn3) => {
  onStartQueue.delete(fn3);
  onFrameQueue.delete(fn3);
  onFinishQueue.delete(fn3);
  updateQueue.delete(fn3);
  writeQueue.delete(fn3);
};
raf.sync = (fn3) => {
  sync = true;
  raf.batchedUpdates(fn3);
  sync = false;
};
raf.throttle = (fn3) => {
  let lastArgs;
  function queuedFn() {
    try {
      fn3(...lastArgs);
    } finally {
      lastArgs = null;
    }
  }
  function throttled(...args) {
    lastArgs = args;
    raf.onStart(queuedFn);
  }
  throttled.handler = fn3;
  throttled.cancel = () => {
    onStartQueue.delete(queuedFn);
    lastArgs = null;
  };
  return throttled;
};
var nativeRaf = typeof window != "undefined" ? window.requestAnimationFrame : (
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  () => {
  }
);
raf.use = (impl) => nativeRaf = impl;
raf.now = typeof performance != "undefined" ? () => performance.now() : Date.now;
raf.batchedUpdates = (fn3) => fn3();
raf.catch = console.error;
raf.frameLoop = "always";
raf.advance = () => {
  if (raf.frameLoop !== "demand") {
    console.warn(
      "Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"
    );
  } else {
    update();
  }
};
var ts = -1;
var pendingCount = 0;
var sync = false;
function schedule(fn3, queue) {
  if (sync) {
    queue.delete(fn3);
    fn3(0);
  } else {
    queue.add(fn3);
    start();
  }
}
function start() {
  if (ts < 0) {
    ts = 0;
    if (raf.frameLoop !== "demand") {
      nativeRaf(loop);
    }
  }
}
function stop() {
  ts = -1;
}
function loop() {
  if (~ts) {
    nativeRaf(loop);
    raf.batchedUpdates(update);
  }
}
function update() {
  const prevTs = ts;
  ts = raf.now();
  const count3 = findTimeout(ts);
  if (count3) {
    eachSafely(timeouts.splice(0, count3), (t8) => t8.handler());
    pendingCount -= count3;
  }
  if (!pendingCount) {
    stop();
    return;
  }
  onStartQueue.flush();
  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);
  onFrameQueue.flush();
  writeQueue.flush();
  onFinishQueue.flush();
}
function makeQueue() {
  let next = /* @__PURE__ */ new Set();
  let current = next;
  return {
    add(fn3) {
      pendingCount += current == next && !next.has(fn3) ? 1 : 0;
      next.add(fn3);
    },
    delete(fn3) {
      pendingCount -= current == next && next.has(fn3) ? 1 : 0;
      return next.delete(fn3);
    },
    flush(arg) {
      if (current.size) {
        next = /* @__PURE__ */ new Set();
        pendingCount -= current.size;
        eachSafely(current, (fn3) => fn3(arg) && next.add(fn3));
        pendingCount += next.size;
        current = next;
      }
    }
  };
}
function eachSafely(values, each2) {
  values.forEach((value) => {
    try {
      each2(value);
    } catch (e11) {
      raf.catch(e11);
    }
  });
}

// node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_react8 = __toESM(require_react(), 1);
var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var globals_exports = {};
__export(globals_exports, {
  assign: () => assign,
  colors: () => colors,
  createStringInterpolator: () => createStringInterpolator,
  skipAnimation: () => skipAnimation,
  to: () => to,
  willAdvance: () => willAdvance
});
function noop() {
}
var defineHidden = (obj, key, value) => Object.defineProperty(obj, key, { value, writable: true, configurable: true });
var is = {
  arr: Array.isArray,
  obj: (a5) => !!a5 && a5.constructor.name === "Object",
  fun: (a5) => typeof a5 === "function",
  str: (a5) => typeof a5 === "string",
  num: (a5) => typeof a5 === "number",
  und: (a5) => a5 === void 0
};
function isEqual(a5, b5) {
  if (is.arr(a5)) {
    if (!is.arr(b5) || a5.length !== b5.length)
      return false;
    for (let i6 = 0; i6 < a5.length; i6++) {
      if (a5[i6] !== b5[i6])
        return false;
    }
    return true;
  }
  return a5 === b5;
}
var each = (obj, fn3) => obj.forEach(fn3);
function eachProp(obj, fn3, ctx2) {
  if (is.arr(obj)) {
    for (let i6 = 0; i6 < obj.length; i6++) {
      fn3.call(ctx2, obj[i6], `${i6}`);
    }
    return;
  }
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      fn3.call(ctx2, obj[key], key);
    }
  }
}
var toArray = (a5) => is.und(a5) ? [] : is.arr(a5) ? a5 : [a5];
function flush(queue, iterator) {
  if (queue.size) {
    const items = Array.from(queue);
    queue.clear();
    each(items, iterator);
  }
}
var flushCalls = (queue, ...args) => flush(queue, (fn3) => fn3(...args));
var isSSR = () => typeof window === "undefined" || !window.navigator || /ServerSideRendering|^Deno\//.test(window.navigator.userAgent);
var createStringInterpolator;
var to;
var colors = null;
var skipAnimation = false;
var willAdvance = noop;
var assign = (globals) => {
  if (globals.to)
    to = globals.to;
  if (globals.now)
    raf.now = globals.now;
  if (globals.colors !== void 0)
    colors = globals.colors;
  if (globals.skipAnimation != null)
    skipAnimation = globals.skipAnimation;
  if (globals.createStringInterpolator)
    createStringInterpolator = globals.createStringInterpolator;
  if (globals.requestAnimationFrame)
    raf.use(globals.requestAnimationFrame);
  if (globals.batchedUpdates)
    raf.batchedUpdates = globals.batchedUpdates;
  if (globals.willAdvance)
    willAdvance = globals.willAdvance;
  if (globals.frameLoop)
    raf.frameLoop = globals.frameLoop;
};
var startQueue = /* @__PURE__ */ new Set();
var currentFrame = [];
var prevFrame = [];
var priority = 0;
var frameLoop = {
  get idle() {
    return !startQueue.size && !currentFrame.length;
  },
  /** Advance the given animation on every frame until idle. */
  start(animation) {
    if (priority > animation.priority) {
      startQueue.add(animation);
      raf.onStart(flushStartQueue);
    } else {
      startSafely(animation);
      raf(advance);
    }
  },
  /** Advance all animations by the given time. */
  advance,
  /** Call this when an animation's priority changes. */
  sort(animation) {
    if (priority) {
      raf.onFrame(() => frameLoop.sort(animation));
    } else {
      const prevIndex = currentFrame.indexOf(animation);
      if (~prevIndex) {
        currentFrame.splice(prevIndex, 1);
        startUnsafely(animation);
      }
    }
  },
  /**
   * Clear all animations. For testing purposes.
   *
   * ☠️ Never call this from within the frameloop.
   */
  clear() {
    currentFrame = [];
    startQueue.clear();
  }
};
function flushStartQueue() {
  startQueue.forEach(startSafely);
  startQueue.clear();
  raf(advance);
}
function startSafely(animation) {
  if (!currentFrame.includes(animation))
    startUnsafely(animation);
}
function startUnsafely(animation) {
  currentFrame.splice(
    findIndex(currentFrame, (other) => other.priority > animation.priority),
    0,
    animation
  );
}
function advance(dt2) {
  const nextFrame = prevFrame;
  for (let i6 = 0; i6 < currentFrame.length; i6++) {
    const animation = currentFrame[i6];
    priority = animation.priority;
    if (!animation.idle) {
      willAdvance(animation);
      animation.advance(dt2);
      if (!animation.idle) {
        nextFrame.push(animation);
      }
    }
  }
  priority = 0;
  prevFrame = currentFrame;
  prevFrame.length = 0;
  currentFrame = nextFrame;
  return currentFrame.length > 0;
}
function findIndex(arr, test) {
  const index3 = arr.findIndex(test);
  return index3 < 0 ? arr.length : index3;
}
var clamp = (min4, max4, v6) => Math.min(Math.max(v6, min4), max4);
var colors2 = {
  transparent: 0,
  aliceblue: 4042850303,
  antiquewhite: 4209760255,
  aqua: 16777215,
  aquamarine: 2147472639,
  azure: 4043309055,
  beige: 4126530815,
  bisque: 4293182719,
  black: 255,
  blanchedalmond: 4293643775,
  blue: 65535,
  blueviolet: 2318131967,
  brown: 2771004159,
  burlywood: 3736635391,
  burntsienna: 3934150143,
  cadetblue: 1604231423,
  chartreuse: 2147418367,
  chocolate: 3530104575,
  coral: 4286533887,
  cornflowerblue: 1687547391,
  cornsilk: 4294499583,
  crimson: 3692313855,
  cyan: 16777215,
  darkblue: 35839,
  darkcyan: 9145343,
  darkgoldenrod: 3095792639,
  darkgray: 2846468607,
  darkgreen: 6553855,
  darkgrey: 2846468607,
  darkkhaki: 3182914559,
  darkmagenta: 2332068863,
  darkolivegreen: 1433087999,
  darkorange: 4287365375,
  darkorchid: 2570243327,
  darkred: 2332033279,
  darksalmon: 3918953215,
  darkseagreen: 2411499519,
  darkslateblue: 1211993087,
  darkslategray: 793726975,
  darkslategrey: 793726975,
  darkturquoise: 13554175,
  darkviolet: 2483082239,
  deeppink: 4279538687,
  deepskyblue: 12582911,
  dimgray: 1768516095,
  dimgrey: 1768516095,
  dodgerblue: 512819199,
  firebrick: 2988581631,
  floralwhite: 4294635775,
  forestgreen: 579543807,
  fuchsia: 4278255615,
  gainsboro: 3705462015,
  ghostwhite: 4177068031,
  gold: 4292280575,
  goldenrod: 3668254975,
  gray: 2155905279,
  green: 8388863,
  greenyellow: 2919182335,
  grey: 2155905279,
  honeydew: 4043305215,
  hotpink: 4285117695,
  indianred: 3445382399,
  indigo: 1258324735,
  ivory: 4294963455,
  khaki: 4041641215,
  lavender: 3873897215,
  lavenderblush: 4293981695,
  lawngreen: 2096890111,
  lemonchiffon: 4294626815,
  lightblue: 2916673279,
  lightcoral: 4034953471,
  lightcyan: 3774873599,
  lightgoldenrodyellow: 4210742015,
  lightgray: 3553874943,
  lightgreen: 2431553791,
  lightgrey: 3553874943,
  lightpink: 4290167295,
  lightsalmon: 4288707327,
  lightseagreen: 548580095,
  lightskyblue: 2278488831,
  lightslategray: 2005441023,
  lightslategrey: 2005441023,
  lightsteelblue: 2965692159,
  lightyellow: 4294959359,
  lime: 16711935,
  limegreen: 852308735,
  linen: 4210091775,
  magenta: 4278255615,
  maroon: 2147483903,
  mediumaquamarine: 1724754687,
  mediumblue: 52735,
  mediumorchid: 3126187007,
  mediumpurple: 2473647103,
  mediumseagreen: 1018393087,
  mediumslateblue: 2070474495,
  mediumspringgreen: 16423679,
  mediumturquoise: 1221709055,
  mediumvioletred: 3340076543,
  midnightblue: 421097727,
  mintcream: 4127193855,
  mistyrose: 4293190143,
  moccasin: 4293178879,
  navajowhite: 4292783615,
  navy: 33023,
  oldlace: 4260751103,
  olive: 2155872511,
  olivedrab: 1804477439,
  orange: 4289003775,
  orangered: 4282712319,
  orchid: 3664828159,
  palegoldenrod: 4008225535,
  palegreen: 2566625535,
  paleturquoise: 2951671551,
  palevioletred: 3681588223,
  papayawhip: 4293907967,
  peachpuff: 4292524543,
  peru: 3448061951,
  pink: 4290825215,
  plum: 3718307327,
  powderblue: 2967529215,
  purple: 2147516671,
  rebeccapurple: 1714657791,
  red: 4278190335,
  rosybrown: 3163525119,
  royalblue: 1097458175,
  saddlebrown: 2336560127,
  salmon: 4202722047,
  sandybrown: 4104413439,
  seagreen: 780883967,
  seashell: 4294307583,
  sienna: 2689740287,
  silver: 3233857791,
  skyblue: 2278484991,
  slateblue: 1784335871,
  slategray: 1887473919,
  slategrey: 1887473919,
  snow: 4294638335,
  springgreen: 16744447,
  steelblue: 1182971135,
  tan: 3535047935,
  teal: 8421631,
  thistle: 3636451583,
  tomato: 4284696575,
  turquoise: 1088475391,
  violet: 4001558271,
  wheat: 4125012991,
  white: 4294967295,
  whitesmoke: 4126537215,
  yellow: 4294902015,
  yellowgreen: 2597139199
};
var NUMBER = "[-+]?\\d*\\.?\\d+";
var PERCENTAGE = NUMBER + "%";
function call(...parts) {
  return "\\(\\s*(" + parts.join(")\\s*,\\s*(") + ")\\s*\\)";
}
var rgb2 = new RegExp("rgb" + call(NUMBER, NUMBER, NUMBER));
var rgba = new RegExp("rgba" + call(NUMBER, NUMBER, NUMBER, NUMBER));
var hsl = new RegExp("hsl" + call(NUMBER, PERCENTAGE, PERCENTAGE));
var hsla = new RegExp(
  "hsla" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)
);
var hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex6 = /^#([0-9a-fA-F]{6})$/;
var hex8 = /^#([0-9a-fA-F]{8})$/;
function normalizeColor(color) {
  let match;
  if (typeof color === "number") {
    return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;
  }
  if (match = hex6.exec(color))
    return parseInt(match[1] + "ff", 16) >>> 0;
  if (colors && colors[color] !== void 0) {
    return colors[color];
  }
  if (match = rgb2.exec(color)) {
    return (parse255(match[1]) << 24 | // r
    parse255(match[2]) << 16 | // g
    parse255(match[3]) << 8 | // b
    255) >>> // a
    0;
  }
  if (match = rgba.exec(color)) {
    return (parse255(match[1]) << 24 | // r
    parse255(match[2]) << 16 | // g
    parse255(match[3]) << 8 | // b
    parse1(match[4])) >>> // a
    0;
  }
  if (match = hex3.exec(color)) {
    return parseInt(
      match[1] + match[1] + // r
      match[2] + match[2] + // g
      match[3] + match[3] + // b
      "ff",
      // a
      16
    ) >>> 0;
  }
  if (match = hex8.exec(color))
    return parseInt(match[1], 16) >>> 0;
  if (match = hex4.exec(color)) {
    return parseInt(
      match[1] + match[1] + // r
      match[2] + match[2] + // g
      match[3] + match[3] + // b
      match[4] + match[4],
      // a
      16
    ) >>> 0;
  }
  if (match = hsl.exec(color)) {
    return (hslToRgb(
      parse360(match[1]),
      // h
      parsePercentage(match[2]),
      // s
      parsePercentage(match[3])
      // l
    ) | 255) >>> // a
    0;
  }
  if (match = hsla.exec(color)) {
    return (hslToRgb(
      parse360(match[1]),
      // h
      parsePercentage(match[2]),
      // s
      parsePercentage(match[3])
      // l
    ) | parse1(match[4])) >>> // a
    0;
  }
  return null;
}
function hue2rgb(p4, q2, t8) {
  if (t8 < 0)
    t8 += 1;
  if (t8 > 1)
    t8 -= 1;
  if (t8 < 1 / 6)
    return p4 + (q2 - p4) * 6 * t8;
  if (t8 < 1 / 2)
    return q2;
  if (t8 < 2 / 3)
    return p4 + (q2 - p4) * (2 / 3 - t8) * 6;
  return p4;
}
function hslToRgb(h2, s5, l5) {
  const q2 = l5 < 0.5 ? l5 * (1 + s5) : l5 + s5 - l5 * s5;
  const p4 = 2 * l5 - q2;
  const r7 = hue2rgb(p4, q2, h2 + 1 / 3);
  const g4 = hue2rgb(p4, q2, h2);
  const b5 = hue2rgb(p4, q2, h2 - 1 / 3);
  return Math.round(r7 * 255) << 24 | Math.round(g4 * 255) << 16 | Math.round(b5 * 255) << 8;
}
function parse255(str) {
  const int = parseInt(str, 10);
  if (int < 0)
    return 0;
  if (int > 255)
    return 255;
  return int;
}
function parse360(str) {
  const int = parseFloat(str);
  return (int % 360 + 360) % 360 / 360;
}
function parse1(str) {
  const num = parseFloat(str);
  if (num < 0)
    return 0;
  if (num > 1)
    return 255;
  return Math.round(num * 255);
}
function parsePercentage(str) {
  const int = parseFloat(str);
  if (int < 0)
    return 0;
  if (int > 100)
    return 1;
  return int / 100;
}
function colorToRgba(input) {
  let int32Color = normalizeColor(input);
  if (int32Color === null)
    return input;
  int32Color = int32Color || 0;
  const r7 = (int32Color & 4278190080) >>> 24;
  const g4 = (int32Color & 16711680) >>> 16;
  const b5 = (int32Color & 65280) >>> 8;
  const a5 = (int32Color & 255) / 255;
  return `rgba(${r7}, ${g4}, ${b5}, ${a5})`;
}
var createInterpolator = (range2, output, extrapolate) => {
  if (is.fun(range2)) {
    return range2;
  }
  if (is.arr(range2)) {
    return createInterpolator({
      range: range2,
      output,
      extrapolate
    });
  }
  if (is.str(range2.output[0])) {
    return createStringInterpolator(range2);
  }
  const config2 = range2;
  const outputRange = config2.output;
  const inputRange = config2.range || [0, 1];
  const extrapolateLeft = config2.extrapolateLeft || config2.extrapolate || "extend";
  const extrapolateRight = config2.extrapolateRight || config2.extrapolate || "extend";
  const easing = config2.easing || ((t8) => t8);
  return (input) => {
    const range22 = findRange(input, inputRange);
    return interpolate(
      input,
      inputRange[range22],
      inputRange[range22 + 1],
      outputRange[range22],
      outputRange[range22 + 1],
      easing,
      extrapolateLeft,
      extrapolateRight,
      config2.map
    );
  };
};
function interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map7) {
  let result = map7 ? map7(input) : input;
  if (result < inputMin) {
    if (extrapolateLeft === "identity")
      return result;
    else if (extrapolateLeft === "clamp")
      result = inputMin;
  }
  if (result > inputMax) {
    if (extrapolateRight === "identity")
      return result;
    else if (extrapolateRight === "clamp")
      result = inputMax;
  }
  if (outputMin === outputMax)
    return outputMin;
  if (inputMin === inputMax)
    return input <= inputMin ? outputMin : outputMax;
  if (inputMin === -Infinity)
    result = -result;
  else if (inputMax === Infinity)
    result = result - inputMin;
  else
    result = (result - inputMin) / (inputMax - inputMin);
  result = easing(result);
  if (outputMin === -Infinity)
    result = -result;
  else if (outputMax === Infinity)
    result = result + outputMin;
  else
    result = result * (outputMax - outputMin) + outputMin;
  return result;
}
function findRange(input, inputRange) {
  for (var i6 = 1; i6 < inputRange.length - 1; ++i6)
    if (inputRange[i6] >= input)
      break;
  return i6 - 1;
}
var steps = (steps2, direction = "end") => (progress2) => {
  progress2 = direction === "end" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);
  const expanded = progress2 * steps2;
  const rounded = direction === "end" ? Math.floor(expanded) : Math.ceil(expanded);
  return clamp(0, 1, rounded / steps2);
};
var c1 = 1.70158;
var c2 = c1 * 1.525;
var c3 = c1 + 1;
var c4 = 2 * Math.PI / 3;
var c5 = 2 * Math.PI / 4.5;
var bounceOut = (x4) => {
  const n1 = 7.5625;
  const d1 = 2.75;
  if (x4 < 1 / d1) {
    return n1 * x4 * x4;
  } else if (x4 < 2 / d1) {
    return n1 * (x4 -= 1.5 / d1) * x4 + 0.75;
  } else if (x4 < 2.5 / d1) {
    return n1 * (x4 -= 2.25 / d1) * x4 + 0.9375;
  } else {
    return n1 * (x4 -= 2.625 / d1) * x4 + 0.984375;
  }
};
var easings = {
  linear: (x4) => x4,
  easeInQuad: (x4) => x4 * x4,
  easeOutQuad: (x4) => 1 - (1 - x4) * (1 - x4),
  easeInOutQuad: (x4) => x4 < 0.5 ? 2 * x4 * x4 : 1 - Math.pow(-2 * x4 + 2, 2) / 2,
  easeInCubic: (x4) => x4 * x4 * x4,
  easeOutCubic: (x4) => 1 - Math.pow(1 - x4, 3),
  easeInOutCubic: (x4) => x4 < 0.5 ? 4 * x4 * x4 * x4 : 1 - Math.pow(-2 * x4 + 2, 3) / 2,
  easeInQuart: (x4) => x4 * x4 * x4 * x4,
  easeOutQuart: (x4) => 1 - Math.pow(1 - x4, 4),
  easeInOutQuart: (x4) => x4 < 0.5 ? 8 * x4 * x4 * x4 * x4 : 1 - Math.pow(-2 * x4 + 2, 4) / 2,
  easeInQuint: (x4) => x4 * x4 * x4 * x4 * x4,
  easeOutQuint: (x4) => 1 - Math.pow(1 - x4, 5),
  easeInOutQuint: (x4) => x4 < 0.5 ? 16 * x4 * x4 * x4 * x4 * x4 : 1 - Math.pow(-2 * x4 + 2, 5) / 2,
  easeInSine: (x4) => 1 - Math.cos(x4 * Math.PI / 2),
  easeOutSine: (x4) => Math.sin(x4 * Math.PI / 2),
  easeInOutSine: (x4) => -(Math.cos(Math.PI * x4) - 1) / 2,
  easeInExpo: (x4) => x4 === 0 ? 0 : Math.pow(2, 10 * x4 - 10),
  easeOutExpo: (x4) => x4 === 1 ? 1 : 1 - Math.pow(2, -10 * x4),
  easeInOutExpo: (x4) => x4 === 0 ? 0 : x4 === 1 ? 1 : x4 < 0.5 ? Math.pow(2, 20 * x4 - 10) / 2 : (2 - Math.pow(2, -20 * x4 + 10)) / 2,
  easeInCirc: (x4) => 1 - Math.sqrt(1 - Math.pow(x4, 2)),
  easeOutCirc: (x4) => Math.sqrt(1 - Math.pow(x4 - 1, 2)),
  easeInOutCirc: (x4) => x4 < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x4, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x4 + 2, 2)) + 1) / 2,
  easeInBack: (x4) => c3 * x4 * x4 * x4 - c1 * x4 * x4,
  easeOutBack: (x4) => 1 + c3 * Math.pow(x4 - 1, 3) + c1 * Math.pow(x4 - 1, 2),
  easeInOutBack: (x4) => x4 < 0.5 ? Math.pow(2 * x4, 2) * ((c2 + 1) * 2 * x4 - c2) / 2 : (Math.pow(2 * x4 - 2, 2) * ((c2 + 1) * (x4 * 2 - 2) + c2) + 2) / 2,
  easeInElastic: (x4) => x4 === 0 ? 0 : x4 === 1 ? 1 : -Math.pow(2, 10 * x4 - 10) * Math.sin((x4 * 10 - 10.75) * c4),
  easeOutElastic: (x4) => x4 === 0 ? 0 : x4 === 1 ? 1 : Math.pow(2, -10 * x4) * Math.sin((x4 * 10 - 0.75) * c4) + 1,
  easeInOutElastic: (x4) => x4 === 0 ? 0 : x4 === 1 ? 1 : x4 < 0.5 ? -(Math.pow(2, 20 * x4 - 10) * Math.sin((20 * x4 - 11.125) * c5)) / 2 : Math.pow(2, -20 * x4 + 10) * Math.sin((20 * x4 - 11.125) * c5) / 2 + 1,
  easeInBounce: (x4) => 1 - bounceOut(1 - x4),
  easeOutBounce: bounceOut,
  easeInOutBounce: (x4) => x4 < 0.5 ? (1 - bounceOut(1 - 2 * x4)) / 2 : (1 + bounceOut(2 * x4 - 1)) / 2,
  steps
};
var $get = Symbol.for("FluidValue.get");
var $observers = Symbol.for("FluidValue.observers");
var hasFluidValue = (arg) => Boolean(arg && arg[$get]);
var getFluidValue = (arg) => arg && arg[$get] ? arg[$get]() : arg;
var getFluidObservers = (target) => target[$observers] || null;
function callFluidObserver(observer2, event) {
  if (observer2.eventObserved) {
    observer2.eventObserved(event);
  } else {
    observer2(event);
  }
}
function callFluidObservers(target, event) {
  const observers = target[$observers];
  if (observers) {
    observers.forEach((observer2) => {
      callFluidObserver(observer2, event);
    });
  }
}
var FluidValue = class {
  constructor(get) {
    if (!get && !(get = this.get)) {
      throw Error("Unknown getter");
    }
    setFluidGetter(this, get);
  }
};
var setFluidGetter = (target, get) => setHidden(target, $get, get);
function addFluidObserver(target, observer2) {
  if (target[$get]) {
    let observers = target[$observers];
    if (!observers) {
      setHidden(target, $observers, observers = /* @__PURE__ */ new Set());
    }
    if (!observers.has(observer2)) {
      observers.add(observer2);
      if (target.observerAdded) {
        target.observerAdded(observers.size, observer2);
      }
    }
  }
  return observer2;
}
function removeFluidObserver(target, observer2) {
  const observers = target[$observers];
  if (observers && observers.has(observer2)) {
    const count3 = observers.size - 1;
    if (count3) {
      observers.delete(observer2);
    } else {
      target[$observers] = null;
    }
    if (target.observerRemoved) {
      target.observerRemoved(count3, observer2);
    }
  }
}
var setHidden = (target, key, value) => Object.defineProperty(target, key, {
  value,
  writable: true,
  configurable: true
});
var numberRegex = /[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g;
var colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi;
var unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, "i");
var rgbaRegex = /rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi;
var cssVariableRegex = /var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;
var variableToRgba = (input) => {
  const [token, fallback] = parseCSSVariable(input);
  if (!token || isSSR()) {
    return input;
  }
  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);
  if (value) {
    return value.trim();
  } else if (fallback && fallback.startsWith("--")) {
    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);
    if (value2) {
      return value2;
    } else {
      return input;
    }
  } else if (fallback && cssVariableRegex.test(fallback)) {
    return variableToRgba(fallback);
  } else if (fallback) {
    return fallback;
  }
  return input;
};
var parseCSSVariable = (current) => {
  const match = cssVariableRegex.exec(current);
  if (!match)
    return [,];
  const [, token, fallback] = match;
  return [token, fallback];
};
var namedColorRegex;
var rgbaRound = (_3, p1, p22, p32, p4) => `rgba(${Math.round(p1)}, ${Math.round(p22)}, ${Math.round(p32)}, ${p4})`;
var createStringInterpolator2 = (config2) => {
  if (!namedColorRegex)
    namedColorRegex = colors ? (
      // match color names, ignore partial matches
      new RegExp(`(${Object.keys(colors).join("|")})(?!\\w)`, "g")
    ) : (
      // never match
      /^\b$/
    );
  const output = config2.output.map((value) => {
    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);
  });
  const keyframes = output.map((value) => value.match(numberRegex).map(Number));
  const outputRanges = keyframes[0].map(
    (_3, i6) => keyframes.map((values) => {
      if (!(i6 in values)) {
        throw Error('The arity of each "output" value must be equal');
      }
      return values[i6];
    })
  );
  const interpolators = outputRanges.map(
    (output2) => createInterpolator({ ...config2, output: output2 })
  );
  return (input) => {
    var _a;
    const missingUnit = !unitRegex.test(output[0]) && ((_a = output.find((value) => unitRegex.test(value))) == null ? void 0 : _a.replace(numberRegex, ""));
    let i6 = 0;
    return output[0].replace(
      numberRegex,
      () => `${interpolators[i6++](input)}${missingUnit || ""}`
    ).replace(rgbaRegex, rgbaRound);
  };
};
var prefix = "react-spring: ";
var once = (fn3) => {
  const func = fn3;
  let called = false;
  if (typeof func != "function") {
    throw new TypeError(`${prefix}once requires a function parameter`);
  }
  return (...args) => {
    if (!called) {
      func(...args);
      called = true;
    }
  };
};
var warnInterpolate = once(console.warn);
function deprecateInterpolate() {
  warnInterpolate(
    `${prefix}The "interpolate" function is deprecated in v9 (use "to" instead)`
  );
}
var warnDirectCall = once(console.warn);
function deprecateDirectCall() {
  warnDirectCall(
    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`
  );
}
function isAnimatedString(value) {
  return is.str(value) && (value[0] == "#" || /\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR
  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));
}
var useIsomorphicLayoutEffect = isSSR() ? import_react4.useEffect : import_react4.useLayoutEffect;
var useIsMounted = () => {
  const isMounted = (0, import_react3.useRef)(false);
  useIsomorphicLayoutEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  return isMounted;
};
function useForceUpdate() {
  const update3 = (0, import_react2.useState)()[1];
  const isMounted = useIsMounted();
  return () => {
    if (isMounted.current) {
      update3(Math.random());
    }
  };
}
function useMemoOne(getResult, inputs) {
  const [initial] = (0, import_react5.useState)(
    () => ({
      inputs,
      result: getResult()
    })
  );
  const committed = (0, import_react5.useRef)();
  const prevCache = committed.current;
  let cache = prevCache;
  if (cache) {
    const useCache = Boolean(
      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)
    );
    if (!useCache) {
      cache = {
        inputs,
        result: getResult()
      };
    }
  } else {
    cache = initial;
  }
  (0, import_react5.useEffect)(() => {
    committed.current = cache;
    if (prevCache == initial) {
      initial.inputs = initial.result = void 0;
    }
  }, [cache]);
  return cache.result;
}
function areInputsEqual(next, prev) {
  if (next.length !== prev.length) {
    return false;
  }
  for (let i6 = 0; i6 < next.length; i6++) {
    if (next[i6] !== prev[i6]) {
      return false;
    }
  }
  return true;
}
var useOnce = (effect) => (0, import_react6.useEffect)(effect, emptyDeps);
var emptyDeps = [];
function usePrev(value) {
  const prevRef = (0, import_react7.useRef)();
  (0, import_react7.useEffect)(() => {
    prevRef.current = value;
  });
  return prevRef.current;
}

// node_modules/@react-spring/core/dist/react-spring_core.modern.mjs
var import_react10 = __toESM(require_react(), 1);

// node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs
var React = __toESM(require_react(), 1);
var import_react9 = __toESM(require_react(), 1);
var $node = Symbol.for("Animated:node");
var isAnimated = (value) => !!value && value[$node] === value;
var getAnimated = (owner) => owner && owner[$node];
var setAnimated = (owner, node) => defineHidden(owner, $node, node);
var getPayload = (owner) => owner && owner[$node] && owner[$node].getPayload();
var Animated = class {
  constructor() {
    setAnimated(this, this);
  }
  /** Get every `AnimatedValue` used by this node. */
  getPayload() {
    return this.payload || [];
  }
};
var AnimatedValue = class extends Animated {
  constructor(_value) {
    super();
    this._value = _value;
    this.done = true;
    this.durationProgress = 0;
    if (is.num(this._value)) {
      this.lastPosition = this._value;
    }
  }
  /** @internal */
  static create(value) {
    return new AnimatedValue(value);
  }
  getPayload() {
    return [this];
  }
  getValue() {
    return this._value;
  }
  setValue(value, step) {
    if (is.num(value)) {
      this.lastPosition = value;
      if (step) {
        value = Math.round(value / step) * step;
        if (this.done) {
          this.lastPosition = value;
        }
      }
    }
    if (this._value === value) {
      return false;
    }
    this._value = value;
    return true;
  }
  reset() {
    const { done } = this;
    this.done = false;
    if (is.num(this._value)) {
      this.elapsedTime = 0;
      this.durationProgress = 0;
      this.lastPosition = this._value;
      if (done)
        this.lastVelocity = null;
      this.v0 = null;
    }
  }
};
var AnimatedString = class extends AnimatedValue {
  constructor(value) {
    super(0);
    this._string = null;
    this._toString = createInterpolator({
      output: [value, value]
    });
  }
  /** @internal */
  static create(value) {
    return new AnimatedString(value);
  }
  getValue() {
    const value = this._string;
    return value == null ? this._string = this._toString(this._value) : value;
  }
  setValue(value) {
    if (is.str(value)) {
      if (value == this._string) {
        return false;
      }
      this._string = value;
      this._value = 1;
    } else if (super.setValue(value)) {
      this._string = null;
    } else {
      return false;
    }
    return true;
  }
  reset(goal) {
    if (goal) {
      this._toString = createInterpolator({
        output: [this.getValue(), goal]
      });
    }
    this._value = 0;
    super.reset();
  }
};
var TreeContext = { dependencies: null };
var AnimatedObject = class extends Animated {
  constructor(source) {
    super();
    this.source = source;
    this.setValue(source);
  }
  getValue(animated2) {
    const values = {};
    eachProp(this.source, (source, key) => {
      if (isAnimated(source)) {
        values[key] = source.getValue(animated2);
      } else if (hasFluidValue(source)) {
        values[key] = getFluidValue(source);
      } else if (!animated2) {
        values[key] = source;
      }
    });
    return values;
  }
  /** Replace the raw object data */
  setValue(source) {
    this.source = source;
    this.payload = this._makePayload(source);
  }
  reset() {
    if (this.payload) {
      each(this.payload, (node) => node.reset());
    }
  }
  /** Create a payload set. */
  _makePayload(source) {
    if (source) {
      const payload = /* @__PURE__ */ new Set();
      eachProp(source, this._addToPayload, payload);
      return Array.from(payload);
    }
  }
  /** Add to a payload set. */
  _addToPayload(source) {
    if (TreeContext.dependencies && hasFluidValue(source)) {
      TreeContext.dependencies.add(source);
    }
    const payload = getPayload(source);
    if (payload) {
      each(payload, (node) => this.add(node));
    }
  }
};
var AnimatedArray = class extends AnimatedObject {
  constructor(source) {
    super(source);
  }
  /** @internal */
  static create(source) {
    return new AnimatedArray(source);
  }
  getValue() {
    return this.source.map((node) => node.getValue());
  }
  setValue(source) {
    const payload = this.getPayload();
    if (source.length == payload.length) {
      return payload.map((node, i6) => node.setValue(source[i6])).some(Boolean);
    }
    super.setValue(source.map(makeAnimated));
    return true;
  }
};
function makeAnimated(value) {
  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;
  return nodeType.create(value);
}
function getAnimatedType(value) {
  const parentNode = getAnimated(value);
  return parentNode ? parentNode.constructor : is.arr(value) ? AnimatedArray : isAnimatedString(value) ? AnimatedString : AnimatedValue;
}
var withAnimated = (Component, host2) => {
  const hasInstance = (
    // Function components must use "forwardRef" to avoid being
    // re-rendered on every animation frame.
    !is.fun(Component) || Component.prototype && Component.prototype.isReactComponent
  );
  return (0, import_react9.forwardRef)((givenProps, givenRef) => {
    const instanceRef = (0, import_react9.useRef)(null);
    const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks
    (0, import_react9.useCallback)(
      (value) => {
        instanceRef.current = updateRef(givenRef, value);
      },
      [givenRef]
    );
    const [props, deps] = getAnimatedState(givenProps, host2);
    const forceUpdate = useForceUpdate();
    const callback = () => {
      const instance = instanceRef.current;
      if (hasInstance && !instance) {
        return;
      }
      const didUpdate = instance ? host2.applyAnimatedValues(instance, props.getValue(true)) : false;
      if (didUpdate === false) {
        forceUpdate();
      }
    };
    const observer = new PropsObserver(callback, deps);
    const observerRef = (0, import_react9.useRef)();
    useIsomorphicLayoutEffect(() => {
      observerRef.current = observer;
      each(deps, (dep) => addFluidObserver(dep, observer));
      return () => {
        if (observerRef.current) {
          each(
            observerRef.current.deps,
            (dep) => removeFluidObserver(dep, observerRef.current)
          );
          raf.cancel(observerRef.current.update);
        }
      };
    });
    (0, import_react9.useEffect)(callback, []);
    useOnce(() => () => {
      const observer2 = observerRef.current;
      each(observer2.deps, (dep) => removeFluidObserver(dep, observer2));
    });
    const usedProps = host2.getComponentProps(props.getValue());
    return React.createElement(Component, { ...usedProps, ref });
  });
};
var PropsObserver = class {
  constructor(update3, deps) {
    this.update = update3;
    this.deps = deps;
  }
  eventObserved(event) {
    if (event.type == "change") {
      raf.write(this.update);
    }
  }
};
function getAnimatedState(props, host2) {
  const dependencies = /* @__PURE__ */ new Set();
  TreeContext.dependencies = dependencies;
  if (props.style)
    props = {
      ...props,
      style: host2.createAnimatedStyle(props.style)
    };
  props = new AnimatedObject(props);
  TreeContext.dependencies = null;
  return [props, dependencies];
}
function updateRef(ref, value) {
  if (ref) {
    if (is.fun(ref))
      ref(value);
    else
      ref.current = value;
  }
  return value;
}
var cacheKey = Symbol.for("AnimatedComponent");
var createHost = (components, {
  applyAnimatedValues: applyAnimatedValues2 = () => false,
  createAnimatedStyle = (style) => new AnimatedObject(style),
  getComponentProps = (props) => props
} = {}) => {
  const hostConfig = {
    applyAnimatedValues: applyAnimatedValues2,
    createAnimatedStyle,
    getComponentProps
  };
  const animated2 = (Component) => {
    const displayName = getDisplayName(Component) || "Anonymous";
    if (is.str(Component)) {
      Component = animated2[Component] || (animated2[Component] = withAnimated(Component, hostConfig));
    } else {
      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));
    }
    Component.displayName = `Animated(${displayName})`;
    return Component;
  };
  eachProp(components, (Component, key) => {
    if (is.arr(components)) {
      key = getDisplayName(Component);
    }
    animated2[key] = animated2(Component);
  });
  return {
    animated: animated2
  };
};
var getDisplayName = (arg) => is.str(arg) ? arg : arg && is.str(arg.displayName) ? arg.displayName : is.fun(arg) && arg.name || null;

// node_modules/@react-spring/core/dist/react-spring_core.modern.mjs
var React2 = __toESM(require_react(), 1);
var import_react11 = __toESM(require_react(), 1);
var import_react12 = __toESM(require_react(), 1);
var React22 = __toESM(require_react(), 1);
var import_react13 = __toESM(require_react(), 1);
var import_react14 = __toESM(require_react(), 1);
function callProp(value, ...args) {
  return is.fun(value) ? value(...args) : value;
}
var matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));
var resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;
var getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;
var noopTransform = (value) => value;
var getDefaultProps = (props, transform = noopTransform) => {
  let keys = DEFAULT_PROPS;
  if (props.default && props.default !== true) {
    props = props.default;
    keys = Object.keys(props);
  }
  const defaults2 = {};
  for (const key of keys) {
    const value = transform(props[key], key);
    if (!is.und(value)) {
      defaults2[key] = value;
    }
  }
  return defaults2;
};
var DEFAULT_PROPS = [
  "config",
  "onProps",
  "onStart",
  "onChange",
  "onPause",
  "onResume",
  "onRest"
];
var RESERVED_PROPS = {
  config: 1,
  from: 1,
  to: 1,
  ref: 1,
  loop: 1,
  reset: 1,
  pause: 1,
  cancel: 1,
  reverse: 1,
  immediate: 1,
  default: 1,
  delay: 1,
  onProps: 1,
  onStart: 1,
  onChange: 1,
  onPause: 1,
  onResume: 1,
  onRest: 1,
  onResolve: 1,
  // Transition props
  items: 1,
  trail: 1,
  sort: 1,
  expires: 1,
  initial: 1,
  enter: 1,
  update: 1,
  leave: 1,
  children: 1,
  onDestroyed: 1,
  // Internal props
  keys: 1,
  callId: 1,
  parentId: 1
};
function getForwardProps(props) {
  const forward = {};
  let count3 = 0;
  eachProp(props, (value, prop) => {
    if (!RESERVED_PROPS[prop]) {
      forward[prop] = value;
      count3++;
    }
  });
  if (count3) {
    return forward;
  }
}
function inferTo(props) {
  const to22 = getForwardProps(props);
  if (to22) {
    const out = { to: to22 };
    eachProp(props, (val, key) => key in to22 || (out[key] = val));
    return out;
  }
  return { ...props };
}
function computeGoal(value) {
  value = getFluidValue(value);
  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? globals_exports.createStringInterpolator({
    range: [0, 1],
    output: [value, value]
  })(1) : value;
}
function hasProps(props) {
  for (const _3 in props)
    return true;
  return false;
}
function isAsyncTo(to22) {
  return is.fun(to22) || is.arr(to22) && is.obj(to22[0]);
}
function detachRefs(ctrl, ref) {
  var _a;
  (_a = ctrl.ref) == null ? void 0 : _a.delete(ctrl);
  ref == null ? void 0 : ref.delete(ctrl);
}
function replaceRef(ctrl, ref) {
  var _a;
  if (ref && ctrl.ref !== ref) {
    (_a = ctrl.ref) == null ? void 0 : _a.delete(ctrl);
    ref.add(ctrl);
    ctrl.ref = ref;
  }
}
var config = {
  default: { tension: 170, friction: 26 },
  gentle: { tension: 120, friction: 14 },
  wobbly: { tension: 180, friction: 12 },
  stiff: { tension: 210, friction: 20 },
  slow: { tension: 280, friction: 60 },
  molasses: { tension: 280, friction: 120 }
};
var defaults = {
  ...config.default,
  mass: 1,
  damping: 1,
  easing: easings.linear,
  clamp: false
};
var AnimationConfig = class {
  constructor() {
    this.velocity = 0;
    Object.assign(this, defaults);
  }
};
function mergeConfig(config2, newConfig, defaultConfig) {
  if (defaultConfig) {
    defaultConfig = { ...defaultConfig };
    sanitizeConfig(defaultConfig, newConfig);
    newConfig = { ...defaultConfig, ...newConfig };
  }
  sanitizeConfig(config2, newConfig);
  Object.assign(config2, newConfig);
  for (const key in defaults) {
    if (config2[key] == null) {
      config2[key] = defaults[key];
    }
  }
  let { frequency, damping } = config2;
  const { mass } = config2;
  if (!is.und(frequency)) {
    if (frequency < 0.01)
      frequency = 0.01;
    if (damping < 0)
      damping = 0;
    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;
    config2.friction = 4 * Math.PI * damping * mass / frequency;
  }
  return config2;
}
function sanitizeConfig(config2, props) {
  if (!is.und(props.decay)) {
    config2.duration = void 0;
  } else {
    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction);
    if (isTensionConfig || !is.und(props.frequency) || !is.und(props.damping) || !is.und(props.mass)) {
      config2.duration = void 0;
      config2.decay = void 0;
    }
    if (isTensionConfig) {
      config2.frequency = void 0;
    }
  }
}
var emptyArray = [];
var Animation = class {
  constructor() {
    this.changed = false;
    this.values = emptyArray;
    this.toValues = null;
    this.fromValues = emptyArray;
    this.config = new AnimationConfig();
    this.immediate = false;
  }
};
function scheduleProps(callId, { key, props, defaultProps, state, actions }) {
  return new Promise((resolve, reject) => {
    let delay;
    let timeout;
    let cancel = matchProp(props.cancel ?? (defaultProps == null ? void 0 : defaultProps.cancel), key);
    if (cancel) {
      onStart();
    } else {
      if (!is.und(props.pause)) {
        state.paused = matchProp(props.pause, key);
      }
      let pause = defaultProps == null ? void 0 : defaultProps.pause;
      if (pause !== true) {
        pause = state.paused || matchProp(pause, key);
      }
      delay = callProp(props.delay || 0, key);
      if (pause) {
        state.resumeQueue.add(onResume);
        actions.pause();
      } else {
        actions.resume();
        onResume();
      }
    }
    function onPause() {
      state.resumeQueue.add(onResume);
      state.timeouts.delete(timeout);
      timeout.cancel();
      delay = timeout.time - raf.now();
    }
    function onResume() {
      if (delay > 0 && !globals_exports.skipAnimation) {
        state.delayed = true;
        timeout = raf.setTimeout(onStart, delay);
        state.pauseQueue.add(onPause);
        state.timeouts.add(timeout);
      } else {
        onStart();
      }
    }
    function onStart() {
      if (state.delayed) {
        state.delayed = false;
      }
      state.pauseQueue.delete(onPause);
      state.timeouts.delete(timeout);
      if (callId <= (state.cancelId || 0)) {
        cancel = true;
      }
      try {
        actions.start({ ...props, callId, cancel }, resolve);
      } catch (err) {
        reject(err);
      }
    }
  });
}
var getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some((result) => result.cancelled) ? getCancelledResult(target.get()) : results.every((result) => result.noop) ? getNoopResult(target.get()) : getFinishedResult(
  target.get(),
  results.every((result) => result.finished)
);
var getNoopResult = (value) => ({
  value,
  noop: true,
  finished: true,
  cancelled: false
});
var getFinishedResult = (value, finished, cancelled = false) => ({
  value,
  finished,
  cancelled
});
var getCancelledResult = (value) => ({
  value,
  cancelled: true,
  finished: false
});
function runAsync(to22, props, state, target) {
  const { callId, parentId, onRest } = props;
  const { asyncTo: prevTo, promise: prevPromise } = state;
  if (!parentId && to22 === prevTo && !props.reset) {
    return prevPromise;
  }
  return state.promise = (async () => {
    state.asyncId = callId;
    state.asyncTo = to22;
    const defaultProps = getDefaultProps(
      props,
      (value, key) => (
        // The `onRest` prop is only called when the `runAsync` promise is resolved.
        key === "onRest" ? void 0 : value
      )
    );
    let preventBail;
    let bail;
    const bailPromise = new Promise(
      (resolve, reject) => (preventBail = resolve, bail = reject)
    );
    const bailIfEnded = (bailSignal) => {
      const bailResult = (
        // The `cancel` prop or `stop` method was used.
        callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.
        callId !== state.asyncId && getFinishedResult(target, false)
      );
      if (bailResult) {
        bailSignal.result = bailResult;
        bail(bailSignal);
        throw bailSignal;
      }
    };
    const animate = (arg1, arg2) => {
      const bailSignal = new BailSignal();
      const skipAnimationSignal = new SkipAnimationSignal();
      return (async () => {
        if (globals_exports.skipAnimation) {
          stopAsync(state);
          skipAnimationSignal.result = getFinishedResult(target, false);
          bail(skipAnimationSignal);
          throw skipAnimationSignal;
        }
        bailIfEnded(bailSignal);
        const props2 = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 };
        props2.parentId = callId;
        eachProp(defaultProps, (value, key) => {
          if (is.und(props2[key])) {
            props2[key] = value;
          }
        });
        const result2 = await target.start(props2);
        bailIfEnded(bailSignal);
        if (state.paused) {
          await new Promise((resume) => {
            state.resumeQueue.add(resume);
          });
        }
        return result2;
      })();
    };
    let result;
    if (globals_exports.skipAnimation) {
      stopAsync(state);
      return getFinishedResult(target, false);
    }
    try {
      let animating;
      if (is.arr(to22)) {
        animating = (async (queue) => {
          for (const props2 of queue) {
            await animate(props2);
          }
        })(to22);
      } else {
        animating = Promise.resolve(to22(animate, target.stop.bind(target)));
      }
      await Promise.all([animating.then(preventBail), bailPromise]);
      result = getFinishedResult(target.get(), true, false);
    } catch (err) {
      if (err instanceof BailSignal) {
        result = err.result;
      } else if (err instanceof SkipAnimationSignal) {
        result = err.result;
      } else {
        throw err;
      }
    } finally {
      if (callId == state.asyncId) {
        state.asyncId = parentId;
        state.asyncTo = parentId ? prevTo : void 0;
        state.promise = parentId ? prevPromise : void 0;
      }
    }
    if (is.fun(onRest)) {
      raf.batchedUpdates(() => {
        onRest(result, target, target.item);
      });
    }
    return result;
  })();
}
function stopAsync(state, cancelId) {
  flush(state.timeouts, (t8) => t8.cancel());
  state.pauseQueue.clear();
  state.resumeQueue.clear();
  state.asyncId = state.asyncTo = state.promise = void 0;
  if (cancelId)
    state.cancelId = cancelId;
}
var BailSignal = class extends Error {
  constructor() {
    super(
      "An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise."
    );
  }
};
var SkipAnimationSignal = class extends Error {
  constructor() {
    super("SkipAnimationSignal");
  }
};
var isFrameValue = (value) => value instanceof FrameValue;
var nextId = 1;
var FrameValue = class extends FluidValue {
  constructor() {
    super(...arguments);
    this.id = nextId++;
    this._priority = 0;
  }
  get priority() {
    return this._priority;
  }
  set priority(priority2) {
    if (this._priority != priority2) {
      this._priority = priority2;
      this._onPriorityChange(priority2);
    }
  }
  /** Get the current value */
  get() {
    const node = getAnimated(this);
    return node && node.getValue();
  }
  /** Create a spring that maps our value to another value */
  to(...args) {
    return globals_exports.to(this, args);
  }
  /** @deprecated Use the `to` method instead. */
  interpolate(...args) {
    deprecateInterpolate();
    return globals_exports.to(this, args);
  }
  toJSON() {
    return this.get();
  }
  observerAdded(count3) {
    if (count3 == 1)
      this._attach();
  }
  observerRemoved(count3) {
    if (count3 == 0)
      this._detach();
  }
  /** Called when the first child is added. */
  _attach() {
  }
  /** Called when the last child is removed. */
  _detach() {
  }
  /** Tell our children about our new value */
  _onChange(value, idle = false) {
    callFluidObservers(this, {
      type: "change",
      parent: this,
      value,
      idle
    });
  }
  /** Tell our children about our new priority */
  _onPriorityChange(priority2) {
    if (!this.idle) {
      frameLoop.sort(this);
    }
    callFluidObservers(this, {
      type: "priority",
      parent: this,
      priority: priority2
    });
  }
};
var $P = Symbol.for("SpringPhase");
var HAS_ANIMATED = 1;
var IS_ANIMATING = 2;
var IS_PAUSED = 4;
var hasAnimated = (target) => (target[$P] & HAS_ANIMATED) > 0;
var isAnimating = (target) => (target[$P] & IS_ANIMATING) > 0;
var isPaused = (target) => (target[$P] & IS_PAUSED) > 0;
var setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;
var setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;
var SpringValue = class extends FrameValue {
  constructor(arg1, arg2) {
    super();
    this.animation = new Animation();
    this.defaultProps = {};
    this._state = {
      paused: false,
      delayed: false,
      pauseQueue: /* @__PURE__ */ new Set(),
      resumeQueue: /* @__PURE__ */ new Set(),
      timeouts: /* @__PURE__ */ new Set()
    };
    this._pendingCalls = /* @__PURE__ */ new Set();
    this._lastCallId = 0;
    this._lastToId = 0;
    this._memoizedDuration = 0;
    if (!is.und(arg1) || !is.und(arg2)) {
      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 };
      if (is.und(props.default)) {
        props.default = true;
      }
      this.start(props);
    }
  }
  /** Equals true when not advancing on each frame. */
  get idle() {
    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);
  }
  get goal() {
    return getFluidValue(this.animation.to);
  }
  get velocity() {
    const node = getAnimated(this);
    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map((node2) => node2.lastVelocity || 0);
  }
  /**
   * When true, this value has been animated at least once.
   */
  get hasAnimated() {
    return hasAnimated(this);
  }
  /**
   * When true, this value has an unfinished animation,
   * which is either active or paused.
   */
  get isAnimating() {
    return isAnimating(this);
  }
  /**
   * When true, all current and future animations are paused.
   */
  get isPaused() {
    return isPaused(this);
  }
  /**
   *
   *
   */
  get isDelayed() {
    return this._state.delayed;
  }
  /** Advance the current animation by a number of milliseconds */
  advance(dt2) {
    let idle = true;
    let changed = false;
    const anim = this.animation;
    let { toValues } = anim;
    const { config: config2 } = anim;
    const payload = getPayload(anim.to);
    if (!payload && hasFluidValue(anim.to)) {
      toValues = toArray(getFluidValue(anim.to));
    }
    anim.values.forEach((node2, i6) => {
      if (node2.done)
        return;
      const to22 = (
        // Animated strings always go from 0 to 1.
        node2.constructor == AnimatedString ? 1 : payload ? payload[i6].lastPosition : toValues[i6]
      );
      let finished = anim.immediate;
      let position = to22;
      if (!finished) {
        position = node2.lastPosition;
        if (config2.tension <= 0) {
          node2.done = true;
          return;
        }
        let elapsed = node2.elapsedTime += dt2;
        const from = anim.fromValues[i6];
        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = is.arr(config2.velocity) ? config2.velocity[i6] : config2.velocity;
        let velocity;
        const precision = config2.precision || (from == to22 ? 5e-3 : Math.min(1, Math.abs(to22 - from) * 1e-3));
        if (!is.und(config2.duration)) {
          let p4 = 1;
          if (config2.duration > 0) {
            if (this._memoizedDuration !== config2.duration) {
              this._memoizedDuration = config2.duration;
              if (node2.durationProgress > 0) {
                node2.elapsedTime = config2.duration * node2.durationProgress;
                elapsed = node2.elapsedTime += dt2;
              }
            }
            p4 = (config2.progress || 0) + elapsed / this._memoizedDuration;
            p4 = p4 > 1 ? 1 : p4 < 0 ? 0 : p4;
            node2.durationProgress = p4;
          }
          position = from + config2.easing(p4) * (to22 - from);
          velocity = (position - node2.lastPosition) / dt2;
          finished = p4 == 1;
        } else if (config2.decay) {
          const decay = config2.decay === true ? 0.998 : config2.decay;
          const e11 = Math.exp(-(1 - decay) * elapsed);
          position = from + v0 / (1 - decay) * (1 - e11);
          finished = Math.abs(node2.lastPosition - position) <= precision;
          velocity = v0 * e11;
        } else {
          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;
          const restVelocity = config2.restVelocity || precision / 10;
          const bounceFactor = config2.clamp ? 0 : config2.bounce;
          const canBounce = !is.und(bounceFactor);
          const isGrowing = from == to22 ? node2.v0 > 0 : from < to22;
          let isMoving;
          let isBouncing = false;
          const step = 1;
          const numSteps = Math.ceil(dt2 / step);
          for (let n7 = 0; n7 < numSteps; ++n7) {
            isMoving = Math.abs(velocity) > restVelocity;
            if (!isMoving) {
              finished = Math.abs(to22 - position) <= precision;
              if (finished) {
                break;
              }
            }
            if (canBounce) {
              isBouncing = position == to22 || position > to22 == isGrowing;
              if (isBouncing) {
                velocity = -velocity * bounceFactor;
                position = to22;
              }
            }
            const springForce = -config2.tension * 1e-6 * (position - to22);
            const dampingForce = -config2.friction * 1e-3 * velocity;
            const acceleration = (springForce + dampingForce) / config2.mass;
            velocity = velocity + acceleration * step;
            position = position + velocity * step;
          }
        }
        node2.lastVelocity = velocity;
        if (Number.isNaN(position)) {
          console.warn(`Got NaN while animating:`, this);
          finished = true;
        }
      }
      if (payload && !payload[i6].done) {
        finished = false;
      }
      if (finished) {
        node2.done = true;
      } else {
        idle = false;
      }
      if (node2.setValue(position, config2.round)) {
        changed = true;
      }
    });
    const node = getAnimated(this);
    const currVal = node.getValue();
    if (idle) {
      const finalVal = getFluidValue(anim.to);
      if ((currVal !== finalVal || changed) && !config2.decay) {
        node.setValue(finalVal);
        this._onChange(finalVal);
      } else if (changed && config2.decay) {
        this._onChange(currVal);
      }
      this._stop();
    } else if (changed) {
      this._onChange(currVal);
    }
  }
  /** Set the current value, while stopping the current animation */
  set(value) {
    raf.batchedUpdates(() => {
      this._stop();
      this._focus(value);
      this._set(value);
    });
    return this;
  }
  /**
   * Freeze the active animation in time, as well as any updates merged
   * before `resume` is called.
   */
  pause() {
    this._update({ pause: true });
  }
  /** Resume the animation if paused. */
  resume() {
    this._update({ pause: false });
  }
  /** Skip to the end of the current animation. */
  finish() {
    if (isAnimating(this)) {
      const { to: to22, config: config2 } = this.animation;
      raf.batchedUpdates(() => {
        this._onStart();
        if (!config2.decay) {
          this._set(to22, false);
        }
        this._stop();
      });
    }
    return this;
  }
  /** Push props into the pending queue. */
  update(props) {
    const queue = this.queue || (this.queue = []);
    queue.push(props);
    return this;
  }
  start(to22, arg2) {
    let queue;
    if (!is.und(to22)) {
      queue = [is.obj(to22) ? to22 : { ...arg2, to: to22 }];
    } else {
      queue = this.queue || [];
      this.queue = [];
    }
    return Promise.all(
      queue.map((props) => {
        const up = this._update(props);
        return up;
      })
    ).then((results) => getCombinedResult(this, results));
  }
  /**
   * Stop the current animation, and cancel any delayed updates.
   *
   * Pass `true` to call `onRest` with `cancelled: true`.
   */
  stop(cancel) {
    const { to: to22 } = this.animation;
    this._focus(this.get());
    stopAsync(this._state, cancel && this._lastCallId);
    raf.batchedUpdates(() => this._stop(to22, cancel));
    return this;
  }
  /** Restart the animation. */
  reset() {
    this._update({ reset: true });
  }
  /** @internal */
  eventObserved(event) {
    if (event.type == "change") {
      this._start();
    } else if (event.type == "priority") {
      this.priority = event.priority + 1;
    }
  }
  /**
   * Parse the `to` and `from` range from the given `props` object.
   *
   * This also ensures the initial value is available to animated components
   * during the render phase.
   */
  _prepareNode(props) {
    const key = this.key || "";
    let { to: to22, from } = props;
    to22 = is.obj(to22) ? to22[key] : to22;
    if (to22 == null || isAsyncTo(to22)) {
      to22 = void 0;
    }
    from = is.obj(from) ? from[key] : from;
    if (from == null) {
      from = void 0;
    }
    const range2 = { to: to22, from };
    if (!hasAnimated(this)) {
      if (props.reverse)
        [to22, from] = [from, to22];
      from = getFluidValue(from);
      if (!is.und(from)) {
        this._set(from);
      } else if (!getAnimated(this)) {
        this._set(to22);
      }
    }
    return range2;
  }
  /** Every update is processed by this method before merging. */
  _update({ ...props }, isLoop) {
    const { key, defaultProps } = this;
    if (props.default)
      Object.assign(
        defaultProps,
        getDefaultProps(
          props,
          (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value
        )
      );
    mergeActiveFn(this, props, "onProps");
    sendEvent(this, "onProps", props, this);
    const range2 = this._prepareNode(props);
    if (Object.isFrozen(this)) {
      throw Error(
        "Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?"
      );
    }
    const state = this._state;
    return scheduleProps(++this._lastCallId, {
      key,
      props,
      defaultProps,
      state,
      actions: {
        pause: () => {
          if (!isPaused(this)) {
            setPausedBit(this, true);
            flushCalls(state.pauseQueue);
            sendEvent(
              this,
              "onPause",
              getFinishedResult(this, checkFinished(this, this.animation.to)),
              this
            );
          }
        },
        resume: () => {
          if (isPaused(this)) {
            setPausedBit(this, false);
            if (isAnimating(this)) {
              this._resume();
            }
            flushCalls(state.resumeQueue);
            sendEvent(
              this,
              "onResume",
              getFinishedResult(this, checkFinished(this, this.animation.to)),
              this
            );
          }
        },
        start: this._merge.bind(this, range2)
      }
    }).then((result) => {
      if (props.loop && result.finished && !(isLoop && result.noop)) {
        const nextProps = createLoopUpdate(props);
        if (nextProps) {
          return this._update(nextProps, true);
        }
      }
      return result;
    });
  }
  /** Merge props into the current animation */
  _merge(range2, props, resolve) {
    if (props.cancel) {
      this.stop(true);
      return resolve(getCancelledResult(this));
    }
    const hasToProp = !is.und(range2.to);
    const hasFromProp = !is.und(range2.from);
    if (hasToProp || hasFromProp) {
      if (props.callId > this._lastToId) {
        this._lastToId = props.callId;
      } else {
        return resolve(getCancelledResult(this));
      }
    }
    const { key, defaultProps, animation: anim } = this;
    const { to: prevTo, from: prevFrom } = anim;
    let { to: to22 = prevTo, from = prevFrom } = range2;
    if (hasFromProp && !hasToProp && (!props.default || is.und(to22))) {
      to22 = from;
    }
    if (props.reverse)
      [to22, from] = [from, to22];
    const hasFromChanged = !isEqual(from, prevFrom);
    if (hasFromChanged) {
      anim.from = from;
    }
    from = getFluidValue(from);
    const hasToChanged = !isEqual(to22, prevTo);
    if (hasToChanged) {
      this._focus(to22);
    }
    const hasAsyncTo = isAsyncTo(props.to);
    const { config: config2 } = anim;
    const { decay, velocity } = config2;
    if (hasToProp || hasFromProp) {
      config2.velocity = 0;
    }
    if (props.config && !hasAsyncTo) {
      mergeConfig(
        config2,
        callProp(props.config, key),
        // Avoid calling the same "config" prop twice.
        props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0
      );
    }
    let node = getAnimated(this);
    if (!node || is.und(to22)) {
      return resolve(getFinishedResult(this, true));
    }
    const reset = (
      // When `reset` is undefined, the `from` prop implies `reset: true`,
      // except for declarative updates. When `reset` is defined, there
      // must exist a value to animate from.
      is.und(props.reset) ? hasFromProp && !props.default : !is.und(from) && matchProp(props.reset, key)
    );
    const value = reset ? from : this.get();
    const goal = computeGoal(to22);
    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal);
    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));
    if (hasToChanged) {
      const nodeType = getAnimatedType(to22);
      if (nodeType !== node.constructor) {
        if (immediate) {
          node = this._set(goal);
        } else
          throw Error(
            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the "to" prop suggests`
          );
      }
    }
    const goalType = node.constructor;
    let started = hasFluidValue(to22);
    let finished = false;
    if (!started) {
      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;
      if (hasToChanged || hasValueChanged) {
        finished = isEqual(computeGoal(value), goal);
        started = !finished;
      }
      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config2.decay, decay) || !isEqual(config2.velocity, velocity)) {
        started = true;
      }
    }
    if (finished && isAnimating(this)) {
      if (anim.changed && !reset) {
        started = true;
      } else if (!started) {
        this._stop(prevTo);
      }
    }
    if (!hasAsyncTo) {
      if (started || hasFluidValue(prevTo)) {
        anim.values = node.getPayload();
        anim.toValues = hasFluidValue(to22) ? null : goalType == AnimatedString ? [1] : toArray(goal);
      }
      if (anim.immediate != immediate) {
        anim.immediate = immediate;
        if (!immediate && !reset) {
          this._set(prevTo);
        }
      }
      if (started) {
        const { onRest } = anim;
        each(ACTIVE_EVENTS, (type) => mergeActiveFn(this, props, type));
        const result = getFinishedResult(this, checkFinished(this, prevTo));
        flushCalls(this._pendingCalls, result);
        this._pendingCalls.add(resolve);
        if (anim.changed)
          raf.batchedUpdates(() => {
            var _a;
            anim.changed = !reset;
            onRest == null ? void 0 : onRest(result, this);
            if (reset) {
              callProp(defaultProps.onRest, result);
            } else {
              (_a = anim.onStart) == null ? void 0 : _a.call(anim, result, this);
            }
          });
      }
    }
    if (reset) {
      this._set(value);
    }
    if (hasAsyncTo) {
      resolve(runAsync(props.to, props, this._state, this));
    } else if (started) {
      this._start();
    } else if (isAnimating(this) && !hasToChanged) {
      this._pendingCalls.add(resolve);
    } else {
      resolve(getNoopResult(value));
    }
  }
  /** Update the `animation.to` value, which might be a `FluidValue` */
  _focus(value) {
    const anim = this.animation;
    if (value !== anim.to) {
      if (getFluidObservers(this)) {
        this._detach();
      }
      anim.to = value;
      if (getFluidObservers(this)) {
        this._attach();
      }
    }
  }
  _attach() {
    let priority2 = 0;
    const { to: to22 } = this.animation;
    if (hasFluidValue(to22)) {
      addFluidObserver(to22, this);
      if (isFrameValue(to22)) {
        priority2 = to22.priority + 1;
      }
    }
    this.priority = priority2;
  }
  _detach() {
    const { to: to22 } = this.animation;
    if (hasFluidValue(to22)) {
      removeFluidObserver(to22, this);
    }
  }
  /**
   * Update the current value from outside the frameloop,
   * and return the `Animated` node.
   */
  _set(arg, idle = true) {
    const value = getFluidValue(arg);
    if (!is.und(value)) {
      const oldNode = getAnimated(this);
      if (!oldNode || !isEqual(value, oldNode.getValue())) {
        const nodeType = getAnimatedType(value);
        if (!oldNode || oldNode.constructor != nodeType) {
          setAnimated(this, nodeType.create(value));
        } else {
          oldNode.setValue(value);
        }
        if (oldNode) {
          raf.batchedUpdates(() => {
            this._onChange(value, idle);
          });
        }
      }
    }
    return getAnimated(this);
  }
  _onStart() {
    const anim = this.animation;
    if (!anim.changed) {
      anim.changed = true;
      sendEvent(
        this,
        "onStart",
        getFinishedResult(this, checkFinished(this, anim.to)),
        this
      );
    }
  }
  _onChange(value, idle) {
    if (!idle) {
      this._onStart();
      callProp(this.animation.onChange, value, this);
    }
    callProp(this.defaultProps.onChange, value, this);
    super._onChange(value, idle);
  }
  // This method resets the animation state (even if already animating) to
  // ensure the latest from/to range is used, and it also ensures this spring
  // is added to the frameloop.
  _start() {
    const anim = this.animation;
    getAnimated(this).reset(getFluidValue(anim.to));
    if (!anim.immediate) {
      anim.fromValues = anim.values.map((node) => node.lastPosition);
    }
    if (!isAnimating(this)) {
      setActiveBit(this, true);
      if (!isPaused(this)) {
        this._resume();
      }
    }
  }
  _resume() {
    if (globals_exports.skipAnimation) {
      this.finish();
    } else {
      frameLoop.start(this);
    }
  }
  /**
   * Exit the frameloop and notify `onRest` listeners.
   *
   * Always wrap `_stop` calls with `batchedUpdates`.
   */
  _stop(goal, cancel) {
    if (isAnimating(this)) {
      setActiveBit(this, false);
      const anim = this.animation;
      each(anim.values, (node) => {
        node.done = true;
      });
      if (anim.toValues) {
        anim.onChange = anim.onPause = anim.onResume = void 0;
      }
      callFluidObservers(this, {
        type: "idle",
        parent: this
      });
      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));
      flushCalls(this._pendingCalls, result);
      if (anim.changed) {
        anim.changed = false;
        sendEvent(this, "onRest", result, this);
      }
    }
  }
};
function checkFinished(target, to22) {
  const goal = computeGoal(to22);
  const value = computeGoal(target.get());
  return isEqual(value, goal);
}
function createLoopUpdate(props, loop2 = props.loop, to22 = props.to) {
  const loopRet = callProp(loop2);
  if (loopRet) {
    const overrides = loopRet !== true && inferTo(loopRet);
    const reverse3 = (overrides || props).reverse;
    const reset = !overrides || overrides.reset;
    return createUpdate({
      ...props,
      loop: loop2,
      // Avoid updating default props when looping.
      default: false,
      // Never loop the `pause` prop.
      pause: void 0,
      // For the "reverse" prop to loop as expected, the "to" prop
      // must be undefined. The "reverse" prop is ignored when the
      // "to" prop is an array or function.
      to: !reverse3 || isAsyncTo(to22) ? to22 : void 0,
      // Ignore the "from" prop except on reset.
      from: reset ? props.from : void 0,
      reset,
      // The "loop" prop can return a "useSpring" props object to
      // override any of the original props.
      ...overrides
    });
  }
}
function createUpdate(props) {
  const { to: to22, from } = props = inferTo(props);
  const keys = /* @__PURE__ */ new Set();
  if (is.obj(to22))
    findDefined(to22, keys);
  if (is.obj(from))
    findDefined(from, keys);
  props.keys = keys.size ? Array.from(keys) : null;
  return props;
}
function declareUpdate(props) {
  const update22 = createUpdate(props);
  if (is.und(update22.default)) {
    update22.default = getDefaultProps(update22);
  }
  return update22;
}
function findDefined(values, keys) {
  eachProp(values, (value, key) => value != null && keys.add(key));
}
var ACTIVE_EVENTS = [
  "onStart",
  "onRest",
  "onChange",
  "onPause",
  "onResume"
];
function mergeActiveFn(target, props, type) {
  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;
}
function sendEvent(target, type, ...args) {
  var _a, _b, _c, _d;
  (_b = (_a = target.animation)[type]) == null ? void 0 : _b.call(_a, ...args);
  (_d = (_c = target.defaultProps)[type]) == null ? void 0 : _d.call(_c, ...args);
}
var BATCHED_EVENTS = ["onStart", "onChange", "onRest"];
var nextId2 = 1;
var Controller = class {
  constructor(props, flush3) {
    this.id = nextId2++;
    this.springs = {};
    this.queue = [];
    this._lastAsyncId = 0;
    this._active = /* @__PURE__ */ new Set();
    this._changed = /* @__PURE__ */ new Set();
    this._started = false;
    this._state = {
      paused: false,
      pauseQueue: /* @__PURE__ */ new Set(),
      resumeQueue: /* @__PURE__ */ new Set(),
      timeouts: /* @__PURE__ */ new Set()
    };
    this._events = {
      onStart: /* @__PURE__ */ new Map(),
      onChange: /* @__PURE__ */ new Map(),
      onRest: /* @__PURE__ */ new Map()
    };
    this._onFrame = this._onFrame.bind(this);
    if (flush3) {
      this._flush = flush3;
    }
    if (props) {
      this.start({ default: true, ...props });
    }
  }
  /**
   * Equals `true` when no spring values are in the frameloop, and
   * no async animation is currently active.
   */
  get idle() {
    return !this._state.asyncTo && Object.values(this.springs).every((spring) => {
      return spring.idle && !spring.isDelayed && !spring.isPaused;
    });
  }
  get item() {
    return this._item;
  }
  set item(item) {
    this._item = item;
  }
  /** Get the current values of our springs */
  get() {
    const values = {};
    this.each((spring, key) => values[key] = spring.get());
    return values;
  }
  /** Set the current values without animating. */
  set(values) {
    for (const key in values) {
      const value = values[key];
      if (!is.und(value)) {
        this.springs[key].set(value);
      }
    }
  }
  /** Push an update onto the queue of each value. */
  update(props) {
    if (props) {
      this.queue.push(createUpdate(props));
    }
    return this;
  }
  /**
   * Start the queued animations for every spring, and resolve the returned
   * promise once all queued animations have finished or been cancelled.
   *
   * When you pass a queue (instead of nothing), that queue is used instead of
   * the queued animations added with the `update` method, which are left alone.
   */
  start(props) {
    let { queue } = this;
    if (props) {
      queue = toArray(props).map(createUpdate);
    } else {
      this.queue = [];
    }
    if (this._flush) {
      return this._flush(this, queue);
    }
    prepareKeys(this, queue);
    return flushUpdateQueue(this, queue);
  }
  /** @internal */
  stop(arg, keys) {
    if (arg !== !!arg) {
      keys = arg;
    }
    if (keys) {
      const springs = this.springs;
      each(toArray(keys), (key) => springs[key].stop(!!arg));
    } else {
      stopAsync(this._state, this._lastAsyncId);
      this.each((spring) => spring.stop(!!arg));
    }
    return this;
  }
  /** Freeze the active animation in time */
  pause(keys) {
    if (is.und(keys)) {
      this.start({ pause: true });
    } else {
      const springs = this.springs;
      each(toArray(keys), (key) => springs[key].pause());
    }
    return this;
  }
  /** Resume the animation if paused. */
  resume(keys) {
    if (is.und(keys)) {
      this.start({ pause: false });
    } else {
      const springs = this.springs;
      each(toArray(keys), (key) => springs[key].resume());
    }
    return this;
  }
  /** Call a function once per spring value */
  each(iterator) {
    eachProp(this.springs, iterator);
  }
  /** @internal Called at the end of every animation frame */
  _onFrame() {
    const { onStart, onChange, onRest } = this._events;
    const active = this._active.size > 0;
    const changed = this._changed.size > 0;
    if (active && !this._started || changed && !this._started) {
      this._started = true;
      flush(onStart, ([onStart2, result]) => {
        result.value = this.get();
        onStart2(result, this, this._item);
      });
    }
    const idle = !active && this._started;
    const values = changed || idle && onRest.size ? this.get() : null;
    if (changed && onChange.size) {
      flush(onChange, ([onChange2, result]) => {
        result.value = values;
        onChange2(result, this, this._item);
      });
    }
    if (idle) {
      this._started = false;
      flush(onRest, ([onRest2, result]) => {
        result.value = values;
        onRest2(result, this, this._item);
      });
    }
  }
  /** @internal */
  eventObserved(event) {
    if (event.type == "change") {
      this._changed.add(event.parent);
      if (!event.idle) {
        this._active.add(event.parent);
      }
    } else if (event.type == "idle") {
      this._active.delete(event.parent);
    } else
      return;
    raf.onFrame(this._onFrame);
  }
};
function flushUpdateQueue(ctrl, queue) {
  return Promise.all(queue.map((props) => flushUpdate(ctrl, props))).then(
    (results) => getCombinedResult(ctrl, results)
  );
}
async function flushUpdate(ctrl, props, isLoop) {
  const { keys, to: to22, from, loop: loop2, onRest, onResolve } = props;
  const defaults2 = is.obj(props.default) && props.default;
  if (loop2) {
    props.loop = false;
  }
  if (to22 === false)
    props.to = null;
  if (from === false)
    props.from = null;
  const asyncTo = is.arr(to22) || is.fun(to22) ? to22 : void 0;
  if (asyncTo) {
    props.to = void 0;
    props.onRest = void 0;
    if (defaults2) {
      defaults2.onRest = void 0;
    }
  } else {
    each(BATCHED_EVENTS, (key) => {
      const handler = props[key];
      if (is.fun(handler)) {
        const queue = ctrl["_events"][key];
        props[key] = ({ finished, cancelled }) => {
          const result2 = queue.get(handler);
          if (result2) {
            if (!finished)
              result2.finished = false;
            if (cancelled)
              result2.cancelled = true;
          } else {
            queue.set(handler, {
              value: null,
              finished: finished || false,
              cancelled: cancelled || false
            });
          }
        };
        if (defaults2) {
          defaults2[key] = props[key];
        }
      }
    });
  }
  const state = ctrl["_state"];
  if (props.pause === !state.paused) {
    state.paused = props.pause;
    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue);
  } else if (state.paused) {
    props.pause = true;
  }
  const promises = (keys || Object.keys(ctrl.springs)).map(
    (key) => ctrl.springs[key].start(props)
  );
  const cancel = props.cancel === true || getDefaultProp(props, "cancel") === true;
  if (asyncTo || cancel && state.asyncId) {
    promises.push(
      scheduleProps(++ctrl["_lastAsyncId"], {
        props,
        state,
        actions: {
          pause: noop,
          resume: noop,
          start(props2, resolve) {
            if (cancel) {
              stopAsync(state, ctrl["_lastAsyncId"]);
              resolve(getCancelledResult(ctrl));
            } else {
              props2.onRest = onRest;
              resolve(
                runAsync(
                  asyncTo,
                  props2,
                  state,
                  ctrl
                )
              );
            }
          }
        }
      })
    );
  }
  if (state.paused) {
    await new Promise((resume) => {
      state.resumeQueue.add(resume);
    });
  }
  const result = getCombinedResult(ctrl, await Promise.all(promises));
  if (loop2 && result.finished && !(isLoop && result.noop)) {
    const nextProps = createLoopUpdate(props, loop2, to22);
    if (nextProps) {
      prepareKeys(ctrl, [nextProps]);
      return flushUpdate(ctrl, nextProps, true);
    }
  }
  if (onResolve) {
    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));
  }
  return result;
}
function getSprings(ctrl, props) {
  const springs = { ...ctrl.springs };
  if (props) {
    each(toArray(props), (props2) => {
      if (is.und(props2.keys)) {
        props2 = createUpdate(props2);
      }
      if (!is.obj(props2.to)) {
        props2 = { ...props2, to: void 0 };
      }
      prepareSprings(springs, props2, (key) => {
        return createSpring(key);
      });
    });
  }
  setSprings(ctrl, springs);
  return springs;
}
function setSprings(ctrl, springs) {
  eachProp(springs, (spring, key) => {
    if (!ctrl.springs[key]) {
      ctrl.springs[key] = spring;
      addFluidObserver(spring, ctrl);
    }
  });
}
function createSpring(key, observer) {
  const spring = new SpringValue();
  spring.key = key;
  if (observer) {
    addFluidObserver(spring, observer);
  }
  return spring;
}
function prepareSprings(springs, props, create) {
  if (props.keys) {
    each(props.keys, (key) => {
      const spring = springs[key] || (springs[key] = create(key));
      spring["_prepareNode"](props);
    });
  }
}
function prepareKeys(ctrl, queue) {
  each(queue, (props) => {
    prepareSprings(ctrl.springs, props, (key) => {
      return createSpring(key, ctrl);
    });
  });
}
var SpringContext = ({
  children,
  ...props
}) => {
  const inherited = (0, import_react11.useContext)(ctx);
  const pause = props.pause || !!inherited.pause, immediate = props.immediate || !!inherited.immediate;
  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate]);
  const { Provider } = ctx;
  return React2.createElement(Provider, { value: props }, children);
};
var ctx = makeContext(SpringContext, {});
SpringContext.Provider = ctx.Provider;
SpringContext.Consumer = ctx.Consumer;
function makeContext(target, init) {
  Object.assign(target, React2.createContext(init));
  target.Provider._context = target;
  target.Consumer._context = target;
  return target;
}
var SpringRef = () => {
  const current = [];
  const SpringRef2 = function(props) {
    deprecateDirectCall();
    const results = [];
    each(current, (ctrl, i6) => {
      if (is.und(props)) {
        results.push(ctrl.start());
      } else {
        const update22 = _getProps(props, ctrl, i6);
        if (update22) {
          results.push(ctrl.start(update22));
        }
      }
    });
    return results;
  };
  SpringRef2.current = current;
  SpringRef2.add = function(ctrl) {
    if (!current.includes(ctrl)) {
      current.push(ctrl);
    }
  };
  SpringRef2.delete = function(ctrl) {
    const i6 = current.indexOf(ctrl);
    if (~i6)
      current.splice(i6, 1);
  };
  SpringRef2.pause = function() {
    each(current, (ctrl) => ctrl.pause(...arguments));
    return this;
  };
  SpringRef2.resume = function() {
    each(current, (ctrl) => ctrl.resume(...arguments));
    return this;
  };
  SpringRef2.set = function(values) {
    each(current, (ctrl, i6) => {
      const update22 = is.fun(values) ? values(i6, ctrl) : values;
      if (update22) {
        ctrl.set(update22);
      }
    });
  };
  SpringRef2.start = function(props) {
    const results = [];
    each(current, (ctrl, i6) => {
      if (is.und(props)) {
        results.push(ctrl.start());
      } else {
        const update22 = this._getProps(props, ctrl, i6);
        if (update22) {
          results.push(ctrl.start(update22));
        }
      }
    });
    return results;
  };
  SpringRef2.stop = function() {
    each(current, (ctrl) => ctrl.stop(...arguments));
    return this;
  };
  SpringRef2.update = function(props) {
    each(current, (ctrl, i6) => ctrl.update(this._getProps(props, ctrl, i6)));
    return this;
  };
  const _getProps = function(arg, ctrl, index3) {
    return is.fun(arg) ? arg(index3, ctrl) : arg;
  };
  SpringRef2._getProps = _getProps;
  return SpringRef2;
};
function useSprings(length, props, deps) {
  const propsFn = is.fun(props) && props;
  if (propsFn && !deps)
    deps = [];
  const ref = (0, import_react10.useMemo)(
    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,
    []
  );
  const layoutId = (0, import_react10.useRef)(0);
  const forceUpdate = useForceUpdate();
  const state = (0, import_react10.useMemo)(
    () => ({
      ctrls: [],
      queue: [],
      flush(ctrl, updates2) {
        const springs2 = getSprings(ctrl, updates2);
        const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some((key) => !ctrl.springs[key]);
        return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise((resolve) => {
          setSprings(ctrl, springs2);
          state.queue.push(() => {
            resolve(flushUpdateQueue(ctrl, updates2));
          });
          forceUpdate();
        });
      }
    }),
    []
  );
  const ctrls = (0, import_react10.useRef)([...state.ctrls]);
  const updates = [];
  const prevLength = usePrev(length) || 0;
  (0, import_react10.useMemo)(() => {
    each(ctrls.current.slice(length, prevLength), (ctrl) => {
      detachRefs(ctrl, ref);
      ctrl.stop(true);
    });
    ctrls.current.length = length;
    declareUpdates(prevLength, length);
  }, [length]);
  (0, import_react10.useMemo)(() => {
    declareUpdates(0, Math.min(prevLength, length));
  }, deps);
  function declareUpdates(startIndex, endIndex) {
    for (let i6 = startIndex; i6 < endIndex; i6++) {
      const ctrl = ctrls.current[i6] || (ctrls.current[i6] = new Controller(null, state.flush));
      const update22 = propsFn ? propsFn(i6, ctrl) : props[i6];
      if (update22) {
        updates[i6] = declareUpdate(update22);
      }
    }
  }
  const springs = ctrls.current.map((ctrl, i6) => getSprings(ctrl, updates[i6]));
  const context = (0, import_react10.useContext)(SpringContext);
  const prevContext = usePrev(context);
  const hasContext = context !== prevContext && hasProps(context);
  useIsomorphicLayoutEffect(() => {
    layoutId.current++;
    state.ctrls = ctrls.current;
    const { queue } = state;
    if (queue.length) {
      state.queue = [];
      each(queue, (cb) => cb());
    }
    each(ctrls.current, (ctrl, i6) => {
      ref == null ? void 0 : ref.add(ctrl);
      if (hasContext) {
        ctrl.start({ default: context });
      }
      const update22 = updates[i6];
      if (update22) {
        replaceRef(ctrl, update22.ref);
        if (ctrl.ref) {
          ctrl.queue.push(update22);
        } else {
          ctrl.start(update22);
        }
      }
    });
  });
  useOnce(() => () => {
    each(state.ctrls, (ctrl) => ctrl.stop(true));
  });
  const values = springs.map((x4) => ({ ...x4 }));
  return ref ? [values, ref] : values;
}
function useSpring(props, deps) {
  const isFn = is.fun(props);
  const [[values], ref] = useSprings(
    1,
    isFn ? props : [props],
    isFn ? deps || [] : deps
  );
  return isFn || arguments.length == 2 ? [values, ref] : values;
}
function useTransition(data, props, deps) {
  const propsFn = is.fun(props) && props;
  const {
    reset,
    sort: sort3,
    trail = 0,
    expires = true,
    exitBeforeEnter = false,
    onDestroyed,
    ref: propsRef,
    config: propsConfig
  } = propsFn ? propsFn() : props;
  const ref = (0, import_react13.useMemo)(
    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,
    []
  );
  const items = toArray(data);
  const transitions = [];
  const usedTransitions = (0, import_react13.useRef)(null);
  const prevTransitions = reset ? null : usedTransitions.current;
  useIsomorphicLayoutEffect(() => {
    usedTransitions.current = transitions;
  });
  useOnce(() => {
    each(transitions, (t8) => {
      ref == null ? void 0 : ref.add(t8.ctrl);
      t8.ctrl.ref = ref;
    });
    return () => {
      each(usedTransitions.current, (t8) => {
        if (t8.expired) {
          clearTimeout(t8.expirationId);
        }
        detachRefs(t8.ctrl, ref);
        t8.ctrl.stop(true);
      });
    };
  });
  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);
  const expired = reset && usedTransitions.current || [];
  useIsomorphicLayoutEffect(
    () => each(expired, ({ ctrl, item, key }) => {
      detachRefs(ctrl, ref);
      callProp(onDestroyed, item, key);
    })
  );
  const reused = [];
  if (prevTransitions)
    each(prevTransitions, (t8, i6) => {
      if (t8.expired) {
        clearTimeout(t8.expirationId);
        expired.push(t8);
      } else {
        i6 = reused[i6] = keys.indexOf(t8.key);
        if (~i6)
          transitions[i6] = t8;
      }
    });
  each(items, (item, i6) => {
    if (!transitions[i6]) {
      transitions[i6] = {
        key: keys[i6],
        item,
        phase: "mount",
        ctrl: new Controller()
      };
      transitions[i6].ctrl.item = item;
    }
  });
  if (reused.length) {
    let i6 = -1;
    const { leave } = propsFn ? propsFn() : props;
    each(reused, (keyIndex, prevIndex) => {
      const t8 = prevTransitions[prevIndex];
      if (~keyIndex) {
        i6 = transitions.indexOf(t8);
        transitions[i6] = { ...t8, item: items[keyIndex] };
      } else if (leave) {
        transitions.splice(++i6, 0, t8);
      }
    });
  }
  if (is.fun(sort3)) {
    transitions.sort((a5, b5) => sort3(a5.item, b5.item));
  }
  let delay = -trail;
  const forceUpdate = useForceUpdate();
  const defaultProps = getDefaultProps(props);
  const changes = /* @__PURE__ */ new Map();
  const exitingTransitions = (0, import_react13.useRef)(/* @__PURE__ */ new Map());
  const forceChange = (0, import_react13.useRef)(false);
  each(transitions, (t8, i6) => {
    const key = t8.key;
    const prevPhase = t8.phase;
    const p4 = propsFn ? propsFn() : props;
    let to22;
    let phase;
    const propsDelay = callProp(p4.delay || 0, key);
    if (prevPhase == "mount") {
      to22 = p4.enter;
      phase = "enter";
    } else {
      const isLeave = keys.indexOf(key) < 0;
      if (prevPhase != "leave") {
        if (isLeave) {
          to22 = p4.leave;
          phase = "leave";
        } else if (to22 = p4.update) {
          phase = "update";
        } else
          return;
      } else if (!isLeave) {
        to22 = p4.enter;
        phase = "enter";
      } else
        return;
    }
    to22 = callProp(to22, t8.item, i6);
    to22 = is.obj(to22) ? inferTo(to22) : { to: to22 };
    if (!to22.config) {
      const config2 = propsConfig || defaultProps.config;
      to22.config = callProp(config2, t8.item, i6, phase);
    }
    delay += trail;
    const payload = {
      ...defaultProps,
      // we need to add our props.delay value you here.
      delay: propsDelay + delay,
      ref: propsRef,
      immediate: p4.immediate,
      // This prevents implied resets.
      reset: false,
      // Merge any phase-specific props.
      ...to22
    };
    if (phase == "enter" && is.und(payload.from)) {
      const p22 = propsFn ? propsFn() : props;
      const from = is.und(p22.initial) || prevTransitions ? p22.from : p22.initial;
      payload.from = callProp(from, t8.item, i6);
    }
    const { onResolve } = payload;
    payload.onResolve = (result) => {
      callProp(onResolve, result);
      const transitions2 = usedTransitions.current;
      const t22 = transitions2.find((t32) => t32.key === key);
      if (!t22)
        return;
      if (result.cancelled && t22.phase != "update") {
        return;
      }
      if (t22.ctrl.idle) {
        const idle = transitions2.every((t32) => t32.ctrl.idle);
        if (t22.phase == "leave") {
          const expiry = callProp(expires, t22.item);
          if (expiry !== false) {
            const expiryMs = expiry === true ? 0 : expiry;
            t22.expired = true;
            if (!idle && expiryMs > 0) {
              if (expiryMs <= 2147483647)
                t22.expirationId = setTimeout(forceUpdate, expiryMs);
              return;
            }
          }
        }
        if (idle && transitions2.some((t32) => t32.expired)) {
          exitingTransitions.current.delete(t22);
          if (exitBeforeEnter) {
            forceChange.current = true;
          }
          forceUpdate();
        }
      }
    };
    const springs = getSprings(t8.ctrl, payload);
    if (phase === "leave" && exitBeforeEnter) {
      exitingTransitions.current.set(t8, { phase, springs, payload });
    } else {
      changes.set(t8, { phase, springs, payload });
    }
  });
  const context = (0, import_react13.useContext)(SpringContext);
  const prevContext = usePrev(context);
  const hasContext = context !== prevContext && hasProps(context);
  useIsomorphicLayoutEffect(() => {
    if (hasContext) {
      each(transitions, (t8) => {
        t8.ctrl.start({ default: context });
      });
    }
  }, [context]);
  each(changes, (_3, t8) => {
    if (exitingTransitions.current.size) {
      const ind = transitions.findIndex((state) => state.key === t8.key);
      transitions.splice(ind, 1);
    }
  });
  useIsomorphicLayoutEffect(
    () => {
      each(
        exitingTransitions.current.size ? exitingTransitions.current : changes,
        ({ phase, payload }, t8) => {
          const { ctrl } = t8;
          t8.phase = phase;
          ref == null ? void 0 : ref.add(ctrl);
          if (hasContext && phase == "enter") {
            ctrl.start({ default: context });
          }
          if (payload) {
            replaceRef(ctrl, payload.ref);
            if ((ctrl.ref || ref) && !forceChange.current) {
              ctrl.update(payload);
            } else {
              ctrl.start(payload);
              if (forceChange.current) {
                forceChange.current = false;
              }
            }
          }
        }
      );
    },
    reset ? void 0 : deps
  );
  const renderTransitions = (render) => React22.createElement(React22.Fragment, null, transitions.map((t8, i6) => {
    const { springs } = changes.get(t8) || t8.ctrl;
    const elem = render({ ...springs }, t8.item, t8, i6);
    return elem && elem.type ? React22.createElement(
      elem.type,
      {
        ...elem.props,
        key: is.str(t8.key) || is.num(t8.key) ? t8.key : t8.ctrl.id,
        ref: elem.ref
      }
    ) : elem;
  }));
  return ref ? [renderTransitions, ref] : renderTransitions;
}
var nextKey = 1;
function getKeys(items, { key, keys = key }, prevTransitions) {
  if (keys === null) {
    const reused = /* @__PURE__ */ new Set();
    return items.map((item) => {
      const t8 = prevTransitions && prevTransitions.find(
        (t22) => t22.item === item && t22.phase !== "leave" && !reused.has(t22)
      );
      if (t8) {
        reused.add(t8);
        return t8.key;
      }
      return nextKey++;
    });
  }
  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys);
}
var Interpolation = class extends FrameValue {
  constructor(source, args) {
    super();
    this.source = source;
    this.idle = true;
    this._active = /* @__PURE__ */ new Set();
    this.calc = createInterpolator(...args);
    const value = this._get();
    const nodeType = getAnimatedType(value);
    setAnimated(this, nodeType.create(value));
  }
  advance(_dt) {
    const value = this._get();
    const oldValue = this.get();
    if (!isEqual(value, oldValue)) {
      getAnimated(this).setValue(value);
      this._onChange(value, this.idle);
    }
    if (!this.idle && checkIdle(this._active)) {
      becomeIdle(this);
    }
  }
  _get() {
    const inputs = is.arr(this.source) ? this.source.map(getFluidValue) : toArray(getFluidValue(this.source));
    return this.calc(...inputs);
  }
  _start() {
    if (this.idle && !checkIdle(this._active)) {
      this.idle = false;
      each(getPayload(this), (node) => {
        node.done = false;
      });
      if (globals_exports.skipAnimation) {
        raf.batchedUpdates(() => this.advance());
        becomeIdle(this);
      } else {
        frameLoop.start(this);
      }
    }
  }
  // Observe our sources only when we're observed.
  _attach() {
    let priority2 = 1;
    each(toArray(this.source), (source) => {
      if (hasFluidValue(source)) {
        addFluidObserver(source, this);
      }
      if (isFrameValue(source)) {
        if (!source.idle) {
          this._active.add(source);
        }
        priority2 = Math.max(priority2, source.priority + 1);
      }
    });
    this.priority = priority2;
    this._start();
  }
  // Stop observing our sources once we have no observers.
  _detach() {
    each(toArray(this.source), (source) => {
      if (hasFluidValue(source)) {
        removeFluidObserver(source, this);
      }
    });
    this._active.clear();
    becomeIdle(this);
  }
  /** @internal */
  eventObserved(event) {
    if (event.type == "change") {
      if (event.idle) {
        this.advance();
      } else {
        this._active.add(event.parent);
        this._start();
      }
    } else if (event.type == "idle") {
      this._active.delete(event.parent);
    } else if (event.type == "priority") {
      this.priority = toArray(this.source).reduce(
        (highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),
        0
      );
    }
  }
};
function isIdle(source) {
  return source.idle !== false;
}
function checkIdle(active) {
  return !active.size || Array.from(active).every(isIdle);
}
function becomeIdle(self) {
  if (!self.idle) {
    self.idle = true;
    each(getPayload(self), (node) => {
      node.done = true;
    });
    callFluidObservers(self, {
      type: "idle",
      parent: self
    });
  }
}
var to2 = (source, ...args) => new Interpolation(source, args);
globals_exports.assign({
  createStringInterpolator: createStringInterpolator2,
  to: (source, args) => new Interpolation(source, args)
});
var update2 = frameLoop.advance;

// node_modules/@react-spring/web/dist/react-spring_web.modern.mjs
var import_react_dom = __toESM(require_react_dom(), 1);
var isCustomPropRE = /^--/;
function dangerousStyleValue(name, value) {
  if (value == null || typeof value === "boolean" || value === "")
    return "";
  if (typeof value === "number" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))
    return value + "px";
  return ("" + value).trim();
}
var attributeCache = {};
function applyAnimatedValues(instance, props) {
  if (!instance.nodeType || !instance.setAttribute) {
    return false;
  }
  const isFilterElement = instance.nodeName === "filter" || instance.parentNode && instance.parentNode.nodeName === "filter";
  const { style, children, scrollTop, scrollLeft, viewBox, ...attributes } = props;
  const values = Object.values(attributes);
  const names = Object.keys(attributes).map(
    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(
      /([A-Z])/g,
      // Attributes are written in dash case
      (n7) => "-" + n7.toLowerCase()
    ))
  );
  if (children !== void 0) {
    instance.textContent = children;
  }
  for (const name in style) {
    if (style.hasOwnProperty(name)) {
      const value = dangerousStyleValue(name, style[name]);
      if (isCustomPropRE.test(name)) {
        instance.style.setProperty(name, value);
      } else {
        instance.style[name] = value;
      }
    }
  }
  names.forEach((name, i6) => {
    instance.setAttribute(name, values[i6]);
  });
  if (scrollTop !== void 0) {
    instance.scrollTop = scrollTop;
  }
  if (scrollLeft !== void 0) {
    instance.scrollLeft = scrollLeft;
  }
  if (viewBox !== void 0) {
    instance.setAttribute("viewBox", viewBox);
  }
}
var isUnitlessNumber = {
  animationIterationCount: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var prefixKey = (prefix2, key) => prefix2 + key.charAt(0).toUpperCase() + key.substring(1);
var prefixes = ["Webkit", "Ms", "Moz", "O"];
isUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {
  prefixes.forEach((prefix2) => acc[prefixKey(prefix2, prop)] = acc[prop]);
  return acc;
}, isUnitlessNumber);
var domTransforms = /^(matrix|translate|scale|rotate|skew)/;
var pxTransforms = /^(translate)/;
var degTransforms = /^(rotate|skew)/;
var addUnit = (value, unit2) => is.num(value) && value !== 0 ? value + unit2 : value;
var isValueIdentity = (value, id) => is.arr(value) ? value.every((v6) => isValueIdentity(v6, id)) : is.num(value) ? value === id : parseFloat(value) === id;
var AnimatedStyle = class extends AnimatedObject {
  constructor({ x: x4, y: y4, z: z6, ...style }) {
    const inputs = [];
    const transforms = [];
    if (x4 || y4 || z6) {
      inputs.push([x4 || 0, y4 || 0, z6 || 0]);
      transforms.push((xyz) => [
        `translate3d(${xyz.map((v6) => addUnit(v6, "px")).join(",")})`,
        // prettier-ignore
        isValueIdentity(xyz, 0)
      ]);
    }
    eachProp(style, (value, key) => {
      if (key === "transform") {
        inputs.push([value || ""]);
        transforms.push((transform) => [transform, transform === ""]);
      } else if (domTransforms.test(key)) {
        delete style[key];
        if (is.und(value))
          return;
        const unit2 = pxTransforms.test(key) ? "px" : degTransforms.test(key) ? "deg" : "";
        inputs.push(toArray(value));
        transforms.push(
          key === "rotate3d" ? ([x22, y22, z22, deg]) => [
            `rotate3d(${x22},${y22},${z22},${addUnit(deg, unit2)})`,
            isValueIdentity(deg, 0)
          ] : (input) => [
            `${key}(${input.map((v6) => addUnit(v6, unit2)).join(",")})`,
            isValueIdentity(input, key.startsWith("scale") ? 1 : 0)
          ]
        );
      }
    });
    if (inputs.length) {
      style.transform = new FluidTransform(inputs, transforms);
    }
    super(style);
  }
};
var FluidTransform = class extends FluidValue {
  constructor(inputs, transforms) {
    super();
    this.inputs = inputs;
    this.transforms = transforms;
    this._value = null;
  }
  get() {
    return this._value || (this._value = this._get());
  }
  _get() {
    let transform = "";
    let identity4 = true;
    each(this.inputs, (input, i6) => {
      const arg1 = getFluidValue(input[0]);
      const [t8, id] = this.transforms[i6](
        is.arr(arg1) ? arg1 : input.map(getFluidValue)
      );
      transform += " " + t8;
      identity4 = identity4 && id;
    });
    return identity4 ? "none" : transform;
  }
  // Start observing our inputs once we have an observer.
  observerAdded(count3) {
    if (count3 == 1)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && addFluidObserver(value, this)
        )
      );
  }
  // Stop observing our inputs once we have no observers.
  observerRemoved(count3) {
    if (count3 == 0)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && removeFluidObserver(value, this)
        )
      );
  }
  eventObserved(event) {
    if (event.type == "change") {
      this._value = null;
    }
    callFluidObservers(this, event);
  }
};
var primitives = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "big",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rp",
  "rt",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr",
  // SVG
  "circle",
  "clipPath",
  "defs",
  "ellipse",
  "foreignObject",
  "g",
  "image",
  "line",
  "linearGradient",
  "mask",
  "path",
  "pattern",
  "polygon",
  "polyline",
  "radialGradient",
  "rect",
  "stop",
  "svg",
  "text",
  "tspan"
];
globals_exports.assign({
  batchedUpdates: import_react_dom.unstable_batchedUpdates,
  createStringInterpolator: createStringInterpolator2,
  colors: colors2
});
var host = createHost(primitives, {
  applyAnimatedValues,
  createAnimatedStyle: (style) => new AnimatedStyle(style),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props
});
var animated = host.animated;

// node_modules/@nivo/tooltip/dist/nivo-tooltip.es.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
function v() {
  return v = Object.assign ? Object.assign.bind() : function(t8) {
    for (var i6 = 1; i6 < arguments.length; i6++) {
      var o5 = arguments[i6];
      for (var n7 in o5) Object.prototype.hasOwnProperty.call(o5, n7) && (t8[n7] = o5[n7]);
    }
    return t8;
  }, v.apply(this, arguments);
}
var x = { pointerEvents: "none", position: "absolute", zIndex: 10, top: 0, left: 0 };
var m = function(t8, i6) {
  return "translate(" + t8 + "px, " + i6 + "px)";
};
var b = (0, import_react15.memo)(function(t8) {
  var o5, n7 = t8.position, r7 = t8.anchor, e11 = t8.children, l5 = zt(), d3 = Ur(), y4 = d3.animate, f3 = d3.config, b5 = kt(), g4 = b5[0], w5 = b5[1], T4 = (0, import_react15.useRef)(false), C5 = void 0, E3 = false, P4 = w5.width > 0 && w5.height > 0, j3 = Math.round(n7[0]), N2 = Math.round(n7[1]);
  P4 && ("top" === r7 ? (j3 -= w5.width / 2, N2 -= w5.height + 14) : "right" === r7 ? (j3 += 14, N2 -= w5.height / 2) : "bottom" === r7 ? (j3 -= w5.width / 2, N2 += 14) : "left" === r7 ? (j3 -= w5.width + 14, N2 -= w5.height / 2) : "center" === r7 && (j3 -= w5.width / 2, N2 -= w5.height / 2), C5 = { transform: m(j3, N2) }, T4.current || (E3 = true), T4.current = [j3, N2]);
  var O5 = useSpring({ to: C5, config: f3, immediate: !y4 || E3 }), V2 = v({}, x, l5.tooltip.wrapper, { transform: null != (o5 = O5.transform) ? o5 : m(j3, N2), opacity: O5.transform ? 1 : 0 });
  return (0, import_jsx_runtime.jsx)(animated.div, { ref: g4, style: V2, children: e11 });
});
b.displayName = "TooltipWrapper";
var g = (0, import_react15.memo)(function(t8) {
  var i6 = t8.size, o5 = void 0 === i6 ? 12 : i6, n7 = t8.color, r7 = t8.style;
  return (0, import_jsx_runtime.jsx)("span", { style: v({ display: "block", width: o5, height: o5, background: n7 }, void 0 === r7 ? {} : r7) });
});
var w = (0, import_react15.memo)(function(t8) {
  var i6, o5 = t8.id, n7 = t8.value, r7 = t8.format, e11 = t8.enableChip, l5 = void 0 !== e11 && e11, a5 = t8.color, c9 = t8.renderContent, h2 = zt(), u6 = Ot(r7);
  if ("function" == typeof c9) i6 = c9();
  else {
    var f3 = n7;
    void 0 !== u6 && void 0 !== f3 && (f3 = u6(f3)), i6 = (0, import_jsx_runtime.jsxs)("div", { style: h2.tooltip.basic, children: [l5 && (0, import_jsx_runtime.jsx)(g, { color: a5, style: h2.tooltip.chip }), void 0 !== f3 ? (0, import_jsx_runtime.jsxs)("span", { children: [o5, ": ", (0, import_jsx_runtime.jsx)("strong", { children: "" + f3 })] }) : o5] });
  }
  return (0, import_jsx_runtime.jsx)("div", { style: h2.tooltip.container, children: i6 });
});
var T = { width: "100%", borderCollapse: "collapse" };
var C = (0, import_react15.memo)(function(t8) {
  var i6, o5 = t8.title, n7 = t8.rows, r7 = void 0 === n7 ? [] : n7, e11 = t8.renderContent, l5 = zt();
  return r7.length ? (i6 = "function" == typeof e11 ? e11() : (0, import_jsx_runtime.jsxs)("div", { children: [o5 && o5, (0, import_jsx_runtime.jsx)("table", { style: v({}, T, l5.tooltip.table), children: (0, import_jsx_runtime.jsx)("tbody", { children: r7.map(function(t9, i7) {
    return (0, import_jsx_runtime.jsx)("tr", { children: t9.map(function(t10, i8) {
      return (0, import_jsx_runtime.jsx)("td", { style: l5.tooltip.tableCell, children: t10 }, i8);
    }) }, i7);
  }) }) })] }), (0, import_jsx_runtime.jsx)("div", { style: l5.tooltip.container, children: i6 })) : null;
});
C.displayName = "TableTooltip";
var E = (0, import_react15.memo)(function(t8) {
  var i6 = t8.x0, n7 = t8.x1, r7 = t8.y0, e11 = t8.y1, l5 = zt(), u6 = Ur(), d3 = u6.animate, y4 = u6.config, f3 = (0, import_react15.useMemo)(function() {
    return v({}, l5.crosshair.line, { pointerEvents: "none" });
  }, [l5.crosshair.line]), x4 = useSpring({ x1: i6, x2: n7, y1: r7, y2: e11, config: y4, immediate: !d3 });
  return (0, import_jsx_runtime.jsx)(animated.line, v({}, x4, { fill: "none", style: f3 }));
});
E.displayName = "CrosshairLine";
var P = (0, import_react15.memo)(function(t8) {
  var i6, o5, n7 = t8.width, r7 = t8.height, e11 = t8.type, l5 = t8.x, a5 = t8.y;
  return "cross" === e11 ? (i6 = { x0: l5, x1: l5, y0: 0, y1: r7 }, o5 = { x0: 0, x1: n7, y0: a5, y1: a5 }) : "top-left" === e11 ? (i6 = { x0: l5, x1: l5, y0: 0, y1: a5 }, o5 = { x0: 0, x1: l5, y0: a5, y1: a5 }) : "top" === e11 ? i6 = { x0: l5, x1: l5, y0: 0, y1: a5 } : "top-right" === e11 ? (i6 = { x0: l5, x1: l5, y0: 0, y1: a5 }, o5 = { x0: l5, x1: n7, y0: a5, y1: a5 }) : "right" === e11 ? o5 = { x0: l5, x1: n7, y0: a5, y1: a5 } : "bottom-right" === e11 ? (i6 = { x0: l5, x1: l5, y0: a5, y1: r7 }, o5 = { x0: l5, x1: n7, y0: a5, y1: a5 }) : "bottom" === e11 ? i6 = { x0: l5, x1: l5, y0: a5, y1: r7 } : "bottom-left" === e11 ? (i6 = { x0: l5, x1: l5, y0: a5, y1: r7 }, o5 = { x0: 0, x1: l5, y0: a5, y1: a5 }) : "left" === e11 ? o5 = { x0: 0, x1: l5, y0: a5, y1: a5 } : "x" === e11 ? i6 = { x0: l5, x1: l5, y0: 0, y1: r7 } : "y" === e11 && (o5 = { x0: 0, x1: n7, y0: a5, y1: a5 }), (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [i6 && (0, import_jsx_runtime.jsx)(E, { x0: i6.x0, x1: i6.x1, y0: i6.y0, y1: i6.y1 }), o5 && (0, import_jsx_runtime.jsx)(E, { x0: o5.x0, x1: o5.x1, y0: o5.y0, y1: o5.y1 })] });
});
P.displayName = "Crosshair";
var j = (0, import_react15.createContext)({ showTooltipAt: function() {
}, showTooltipFromEvent: function() {
}, hideTooltip: function() {
} });
var N = { isVisible: false, position: [null, null], content: null, anchor: null };
var O = (0, import_react15.createContext)(N);
var V = function(t8) {
  var i6 = (0, import_react15.useState)(N), n7 = i6[0], l5 = i6[1], a5 = (0, import_react15.useCallback)(function(t9, i7, o5) {
    var n8 = i7[0], r7 = i7[1];
    void 0 === o5 && (o5 = "top"), l5({ isVisible: true, position: [n8, r7], anchor: o5, content: t9 });
  }, [l5]), c9 = (0, import_react15.useCallback)(function(i7, o5, n8) {
    void 0 === n8 && (n8 = "top");
    var r7 = t8.current.getBoundingClientRect(), e11 = t8.current.offsetWidth, a6 = e11 === r7.width ? 1 : e11 / r7.width, c10 = "touches" in o5 ? o5.touches[0] : o5, s6 = c10.clientX, h2 = c10.clientY, u6 = (s6 - r7.left) * a6, d3 = (h2 - r7.top) * a6;
    "left" !== n8 && "right" !== n8 || (n8 = u6 < r7.width / 2 ? "right" : "left"), l5({ isVisible: true, position: [u6, d3], anchor: n8, content: i7 });
  }, [t8, l5]), s5 = (0, import_react15.useCallback)(function() {
    l5(N);
  }, [l5]);
  return { actions: (0, import_react15.useMemo)(function() {
    return { showTooltipAt: a5, showTooltipFromEvent: c9, hideTooltip: s5 };
  }, [a5, c9, s5]), state: n7 };
};
var k = function() {
  var t8 = (0, import_react15.useContext)(j);
  if (void 0 === t8) throw new Error("useTooltip must be used within a TooltipProvider");
  return t8;
};
var z = function() {
  var t8 = (0, import_react15.useContext)(O);
  if (void 0 === t8) throw new Error("useTooltipState must be used within a TooltipProvider");
  return t8;
};
var A = function(t8) {
  return t8.isVisible;
};
var F = function() {
  var t8 = z();
  return A(t8) ? (0, import_jsx_runtime.jsx)(b, { position: t8.position, anchor: t8.anchor, children: t8.content }) : null;
};
var M = function(t8) {
  var i6 = t8.container, o5 = t8.children, n7 = V(i6), r7 = n7.actions, e11 = n7.state;
  return (0, import_jsx_runtime.jsx)(j.Provider, { value: r7, children: (0, import_jsx_runtime.jsx)(O.Provider, { value: e11, children: o5 }) });
};

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_merge3 = __toESM(require_merge());
var import_get = __toESM(require_get());
var import_set2 = __toESM(require_set());
var import_isString = __toESM(require_isString());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_last = __toESM(require_last());
var import_isArray = __toESM(require_isArray());

// node_modules/d3-array/src/ascending.js
function ascending(a5, b5) {
  return a5 == null || b5 == null ? NaN : a5 < b5 ? -1 : a5 > b5 ? 1 : a5 >= b5 ? 0 : NaN;
}

// node_modules/d3-array/src/descending.js
function descending(a5, b5) {
  return a5 == null || b5 == null ? NaN : b5 < a5 ? -1 : b5 > a5 ? 1 : b5 >= a5 ? 0 : NaN;
}

// node_modules/d3-array/src/bisector.js
function bisector(f3) {
  let compare1, compare2, delta;
  if (f3.length !== 2) {
    compare1 = ascending;
    compare2 = (d3, x4) => ascending(f3(d3), x4);
    delta = (d3, x4) => f3(d3) - x4;
  } else {
    compare1 = f3 === ascending || f3 === descending ? f3 : zero;
    compare2 = f3;
    delta = f3;
  }
  function left(a5, x4, lo = 0, hi = a5.length) {
    if (lo < hi) {
      if (compare1(x4, x4) !== 0) return hi;
      do {
        const mid = lo + hi >>> 1;
        if (compare2(a5[mid], x4) < 0) lo = mid + 1;
        else hi = mid;
      } while (lo < hi);
    }
    return lo;
  }
  function right(a5, x4, lo = 0, hi = a5.length) {
    if (lo < hi) {
      if (compare1(x4, x4) !== 0) return hi;
      do {
        const mid = lo + hi >>> 1;
        if (compare2(a5[mid], x4) <= 0) lo = mid + 1;
        else hi = mid;
      } while (lo < hi);
    }
    return lo;
  }
  function center(a5, x4, lo = 0, hi = a5.length) {
    const i6 = left(a5, x4, lo, hi - 1);
    return i6 > lo && delta(a5[i6 - 1], x4) > -delta(a5[i6], x4) ? i6 - 1 : i6;
  }
  return { left, center, right };
}
function zero() {
  return 0;
}

// node_modules/d3-array/src/number.js
function number(x4) {
  return x4 === null ? NaN : +x4;
}

// node_modules/d3-array/src/bisect.js
var ascendingBisect = bisector(ascending);
var bisectRight = ascendingBisect.right;
var bisectLeft = ascendingBisect.left;
var bisectCenter = bisector(number).center;
var bisect_default = bisectRight;

// node_modules/d3-array/src/blur.js
var blur2 = Blur2(blurf);
var blurImage = Blur2(blurfImage);
function Blur2(blur3) {
  return function(data, rx, ry = rx) {
    if (!((rx = +rx) >= 0)) throw new RangeError("invalid rx");
    if (!((ry = +ry) >= 0)) throw new RangeError("invalid ry");
    let { data: values, width, height } = data;
    if (!((width = Math.floor(width)) >= 0)) throw new RangeError("invalid width");
    if (!((height = Math.floor(height !== void 0 ? height : values.length / width)) >= 0)) throw new RangeError("invalid height");
    if (!width || !height || !rx && !ry) return data;
    const blurx = rx && blur3(rx);
    const blury = ry && blur3(ry);
    const temp = values.slice();
    if (blurx && blury) {
      blurh(blurx, temp, values, width, height);
      blurh(blurx, values, temp, width, height);
      blurh(blurx, temp, values, width, height);
      blurv(blury, values, temp, width, height);
      blurv(blury, temp, values, width, height);
      blurv(blury, values, temp, width, height);
    } else if (blurx) {
      blurh(blurx, values, temp, width, height);
      blurh(blurx, temp, values, width, height);
      blurh(blurx, values, temp, width, height);
    } else if (blury) {
      blurv(blury, values, temp, width, height);
      blurv(blury, temp, values, width, height);
      blurv(blury, values, temp, width, height);
    }
    return data;
  };
}
function blurh(blur3, T4, S3, w5, h2) {
  for (let y4 = 0, n7 = w5 * h2; y4 < n7; ) {
    blur3(T4, S3, y4, y4 += w5, 1);
  }
}
function blurv(blur3, T4, S3, w5, h2) {
  for (let x4 = 0, n7 = w5 * h2; x4 < w5; ++x4) {
    blur3(T4, S3, x4, x4 + n7, w5);
  }
}
function blurfImage(radius) {
  const blur3 = blurf(radius);
  return (T4, S3, start2, stop2, step) => {
    start2 <<= 2, stop2 <<= 2, step <<= 2;
    blur3(T4, S3, start2 + 0, stop2 + 0, step);
    blur3(T4, S3, start2 + 1, stop2 + 1, step);
    blur3(T4, S3, start2 + 2, stop2 + 2, step);
    blur3(T4, S3, start2 + 3, stop2 + 3, step);
  };
}
function blurf(radius) {
  const radius0 = Math.floor(radius);
  if (radius0 === radius) return bluri(radius);
  const t8 = radius - radius0;
  const w5 = 2 * radius + 1;
  return (T4, S3, start2, stop2, step) => {
    if (!((stop2 -= step) >= start2)) return;
    let sum5 = radius0 * S3[start2];
    const s0 = step * radius0;
    const s1 = s0 + step;
    for (let i6 = start2, j3 = start2 + s0; i6 < j3; i6 += step) {
      sum5 += S3[Math.min(stop2, i6)];
    }
    for (let i6 = start2, j3 = stop2; i6 <= j3; i6 += step) {
      sum5 += S3[Math.min(stop2, i6 + s0)];
      T4[i6] = (sum5 + t8 * (S3[Math.max(start2, i6 - s1)] + S3[Math.min(stop2, i6 + s1)])) / w5;
      sum5 -= S3[Math.max(start2, i6 - s0)];
    }
  };
}
function bluri(radius) {
  const w5 = 2 * radius + 1;
  return (T4, S3, start2, stop2, step) => {
    if (!((stop2 -= step) >= start2)) return;
    let sum5 = radius * S3[start2];
    const s5 = step * radius;
    for (let i6 = start2, j3 = start2 + s5; i6 < j3; i6 += step) {
      sum5 += S3[Math.min(stop2, i6)];
    }
    for (let i6 = start2, j3 = stop2; i6 <= j3; i6 += step) {
      sum5 += S3[Math.min(stop2, i6 + s5)];
      T4[i6] = sum5 / w5;
      sum5 -= S3[Math.max(start2, i6 - s5)];
    }
  };
}

// node_modules/internmap/src/index.js
var InternMap = class extends Map {
  constructor(entries, key = keyof) {
    super();
    Object.defineProperties(this, { _intern: { value: /* @__PURE__ */ new Map() }, _key: { value: key } });
    if (entries != null) for (const [key2, value] of entries) this.set(key2, value);
  }
  get(key) {
    return super.get(intern_get(this, key));
  }
  has(key) {
    return super.has(intern_get(this, key));
  }
  set(key, value) {
    return super.set(intern_set(this, key), value);
  }
  delete(key) {
    return super.delete(intern_delete(this, key));
  }
};
function intern_get({ _intern, _key }, value) {
  const key = _key(value);
  return _intern.has(key) ? _intern.get(key) : value;
}
function intern_set({ _intern, _key }, value) {
  const key = _key(value);
  if (_intern.has(key)) return _intern.get(key);
  _intern.set(key, value);
  return value;
}
function intern_delete({ _intern, _key }, value) {
  const key = _key(value);
  if (_intern.has(key)) {
    value = _intern.get(key);
    _intern.delete(key);
  }
  return value;
}
function keyof(value) {
  return value !== null && typeof value === "object" ? value.valueOf() : value;
}

// node_modules/d3-array/src/array.js
var array = Array.prototype;
var slice = array.slice;
var map = array.map;

// node_modules/d3-array/src/ticks.js
var e10 = Math.sqrt(50);
var e5 = Math.sqrt(10);
var e2 = Math.sqrt(2);
function tickSpec(start2, stop2, count3) {
  const step = (stop2 - start2) / Math.max(0, count3), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;
  let i1, i22, inc;
  if (power < 0) {
    inc = Math.pow(10, -power) / factor;
    i1 = Math.round(start2 * inc);
    i22 = Math.round(stop2 * inc);
    if (i1 / inc < start2) ++i1;
    if (i22 / inc > stop2) --i22;
    inc = -inc;
  } else {
    inc = Math.pow(10, power) * factor;
    i1 = Math.round(start2 / inc);
    i22 = Math.round(stop2 / inc);
    if (i1 * inc < start2) ++i1;
    if (i22 * inc > stop2) --i22;
  }
  if (i22 < i1 && 0.5 <= count3 && count3 < 2) return tickSpec(start2, stop2, count3 * 2);
  return [i1, i22, inc];
}
function ticks(start2, stop2, count3) {
  stop2 = +stop2, start2 = +start2, count3 = +count3;
  if (!(count3 > 0)) return [];
  if (start2 === stop2) return [start2];
  const reverse3 = stop2 < start2, [i1, i22, inc] = reverse3 ? tickSpec(stop2, start2, count3) : tickSpec(start2, stop2, count3);
  if (!(i22 >= i1)) return [];
  const n7 = i22 - i1 + 1, ticks2 = new Array(n7);
  if (reverse3) {
    if (inc < 0) for (let i6 = 0; i6 < n7; ++i6) ticks2[i6] = (i22 - i6) / -inc;
    else for (let i6 = 0; i6 < n7; ++i6) ticks2[i6] = (i22 - i6) * inc;
  } else {
    if (inc < 0) for (let i6 = 0; i6 < n7; ++i6) ticks2[i6] = (i1 + i6) / -inc;
    else for (let i6 = 0; i6 < n7; ++i6) ticks2[i6] = (i1 + i6) * inc;
  }
  return ticks2;
}
function tickIncrement(start2, stop2, count3) {
  stop2 = +stop2, start2 = +start2, count3 = +count3;
  return tickSpec(start2, stop2, count3)[2];
}
function tickStep(start2, stop2, count3) {
  stop2 = +stop2, start2 = +start2, count3 = +count3;
  const reverse3 = stop2 < start2, inc = reverse3 ? tickIncrement(stop2, start2, count3) : tickIncrement(start2, stop2, count3);
  return (reverse3 ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);
}

// node_modules/d3-array/src/range.js
function range(start2, stop2, step) {
  start2 = +start2, stop2 = +stop2, step = (n7 = arguments.length) < 2 ? (stop2 = start2, start2 = 0, 1) : n7 < 3 ? 1 : +step;
  var i6 = -1, n7 = Math.max(0, Math.ceil((stop2 - start2) / step)) | 0, range2 = new Array(n7);
  while (++i6 < n7) {
    range2[i6] = start2 + i6 * step;
  }
  return range2;
}

// node_modules/d3-array/src/shuffle.js
var shuffle_default = shuffler(Math.random);
function shuffler(random) {
  return function shuffle(array3, i0 = 0, i1 = array3.length) {
    let m5 = i1 - (i0 = +i0);
    while (m5) {
      const i6 = random() * m5-- | 0, t8 = array3[m5 + i0];
      array3[m5 + i0] = array3[i6 + i0];
      array3[i6 + i0] = t8;
    }
    return array3;
  };
}

// node_modules/d3-scale/src/init.js
function initRange(domain, range2) {
  switch (arguments.length) {
    case 0:
      break;
    case 1:
      this.range(domain);
      break;
    default:
      this.range(range2).domain(domain);
      break;
  }
  return this;
}

// node_modules/d3-scale/src/ordinal.js
var implicit = Symbol("implicit");
function ordinal() {
  var index3 = new InternMap(), domain = [], range2 = [], unknown = implicit;
  function scale2(d3) {
    let i6 = index3.get(d3);
    if (i6 === void 0) {
      if (unknown !== implicit) return unknown;
      index3.set(d3, i6 = domain.push(d3) - 1);
    }
    return range2[i6 % range2.length];
  }
  scale2.domain = function(_3) {
    if (!arguments.length) return domain.slice();
    domain = [], index3 = new InternMap();
    for (const value of _3) {
      if (index3.has(value)) continue;
      index3.set(value, domain.push(value) - 1);
    }
    return scale2;
  };
  scale2.range = function(_3) {
    return arguments.length ? (range2 = Array.from(_3), scale2) : range2.slice();
  };
  scale2.unknown = function(_3) {
    return arguments.length ? (unknown = _3, scale2) : unknown;
  };
  scale2.copy = function() {
    return ordinal(domain, range2).unknown(unknown);
  };
  initRange.apply(scale2, arguments);
  return scale2;
}

// node_modules/d3-scale/src/band.js
function band() {
  var scale2 = ordinal().unknown(void 0), domain = scale2.domain, ordinalRange = scale2.range, r0 = 0, r1 = 1, step, bandwidth, round = false, paddingInner = 0, paddingOuter = 0, align = 0.5;
  delete scale2.unknown;
  function rescale() {
    var n7 = domain().length, reverse3 = r1 < r0, start2 = reverse3 ? r1 : r0, stop2 = reverse3 ? r0 : r1;
    step = (stop2 - start2) / Math.max(1, n7 - paddingInner + paddingOuter * 2);
    if (round) step = Math.floor(step);
    start2 += (stop2 - start2 - step * (n7 - paddingInner)) * align;
    bandwidth = step * (1 - paddingInner);
    if (round) start2 = Math.round(start2), bandwidth = Math.round(bandwidth);
    var values = range(n7).map(function(i6) {
      return start2 + step * i6;
    });
    return ordinalRange(reverse3 ? values.reverse() : values);
  }
  scale2.domain = function(_3) {
    return arguments.length ? (domain(_3), rescale()) : domain();
  };
  scale2.range = function(_3) {
    return arguments.length ? ([r0, r1] = _3, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];
  };
  scale2.rangeRound = function(_3) {
    return [r0, r1] = _3, r0 = +r0, r1 = +r1, round = true, rescale();
  };
  scale2.bandwidth = function() {
    return bandwidth;
  };
  scale2.step = function() {
    return step;
  };
  scale2.round = function(_3) {
    return arguments.length ? (round = !!_3, rescale()) : round;
  };
  scale2.padding = function(_3) {
    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_3), rescale()) : paddingInner;
  };
  scale2.paddingInner = function(_3) {
    return arguments.length ? (paddingInner = Math.min(1, _3), rescale()) : paddingInner;
  };
  scale2.paddingOuter = function(_3) {
    return arguments.length ? (paddingOuter = +_3, rescale()) : paddingOuter;
  };
  scale2.align = function(_3) {
    return arguments.length ? (align = Math.max(0, Math.min(1, _3)), rescale()) : align;
  };
  scale2.copy = function() {
    return band(domain(), [r0, r1]).round(round).paddingInner(paddingInner).paddingOuter(paddingOuter).align(align);
  };
  return initRange.apply(rescale(), arguments);
}
function pointish(scale2) {
  var copy3 = scale2.copy;
  scale2.padding = scale2.paddingOuter;
  delete scale2.paddingInner;
  delete scale2.paddingOuter;
  scale2.copy = function() {
    return pointish(copy3());
  };
  return scale2;
}
function point() {
  return pointish(band.apply(null, arguments).paddingInner(1));
}

// node_modules/d3-scale/src/constant.js
function constants(x4) {
  return function() {
    return x4;
  };
}

// node_modules/d3-scale/src/number.js
function number2(x4) {
  return +x4;
}

// node_modules/d3-scale/src/continuous.js
var unit = [0, 1];
function identity2(x4) {
  return x4;
}
function normalize(a5, b5) {
  return (b5 -= a5 = +a5) ? function(x4) {
    return (x4 - a5) / b5;
  } : constants(isNaN(b5) ? NaN : 0.5);
}
function clamper(a5, b5) {
  var t8;
  if (a5 > b5) t8 = a5, a5 = b5, b5 = t8;
  return function(x4) {
    return Math.max(a5, Math.min(b5, x4));
  };
}
function bimap(domain, range2, interpolate2) {
  var d0 = domain[0], d1 = domain[1], r0 = range2[0], r1 = range2[1];
  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate2(r1, r0);
  else d0 = normalize(d0, d1), r0 = interpolate2(r0, r1);
  return function(x4) {
    return r0(d0(x4));
  };
}
function polymap(domain, range2, interpolate2) {
  var j3 = Math.min(domain.length, range2.length) - 1, d3 = new Array(j3), r7 = new Array(j3), i6 = -1;
  if (domain[j3] < domain[0]) {
    domain = domain.slice().reverse();
    range2 = range2.slice().reverse();
  }
  while (++i6 < j3) {
    d3[i6] = normalize(domain[i6], domain[i6 + 1]);
    r7[i6] = interpolate2(range2[i6], range2[i6 + 1]);
  }
  return function(x4) {
    var i7 = bisect_default(domain, x4, 1, j3) - 1;
    return r7[i7](d3[i7](x4));
  };
}
function copy(source, target) {
  return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());
}
function transformer() {
  var domain = unit, range2 = unit, interpolate2 = value_default, transform, untransform, unknown, clamp2 = identity2, piecewise2, output, input;
  function rescale() {
    var n7 = Math.min(domain.length, range2.length);
    if (clamp2 !== identity2) clamp2 = clamper(domain[0], domain[n7 - 1]);
    piecewise2 = n7 > 2 ? polymap : bimap;
    output = input = null;
    return scale2;
  }
  function scale2(x4) {
    return x4 == null || isNaN(x4 = +x4) ? unknown : (output || (output = piecewise2(domain.map(transform), range2, interpolate2)))(transform(clamp2(x4)));
  }
  scale2.invert = function(y4) {
    return clamp2(untransform((input || (input = piecewise2(range2, domain.map(transform), number_default)))(y4)));
  };
  scale2.domain = function(_3) {
    return arguments.length ? (domain = Array.from(_3, number2), rescale()) : domain.slice();
  };
  scale2.range = function(_3) {
    return arguments.length ? (range2 = Array.from(_3), rescale()) : range2.slice();
  };
  scale2.rangeRound = function(_3) {
    return range2 = Array.from(_3), interpolate2 = round_default, rescale();
  };
  scale2.clamp = function(_3) {
    return arguments.length ? (clamp2 = _3 ? true : identity2, rescale()) : clamp2 !== identity2;
  };
  scale2.interpolate = function(_3) {
    return arguments.length ? (interpolate2 = _3, rescale()) : interpolate2;
  };
  scale2.unknown = function(_3) {
    return arguments.length ? (unknown = _3, scale2) : unknown;
  };
  return function(t8, u6) {
    transform = t8, untransform = u6;
    return rescale();
  };
}
function continuous() {
  return transformer()(identity2, identity2);
}

// node_modules/d3-scale/node_modules/d3-format/src/formatDecimal.js
function formatDecimal_default(x4) {
  return Math.abs(x4 = Math.round(x4)) >= 1e21 ? x4.toLocaleString("en").replace(/,/g, "") : x4.toString(10);
}
function formatDecimalParts(x4, p4) {
  if ((i6 = (x4 = p4 ? x4.toExponential(p4 - 1) : x4.toExponential()).indexOf("e")) < 0) return null;
  var i6, coefficient = x4.slice(0, i6);
  return [
    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
    +x4.slice(i6 + 1)
  ];
}

// node_modules/d3-scale/node_modules/d3-format/src/exponent.js
function exponent_default(x4) {
  return x4 = formatDecimalParts(Math.abs(x4)), x4 ? x4[1] : NaN;
}

// node_modules/d3-scale/node_modules/d3-format/src/formatGroup.js
function formatGroup_default(grouping, thousands) {
  return function(value, width) {
    var i6 = value.length, t8 = [], j3 = 0, g4 = grouping[0], length = 0;
    while (i6 > 0 && g4 > 0) {
      if (length + g4 + 1 > width) g4 = Math.max(1, width - length);
      t8.push(value.substring(i6 -= g4, i6 + g4));
      if ((length += g4 + 1) > width) break;
      g4 = grouping[j3 = (j3 + 1) % grouping.length];
    }
    return t8.reverse().join(thousands);
  };
}

// node_modules/d3-scale/node_modules/d3-format/src/formatNumerals.js
function formatNumerals_default(numerals) {
  return function(value) {
    return value.replace(/[0-9]/g, function(i6) {
      return numerals[+i6];
    });
  };
}

// node_modules/d3-scale/node_modules/d3-format/src/formatSpecifier.js
var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier(specifier) {
  if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
  var match;
  return new FormatSpecifier({
    fill: match[1],
    align: match[2],
    sign: match[3],
    symbol: match[4],
    zero: match[5],
    width: match[6],
    comma: match[7],
    precision: match[8] && match[8].slice(1),
    trim: match[9],
    type: match[10]
  });
}
formatSpecifier.prototype = FormatSpecifier.prototype;
function FormatSpecifier(specifier) {
  this.fill = specifier.fill === void 0 ? " " : specifier.fill + "";
  this.align = specifier.align === void 0 ? ">" : specifier.align + "";
  this.sign = specifier.sign === void 0 ? "-" : specifier.sign + "";
  this.symbol = specifier.symbol === void 0 ? "" : specifier.symbol + "";
  this.zero = !!specifier.zero;
  this.width = specifier.width === void 0 ? void 0 : +specifier.width;
  this.comma = !!specifier.comma;
  this.precision = specifier.precision === void 0 ? void 0 : +specifier.precision;
  this.trim = !!specifier.trim;
  this.type = specifier.type === void 0 ? "" : specifier.type + "";
}
FormatSpecifier.prototype.toString = function() {
  return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (this.width === void 0 ? "" : Math.max(1, this.width | 0)) + (this.comma ? "," : "") + (this.precision === void 0 ? "" : "." + Math.max(0, this.precision | 0)) + (this.trim ? "~" : "") + this.type;
};

// node_modules/d3-scale/node_modules/d3-format/src/formatTrim.js
function formatTrim_default(s5) {
  out: for (var n7 = s5.length, i6 = 1, i0 = -1, i1; i6 < n7; ++i6) {
    switch (s5[i6]) {
      case ".":
        i0 = i1 = i6;
        break;
      case "0":
        if (i0 === 0) i0 = i6;
        i1 = i6;
        break;
      default:
        if (!+s5[i6]) break out;
        if (i0 > 0) i0 = 0;
        break;
    }
  }
  return i0 > 0 ? s5.slice(0, i0) + s5.slice(i1 + 1) : s5;
}

// node_modules/d3-scale/node_modules/d3-format/src/formatPrefixAuto.js
var prefixExponent;
function formatPrefixAuto_default(x4, p4) {
  var d3 = formatDecimalParts(x4, p4);
  if (!d3) return x4 + "";
  var coefficient = d3[0], exponent = d3[1], i6 = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n7 = coefficient.length;
  return i6 === n7 ? coefficient : i6 > n7 ? coefficient + new Array(i6 - n7 + 1).join("0") : i6 > 0 ? coefficient.slice(0, i6) + "." + coefficient.slice(i6) : "0." + new Array(1 - i6).join("0") + formatDecimalParts(x4, Math.max(0, p4 + i6 - 1))[0];
}

// node_modules/d3-scale/node_modules/d3-format/src/formatRounded.js
function formatRounded_default(x4, p4) {
  var d3 = formatDecimalParts(x4, p4);
  if (!d3) return x4 + "";
  var coefficient = d3[0], exponent = d3[1];
  return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join("0");
}

// node_modules/d3-scale/node_modules/d3-format/src/formatTypes.js
var formatTypes_default = {
  "%": (x4, p4) => (x4 * 100).toFixed(p4),
  "b": (x4) => Math.round(x4).toString(2),
  "c": (x4) => x4 + "",
  "d": formatDecimal_default,
  "e": (x4, p4) => x4.toExponential(p4),
  "f": (x4, p4) => x4.toFixed(p4),
  "g": (x4, p4) => x4.toPrecision(p4),
  "o": (x4) => Math.round(x4).toString(8),
  "p": (x4, p4) => formatRounded_default(x4 * 100, p4),
  "r": formatRounded_default,
  "s": formatPrefixAuto_default,
  "X": (x4) => Math.round(x4).toString(16).toUpperCase(),
  "x": (x4) => Math.round(x4).toString(16)
};

// node_modules/d3-scale/node_modules/d3-format/src/identity.js
function identity_default(x4) {
  return x4;
}

// node_modules/d3-scale/node_modules/d3-format/src/locale.js
var map3 = Array.prototype.map;
var prefixes2 = ["y", "z", "a", "f", "p", "n", "µ", "m", "", "k", "M", "G", "T", "P", "E", "Z", "Y"];
function locale_default(locale5) {
  var group3 = locale5.grouping === void 0 || locale5.thousands === void 0 ? identity_default : formatGroup_default(map3.call(locale5.grouping, Number), locale5.thousands + ""), currencyPrefix = locale5.currency === void 0 ? "" : locale5.currency[0] + "", currencySuffix = locale5.currency === void 0 ? "" : locale5.currency[1] + "", decimal = locale5.decimal === void 0 ? "." : locale5.decimal + "", numerals = locale5.numerals === void 0 ? identity_default : formatNumerals_default(map3.call(locale5.numerals, String)), percent = locale5.percent === void 0 ? "%" : locale5.percent + "", minus = locale5.minus === void 0 ? "−" : locale5.minus + "", nan = locale5.nan === void 0 ? "NaN" : locale5.nan + "";
  function newFormat(specifier) {
    specifier = formatSpecifier(specifier);
    var fill = specifier.fill, align = specifier.align, sign2 = specifier.sign, symbol = specifier.symbol, zero2 = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;
    if (type === "n") comma = true, type = "g";
    else if (!formatTypes_default[type]) precision === void 0 && (precision = 12), trim = true, type = "g";
    if (zero2 || fill === "0" && align === "=") zero2 = true, fill = "0", align = "=";
    var prefix2 = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";
    var formatType = formatTypes_default[type], maybeSuffix = /[defgprs%]/.test(type);
    precision = precision === void 0 ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
    function format3(value) {
      var valuePrefix = prefix2, valueSuffix = suffix, i6, n7, c9;
      if (type === "c") {
        valueSuffix = formatType(value) + valueSuffix;
        value = "";
      } else {
        value = +value;
        var valueNegative = value < 0 || 1 / value < 0;
        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);
        if (trim) value = formatTrim_default(value);
        if (valueNegative && +value === 0 && sign2 !== "+") valueNegative = false;
        valuePrefix = (valueNegative ? sign2 === "(" ? sign2 : minus : sign2 === "-" || sign2 === "(" ? "" : sign2) + valuePrefix;
        valueSuffix = (type === "s" ? prefixes2[8 + prefixExponent / 3] : "") + valueSuffix + (valueNegative && sign2 === "(" ? ")" : "");
        if (maybeSuffix) {
          i6 = -1, n7 = value.length;
          while (++i6 < n7) {
            if (c9 = value.charCodeAt(i6), 48 > c9 || c9 > 57) {
              valueSuffix = (c9 === 46 ? decimal + value.slice(i6 + 1) : value.slice(i6)) + valueSuffix;
              value = value.slice(0, i6);
              break;
            }
          }
        }
      }
      if (comma && !zero2) value = group3(value, Infinity);
      var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
      if (comma && zero2) value = group3(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";
      switch (align) {
        case "<":
          value = valuePrefix + value + valueSuffix + padding;
          break;
        case "=":
          value = valuePrefix + padding + value + valueSuffix;
          break;
        case "^":
          value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);
          break;
        default:
          value = padding + valuePrefix + value + valueSuffix;
          break;
      }
      return numerals(value);
    }
    format3.toString = function() {
      return specifier + "";
    };
    return format3;
  }
  function formatPrefix3(specifier, value) {
    var f3 = newFormat((specifier = formatSpecifier(specifier), specifier.type = "f", specifier)), e11 = Math.max(-8, Math.min(8, Math.floor(exponent_default(value) / 3))) * 3, k5 = Math.pow(10, -e11), prefix2 = prefixes2[8 + e11 / 3];
    return function(value2) {
      return f3(k5 * value2) + prefix2;
    };
  }
  return {
    format: newFormat,
    formatPrefix: formatPrefix3
  };
}

// node_modules/d3-scale/node_modules/d3-format/src/defaultLocale.js
var locale;
var format;
var formatPrefix;
defaultLocale({
  thousands: ",",
  grouping: [3],
  currency: ["$", ""]
});
function defaultLocale(definition) {
  locale = locale_default(definition);
  format = locale.format;
  formatPrefix = locale.formatPrefix;
  return locale;
}

// node_modules/d3-scale/node_modules/d3-format/src/precisionFixed.js
function precisionFixed_default(step) {
  return Math.max(0, -exponent_default(Math.abs(step)));
}

// node_modules/d3-scale/node_modules/d3-format/src/precisionPrefix.js
function precisionPrefix_default(step, value) {
  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent_default(value) / 3))) * 3 - exponent_default(Math.abs(step)));
}

// node_modules/d3-scale/node_modules/d3-format/src/precisionRound.js
function precisionRound_default(step, max4) {
  step = Math.abs(step), max4 = Math.abs(max4) - step;
  return Math.max(0, exponent_default(max4) - exponent_default(step)) + 1;
}

// node_modules/d3-scale/src/tickFormat.js
function tickFormat(start2, stop2, count3, specifier) {
  var step = tickStep(start2, stop2, count3), precision;
  specifier = formatSpecifier(specifier == null ? ",f" : specifier);
  switch (specifier.type) {
    case "s": {
      var value = Math.max(Math.abs(start2), Math.abs(stop2));
      if (specifier.precision == null && !isNaN(precision = precisionPrefix_default(step, value))) specifier.precision = precision;
      return formatPrefix(specifier, value);
    }
    case "":
    case "e":
    case "g":
    case "p":
    case "r": {
      if (specifier.precision == null && !isNaN(precision = precisionRound_default(step, Math.max(Math.abs(start2), Math.abs(stop2))))) specifier.precision = precision - (specifier.type === "e");
      break;
    }
    case "f":
    case "%": {
      if (specifier.precision == null && !isNaN(precision = precisionFixed_default(step))) specifier.precision = precision - (specifier.type === "%") * 2;
      break;
    }
  }
  return format(specifier);
}

// node_modules/d3-scale/src/linear.js
function linearish(scale2) {
  var domain = scale2.domain;
  scale2.ticks = function(count3) {
    var d3 = domain();
    return ticks(d3[0], d3[d3.length - 1], count3 == null ? 10 : count3);
  };
  scale2.tickFormat = function(count3, specifier) {
    var d3 = domain();
    return tickFormat(d3[0], d3[d3.length - 1], count3 == null ? 10 : count3, specifier);
  };
  scale2.nice = function(count3) {
    if (count3 == null) count3 = 10;
    var d3 = domain();
    var i0 = 0;
    var i1 = d3.length - 1;
    var start2 = d3[i0];
    var stop2 = d3[i1];
    var prestep;
    var step;
    var maxIter = 10;
    if (stop2 < start2) {
      step = start2, start2 = stop2, stop2 = step;
      step = i0, i0 = i1, i1 = step;
    }
    while (maxIter-- > 0) {
      step = tickIncrement(start2, stop2, count3);
      if (step === prestep) {
        d3[i0] = start2;
        d3[i1] = stop2;
        return domain(d3);
      } else if (step > 0) {
        start2 = Math.floor(start2 / step) * step;
        stop2 = Math.ceil(stop2 / step) * step;
      } else if (step < 0) {
        start2 = Math.ceil(start2 * step) / step;
        stop2 = Math.floor(stop2 * step) / step;
      } else {
        break;
      }
      prestep = step;
    }
    return scale2;
  };
  return scale2;
}
function linear() {
  var scale2 = continuous();
  scale2.copy = function() {
    return copy(scale2, linear());
  };
  initRange.apply(scale2, arguments);
  return linearish(scale2);
}

// node_modules/d3-scale/src/nice.js
function nice2(domain, interval) {
  domain = domain.slice();
  var i0 = 0, i1 = domain.length - 1, x0 = domain[i0], x1 = domain[i1], t8;
  if (x1 < x0) {
    t8 = i0, i0 = i1, i1 = t8;
    t8 = x0, x0 = x1, x1 = t8;
  }
  domain[i0] = interval.floor(x0);
  domain[i1] = interval.ceil(x1);
  return domain;
}

// node_modules/d3-scale/src/log.js
function transformLog(x4) {
  return Math.log(x4);
}
function transformExp(x4) {
  return Math.exp(x4);
}
function transformLogn(x4) {
  return -Math.log(-x4);
}
function transformExpn(x4) {
  return -Math.exp(-x4);
}
function pow10(x4) {
  return isFinite(x4) ? +("1e" + x4) : x4 < 0 ? 0 : x4;
}
function powp(base) {
  return base === 10 ? pow10 : base === Math.E ? Math.exp : (x4) => Math.pow(base, x4);
}
function logp(base) {
  return base === Math.E ? Math.log : base === 10 && Math.log10 || base === 2 && Math.log2 || (base = Math.log(base), (x4) => Math.log(x4) / base);
}
function reflect(f3) {
  return (x4, k5) => -f3(-x4, k5);
}
function loggish(transform) {
  const scale2 = transform(transformLog, transformExp);
  const domain = scale2.domain;
  let base = 10;
  let logs;
  let pows;
  function rescale() {
    logs = logp(base), pows = powp(base);
    if (domain()[0] < 0) {
      logs = reflect(logs), pows = reflect(pows);
      transform(transformLogn, transformExpn);
    } else {
      transform(transformLog, transformExp);
    }
    return scale2;
  }
  scale2.base = function(_3) {
    return arguments.length ? (base = +_3, rescale()) : base;
  };
  scale2.domain = function(_3) {
    return arguments.length ? (domain(_3), rescale()) : domain();
  };
  scale2.ticks = (count3) => {
    const d3 = domain();
    let u6 = d3[0];
    let v6 = d3[d3.length - 1];
    const r7 = v6 < u6;
    if (r7) [u6, v6] = [v6, u6];
    let i6 = logs(u6);
    let j3 = logs(v6);
    let k5;
    let t8;
    const n7 = count3 == null ? 10 : +count3;
    let z6 = [];
    if (!(base % 1) && j3 - i6 < n7) {
      i6 = Math.floor(i6), j3 = Math.ceil(j3);
      if (u6 > 0) for (; i6 <= j3; ++i6) {
        for (k5 = 1; k5 < base; ++k5) {
          t8 = i6 < 0 ? k5 / pows(-i6) : k5 * pows(i6);
          if (t8 < u6) continue;
          if (t8 > v6) break;
          z6.push(t8);
        }
      }
      else for (; i6 <= j3; ++i6) {
        for (k5 = base - 1; k5 >= 1; --k5) {
          t8 = i6 > 0 ? k5 / pows(-i6) : k5 * pows(i6);
          if (t8 < u6) continue;
          if (t8 > v6) break;
          z6.push(t8);
        }
      }
      if (z6.length * 2 < n7) z6 = ticks(u6, v6, n7);
    } else {
      z6 = ticks(i6, j3, Math.min(j3 - i6, n7)).map(pows);
    }
    return r7 ? z6.reverse() : z6;
  };
  scale2.tickFormat = (count3, specifier) => {
    if (count3 == null) count3 = 10;
    if (specifier == null) specifier = base === 10 ? "s" : ",";
    if (typeof specifier !== "function") {
      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;
      specifier = format(specifier);
    }
    if (count3 === Infinity) return specifier;
    const k5 = Math.max(1, base * count3 / scale2.ticks().length);
    return (d3) => {
      let i6 = d3 / pows(Math.round(logs(d3)));
      if (i6 * base < base - 0.5) i6 *= base;
      return i6 <= k5 ? specifier(d3) : "";
    };
  };
  scale2.nice = () => {
    return domain(nice2(domain(), {
      floor: (x4) => pows(Math.floor(logs(x4))),
      ceil: (x4) => pows(Math.ceil(logs(x4)))
    }));
  };
  return scale2;
}
function log() {
  const scale2 = loggish(transformer()).domain([1, 10]);
  scale2.copy = () => copy(scale2, log()).base(scale2.base());
  initRange.apply(scale2, arguments);
  return scale2;
}

// node_modules/d3-scale/src/symlog.js
function transformSymlog(c9) {
  return function(x4) {
    return Math.sign(x4) * Math.log1p(Math.abs(x4 / c9));
  };
}
function transformSymexp(c9) {
  return function(x4) {
    return Math.sign(x4) * Math.expm1(Math.abs(x4)) * c9;
  };
}
function symlogish(transform) {
  var c9 = 1, scale2 = transform(transformSymlog(c9), transformSymexp(c9));
  scale2.constant = function(_3) {
    return arguments.length ? transform(transformSymlog(c9 = +_3), transformSymexp(c9)) : c9;
  };
  return linearish(scale2);
}
function symlog() {
  var scale2 = symlogish(transformer());
  scale2.copy = function() {
    return copy(scale2, symlog()).constant(scale2.constant());
  };
  return initRange.apply(scale2, arguments);
}

// node_modules/d3-time/src/interval.js
var t0 = /* @__PURE__ */ new Date();
var t1 = /* @__PURE__ */ new Date();
function timeInterval(floori, offseti, count3, field) {
  function interval(date2) {
    return floori(date2 = arguments.length === 0 ? /* @__PURE__ */ new Date() : /* @__PURE__ */ new Date(+date2)), date2;
  }
  interval.floor = (date2) => {
    return floori(date2 = /* @__PURE__ */ new Date(+date2)), date2;
  };
  interval.ceil = (date2) => {
    return floori(date2 = new Date(date2 - 1)), offseti(date2, 1), floori(date2), date2;
  };
  interval.round = (date2) => {
    const d0 = interval(date2), d1 = interval.ceil(date2);
    return date2 - d0 < d1 - date2 ? d0 : d1;
  };
  interval.offset = (date2, step) => {
    return offseti(date2 = /* @__PURE__ */ new Date(+date2), step == null ? 1 : Math.floor(step)), date2;
  };
  interval.range = (start2, stop2, step) => {
    const range2 = [];
    start2 = interval.ceil(start2);
    step = step == null ? 1 : Math.floor(step);
    if (!(start2 < stop2) || !(step > 0)) return range2;
    let previous;
    do
      range2.push(previous = /* @__PURE__ */ new Date(+start2)), offseti(start2, step), floori(start2);
    while (previous < start2 && start2 < stop2);
    return range2;
  };
  interval.filter = (test) => {
    return timeInterval((date2) => {
      if (date2 >= date2) while (floori(date2), !test(date2)) date2.setTime(date2 - 1);
    }, (date2, step) => {
      if (date2 >= date2) {
        if (step < 0) while (++step <= 0) {
          while (offseti(date2, -1), !test(date2)) {
          }
        }
        else while (--step >= 0) {
          while (offseti(date2, 1), !test(date2)) {
          }
        }
      }
    });
  };
  if (count3) {
    interval.count = (start2, end) => {
      t0.setTime(+start2), t1.setTime(+end);
      floori(t0), floori(t1);
      return Math.floor(count3(t0, t1));
    };
    interval.every = (step) => {
      step = Math.floor(step);
      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? (d3) => field(d3) % step === 0 : (d3) => interval.count(0, d3) % step === 0);
    };
  }
  return interval;
}

// node_modules/d3-time/src/millisecond.js
var millisecond = timeInterval(() => {
}, (date2, step) => {
  date2.setTime(+date2 + step);
}, (start2, end) => {
  return end - start2;
});
millisecond.every = (k5) => {
  k5 = Math.floor(k5);
  if (!isFinite(k5) || !(k5 > 0)) return null;
  if (!(k5 > 1)) return millisecond;
  return timeInterval((date2) => {
    date2.setTime(Math.floor(date2 / k5) * k5);
  }, (date2, step) => {
    date2.setTime(+date2 + step * k5);
  }, (start2, end) => {
    return (end - start2) / k5;
  });
};
var milliseconds = millisecond.range;

// node_modules/d3-time/src/duration.js
var durationSecond = 1e3;
var durationMinute = durationSecond * 60;
var durationHour = durationMinute * 60;
var durationDay = durationHour * 24;
var durationWeek = durationDay * 7;
var durationMonth = durationDay * 30;
var durationYear = durationDay * 365;

// node_modules/d3-time/src/second.js
var second = timeInterval((date2) => {
  date2.setTime(date2 - date2.getMilliseconds());
}, (date2, step) => {
  date2.setTime(+date2 + step * durationSecond);
}, (start2, end) => {
  return (end - start2) / durationSecond;
}, (date2) => {
  return date2.getUTCSeconds();
});
var seconds = second.range;

// node_modules/d3-time/src/minute.js
var timeMinute = timeInterval((date2) => {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationMinute);
}, (start2, end) => {
  return (end - start2) / durationMinute;
}, (date2) => {
  return date2.getMinutes();
});
var timeMinutes = timeMinute.range;
var utcMinute = timeInterval((date2) => {
  date2.setUTCSeconds(0, 0);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationMinute);
}, (start2, end) => {
  return (end - start2) / durationMinute;
}, (date2) => {
  return date2.getUTCMinutes();
});
var utcMinutes = utcMinute.range;

// node_modules/d3-time/src/hour.js
var timeHour = timeInterval((date2) => {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond - date2.getMinutes() * durationMinute);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationHour);
}, (start2, end) => {
  return (end - start2) / durationHour;
}, (date2) => {
  return date2.getHours();
});
var timeHours = timeHour.range;
var utcHour = timeInterval((date2) => {
  date2.setUTCMinutes(0, 0, 0);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationHour);
}, (start2, end) => {
  return (end - start2) / durationHour;
}, (date2) => {
  return date2.getUTCHours();
});
var utcHours = utcHour.range;

// node_modules/d3-time/src/day.js
var timeDay = timeInterval(
  (date2) => date2.setHours(0, 0, 0, 0),
  (date2, step) => date2.setDate(date2.getDate() + step),
  (start2, end) => (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute) / durationDay,
  (date2) => date2.getDate() - 1
);
var timeDays = timeDay.range;
var utcDay = timeInterval((date2) => {
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCDate(date2.getUTCDate() + step);
}, (start2, end) => {
  return (end - start2) / durationDay;
}, (date2) => {
  return date2.getUTCDate() - 1;
});
var utcDays = utcDay.range;
var unixDay = timeInterval((date2) => {
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCDate(date2.getUTCDate() + step);
}, (start2, end) => {
  return (end - start2) / durationDay;
}, (date2) => {
  return Math.floor(date2 / durationDay);
});
var unixDays = unixDay.range;

// node_modules/d3-time/src/week.js
function timeWeekday(i6) {
  return timeInterval((date2) => {
    date2.setDate(date2.getDate() - (date2.getDay() + 7 - i6) % 7);
    date2.setHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setDate(date2.getDate() + step * 7);
  }, (start2, end) => {
    return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute) / durationWeek;
  });
}
var timeSunday = timeWeekday(0);
var timeMonday = timeWeekday(1);
var timeTuesday = timeWeekday(2);
var timeWednesday = timeWeekday(3);
var timeThursday = timeWeekday(4);
var timeFriday = timeWeekday(5);
var timeSaturday = timeWeekday(6);
var timeSundays = timeSunday.range;
var timeMondays = timeMonday.range;
var timeTuesdays = timeTuesday.range;
var timeWednesdays = timeWednesday.range;
var timeThursdays = timeThursday.range;
var timeFridays = timeFriday.range;
var timeSaturdays = timeSaturday.range;
function utcWeekday(i6) {
  return timeInterval((date2) => {
    date2.setUTCDate(date2.getUTCDate() - (date2.getUTCDay() + 7 - i6) % 7);
    date2.setUTCHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setUTCDate(date2.getUTCDate() + step * 7);
  }, (start2, end) => {
    return (end - start2) / durationWeek;
  });
}
var utcSunday = utcWeekday(0);
var utcMonday = utcWeekday(1);
var utcTuesday = utcWeekday(2);
var utcWednesday = utcWeekday(3);
var utcThursday = utcWeekday(4);
var utcFriday = utcWeekday(5);
var utcSaturday = utcWeekday(6);
var utcSundays = utcSunday.range;
var utcMondays = utcMonday.range;
var utcTuesdays = utcTuesday.range;
var utcWednesdays = utcWednesday.range;
var utcThursdays = utcThursday.range;
var utcFridays = utcFriday.range;
var utcSaturdays = utcSaturday.range;

// node_modules/d3-time/src/month.js
var timeMonth = timeInterval((date2) => {
  date2.setDate(1);
  date2.setHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setMonth(date2.getMonth() + step);
}, (start2, end) => {
  return end.getMonth() - start2.getMonth() + (end.getFullYear() - start2.getFullYear()) * 12;
}, (date2) => {
  return date2.getMonth();
});
var timeMonths = timeMonth.range;
var utcMonth = timeInterval((date2) => {
  date2.setUTCDate(1);
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCMonth(date2.getUTCMonth() + step);
}, (start2, end) => {
  return end.getUTCMonth() - start2.getUTCMonth() + (end.getUTCFullYear() - start2.getUTCFullYear()) * 12;
}, (date2) => {
  return date2.getUTCMonth();
});
var utcMonths = utcMonth.range;

// node_modules/d3-time/src/year.js
var timeYear = timeInterval((date2) => {
  date2.setMonth(0, 1);
  date2.setHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setFullYear(date2.getFullYear() + step);
}, (start2, end) => {
  return end.getFullYear() - start2.getFullYear();
}, (date2) => {
  return date2.getFullYear();
});
timeYear.every = (k5) => {
  return !isFinite(k5 = Math.floor(k5)) || !(k5 > 0) ? null : timeInterval((date2) => {
    date2.setFullYear(Math.floor(date2.getFullYear() / k5) * k5);
    date2.setMonth(0, 1);
    date2.setHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setFullYear(date2.getFullYear() + step * k5);
  });
};
var timeYears = timeYear.range;
var utcYear = timeInterval((date2) => {
  date2.setUTCMonth(0, 1);
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCFullYear(date2.getUTCFullYear() + step);
}, (start2, end) => {
  return end.getUTCFullYear() - start2.getUTCFullYear();
}, (date2) => {
  return date2.getUTCFullYear();
});
utcYear.every = (k5) => {
  return !isFinite(k5 = Math.floor(k5)) || !(k5 > 0) ? null : timeInterval((date2) => {
    date2.setUTCFullYear(Math.floor(date2.getUTCFullYear() / k5) * k5);
    date2.setUTCMonth(0, 1);
    date2.setUTCHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setUTCFullYear(date2.getUTCFullYear() + step * k5);
  });
};
var utcYears = utcYear.range;

// node_modules/d3-time/src/ticks.js
function ticker(year3, month3, week, day3, hour3, minute3) {
  const tickIntervals = [
    [second, 1, durationSecond],
    [second, 5, 5 * durationSecond],
    [second, 15, 15 * durationSecond],
    [second, 30, 30 * durationSecond],
    [minute3, 1, durationMinute],
    [minute3, 5, 5 * durationMinute],
    [minute3, 15, 15 * durationMinute],
    [minute3, 30, 30 * durationMinute],
    [hour3, 1, durationHour],
    [hour3, 3, 3 * durationHour],
    [hour3, 6, 6 * durationHour],
    [hour3, 12, 12 * durationHour],
    [day3, 1, durationDay],
    [day3, 2, 2 * durationDay],
    [week, 1, durationWeek],
    [month3, 1, durationMonth],
    [month3, 3, 3 * durationMonth],
    [year3, 1, durationYear]
  ];
  function ticks2(start2, stop2, count3) {
    const reverse3 = stop2 < start2;
    if (reverse3) [start2, stop2] = [stop2, start2];
    const interval = count3 && typeof count3.range === "function" ? count3 : tickInterval(start2, stop2, count3);
    const ticks3 = interval ? interval.range(start2, +stop2 + 1) : [];
    return reverse3 ? ticks3.reverse() : ticks3;
  }
  function tickInterval(start2, stop2, count3) {
    const target = Math.abs(stop2 - start2) / count3;
    const i6 = bisector(([, , step2]) => step2).right(tickIntervals, target);
    if (i6 === tickIntervals.length) return year3.every(tickStep(start2 / durationYear, stop2 / durationYear, count3));
    if (i6 === 0) return millisecond.every(Math.max(tickStep(start2, stop2, count3), 1));
    const [t8, step] = tickIntervals[target / tickIntervals[i6 - 1][2] < tickIntervals[i6][2] / target ? i6 - 1 : i6];
    return t8.every(step);
  }
  return [ticks2, tickInterval];
}
var [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);
var [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);

// node_modules/d3-scale/node_modules/d3-time-format/src/locale.js
function localDate(d3) {
  if (0 <= d3.y && d3.y < 100) {
    var date2 = new Date(-1, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L);
    date2.setFullYear(d3.y);
    return date2;
  }
  return new Date(d3.y, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L);
}
function utcDate(d3) {
  if (0 <= d3.y && d3.y < 100) {
    var date2 = new Date(Date.UTC(-1, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L));
    date2.setUTCFullYear(d3.y);
    return date2;
  }
  return new Date(Date.UTC(d3.y, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L));
}
function newDate(y4, m5, d3) {
  return { y: y4, m: m5, d: d3, H: 0, M: 0, S: 0, L: 0 };
}
function formatLocale(locale5) {
  var locale_dateTime = locale5.dateTime, locale_date = locale5.date, locale_time = locale5.time, locale_periods = locale5.periods, locale_weekdays = locale5.days, locale_shortWeekdays = locale5.shortDays, locale_months = locale5.months, locale_shortMonths = locale5.shortMonths;
  var periodRe = formatRe(locale_periods), periodLookup = formatLookup(locale_periods), weekdayRe = formatRe(locale_weekdays), weekdayLookup = formatLookup(locale_weekdays), shortWeekdayRe = formatRe(locale_shortWeekdays), shortWeekdayLookup = formatLookup(locale_shortWeekdays), monthRe = formatRe(locale_months), monthLookup = formatLookup(locale_months), shortMonthRe = formatRe(locale_shortMonths), shortMonthLookup = formatLookup(locale_shortMonths);
  var formats = {
    "a": formatShortWeekday,
    "A": formatWeekday,
    "b": formatShortMonth,
    "B": formatMonth,
    "c": null,
    "d": formatDayOfMonth,
    "e": formatDayOfMonth,
    "f": formatMicroseconds,
    "g": formatYearISO,
    "G": formatFullYearISO,
    "H": formatHour24,
    "I": formatHour12,
    "j": formatDayOfYear,
    "L": formatMilliseconds,
    "m": formatMonthNumber,
    "M": formatMinutes,
    "p": formatPeriod,
    "q": formatQuarter,
    "Q": formatUnixTimestamp,
    "s": formatUnixTimestampSeconds,
    "S": formatSeconds,
    "u": formatWeekdayNumberMonday,
    "U": formatWeekNumberSunday,
    "V": formatWeekNumberISO,
    "w": formatWeekdayNumberSunday,
    "W": formatWeekNumberMonday,
    "x": null,
    "X": null,
    "y": formatYear,
    "Y": formatFullYear,
    "Z": formatZone,
    "%": formatLiteralPercent
  };
  var utcFormats = {
    "a": formatUTCShortWeekday,
    "A": formatUTCWeekday,
    "b": formatUTCShortMonth,
    "B": formatUTCMonth,
    "c": null,
    "d": formatUTCDayOfMonth,
    "e": formatUTCDayOfMonth,
    "f": formatUTCMicroseconds,
    "g": formatUTCYearISO,
    "G": formatUTCFullYearISO,
    "H": formatUTCHour24,
    "I": formatUTCHour12,
    "j": formatUTCDayOfYear,
    "L": formatUTCMilliseconds,
    "m": formatUTCMonthNumber,
    "M": formatUTCMinutes,
    "p": formatUTCPeriod,
    "q": formatUTCQuarter,
    "Q": formatUnixTimestamp,
    "s": formatUnixTimestampSeconds,
    "S": formatUTCSeconds,
    "u": formatUTCWeekdayNumberMonday,
    "U": formatUTCWeekNumberSunday,
    "V": formatUTCWeekNumberISO,
    "w": formatUTCWeekdayNumberSunday,
    "W": formatUTCWeekNumberMonday,
    "x": null,
    "X": null,
    "y": formatUTCYear,
    "Y": formatUTCFullYear,
    "Z": formatUTCZone,
    "%": formatLiteralPercent
  };
  var parses = {
    "a": parseShortWeekday,
    "A": parseWeekday,
    "b": parseShortMonth,
    "B": parseMonth,
    "c": parseLocaleDateTime,
    "d": parseDayOfMonth,
    "e": parseDayOfMonth,
    "f": parseMicroseconds,
    "g": parseYear,
    "G": parseFullYear,
    "H": parseHour24,
    "I": parseHour24,
    "j": parseDayOfYear,
    "L": parseMilliseconds,
    "m": parseMonthNumber,
    "M": parseMinutes,
    "p": parsePeriod,
    "q": parseQuarter,
    "Q": parseUnixTimestamp,
    "s": parseUnixTimestampSeconds,
    "S": parseSeconds,
    "u": parseWeekdayNumberMonday,
    "U": parseWeekNumberSunday,
    "V": parseWeekNumberISO,
    "w": parseWeekdayNumberSunday,
    "W": parseWeekNumberMonday,
    "x": parseLocaleDate,
    "X": parseLocaleTime,
    "y": parseYear,
    "Y": parseFullYear,
    "Z": parseZone,
    "%": parseLiteralPercent
  };
  formats.x = newFormat(locale_date, formats);
  formats.X = newFormat(locale_time, formats);
  formats.c = newFormat(locale_dateTime, formats);
  utcFormats.x = newFormat(locale_date, utcFormats);
  utcFormats.X = newFormat(locale_time, utcFormats);
  utcFormats.c = newFormat(locale_dateTime, utcFormats);
  function newFormat(specifier, formats2) {
    return function(date2) {
      var string = [], i6 = -1, j3 = 0, n7 = specifier.length, c9, pad3, format3;
      if (!(date2 instanceof Date)) date2 = /* @__PURE__ */ new Date(+date2);
      while (++i6 < n7) {
        if (specifier.charCodeAt(i6) === 37) {
          string.push(specifier.slice(j3, i6));
          if ((pad3 = pads[c9 = specifier.charAt(++i6)]) != null) c9 = specifier.charAt(++i6);
          else pad3 = c9 === "e" ? " " : "0";
          if (format3 = formats2[c9]) c9 = format3(date2, pad3);
          string.push(c9);
          j3 = i6 + 1;
        }
      }
      string.push(specifier.slice(j3, i6));
      return string.join("");
    };
  }
  function newParse(specifier, Z2) {
    return function(string) {
      var d3 = newDate(1900, void 0, 1), i6 = parseSpecifier(d3, specifier, string += "", 0), week, day3;
      if (i6 != string.length) return null;
      if ("Q" in d3) return new Date(d3.Q);
      if ("s" in d3) return new Date(d3.s * 1e3 + ("L" in d3 ? d3.L : 0));
      if (Z2 && !("Z" in d3)) d3.Z = 0;
      if ("p" in d3) d3.H = d3.H % 12 + d3.p * 12;
      if (d3.m === void 0) d3.m = "q" in d3 ? d3.q : 0;
      if ("V" in d3) {
        if (d3.V < 1 || d3.V > 53) return null;
        if (!("w" in d3)) d3.w = 1;
        if ("Z" in d3) {
          week = utcDate(newDate(d3.y, 0, 1)), day3 = week.getUTCDay();
          week = day3 > 4 || day3 === 0 ? utcMonday.ceil(week) : utcMonday(week);
          week = utcDay.offset(week, (d3.V - 1) * 7);
          d3.y = week.getUTCFullYear();
          d3.m = week.getUTCMonth();
          d3.d = week.getUTCDate() + (d3.w + 6) % 7;
        } else {
          week = localDate(newDate(d3.y, 0, 1)), day3 = week.getDay();
          week = day3 > 4 || day3 === 0 ? timeMonday.ceil(week) : timeMonday(week);
          week = timeDay.offset(week, (d3.V - 1) * 7);
          d3.y = week.getFullYear();
          d3.m = week.getMonth();
          d3.d = week.getDate() + (d3.w + 6) % 7;
        }
      } else if ("W" in d3 || "U" in d3) {
        if (!("w" in d3)) d3.w = "u" in d3 ? d3.u % 7 : "W" in d3 ? 1 : 0;
        day3 = "Z" in d3 ? utcDate(newDate(d3.y, 0, 1)).getUTCDay() : localDate(newDate(d3.y, 0, 1)).getDay();
        d3.m = 0;
        d3.d = "W" in d3 ? (d3.w + 6) % 7 + d3.W * 7 - (day3 + 5) % 7 : d3.w + d3.U * 7 - (day3 + 6) % 7;
      }
      if ("Z" in d3) {
        d3.H += d3.Z / 100 | 0;
        d3.M += d3.Z % 100;
        return utcDate(d3);
      }
      return localDate(d3);
    };
  }
  function parseSpecifier(d3, specifier, string, j3) {
    var i6 = 0, n7 = specifier.length, m5 = string.length, c9, parse;
    while (i6 < n7) {
      if (j3 >= m5) return -1;
      c9 = specifier.charCodeAt(i6++);
      if (c9 === 37) {
        c9 = specifier.charAt(i6++);
        parse = parses[c9 in pads ? specifier.charAt(i6++) : c9];
        if (!parse || (j3 = parse(d3, string, j3)) < 0) return -1;
      } else if (c9 != string.charCodeAt(j3++)) {
        return -1;
      }
    }
    return j3;
  }
  function parsePeriod(d3, string, i6) {
    var n7 = periodRe.exec(string.slice(i6));
    return n7 ? (d3.p = periodLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseShortWeekday(d3, string, i6) {
    var n7 = shortWeekdayRe.exec(string.slice(i6));
    return n7 ? (d3.w = shortWeekdayLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseWeekday(d3, string, i6) {
    var n7 = weekdayRe.exec(string.slice(i6));
    return n7 ? (d3.w = weekdayLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseShortMonth(d3, string, i6) {
    var n7 = shortMonthRe.exec(string.slice(i6));
    return n7 ? (d3.m = shortMonthLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseMonth(d3, string, i6) {
    var n7 = monthRe.exec(string.slice(i6));
    return n7 ? (d3.m = monthLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseLocaleDateTime(d3, string, i6) {
    return parseSpecifier(d3, locale_dateTime, string, i6);
  }
  function parseLocaleDate(d3, string, i6) {
    return parseSpecifier(d3, locale_date, string, i6);
  }
  function parseLocaleTime(d3, string, i6) {
    return parseSpecifier(d3, locale_time, string, i6);
  }
  function formatShortWeekday(d3) {
    return locale_shortWeekdays[d3.getDay()];
  }
  function formatWeekday(d3) {
    return locale_weekdays[d3.getDay()];
  }
  function formatShortMonth(d3) {
    return locale_shortMonths[d3.getMonth()];
  }
  function formatMonth(d3) {
    return locale_months[d3.getMonth()];
  }
  function formatPeriod(d3) {
    return locale_periods[+(d3.getHours() >= 12)];
  }
  function formatQuarter(d3) {
    return 1 + ~~(d3.getMonth() / 3);
  }
  function formatUTCShortWeekday(d3) {
    return locale_shortWeekdays[d3.getUTCDay()];
  }
  function formatUTCWeekday(d3) {
    return locale_weekdays[d3.getUTCDay()];
  }
  function formatUTCShortMonth(d3) {
    return locale_shortMonths[d3.getUTCMonth()];
  }
  function formatUTCMonth(d3) {
    return locale_months[d3.getUTCMonth()];
  }
  function formatUTCPeriod(d3) {
    return locale_periods[+(d3.getUTCHours() >= 12)];
  }
  function formatUTCQuarter(d3) {
    return 1 + ~~(d3.getUTCMonth() / 3);
  }
  return {
    format: function(specifier) {
      var f3 = newFormat(specifier += "", formats);
      f3.toString = function() {
        return specifier;
      };
      return f3;
    },
    parse: function(specifier) {
      var p4 = newParse(specifier += "", false);
      p4.toString = function() {
        return specifier;
      };
      return p4;
    },
    utcFormat: function(specifier) {
      var f3 = newFormat(specifier += "", utcFormats);
      f3.toString = function() {
        return specifier;
      };
      return f3;
    },
    utcParse: function(specifier) {
      var p4 = newParse(specifier += "", true);
      p4.toString = function() {
        return specifier;
      };
      return p4;
    }
  };
}
var pads = { "-": "", "_": " ", "0": "0" };
var numberRe = /^\s*\d+/;
var percentRe = /^%/;
var requoteRe = /[\\^$*+?|[\]().{}]/g;
function pad(value, fill, width) {
  var sign2 = value < 0 ? "-" : "", string = (sign2 ? -value : value) + "", length = string.length;
  return sign2 + (length < width ? new Array(width - length + 1).join(fill) + string : string);
}
function requote(s5) {
  return s5.replace(requoteRe, "\\$&");
}
function formatRe(names) {
  return new RegExp("^(?:" + names.map(requote).join("|") + ")", "i");
}
function formatLookup(names) {
  return new Map(names.map((name, i6) => [name.toLowerCase(), i6]));
}
function parseWeekdayNumberSunday(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 1));
  return n7 ? (d3.w = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekdayNumberMonday(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 1));
  return n7 ? (d3.u = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekNumberSunday(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.U = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekNumberISO(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.V = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekNumberMonday(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.W = +n7[0], i6 + n7[0].length) : -1;
}
function parseFullYear(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 4));
  return n7 ? (d3.y = +n7[0], i6 + n7[0].length) : -1;
}
function parseYear(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.y = +n7[0] + (+n7[0] > 68 ? 1900 : 2e3), i6 + n7[0].length) : -1;
}
function parseZone(d3, string, i6) {
  var n7 = /^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(string.slice(i6, i6 + 6));
  return n7 ? (d3.Z = n7[1] ? 0 : -(n7[2] + (n7[3] || "00")), i6 + n7[0].length) : -1;
}
function parseQuarter(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 1));
  return n7 ? (d3.q = n7[0] * 3 - 3, i6 + n7[0].length) : -1;
}
function parseMonthNumber(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.m = n7[0] - 1, i6 + n7[0].length) : -1;
}
function parseDayOfMonth(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.d = +n7[0], i6 + n7[0].length) : -1;
}
function parseDayOfYear(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 3));
  return n7 ? (d3.m = 0, d3.d = +n7[0], i6 + n7[0].length) : -1;
}
function parseHour24(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.H = +n7[0], i6 + n7[0].length) : -1;
}
function parseMinutes(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.M = +n7[0], i6 + n7[0].length) : -1;
}
function parseSeconds(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.S = +n7[0], i6 + n7[0].length) : -1;
}
function parseMilliseconds(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 3));
  return n7 ? (d3.L = +n7[0], i6 + n7[0].length) : -1;
}
function parseMicroseconds(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6, i6 + 6));
  return n7 ? (d3.L = Math.floor(n7[0] / 1e3), i6 + n7[0].length) : -1;
}
function parseLiteralPercent(d3, string, i6) {
  var n7 = percentRe.exec(string.slice(i6, i6 + 1));
  return n7 ? i6 + n7[0].length : -1;
}
function parseUnixTimestamp(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6));
  return n7 ? (d3.Q = +n7[0], i6 + n7[0].length) : -1;
}
function parseUnixTimestampSeconds(d3, string, i6) {
  var n7 = numberRe.exec(string.slice(i6));
  return n7 ? (d3.s = +n7[0], i6 + n7[0].length) : -1;
}
function formatDayOfMonth(d3, p4) {
  return pad(d3.getDate(), p4, 2);
}
function formatHour24(d3, p4) {
  return pad(d3.getHours(), p4, 2);
}
function formatHour12(d3, p4) {
  return pad(d3.getHours() % 12 || 12, p4, 2);
}
function formatDayOfYear(d3, p4) {
  return pad(1 + timeDay.count(timeYear(d3), d3), p4, 3);
}
function formatMilliseconds(d3, p4) {
  return pad(d3.getMilliseconds(), p4, 3);
}
function formatMicroseconds(d3, p4) {
  return formatMilliseconds(d3, p4) + "000";
}
function formatMonthNumber(d3, p4) {
  return pad(d3.getMonth() + 1, p4, 2);
}
function formatMinutes(d3, p4) {
  return pad(d3.getMinutes(), p4, 2);
}
function formatSeconds(d3, p4) {
  return pad(d3.getSeconds(), p4, 2);
}
function formatWeekdayNumberMonday(d3) {
  var day3 = d3.getDay();
  return day3 === 0 ? 7 : day3;
}
function formatWeekNumberSunday(d3, p4) {
  return pad(timeSunday.count(timeYear(d3) - 1, d3), p4, 2);
}
function dISO(d3) {
  var day3 = d3.getDay();
  return day3 >= 4 || day3 === 0 ? timeThursday(d3) : timeThursday.ceil(d3);
}
function formatWeekNumberISO(d3, p4) {
  d3 = dISO(d3);
  return pad(timeThursday.count(timeYear(d3), d3) + (timeYear(d3).getDay() === 4), p4, 2);
}
function formatWeekdayNumberSunday(d3) {
  return d3.getDay();
}
function formatWeekNumberMonday(d3, p4) {
  return pad(timeMonday.count(timeYear(d3) - 1, d3), p4, 2);
}
function formatYear(d3, p4) {
  return pad(d3.getFullYear() % 100, p4, 2);
}
function formatYearISO(d3, p4) {
  d3 = dISO(d3);
  return pad(d3.getFullYear() % 100, p4, 2);
}
function formatFullYear(d3, p4) {
  return pad(d3.getFullYear() % 1e4, p4, 4);
}
function formatFullYearISO(d3, p4) {
  var day3 = d3.getDay();
  d3 = day3 >= 4 || day3 === 0 ? timeThursday(d3) : timeThursday.ceil(d3);
  return pad(d3.getFullYear() % 1e4, p4, 4);
}
function formatZone(d3) {
  var z6 = d3.getTimezoneOffset();
  return (z6 > 0 ? "-" : (z6 *= -1, "+")) + pad(z6 / 60 | 0, "0", 2) + pad(z6 % 60, "0", 2);
}
function formatUTCDayOfMonth(d3, p4) {
  return pad(d3.getUTCDate(), p4, 2);
}
function formatUTCHour24(d3, p4) {
  return pad(d3.getUTCHours(), p4, 2);
}
function formatUTCHour12(d3, p4) {
  return pad(d3.getUTCHours() % 12 || 12, p4, 2);
}
function formatUTCDayOfYear(d3, p4) {
  return pad(1 + utcDay.count(utcYear(d3), d3), p4, 3);
}
function formatUTCMilliseconds(d3, p4) {
  return pad(d3.getUTCMilliseconds(), p4, 3);
}
function formatUTCMicroseconds(d3, p4) {
  return formatUTCMilliseconds(d3, p4) + "000";
}
function formatUTCMonthNumber(d3, p4) {
  return pad(d3.getUTCMonth() + 1, p4, 2);
}
function formatUTCMinutes(d3, p4) {
  return pad(d3.getUTCMinutes(), p4, 2);
}
function formatUTCSeconds(d3, p4) {
  return pad(d3.getUTCSeconds(), p4, 2);
}
function formatUTCWeekdayNumberMonday(d3) {
  var dow = d3.getUTCDay();
  return dow === 0 ? 7 : dow;
}
function formatUTCWeekNumberSunday(d3, p4) {
  return pad(utcSunday.count(utcYear(d3) - 1, d3), p4, 2);
}
function UTCdISO(d3) {
  var day3 = d3.getUTCDay();
  return day3 >= 4 || day3 === 0 ? utcThursday(d3) : utcThursday.ceil(d3);
}
function formatUTCWeekNumberISO(d3, p4) {
  d3 = UTCdISO(d3);
  return pad(utcThursday.count(utcYear(d3), d3) + (utcYear(d3).getUTCDay() === 4), p4, 2);
}
function formatUTCWeekdayNumberSunday(d3) {
  return d3.getUTCDay();
}
function formatUTCWeekNumberMonday(d3, p4) {
  return pad(utcMonday.count(utcYear(d3) - 1, d3), p4, 2);
}
function formatUTCYear(d3, p4) {
  return pad(d3.getUTCFullYear() % 100, p4, 2);
}
function formatUTCYearISO(d3, p4) {
  d3 = UTCdISO(d3);
  return pad(d3.getUTCFullYear() % 100, p4, 2);
}
function formatUTCFullYear(d3, p4) {
  return pad(d3.getUTCFullYear() % 1e4, p4, 4);
}
function formatUTCFullYearISO(d3, p4) {
  var day3 = d3.getUTCDay();
  d3 = day3 >= 4 || day3 === 0 ? utcThursday(d3) : utcThursday.ceil(d3);
  return pad(d3.getUTCFullYear() % 1e4, p4, 4);
}
function formatUTCZone() {
  return "+0000";
}
function formatLiteralPercent() {
  return "%";
}
function formatUnixTimestamp(d3) {
  return +d3;
}
function formatUnixTimestampSeconds(d3) {
  return Math.floor(+d3 / 1e3);
}

// node_modules/d3-scale/node_modules/d3-time-format/src/defaultLocale.js
var locale2;
var timeFormat;
var timeParse;
var utcFormat;
var utcParse;
defaultLocale2({
  dateTime: "%x, %X",
  date: "%-m/%-d/%Y",
  time: "%-I:%M:%S %p",
  periods: ["AM", "PM"],
  days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
  shortDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
  shortMonths: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
});
function defaultLocale2(definition) {
  locale2 = formatLocale(definition);
  timeFormat = locale2.format;
  timeParse = locale2.parse;
  utcFormat = locale2.utcFormat;
  utcParse = locale2.utcParse;
  return locale2;
}

// node_modules/d3-scale/node_modules/d3-time-format/src/isoFormat.js
var isoSpecifier = "%Y-%m-%dT%H:%M:%S.%LZ";
function formatIsoNative(date2) {
  return date2.toISOString();
}
var formatIso = Date.prototype.toISOString ? formatIsoNative : utcFormat(isoSpecifier);

// node_modules/d3-scale/node_modules/d3-time-format/src/isoParse.js
function parseIsoNative(string) {
  var date2 = new Date(string);
  return isNaN(date2) ? null : date2;
}
var parseIso = +/* @__PURE__ */ new Date("2000-01-01T00:00:00.000Z") ? parseIsoNative : utcParse(isoSpecifier);

// node_modules/d3-scale/src/time.js
function date(t8) {
  return new Date(t8);
}
function number3(t8) {
  return t8 instanceof Date ? +t8 : +/* @__PURE__ */ new Date(+t8);
}
function calendar(ticks2, tickInterval, year3, month3, week, day3, hour3, minute3, second4, format3) {
  var scale2 = continuous(), invert = scale2.invert, domain = scale2.domain;
  var formatMillisecond = format3(".%L"), formatSecond = format3(":%S"), formatMinute = format3("%I:%M"), formatHour = format3("%I %p"), formatDay = format3("%a %d"), formatWeek = format3("%b %d"), formatMonth = format3("%B"), formatYear3 = format3("%Y");
  function tickFormat2(date2) {
    return (second4(date2) < date2 ? formatMillisecond : minute3(date2) < date2 ? formatSecond : hour3(date2) < date2 ? formatMinute : day3(date2) < date2 ? formatHour : month3(date2) < date2 ? week(date2) < date2 ? formatDay : formatWeek : year3(date2) < date2 ? formatMonth : formatYear3)(date2);
  }
  scale2.invert = function(y4) {
    return new Date(invert(y4));
  };
  scale2.domain = function(_3) {
    return arguments.length ? domain(Array.from(_3, number3)) : domain().map(date);
  };
  scale2.ticks = function(interval) {
    var d3 = domain();
    return ticks2(d3[0], d3[d3.length - 1], interval == null ? 10 : interval);
  };
  scale2.tickFormat = function(count3, specifier) {
    return specifier == null ? tickFormat2 : format3(specifier);
  };
  scale2.nice = function(interval) {
    var d3 = domain();
    if (!interval || typeof interval.range !== "function") interval = tickInterval(d3[0], d3[d3.length - 1], interval == null ? 10 : interval);
    return interval ? domain(nice2(d3, interval)) : scale2;
  };
  scale2.copy = function() {
    return copy(scale2, calendar(ticks2, tickInterval, year3, month3, week, day3, hour3, minute3, second4, format3));
  };
  return scale2;
}
function time() {
  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute, second, timeFormat).domain([new Date(2e3, 0, 1), new Date(2e3, 0, 2)]), arguments);
}

// node_modules/d3-scale/src/utcTime.js
function utcTime() {
  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcSunday, utcDay, utcHour, utcMinute, second, utcFormat).domain([Date.UTC(2e3, 0, 1), Date.UTC(2e3, 0, 2)]), arguments);
}

// node_modules/d3-scale-chromatic/src/colors.js
function colors_default(specifier) {
  var n7 = specifier.length / 6 | 0, colors3 = new Array(n7), i6 = 0;
  while (i6 < n7) colors3[i6] = "#" + specifier.slice(i6 * 6, ++i6 * 6);
  return colors3;
}

// node_modules/d3-scale-chromatic/src/categorical/category10.js
var category10_default = colors_default("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf");

// node_modules/d3-scale-chromatic/src/categorical/Accent.js
var Accent_default = colors_default("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666");

// node_modules/d3-scale-chromatic/src/categorical/Dark2.js
var Dark2_default = colors_default("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666");

// node_modules/d3-scale-chromatic/src/categorical/observable10.js
var observable10_default = colors_default("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0");

// node_modules/d3-scale-chromatic/src/categorical/Paired.js
var Paired_default = colors_default("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928");

// node_modules/d3-scale-chromatic/src/categorical/Pastel1.js
var Pastel1_default = colors_default("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2");

// node_modules/d3-scale-chromatic/src/categorical/Pastel2.js
var Pastel2_default = colors_default("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc");

// node_modules/d3-scale-chromatic/src/categorical/Set1.js
var Set1_default = colors_default("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999");

// node_modules/d3-scale-chromatic/src/categorical/Set2.js
var Set2_default = colors_default("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3");

// node_modules/d3-scale-chromatic/src/categorical/Set3.js
var Set3_default = colors_default("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f");

// node_modules/d3-scale-chromatic/src/categorical/Tableau10.js
var Tableau10_default = colors_default("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");

// node_modules/d3-scale-chromatic/src/ramp.js
var ramp_default = (scheme28) => rgbBasis(scheme28[scheme28.length - 1]);

// node_modules/d3-scale-chromatic/src/diverging/BrBG.js
var scheme = new Array(3).concat(
  "d8b365f5f5f55ab4ac",
  "a6611adfc27d80cdc1018571",
  "a6611adfc27df5f5f580cdc1018571",
  "8c510ad8b365f6e8c3c7eae55ab4ac01665e",
  "8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e",
  "8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e",
  "8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e",
  "5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30",
  "5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30"
).map(colors_default);
var BrBG_default = ramp_default(scheme);

// node_modules/d3-scale-chromatic/src/diverging/PRGn.js
var scheme2 = new Array(3).concat(
  "af8dc3f7f7f77fbf7b",
  "7b3294c2a5cfa6dba0008837",
  "7b3294c2a5cff7f7f7a6dba0008837",
  "762a83af8dc3e7d4e8d9f0d37fbf7b1b7837",
  "762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837",
  "762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837",
  "762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837",
  "40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b",
  "40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b"
).map(colors_default);
var PRGn_default = ramp_default(scheme2);

// node_modules/d3-scale-chromatic/src/diverging/PiYG.js
var scheme3 = new Array(3).concat(
  "e9a3c9f7f7f7a1d76a",
  "d01c8bf1b6dab8e1864dac26",
  "d01c8bf1b6daf7f7f7b8e1864dac26",
  "c51b7de9a3c9fde0efe6f5d0a1d76a4d9221",
  "c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221",
  "c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221",
  "c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221",
  "8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419",
  "8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419"
).map(colors_default);
var PiYG_default = ramp_default(scheme3);

// node_modules/d3-scale-chromatic/src/diverging/PuOr.js
var scheme4 = new Array(3).concat(
  "998ec3f7f7f7f1a340",
  "5e3c99b2abd2fdb863e66101",
  "5e3c99b2abd2f7f7f7fdb863e66101",
  "542788998ec3d8daebfee0b6f1a340b35806",
  "542788998ec3d8daebf7f7f7fee0b6f1a340b35806",
  "5427888073acb2abd2d8daebfee0b6fdb863e08214b35806",
  "5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806",
  "2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08",
  "2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08"
).map(colors_default);
var PuOr_default = ramp_default(scheme4);

// node_modules/d3-scale-chromatic/src/diverging/RdBu.js
var scheme5 = new Array(3).concat(
  "ef8a62f7f7f767a9cf",
  "ca0020f4a58292c5de0571b0",
  "ca0020f4a582f7f7f792c5de0571b0",
  "b2182bef8a62fddbc7d1e5f067a9cf2166ac",
  "b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac",
  "b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac",
  "b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac",
  "67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061",
  "67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061"
).map(colors_default);
var RdBu_default = ramp_default(scheme5);

// node_modules/d3-scale-chromatic/src/diverging/RdGy.js
var scheme6 = new Array(3).concat(
  "ef8a62ffffff999999",
  "ca0020f4a582bababa404040",
  "ca0020f4a582ffffffbababa404040",
  "b2182bef8a62fddbc7e0e0e09999994d4d4d",
  "b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d",
  "b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d",
  "b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d",
  "67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a",
  "67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a"
).map(colors_default);
var RdGy_default = ramp_default(scheme6);

// node_modules/d3-scale-chromatic/src/diverging/RdYlBu.js
var scheme7 = new Array(3).concat(
  "fc8d59ffffbf91bfdb",
  "d7191cfdae61abd9e92c7bb6",
  "d7191cfdae61ffffbfabd9e92c7bb6",
  "d73027fc8d59fee090e0f3f891bfdb4575b4",
  "d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4",
  "d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4",
  "d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4",
  "a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695",
  "a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695"
).map(colors_default);
var RdYlBu_default = ramp_default(scheme7);

// node_modules/d3-scale-chromatic/src/diverging/RdYlGn.js
var scheme8 = new Array(3).concat(
  "fc8d59ffffbf91cf60",
  "d7191cfdae61a6d96a1a9641",
  "d7191cfdae61ffffbfa6d96a1a9641",
  "d73027fc8d59fee08bd9ef8b91cf601a9850",
  "d73027fc8d59fee08bffffbfd9ef8b91cf601a9850",
  "d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850",
  "d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850",
  "a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837",
  "a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837"
).map(colors_default);
var RdYlGn_default = ramp_default(scheme8);

// node_modules/d3-scale-chromatic/src/diverging/Spectral.js
var scheme9 = new Array(3).concat(
  "fc8d59ffffbf99d594",
  "d7191cfdae61abdda42b83ba",
  "d7191cfdae61ffffbfabdda42b83ba",
  "d53e4ffc8d59fee08be6f59899d5943288bd",
  "d53e4ffc8d59fee08bffffbfe6f59899d5943288bd",
  "d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd",
  "d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd",
  "9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2",
  "9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2"
).map(colors_default);
var Spectral_default = ramp_default(scheme9);

// node_modules/d3-scale-chromatic/src/sequential-multi/BuGn.js
var scheme10 = new Array(3).concat(
  "e5f5f999d8c92ca25f",
  "edf8fbb2e2e266c2a4238b45",
  "edf8fbb2e2e266c2a42ca25f006d2c",
  "edf8fbccece699d8c966c2a42ca25f006d2c",
  "edf8fbccece699d8c966c2a441ae76238b45005824",
  "f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824",
  "f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b"
).map(colors_default);
var BuGn_default = ramp_default(scheme10);

// node_modules/d3-scale-chromatic/src/sequential-multi/BuPu.js
var scheme11 = new Array(3).concat(
  "e0ecf49ebcda8856a7",
  "edf8fbb3cde38c96c688419d",
  "edf8fbb3cde38c96c68856a7810f7c",
  "edf8fbbfd3e69ebcda8c96c68856a7810f7c",
  "edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b",
  "f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b",
  "f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b"
).map(colors_default);
var BuPu_default = ramp_default(scheme11);

// node_modules/d3-scale-chromatic/src/sequential-multi/GnBu.js
var scheme12 = new Array(3).concat(
  "e0f3dba8ddb543a2ca",
  "f0f9e8bae4bc7bccc42b8cbe",
  "f0f9e8bae4bc7bccc443a2ca0868ac",
  "f0f9e8ccebc5a8ddb57bccc443a2ca0868ac",
  "f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e",
  "f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e",
  "f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081"
).map(colors_default);
var GnBu_default = ramp_default(scheme12);

// node_modules/d3-scale-chromatic/src/sequential-multi/OrRd.js
var scheme13 = new Array(3).concat(
  "fee8c8fdbb84e34a33",
  "fef0d9fdcc8afc8d59d7301f",
  "fef0d9fdcc8afc8d59e34a33b30000",
  "fef0d9fdd49efdbb84fc8d59e34a33b30000",
  "fef0d9fdd49efdbb84fc8d59ef6548d7301f990000",
  "fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000",
  "fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000"
).map(colors_default);
var OrRd_default = ramp_default(scheme13);

// node_modules/d3-scale-chromatic/src/sequential-multi/PuBuGn.js
var scheme14 = new Array(3).concat(
  "ece2f0a6bddb1c9099",
  "f6eff7bdc9e167a9cf02818a",
  "f6eff7bdc9e167a9cf1c9099016c59",
  "f6eff7d0d1e6a6bddb67a9cf1c9099016c59",
  "f6eff7d0d1e6a6bddb67a9cf3690c002818a016450",
  "fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450",
  "fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636"
).map(colors_default);
var PuBuGn_default = ramp_default(scheme14);

// node_modules/d3-scale-chromatic/src/sequential-multi/PuBu.js
var scheme15 = new Array(3).concat(
  "ece7f2a6bddb2b8cbe",
  "f1eef6bdc9e174a9cf0570b0",
  "f1eef6bdc9e174a9cf2b8cbe045a8d",
  "f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d",
  "f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b",
  "fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b",
  "fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858"
).map(colors_default);
var PuBu_default = ramp_default(scheme15);

// node_modules/d3-scale-chromatic/src/sequential-multi/PuRd.js
var scheme16 = new Array(3).concat(
  "e7e1efc994c7dd1c77",
  "f1eef6d7b5d8df65b0ce1256",
  "f1eef6d7b5d8df65b0dd1c77980043",
  "f1eef6d4b9dac994c7df65b0dd1c77980043",
  "f1eef6d4b9dac994c7df65b0e7298ace125691003f",
  "f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f",
  "f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f"
).map(colors_default);
var PuRd_default = ramp_default(scheme16);

// node_modules/d3-scale-chromatic/src/sequential-multi/RdPu.js
var scheme17 = new Array(3).concat(
  "fde0ddfa9fb5c51b8a",
  "feebe2fbb4b9f768a1ae017e",
  "feebe2fbb4b9f768a1c51b8a7a0177",
  "feebe2fcc5c0fa9fb5f768a1c51b8a7a0177",
  "feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177",
  "fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177",
  "fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a"
).map(colors_default);
var RdPu_default = ramp_default(scheme17);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlGnBu.js
var scheme18 = new Array(3).concat(
  "edf8b17fcdbb2c7fb8",
  "ffffcca1dab441b6c4225ea8",
  "ffffcca1dab441b6c42c7fb8253494",
  "ffffccc7e9b47fcdbb41b6c42c7fb8253494",
  "ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84",
  "ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84",
  "ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58"
).map(colors_default);
var YlGnBu_default = ramp_default(scheme18);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlGn.js
var scheme19 = new Array(3).concat(
  "f7fcb9addd8e31a354",
  "ffffccc2e69978c679238443",
  "ffffccc2e69978c67931a354006837",
  "ffffccd9f0a3addd8e78c67931a354006837",
  "ffffccd9f0a3addd8e78c67941ab5d238443005a32",
  "ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32",
  "ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529"
).map(colors_default);
var YlGn_default = ramp_default(scheme19);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlOrBr.js
var scheme20 = new Array(3).concat(
  "fff7bcfec44fd95f0e",
  "ffffd4fed98efe9929cc4c02",
  "ffffd4fed98efe9929d95f0e993404",
  "ffffd4fee391fec44ffe9929d95f0e993404",
  "ffffd4fee391fec44ffe9929ec7014cc4c028c2d04",
  "ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04",
  "ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506"
).map(colors_default);
var YlOrBr_default = ramp_default(scheme20);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlOrRd.js
var scheme21 = new Array(3).concat(
  "ffeda0feb24cf03b20",
  "ffffb2fecc5cfd8d3ce31a1c",
  "ffffb2fecc5cfd8d3cf03b20bd0026",
  "ffffb2fed976feb24cfd8d3cf03b20bd0026",
  "ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026",
  "ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026",
  "ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026"
).map(colors_default);
var YlOrRd_default = ramp_default(scheme21);

// node_modules/d3-scale-chromatic/src/sequential-single/Blues.js
var scheme22 = new Array(3).concat(
  "deebf79ecae13182bd",
  "eff3ffbdd7e76baed62171b5",
  "eff3ffbdd7e76baed63182bd08519c",
  "eff3ffc6dbef9ecae16baed63182bd08519c",
  "eff3ffc6dbef9ecae16baed64292c62171b5084594",
  "f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594",
  "f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b"
).map(colors_default);
var Blues_default = ramp_default(scheme22);

// node_modules/d3-scale-chromatic/src/sequential-single/Greens.js
var scheme23 = new Array(3).concat(
  "e5f5e0a1d99b31a354",
  "edf8e9bae4b374c476238b45",
  "edf8e9bae4b374c47631a354006d2c",
  "edf8e9c7e9c0a1d99b74c47631a354006d2c",
  "edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32",
  "f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32",
  "f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b"
).map(colors_default);
var Greens_default = ramp_default(scheme23);

// node_modules/d3-scale-chromatic/src/sequential-single/Greys.js
var scheme24 = new Array(3).concat(
  "f0f0f0bdbdbd636363",
  "f7f7f7cccccc969696525252",
  "f7f7f7cccccc969696636363252525",
  "f7f7f7d9d9d9bdbdbd969696636363252525",
  "f7f7f7d9d9d9bdbdbd969696737373525252252525",
  "fffffff0f0f0d9d9d9bdbdbd969696737373525252252525",
  "fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000"
).map(colors_default);
var Greys_default = ramp_default(scheme24);

// node_modules/d3-scale-chromatic/src/sequential-single/Purples.js
var scheme25 = new Array(3).concat(
  "efedf5bcbddc756bb1",
  "f2f0f7cbc9e29e9ac86a51a3",
  "f2f0f7cbc9e29e9ac8756bb154278f",
  "f2f0f7dadaebbcbddc9e9ac8756bb154278f",
  "f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486",
  "fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486",
  "fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d"
).map(colors_default);
var Purples_default = ramp_default(scheme25);

// node_modules/d3-scale-chromatic/src/sequential-single/Reds.js
var scheme26 = new Array(3).concat(
  "fee0d2fc9272de2d26",
  "fee5d9fcae91fb6a4acb181d",
  "fee5d9fcae91fb6a4ade2d26a50f15",
  "fee5d9fcbba1fc9272fb6a4ade2d26a50f15",
  "fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d",
  "fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d",
  "fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d"
).map(colors_default);
var Reds_default = ramp_default(scheme26);

// node_modules/d3-scale-chromatic/src/sequential-single/Oranges.js
var scheme27 = new Array(3).concat(
  "fee6cefdae6be6550d",
  "feeddefdbe85fd8d3cd94701",
  "feeddefdbe85fd8d3ce6550da63603",
  "feeddefdd0a2fdae6bfd8d3ce6550da63603",
  "feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04",
  "fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04",
  "fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704"
).map(colors_default);
var Oranges_default = ramp_default(scheme27);

// node_modules/d3-scale-chromatic/src/sequential-multi/cividis.js
function cividis_default(t8) {
  t8 = Math.max(0, Math.min(1, t8));
  return "rgb(" + Math.max(0, Math.min(255, Math.round(-4.54 - t8 * (35.34 - t8 * (2381.73 - t8 * (6402.7 - t8 * (7024.72 - t8 * 2710.57))))))) + ", " + Math.max(0, Math.min(255, Math.round(32.49 + t8 * (170.73 + t8 * (52.82 - t8 * (131.46 - t8 * (176.58 - t8 * 67.37))))))) + ", " + Math.max(0, Math.min(255, Math.round(81.24 + t8 * (442.36 - t8 * (2482.43 - t8 * (6167.24 - t8 * (6614.94 - t8 * 2475.67))))))) + ")";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/cubehelix.js
var cubehelix_default = cubehelixLong(cubehelix(300, 0.5, 0), cubehelix(-240, 0.5, 1));

// node_modules/d3-scale-chromatic/src/sequential-multi/rainbow.js
var warm = cubehelixLong(cubehelix(-100, 0.75, 0.35), cubehelix(80, 1.5, 0.8));
var cool = cubehelixLong(cubehelix(260, 0.75, 0.35), cubehelix(80, 1.5, 0.8));
var c = cubehelix();
function rainbow_default(t8) {
  if (t8 < 0 || t8 > 1) t8 -= Math.floor(t8);
  var ts2 = Math.abs(t8 - 0.5);
  c.h = 360 * t8 - 100;
  c.s = 1.5 - 1.5 * ts2;
  c.l = 0.8 - 0.9 * ts2;
  return c + "";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/sinebow.js
var c6 = rgb();
var pi_1_3 = Math.PI / 3;
var pi_2_3 = Math.PI * 2 / 3;
function sinebow_default(t8) {
  var x4;
  t8 = (0.5 - t8) * Math.PI;
  c6.r = 255 * (x4 = Math.sin(t8)) * x4;
  c6.g = 255 * (x4 = Math.sin(t8 + pi_1_3)) * x4;
  c6.b = 255 * (x4 = Math.sin(t8 + pi_2_3)) * x4;
  return c6 + "";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/turbo.js
function turbo_default(t8) {
  t8 = Math.max(0, Math.min(1, t8));
  return "rgb(" + Math.max(0, Math.min(255, Math.round(34.61 + t8 * (1172.33 - t8 * (10793.56 - t8 * (33300.12 - t8 * (38394.49 - t8 * 14825.05))))))) + ", " + Math.max(0, Math.min(255, Math.round(23.31 + t8 * (557.33 + t8 * (1225.33 - t8 * (3574.96 - t8 * (1073.77 + t8 * 707.56))))))) + ", " + Math.max(0, Math.min(255, Math.round(27.2 + t8 * (3211.1 - t8 * (15327.97 - t8 * (27814 - t8 * (22569.18 - t8 * 6838.66))))))) + ")";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/viridis.js
function ramp(range2) {
  var n7 = range2.length;
  return function(t8) {
    return range2[Math.max(0, Math.min(n7 - 1, Math.floor(t8 * n7)))];
  };
}
var viridis_default = ramp(colors_default("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725"));
var magma = ramp(colors_default("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf"));
var inferno = ramp(colors_default("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4"));
var plasma = ramp(colors_default("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_isFunction = __toESM(require_isFunction());
var import_without = __toESM(require_without());

// node_modules/d3-shape/src/constant.js
function constant_default(x4) {
  return function constant2() {
    return x4;
  };
}

// node_modules/d3-shape/src/math.js
var cos = Math.cos;
var sin = Math.sin;
var sqrt2 = Math.sqrt;
var epsilon = 1e-12;
var pi = Math.PI;
var halfPi = pi / 2;
var tau = 2 * pi;

// node_modules/d3-path/src/path.js
var pi2 = Math.PI;
var tau2 = 2 * pi2;
var epsilon2 = 1e-6;
var tauEpsilon = tau2 - epsilon2;
function append(strings) {
  this._ += strings[0];
  for (let i6 = 1, n7 = strings.length; i6 < n7; ++i6) {
    this._ += arguments[i6] + strings[i6];
  }
}
function appendRound(digits) {
  let d3 = Math.floor(digits);
  if (!(d3 >= 0)) throw new Error(`invalid digits: ${digits}`);
  if (d3 > 15) return append;
  const k5 = 10 ** d3;
  return function(strings) {
    this._ += strings[0];
    for (let i6 = 1, n7 = strings.length; i6 < n7; ++i6) {
      this._ += Math.round(arguments[i6] * k5) / k5 + strings[i6];
    }
  };
}
var Path = class {
  constructor(digits) {
    this._x0 = this._y0 = // start of current subpath
    this._x1 = this._y1 = null;
    this._ = "";
    this._append = digits == null ? append : appendRound(digits);
  }
  moveTo(x4, y4) {
    this._append`M${this._x0 = this._x1 = +x4},${this._y0 = this._y1 = +y4}`;
  }
  closePath() {
    if (this._x1 !== null) {
      this._x1 = this._x0, this._y1 = this._y0;
      this._append`Z`;
    }
  }
  lineTo(x4, y4) {
    this._append`L${this._x1 = +x4},${this._y1 = +y4}`;
  }
  quadraticCurveTo(x1, y1, x4, y4) {
    this._append`Q${+x1},${+y1},${this._x1 = +x4},${this._y1 = +y4}`;
  }
  bezierCurveTo(x1, y1, x22, y22, x4, y4) {
    this._append`C${+x1},${+y1},${+x22},${+y22},${this._x1 = +x4},${this._y1 = +y4}`;
  }
  arcTo(x1, y1, x22, y22, r7) {
    x1 = +x1, y1 = +y1, x22 = +x22, y22 = +y22, r7 = +r7;
    if (r7 < 0) throw new Error(`negative radius: ${r7}`);
    let x0 = this._x1, y0 = this._y1, x21 = x22 - x1, y21 = y22 - y1, x01 = x0 - x1, y01 = y0 - y1, l01_2 = x01 * x01 + y01 * y01;
    if (this._x1 === null) {
      this._append`M${this._x1 = x1},${this._y1 = y1}`;
    } else if (!(l01_2 > epsilon2)) ;
    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon2) || !r7) {
      this._append`L${this._x1 = x1},${this._y1 = y1}`;
    } else {
      let x20 = x22 - x0, y20 = y22 - y0, l21_2 = x21 * x21 + y21 * y21, l20_2 = x20 * x20 + y20 * y20, l21 = Math.sqrt(l21_2), l01 = Math.sqrt(l01_2), l5 = r7 * Math.tan((pi2 - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2), t01 = l5 / l01, t21 = l5 / l21;
      if (Math.abs(t01 - 1) > epsilon2) {
        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;
      }
      this._append`A${r7},${r7},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;
    }
  }
  arc(x4, y4, r7, a0, a1, ccw) {
    x4 = +x4, y4 = +y4, r7 = +r7, ccw = !!ccw;
    if (r7 < 0) throw new Error(`negative radius: ${r7}`);
    let dx = r7 * Math.cos(a0), dy = r7 * Math.sin(a0), x0 = x4 + dx, y0 = y4 + dy, cw = 1 ^ ccw, da2 = ccw ? a0 - a1 : a1 - a0;
    if (this._x1 === null) {
      this._append`M${x0},${y0}`;
    } else if (Math.abs(this._x1 - x0) > epsilon2 || Math.abs(this._y1 - y0) > epsilon2) {
      this._append`L${x0},${y0}`;
    }
    if (!r7) return;
    if (da2 < 0) da2 = da2 % tau2 + tau2;
    if (da2 > tauEpsilon) {
      this._append`A${r7},${r7},0,1,${cw},${x4 - dx},${y4 - dy}A${r7},${r7},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;
    } else if (da2 > epsilon2) {
      this._append`A${r7},${r7},0,${+(da2 >= pi2)},${cw},${this._x1 = x4 + r7 * Math.cos(a1)},${this._y1 = y4 + r7 * Math.sin(a1)}`;
    }
  }
  rect(x4, y4, w5, h2) {
    this._append`M${this._x0 = this._x1 = +x4},${this._y0 = this._y1 = +y4}h${w5 = +w5}v${+h2}h${-w5}Z`;
  }
  toString() {
    return this._;
  }
};
function path() {
  return new Path();
}
path.prototype = Path.prototype;

// node_modules/d3-shape/src/path.js
function withPath(shape) {
  let digits = 3;
  shape.digits = function(_3) {
    if (!arguments.length) return digits;
    if (_3 == null) {
      digits = null;
    } else {
      const d3 = Math.floor(_3);
      if (!(d3 >= 0)) throw new RangeError(`invalid digits: ${_3}`);
      digits = d3;
    }
    return shape;
  };
  return () => new Path(digits);
}

// node_modules/d3-shape/src/array.js
var slice2 = Array.prototype.slice;
function array_default(x4) {
  return typeof x4 === "object" && "length" in x4 ? x4 : Array.from(x4);
}

// node_modules/d3-shape/src/curve/linear.js
function Linear(context) {
  this._context = context;
}
Linear.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x4, y4) : this._context.moveTo(x4, y4);
        break;
      case 1:
        this._point = 2;
      // falls through
      default:
        this._context.lineTo(x4, y4);
        break;
    }
  }
};
function linear_default(context) {
  return new Linear(context);
}

// node_modules/d3-shape/src/point.js
function x2(p4) {
  return p4[0];
}
function y2(p4) {
  return p4[1];
}

// node_modules/d3-shape/src/line.js
function line_default(x4, y4) {
  var defined = constant_default(true), context = null, curve = linear_default, output = null, path2 = withPath(line);
  x4 = typeof x4 === "function" ? x4 : x4 === void 0 ? x2 : constant_default(x4);
  y4 = typeof y4 === "function" ? y4 : y4 === void 0 ? y2 : constant_default(y4);
  function line(data) {
    var i6, n7 = (data = array_default(data)).length, d3, defined0 = false, buffer;
    if (context == null) output = curve(buffer = path2());
    for (i6 = 0; i6 <= n7; ++i6) {
      if (!(i6 < n7 && defined(d3 = data[i6], i6, data)) === defined0) {
        if (defined0 = !defined0) output.lineStart();
        else output.lineEnd();
      }
      if (defined0) output.point(+x4(d3, i6, data), +y4(d3, i6, data));
    }
    if (buffer) return output = null, buffer + "" || null;
  }
  line.x = function(_3) {
    return arguments.length ? (x4 = typeof _3 === "function" ? _3 : constant_default(+_3), line) : x4;
  };
  line.y = function(_3) {
    return arguments.length ? (y4 = typeof _3 === "function" ? _3 : constant_default(+_3), line) : y4;
  };
  line.defined = function(_3) {
    return arguments.length ? (defined = typeof _3 === "function" ? _3 : constant_default(!!_3), line) : defined;
  };
  line.curve = function(_3) {
    return arguments.length ? (curve = _3, context != null && (output = curve(context)), line) : curve;
  };
  line.context = function(_3) {
    return arguments.length ? (_3 == null ? context = output = null : output = curve(context = _3), line) : context;
  };
  return line;
}

// node_modules/d3-shape/src/area.js
function area_default(x0, y0, y1) {
  var x1 = null, defined = constant_default(true), context = null, curve = linear_default, output = null, path2 = withPath(area);
  x0 = typeof x0 === "function" ? x0 : x0 === void 0 ? x2 : constant_default(+x0);
  y0 = typeof y0 === "function" ? y0 : y0 === void 0 ? constant_default(0) : constant_default(+y0);
  y1 = typeof y1 === "function" ? y1 : y1 === void 0 ? y2 : constant_default(+y1);
  function area(data) {
    var i6, j3, k5, n7 = (data = array_default(data)).length, d3, defined0 = false, buffer, x0z = new Array(n7), y0z = new Array(n7);
    if (context == null) output = curve(buffer = path2());
    for (i6 = 0; i6 <= n7; ++i6) {
      if (!(i6 < n7 && defined(d3 = data[i6], i6, data)) === defined0) {
        if (defined0 = !defined0) {
          j3 = i6;
          output.areaStart();
          output.lineStart();
        } else {
          output.lineEnd();
          output.lineStart();
          for (k5 = i6 - 1; k5 >= j3; --k5) {
            output.point(x0z[k5], y0z[k5]);
          }
          output.lineEnd();
          output.areaEnd();
        }
      }
      if (defined0) {
        x0z[i6] = +x0(d3, i6, data), y0z[i6] = +y0(d3, i6, data);
        output.point(x1 ? +x1(d3, i6, data) : x0z[i6], y1 ? +y1(d3, i6, data) : y0z[i6]);
      }
    }
    if (buffer) return output = null, buffer + "" || null;
  }
  function arealine() {
    return line_default().defined(defined).curve(curve).context(context);
  }
  area.x = function(_3) {
    return arguments.length ? (x0 = typeof _3 === "function" ? _3 : constant_default(+_3), x1 = null, area) : x0;
  };
  area.x0 = function(_3) {
    return arguments.length ? (x0 = typeof _3 === "function" ? _3 : constant_default(+_3), area) : x0;
  };
  area.x1 = function(_3) {
    return arguments.length ? (x1 = _3 == null ? null : typeof _3 === "function" ? _3 : constant_default(+_3), area) : x1;
  };
  area.y = function(_3) {
    return arguments.length ? (y0 = typeof _3 === "function" ? _3 : constant_default(+_3), y1 = null, area) : y0;
  };
  area.y0 = function(_3) {
    return arguments.length ? (y0 = typeof _3 === "function" ? _3 : constant_default(+_3), area) : y0;
  };
  area.y1 = function(_3) {
    return arguments.length ? (y1 = _3 == null ? null : typeof _3 === "function" ? _3 : constant_default(+_3), area) : y1;
  };
  area.lineX0 = area.lineY0 = function() {
    return arealine().x(x0).y(y0);
  };
  area.lineY1 = function() {
    return arealine().x(x0).y(y1);
  };
  area.lineX1 = function() {
    return arealine().x(x1).y(y0);
  };
  area.defined = function(_3) {
    return arguments.length ? (defined = typeof _3 === "function" ? _3 : constant_default(!!_3), area) : defined;
  };
  area.curve = function(_3) {
    return arguments.length ? (curve = _3, context != null && (output = curve(context)), area) : curve;
  };
  area.context = function(_3) {
    return arguments.length ? (_3 == null ? context = output = null : output = curve(context = _3), area) : context;
  };
  return area;
}

// node_modules/d3-shape/src/curve/radial.js
var curveRadialLinear = curveRadial(linear_default);
function Radial(curve) {
  this._curve = curve;
}
Radial.prototype = {
  areaStart: function() {
    this._curve.areaStart();
  },
  areaEnd: function() {
    this._curve.areaEnd();
  },
  lineStart: function() {
    this._curve.lineStart();
  },
  lineEnd: function() {
    this._curve.lineEnd();
  },
  point: function(a5, r7) {
    this._curve.point(r7 * Math.sin(a5), r7 * -Math.cos(a5));
  }
};
function curveRadial(curve) {
  function radial2(context) {
    return new Radial(curve(context));
  }
  radial2._curve = curve;
  return radial2;
}

// node_modules/d3-shape/src/symbol/asterisk.js
var sqrt3 = sqrt2(3);

// node_modules/d3-shape/src/symbol/diamond.js
var tan30 = sqrt2(1 / 3);
var tan30_2 = tan30 * 2;

// node_modules/d3-shape/src/symbol/star.js
var kr = sin(pi / 10) / sin(7 * pi / 10);
var kx = sin(tau / 10) * kr;
var ky = -cos(tau / 10) * kr;

// node_modules/d3-shape/src/symbol/triangle.js
var sqrt32 = sqrt2(3);

// node_modules/d3-shape/src/symbol/triangle2.js
var sqrt33 = sqrt2(3);

// node_modules/d3-shape/src/symbol/wye.js
var s = sqrt2(3) / 2;
var k2 = 1 / sqrt2(12);
var a = (k2 / 2 + 1) * 3;

// node_modules/d3-shape/src/noop.js
function noop_default() {
}

// node_modules/d3-shape/src/curve/basis.js
function point2(that, x4, y4) {
  that._context.bezierCurveTo(
    (2 * that._x0 + that._x1) / 3,
    (2 * that._y0 + that._y1) / 3,
    (that._x0 + 2 * that._x1) / 3,
    (that._y0 + 2 * that._y1) / 3,
    (that._x0 + 4 * that._x1 + x4) / 6,
    (that._y0 + 4 * that._y1 + y4) / 6
  );
}
function Basis(context) {
  this._context = context;
}
Basis.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 3:
        point2(this, this._x1, this._y1);
      // falls through
      case 2:
        this._context.lineTo(this._x1, this._y1);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x4, y4) : this._context.moveTo(x4, y4);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6);
      // falls through
      default:
        point2(this, x4, y4);
        break;
    }
    this._x0 = this._x1, this._x1 = x4;
    this._y0 = this._y1, this._y1 = y4;
  }
};
function basis_default(context) {
  return new Basis(context);
}

// node_modules/d3-shape/src/curve/basisClosed.js
function BasisClosed(context) {
  this._context = context;
}
BasisClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x2, this._y2);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);
        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x2, this._y2);
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        break;
      }
    }
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x2 = x4, this._y2 = y4;
        break;
      case 1:
        this._point = 2;
        this._x3 = x4, this._y3 = y4;
        break;
      case 2:
        this._point = 3;
        this._x4 = x4, this._y4 = y4;
        this._context.moveTo((this._x0 + 4 * this._x1 + x4) / 6, (this._y0 + 4 * this._y1 + y4) / 6);
        break;
      default:
        point2(this, x4, y4);
        break;
    }
    this._x0 = this._x1, this._x1 = x4;
    this._y0 = this._y1, this._y1 = y4;
  }
};
function basisClosed_default(context) {
  return new BasisClosed(context);
}

// node_modules/d3-shape/src/curve/basisOpen.js
function BasisOpen(context) {
  this._context = context;
}
BasisOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        var x0 = (this._x0 + 4 * this._x1 + x4) / 6, y0 = (this._y0 + 4 * this._y1 + y4) / 6;
        this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0);
        break;
      case 3:
        this._point = 4;
      // falls through
      default:
        point2(this, x4, y4);
        break;
    }
    this._x0 = this._x1, this._x1 = x4;
    this._y0 = this._y1, this._y1 = y4;
  }
};
function basisOpen_default(context) {
  return new BasisOpen(context);
}

// node_modules/d3-shape/src/curve/bundle.js
function Bundle(context, beta) {
  this._basis = new Basis(context);
  this._beta = beta;
}
Bundle.prototype = {
  lineStart: function() {
    this._x = [];
    this._y = [];
    this._basis.lineStart();
  },
  lineEnd: function() {
    var x4 = this._x, y4 = this._y, j3 = x4.length - 1;
    if (j3 > 0) {
      var x0 = x4[0], y0 = y4[0], dx = x4[j3] - x0, dy = y4[j3] - y0, i6 = -1, t8;
      while (++i6 <= j3) {
        t8 = i6 / j3;
        this._basis.point(
          this._beta * x4[i6] + (1 - this._beta) * (x0 + t8 * dx),
          this._beta * y4[i6] + (1 - this._beta) * (y0 + t8 * dy)
        );
      }
    }
    this._x = this._y = null;
    this._basis.lineEnd();
  },
  point: function(x4, y4) {
    this._x.push(+x4);
    this._y.push(+y4);
  }
};
var bundle_default = function custom(beta) {
  function bundle(context) {
    return beta === 1 ? new Basis(context) : new Bundle(context, beta);
  }
  bundle.beta = function(beta2) {
    return custom(+beta2);
  };
  return bundle;
}(0.85);

// node_modules/d3-shape/src/curve/cardinal.js
function point3(that, x4, y4) {
  that._context.bezierCurveTo(
    that._x1 + that._k * (that._x2 - that._x0),
    that._y1 + that._k * (that._y2 - that._y0),
    that._x2 + that._k * (that._x1 - x4),
    that._y2 + that._k * (that._y1 - y4),
    that._x2,
    that._y2
  );
}
function Cardinal(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
Cardinal.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x2, this._y2);
        break;
      case 3:
        point3(this, this._x1, this._y1);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x4, y4) : this._context.moveTo(x4, y4);
        break;
      case 1:
        this._point = 2;
        this._x1 = x4, this._y1 = y4;
        break;
      case 2:
        this._point = 3;
      // falls through
      default:
        point3(this, x4, y4);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x4;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y4;
  }
};
var cardinal_default = function custom2(tension) {
  function cardinal(context) {
    return new Cardinal(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom2(+tension2);
  };
  return cardinal;
}(0);

// node_modules/d3-shape/src/curve/cardinalClosed.js
function CardinalClosed(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
CardinalClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.lineTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        this.point(this._x5, this._y5);
        break;
      }
    }
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x3 = x4, this._y3 = y4;
        break;
      case 1:
        this._point = 2;
        this._context.moveTo(this._x4 = x4, this._y4 = y4);
        break;
      case 2:
        this._point = 3;
        this._x5 = x4, this._y5 = y4;
        break;
      default:
        point3(this, x4, y4);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x4;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y4;
  }
};
var cardinalClosed_default = function custom3(tension) {
  function cardinal(context) {
    return new CardinalClosed(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom3(+tension2);
  };
  return cardinal;
}(0);

// node_modules/d3-shape/src/curve/cardinalOpen.js
function CardinalOpen(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
CardinalOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);
        break;
      case 3:
        this._point = 4;
      // falls through
      default:
        point3(this, x4, y4);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x4;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y4;
  }
};
var cardinalOpen_default = function custom4(tension) {
  function cardinal(context) {
    return new CardinalOpen(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom4(+tension2);
  };
  return cardinal;
}(0);

// node_modules/d3-shape/src/curve/catmullRom.js
function point4(that, x4, y4) {
  var x1 = that._x1, y1 = that._y1, x22 = that._x2, y22 = that._y2;
  if (that._l01_a > epsilon) {
    var a5 = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a, n7 = 3 * that._l01_a * (that._l01_a + that._l12_a);
    x1 = (x1 * a5 - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n7;
    y1 = (y1 * a5 - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n7;
  }
  if (that._l23_a > epsilon) {
    var b5 = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a, m5 = 3 * that._l23_a * (that._l23_a + that._l12_a);
    x22 = (x22 * b5 + that._x1 * that._l23_2a - x4 * that._l12_2a) / m5;
    y22 = (y22 * b5 + that._y1 * that._l23_2a - y4 * that._l12_2a) / m5;
  }
  that._context.bezierCurveTo(x1, y1, x22, y22, that._x2, that._y2);
}
function CatmullRom(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRom.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x2, this._y2);
        break;
      case 3:
        this.point(this._x2, this._y2);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    if (this._point) {
      var x23 = this._x2 - x4, y23 = this._y2 - y4;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x4, y4) : this._context.moveTo(x4, y4);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
      // falls through
      default:
        point4(this, x4, y4);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x4;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y4;
  }
};
var catmullRom_default = function custom5(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom5(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/d3-shape/src/curve/catmullRomClosed.js
function CatmullRomClosed(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRomClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.lineTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        this.point(this._x5, this._y5);
        break;
      }
    }
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    if (this._point) {
      var x23 = this._x2 - x4, y23 = this._y2 - y4;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x3 = x4, this._y3 = y4;
        break;
      case 1:
        this._point = 2;
        this._context.moveTo(this._x4 = x4, this._y4 = y4);
        break;
      case 2:
        this._point = 3;
        this._x5 = x4, this._y5 = y4;
        break;
      default:
        point4(this, x4, y4);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x4;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y4;
  }
};
var catmullRomClosed_default = function custom6(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom6(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/d3-shape/src/curve/catmullRomOpen.js
function CatmullRomOpen(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRomOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    if (this._point) {
      var x23 = this._x2 - x4, y23 = this._y2 - y4;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);
        break;
      case 3:
        this._point = 4;
      // falls through
      default:
        point4(this, x4, y4);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x4;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y4;
  }
};
var catmullRomOpen_default = function custom7(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom7(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/d3-shape/src/curve/linearClosed.js
function LinearClosed(context) {
  this._context = context;
}
LinearClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._point) this._context.closePath();
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    if (this._point) this._context.lineTo(x4, y4);
    else this._point = 1, this._context.moveTo(x4, y4);
  }
};
function linearClosed_default(context) {
  return new LinearClosed(context);
}

// node_modules/d3-shape/src/curve/monotone.js
function sign(x4) {
  return x4 < 0 ? -1 : 1;
}
function slope3(that, x22, y22) {
  var h0 = that._x1 - that._x0, h1 = x22 - that._x1, s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0), s1 = (y22 - that._y1) / (h1 || h0 < 0 && -0), p4 = (s0 * h1 + s1 * h0) / (h0 + h1);
  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p4)) || 0;
}
function slope2(that, t8) {
  var h2 = that._x1 - that._x0;
  return h2 ? (3 * (that._y1 - that._y0) / h2 - t8) / 2 : t8;
}
function point5(that, t04, t14) {
  var x0 = that._x0, y0 = that._y0, x1 = that._x1, y1 = that._y1, dx = (x1 - x0) / 3;
  that._context.bezierCurveTo(x0 + dx, y0 + dx * t04, x1 - dx, y1 - dx * t14, x1, y1);
}
function MonotoneX(context) {
  this._context = context;
}
MonotoneX.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = this._t0 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x1, this._y1);
        break;
      case 3:
        point5(this, this._t0, slope2(this, this._t0));
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    var t14 = NaN;
    x4 = +x4, y4 = +y4;
    if (x4 === this._x1 && y4 === this._y1) return;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x4, y4) : this._context.moveTo(x4, y4);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        point5(this, slope2(this, t14 = slope3(this, x4, y4)), t14);
        break;
      default:
        point5(this, this._t0, t14 = slope3(this, x4, y4));
        break;
    }
    this._x0 = this._x1, this._x1 = x4;
    this._y0 = this._y1, this._y1 = y4;
    this._t0 = t14;
  }
};
function MonotoneY(context) {
  this._context = new ReflectContext(context);
}
(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x4, y4) {
  MonotoneX.prototype.point.call(this, y4, x4);
};
function ReflectContext(context) {
  this._context = context;
}
ReflectContext.prototype = {
  moveTo: function(x4, y4) {
    this._context.moveTo(y4, x4);
  },
  closePath: function() {
    this._context.closePath();
  },
  lineTo: function(x4, y4) {
    this._context.lineTo(y4, x4);
  },
  bezierCurveTo: function(x1, y1, x22, y22, x4, y4) {
    this._context.bezierCurveTo(y1, x1, y22, x22, y4, x4);
  }
};
function monotoneX(context) {
  return new MonotoneX(context);
}
function monotoneY(context) {
  return new MonotoneY(context);
}

// node_modules/d3-shape/src/curve/natural.js
function Natural(context) {
  this._context = context;
}
Natural.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x = [];
    this._y = [];
  },
  lineEnd: function() {
    var x4 = this._x, y4 = this._y, n7 = x4.length;
    if (n7) {
      this._line ? this._context.lineTo(x4[0], y4[0]) : this._context.moveTo(x4[0], y4[0]);
      if (n7 === 2) {
        this._context.lineTo(x4[1], y4[1]);
      } else {
        var px = controlPoints(x4), py = controlPoints(y4);
        for (var i0 = 0, i1 = 1; i1 < n7; ++i0, ++i1) {
          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x4[i1], y4[i1]);
        }
      }
    }
    if (this._line || this._line !== 0 && n7 === 1) this._context.closePath();
    this._line = 1 - this._line;
    this._x = this._y = null;
  },
  point: function(x4, y4) {
    this._x.push(+x4);
    this._y.push(+y4);
  }
};
function controlPoints(x4) {
  var i6, n7 = x4.length - 1, m5, a5 = new Array(n7), b5 = new Array(n7), r7 = new Array(n7);
  a5[0] = 0, b5[0] = 2, r7[0] = x4[0] + 2 * x4[1];
  for (i6 = 1; i6 < n7 - 1; ++i6) a5[i6] = 1, b5[i6] = 4, r7[i6] = 4 * x4[i6] + 2 * x4[i6 + 1];
  a5[n7 - 1] = 2, b5[n7 - 1] = 7, r7[n7 - 1] = 8 * x4[n7 - 1] + x4[n7];
  for (i6 = 1; i6 < n7; ++i6) m5 = a5[i6] / b5[i6 - 1], b5[i6] -= m5, r7[i6] -= m5 * r7[i6 - 1];
  a5[n7 - 1] = r7[n7 - 1] / b5[n7 - 1];
  for (i6 = n7 - 2; i6 >= 0; --i6) a5[i6] = (r7[i6] - a5[i6 + 1]) / b5[i6];
  b5[n7 - 1] = (x4[n7] + a5[n7 - 1]) / 2;
  for (i6 = 0; i6 < n7 - 1; ++i6) b5[i6] = 2 * x4[i6 + 1] - a5[i6 + 1];
  return [a5, b5];
}
function natural_default(context) {
  return new Natural(context);
}

// node_modules/d3-shape/src/curve/step.js
function Step(context, t8) {
  this._context = context;
  this._t = t8;
}
Step.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x = this._y = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;
  },
  point: function(x4, y4) {
    x4 = +x4, y4 = +y4;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x4, y4) : this._context.moveTo(x4, y4);
        break;
      case 1:
        this._point = 2;
      // falls through
      default: {
        if (this._t <= 0) {
          this._context.lineTo(this._x, y4);
          this._context.lineTo(x4, y4);
        } else {
          var x1 = this._x * (1 - this._t) + x4 * this._t;
          this._context.lineTo(x1, this._y);
          this._context.lineTo(x1, y4);
        }
        break;
      }
    }
    this._x = x4, this._y = y4;
  }
};
function step_default(context) {
  return new Step(context, 0.5);
}
function stepBefore(context) {
  return new Step(context, 0);
}
function stepAfter(context) {
  return new Step(context, 1);
}

// node_modules/d3-shape/src/offset/none.js
function none_default(series, order) {
  if (!((n7 = series.length) > 1)) return;
  for (var i6 = 1, j3, s0, s1 = series[order[0]], n7, m5 = s1.length; i6 < n7; ++i6) {
    s0 = s1, s1 = series[order[i6]];
    for (j3 = 0; j3 < m5; ++j3) {
      s1[j3][1] += s1[j3][0] = isNaN(s0[j3][1]) ? s0[j3][0] : s0[j3][1];
    }
  }
}

// node_modules/d3-shape/src/order/none.js
function none_default2(series) {
  var n7 = series.length, o5 = new Array(n7);
  while (--n7 >= 0) o5[n7] = n7;
  return o5;
}

// node_modules/d3-shape/src/offset/expand.js
function expand_default(series, order) {
  if (!((n7 = series.length) > 0)) return;
  for (var i6, n7, j3 = 0, m5 = series[0].length, y4; j3 < m5; ++j3) {
    for (y4 = i6 = 0; i6 < n7; ++i6) y4 += series[i6][j3][1] || 0;
    if (y4) for (i6 = 0; i6 < n7; ++i6) series[i6][j3][1] /= y4;
  }
  none_default(series, order);
}

// node_modules/d3-shape/src/offset/diverging.js
function diverging_default(series, order) {
  if (!((n7 = series.length) > 0)) return;
  for (var i6, j3 = 0, d3, dy, yp, yn3, n7, m5 = series[order[0]].length; j3 < m5; ++j3) {
    for (yp = yn3 = 0, i6 = 0; i6 < n7; ++i6) {
      if ((dy = (d3 = series[order[i6]][j3])[1] - d3[0]) > 0) {
        d3[0] = yp, d3[1] = yp += dy;
      } else if (dy < 0) {
        d3[1] = yn3, d3[0] = yn3 += dy;
      } else {
        d3[0] = 0, d3[1] = dy;
      }
    }
  }
}

// node_modules/d3-shape/src/offset/silhouette.js
function silhouette_default(series, order) {
  if (!((n7 = series.length) > 0)) return;
  for (var j3 = 0, s0 = series[order[0]], n7, m5 = s0.length; j3 < m5; ++j3) {
    for (var i6 = 0, y4 = 0; i6 < n7; ++i6) y4 += series[i6][j3][1] || 0;
    s0[j3][1] += s0[j3][0] = -y4 / 2;
  }
  none_default(series, order);
}

// node_modules/d3-shape/src/offset/wiggle.js
function wiggle_default(series, order) {
  if (!((n7 = series.length) > 0) || !((m5 = (s0 = series[order[0]]).length) > 0)) return;
  for (var y4 = 0, j3 = 1, s0, m5, n7; j3 < m5; ++j3) {
    for (var i6 = 0, s1 = 0, s22 = 0; i6 < n7; ++i6) {
      var si = series[order[i6]], sij0 = si[j3][1] || 0, sij1 = si[j3 - 1][1] || 0, s32 = (sij0 - sij1) / 2;
      for (var k5 = 0; k5 < i6; ++k5) {
        var sk = series[order[k5]], skj0 = sk[j3][1] || 0, skj1 = sk[j3 - 1][1] || 0;
        s32 += skj0 - skj1;
      }
      s1 += sij0, s22 += s32 * sij0;
    }
    s0[j3 - 1][1] += s0[j3 - 1][0] = y4;
    if (s1) y4 -= s22 / s1;
  }
  s0[j3 - 1][1] += s0[j3 - 1][0] = y4;
  none_default(series, order);
}

// node_modules/d3-shape/src/order/appearance.js
function appearance_default(series) {
  var peaks = series.map(peak);
  return none_default2(series).sort(function(a5, b5) {
    return peaks[a5] - peaks[b5];
  });
}
function peak(series) {
  var i6 = -1, j3 = 0, n7 = series.length, vi, vj = -Infinity;
  while (++i6 < n7) if ((vi = +series[i6][1]) > vj) vj = vi, j3 = i6;
  return j3;
}

// node_modules/d3-shape/src/order/ascending.js
function ascending_default(series) {
  var sums = series.map(sum2);
  return none_default2(series).sort(function(a5, b5) {
    return sums[a5] - sums[b5];
  });
}
function sum2(series) {
  var s5 = 0, i6 = -1, n7 = series.length, v6;
  while (++i6 < n7) if (v6 = +series[i6][1]) s5 += v6;
  return s5;
}

// node_modules/d3-shape/src/order/descending.js
function descending_default2(series) {
  return ascending_default(series).reverse();
}

// node_modules/d3-shape/src/order/insideOut.js
function insideOut_default(series) {
  var n7 = series.length, i6, j3, sums = series.map(sum2), order = appearance_default(series), top = 0, bottom = 0, tops = [], bottoms = [];
  for (i6 = 0; i6 < n7; ++i6) {
    j3 = order[i6];
    if (top < bottom) {
      top += sums[j3];
      tops.push(j3);
    } else {
      bottom += sums[j3];
      bottoms.push(j3);
    }
  }
  return bottoms.reverse().concat(tops);
}

// node_modules/d3-shape/src/order/reverse.js
function reverse_default(series) {
  return none_default2(series).reverse();
}

// node_modules/d3-format/src/formatDecimal.js
function formatDecimal_default2(x4) {
  return Math.abs(x4 = Math.round(x4)) >= 1e21 ? x4.toLocaleString("en").replace(/,/g, "") : x4.toString(10);
}
function formatDecimalParts2(x4, p4) {
  if ((i6 = (x4 = p4 ? x4.toExponential(p4 - 1) : x4.toExponential()).indexOf("e")) < 0) return null;
  var i6, coefficient = x4.slice(0, i6);
  return [
    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
    +x4.slice(i6 + 1)
  ];
}

// node_modules/d3-format/src/exponent.js
function exponent_default2(x4) {
  return x4 = formatDecimalParts2(Math.abs(x4)), x4 ? x4[1] : NaN;
}

// node_modules/d3-format/src/formatGroup.js
function formatGroup_default2(grouping, thousands) {
  return function(value, width) {
    var i6 = value.length, t8 = [], j3 = 0, g4 = grouping[0], length = 0;
    while (i6 > 0 && g4 > 0) {
      if (length + g4 + 1 > width) g4 = Math.max(1, width - length);
      t8.push(value.substring(i6 -= g4, i6 + g4));
      if ((length += g4 + 1) > width) break;
      g4 = grouping[j3 = (j3 + 1) % grouping.length];
    }
    return t8.reverse().join(thousands);
  };
}

// node_modules/d3-format/src/formatNumerals.js
function formatNumerals_default2(numerals) {
  return function(value) {
    return value.replace(/[0-9]/g, function(i6) {
      return numerals[+i6];
    });
  };
}

// node_modules/d3-format/src/formatSpecifier.js
var re2 = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier2(specifier) {
  if (!(match = re2.exec(specifier))) throw new Error("invalid format: " + specifier);
  var match;
  return new FormatSpecifier2({
    fill: match[1],
    align: match[2],
    sign: match[3],
    symbol: match[4],
    zero: match[5],
    width: match[6],
    comma: match[7],
    precision: match[8] && match[8].slice(1),
    trim: match[9],
    type: match[10]
  });
}
formatSpecifier2.prototype = FormatSpecifier2.prototype;
function FormatSpecifier2(specifier) {
  this.fill = specifier.fill === void 0 ? " " : specifier.fill + "";
  this.align = specifier.align === void 0 ? ">" : specifier.align + "";
  this.sign = specifier.sign === void 0 ? "-" : specifier.sign + "";
  this.symbol = specifier.symbol === void 0 ? "" : specifier.symbol + "";
  this.zero = !!specifier.zero;
  this.width = specifier.width === void 0 ? void 0 : +specifier.width;
  this.comma = !!specifier.comma;
  this.precision = specifier.precision === void 0 ? void 0 : +specifier.precision;
  this.trim = !!specifier.trim;
  this.type = specifier.type === void 0 ? "" : specifier.type + "";
}
FormatSpecifier2.prototype.toString = function() {
  return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (this.width === void 0 ? "" : Math.max(1, this.width | 0)) + (this.comma ? "," : "") + (this.precision === void 0 ? "" : "." + Math.max(0, this.precision | 0)) + (this.trim ? "~" : "") + this.type;
};

// node_modules/d3-format/src/formatTrim.js
function formatTrim_default2(s5) {
  out: for (var n7 = s5.length, i6 = 1, i0 = -1, i1; i6 < n7; ++i6) {
    switch (s5[i6]) {
      case ".":
        i0 = i1 = i6;
        break;
      case "0":
        if (i0 === 0) i0 = i6;
        i1 = i6;
        break;
      default:
        if (!+s5[i6]) break out;
        if (i0 > 0) i0 = 0;
        break;
    }
  }
  return i0 > 0 ? s5.slice(0, i0) + s5.slice(i1 + 1) : s5;
}

// node_modules/d3-format/src/formatPrefixAuto.js
var prefixExponent2;
function formatPrefixAuto_default2(x4, p4) {
  var d3 = formatDecimalParts2(x4, p4);
  if (!d3) return x4 + "";
  var coefficient = d3[0], exponent = d3[1], i6 = exponent - (prefixExponent2 = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n7 = coefficient.length;
  return i6 === n7 ? coefficient : i6 > n7 ? coefficient + new Array(i6 - n7 + 1).join("0") : i6 > 0 ? coefficient.slice(0, i6) + "." + coefficient.slice(i6) : "0." + new Array(1 - i6).join("0") + formatDecimalParts2(x4, Math.max(0, p4 + i6 - 1))[0];
}

// node_modules/d3-format/src/formatRounded.js
function formatRounded_default2(x4, p4) {
  var d3 = formatDecimalParts2(x4, p4);
  if (!d3) return x4 + "";
  var coefficient = d3[0], exponent = d3[1];
  return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join("0");
}

// node_modules/d3-format/src/formatTypes.js
var formatTypes_default2 = {
  "%": function(x4, p4) {
    return (x4 * 100).toFixed(p4);
  },
  "b": function(x4) {
    return Math.round(x4).toString(2);
  },
  "c": function(x4) {
    return x4 + "";
  },
  "d": formatDecimal_default2,
  "e": function(x4, p4) {
    return x4.toExponential(p4);
  },
  "f": function(x4, p4) {
    return x4.toFixed(p4);
  },
  "g": function(x4, p4) {
    return x4.toPrecision(p4);
  },
  "o": function(x4) {
    return Math.round(x4).toString(8);
  },
  "p": function(x4, p4) {
    return formatRounded_default2(x4 * 100, p4);
  },
  "r": formatRounded_default2,
  "s": formatPrefixAuto_default2,
  "X": function(x4) {
    return Math.round(x4).toString(16).toUpperCase();
  },
  "x": function(x4) {
    return Math.round(x4).toString(16);
  }
};

// node_modules/d3-format/src/identity.js
function identity_default3(x4) {
  return x4;
}

// node_modules/d3-format/src/locale.js
var map4 = Array.prototype.map;
var prefixes3 = ["y", "z", "a", "f", "p", "n", "µ", "m", "", "k", "M", "G", "T", "P", "E", "Z", "Y"];
function locale_default2(locale5) {
  var group3 = locale5.grouping === void 0 || locale5.thousands === void 0 ? identity_default3 : formatGroup_default2(map4.call(locale5.grouping, Number), locale5.thousands + ""), currencyPrefix = locale5.currency === void 0 ? "" : locale5.currency[0] + "", currencySuffix = locale5.currency === void 0 ? "" : locale5.currency[1] + "", decimal = locale5.decimal === void 0 ? "." : locale5.decimal + "", numerals = locale5.numerals === void 0 ? identity_default3 : formatNumerals_default2(map4.call(locale5.numerals, String)), percent = locale5.percent === void 0 ? "%" : locale5.percent + "", minus = locale5.minus === void 0 ? "-" : locale5.minus + "", nan = locale5.nan === void 0 ? "NaN" : locale5.nan + "";
  function newFormat(specifier) {
    specifier = formatSpecifier2(specifier);
    var fill = specifier.fill, align = specifier.align, sign2 = specifier.sign, symbol = specifier.symbol, zero2 = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;
    if (type === "n") comma = true, type = "g";
    else if (!formatTypes_default2[type]) precision === void 0 && (precision = 12), trim = true, type = "g";
    if (zero2 || fill === "0" && align === "=") zero2 = true, fill = "0", align = "=";
    var prefix2 = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";
    var formatType = formatTypes_default2[type], maybeSuffix = /[defgprs%]/.test(type);
    precision = precision === void 0 ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
    function format3(value) {
      var valuePrefix = prefix2, valueSuffix = suffix, i6, n7, c9;
      if (type === "c") {
        valueSuffix = formatType(value) + valueSuffix;
        value = "";
      } else {
        value = +value;
        var valueNegative = value < 0 || 1 / value < 0;
        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);
        if (trim) value = formatTrim_default2(value);
        if (valueNegative && +value === 0 && sign2 !== "+") valueNegative = false;
        valuePrefix = (valueNegative ? sign2 === "(" ? sign2 : minus : sign2 === "-" || sign2 === "(" ? "" : sign2) + valuePrefix;
        valueSuffix = (type === "s" ? prefixes3[8 + prefixExponent2 / 3] : "") + valueSuffix + (valueNegative && sign2 === "(" ? ")" : "");
        if (maybeSuffix) {
          i6 = -1, n7 = value.length;
          while (++i6 < n7) {
            if (c9 = value.charCodeAt(i6), 48 > c9 || c9 > 57) {
              valueSuffix = (c9 === 46 ? decimal + value.slice(i6 + 1) : value.slice(i6)) + valueSuffix;
              value = value.slice(0, i6);
              break;
            }
          }
        }
      }
      if (comma && !zero2) value = group3(value, Infinity);
      var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
      if (comma && zero2) value = group3(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";
      switch (align) {
        case "<":
          value = valuePrefix + value + valueSuffix + padding;
          break;
        case "=":
          value = valuePrefix + padding + value + valueSuffix;
          break;
        case "^":
          value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);
          break;
        default:
          value = padding + valuePrefix + value + valueSuffix;
          break;
      }
      return numerals(value);
    }
    format3.toString = function() {
      return specifier + "";
    };
    return format3;
  }
  function formatPrefix3(specifier, value) {
    var f3 = newFormat((specifier = formatSpecifier2(specifier), specifier.type = "f", specifier)), e11 = Math.max(-8, Math.min(8, Math.floor(exponent_default2(value) / 3))) * 3, k5 = Math.pow(10, -e11), prefix2 = prefixes3[8 + e11 / 3];
    return function(value2) {
      return f3(k5 * value2) + prefix2;
    };
  }
  return {
    format: newFormat,
    formatPrefix: formatPrefix3
  };
}

// node_modules/d3-format/src/defaultLocale.js
var locale3;
var format2;
var formatPrefix2;
defaultLocale3({
  decimal: ".",
  thousands: ",",
  grouping: [3],
  currency: ["$", ""],
  minus: "-"
});
function defaultLocale3(definition) {
  locale3 = locale_default2(definition);
  format2 = locale3.format;
  formatPrefix2 = locale3.formatPrefix;
  return locale3;
}

// node_modules/d3-time-format/node_modules/d3-time/src/interval.js
var t02 = /* @__PURE__ */ new Date();
var t12 = /* @__PURE__ */ new Date();
function newInterval(floori, offseti, count3, field) {
  function interval(date2) {
    return floori(date2 = arguments.length === 0 ? /* @__PURE__ */ new Date() : /* @__PURE__ */ new Date(+date2)), date2;
  }
  interval.floor = function(date2) {
    return floori(date2 = /* @__PURE__ */ new Date(+date2)), date2;
  };
  interval.ceil = function(date2) {
    return floori(date2 = new Date(date2 - 1)), offseti(date2, 1), floori(date2), date2;
  };
  interval.round = function(date2) {
    var d0 = interval(date2), d1 = interval.ceil(date2);
    return date2 - d0 < d1 - date2 ? d0 : d1;
  };
  interval.offset = function(date2, step) {
    return offseti(date2 = /* @__PURE__ */ new Date(+date2), step == null ? 1 : Math.floor(step)), date2;
  };
  interval.range = function(start2, stop2, step) {
    var range2 = [], previous;
    start2 = interval.ceil(start2);
    step = step == null ? 1 : Math.floor(step);
    if (!(start2 < stop2) || !(step > 0)) return range2;
    do
      range2.push(previous = /* @__PURE__ */ new Date(+start2)), offseti(start2, step), floori(start2);
    while (previous < start2 && start2 < stop2);
    return range2;
  };
  interval.filter = function(test) {
    return newInterval(function(date2) {
      if (date2 >= date2) while (floori(date2), !test(date2)) date2.setTime(date2 - 1);
    }, function(date2, step) {
      if (date2 >= date2) {
        if (step < 0) while (++step <= 0) {
          while (offseti(date2, -1), !test(date2)) {
          }
        }
        else while (--step >= 0) {
          while (offseti(date2, 1), !test(date2)) {
          }
        }
      }
    });
  };
  if (count3) {
    interval.count = function(start2, end) {
      t02.setTime(+start2), t12.setTime(+end);
      floori(t02), floori(t12);
      return Math.floor(count3(t02, t12));
    };
    interval.every = function(step) {
      step = Math.floor(step);
      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? function(d3) {
        return field(d3) % step === 0;
      } : function(d3) {
        return interval.count(0, d3) % step === 0;
      });
    };
  }
  return interval;
}

// node_modules/d3-time-format/node_modules/d3-time/src/millisecond.js
var millisecond2 = newInterval(function() {
}, function(date2, step) {
  date2.setTime(+date2 + step);
}, function(start2, end) {
  return end - start2;
});
millisecond2.every = function(k5) {
  k5 = Math.floor(k5);
  if (!isFinite(k5) || !(k5 > 0)) return null;
  if (!(k5 > 1)) return millisecond2;
  return newInterval(function(date2) {
    date2.setTime(Math.floor(date2 / k5) * k5);
  }, function(date2, step) {
    date2.setTime(+date2 + step * k5);
  }, function(start2, end) {
    return (end - start2) / k5;
  });
};
var millisecond_default = millisecond2;
var milliseconds2 = millisecond2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/duration.js
var durationSecond2 = 1e3;
var durationMinute2 = durationSecond2 * 60;
var durationHour2 = durationMinute2 * 60;
var durationDay2 = durationHour2 * 24;
var durationWeek2 = durationDay2 * 7;
var durationMonth2 = durationDay2 * 30;
var durationYear2 = durationDay2 * 365;

// node_modules/d3-time-format/node_modules/d3-time/src/second.js
var second2 = newInterval(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds());
}, function(date2, step) {
  date2.setTime(+date2 + step * durationSecond2);
}, function(start2, end) {
  return (end - start2) / durationSecond2;
}, function(date2) {
  return date2.getUTCSeconds();
});
var second_default = second2;
var seconds2 = second2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/minute.js
var minute = newInterval(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond2);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationMinute2);
}, function(start2, end) {
  return (end - start2) / durationMinute2;
}, function(date2) {
  return date2.getMinutes();
});
var minute_default = minute;
var minutes = minute.range;

// node_modules/d3-time-format/node_modules/d3-time/src/hour.js
var hour = newInterval(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond2 - date2.getMinutes() * durationMinute2);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationHour2);
}, function(start2, end) {
  return (end - start2) / durationHour2;
}, function(date2) {
  return date2.getHours();
});
var hour_default = hour;
var hours = hour.range;

// node_modules/d3-time-format/node_modules/d3-time/src/day.js
var day = newInterval(
  (date2) => date2.setHours(0, 0, 0, 0),
  (date2, step) => date2.setDate(date2.getDate() + step),
  (start2, end) => (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute2) / durationDay2,
  (date2) => date2.getDate() - 1
);
var day_default = day;
var days = day.range;

// node_modules/d3-time-format/node_modules/d3-time/src/week.js
function weekday(i6) {
  return newInterval(function(date2) {
    date2.setDate(date2.getDate() - (date2.getDay() + 7 - i6) % 7);
    date2.setHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setDate(date2.getDate() + step * 7);
  }, function(start2, end) {
    return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute2) / durationWeek2;
  });
}
var sunday = weekday(0);
var monday = weekday(1);
var tuesday = weekday(2);
var wednesday = weekday(3);
var thursday = weekday(4);
var friday = weekday(5);
var saturday = weekday(6);
var sundays = sunday.range;
var mondays = monday.range;
var tuesdays = tuesday.range;
var wednesdays = wednesday.range;
var thursdays = thursday.range;
var fridays = friday.range;
var saturdays = saturday.range;

// node_modules/d3-time-format/node_modules/d3-time/src/month.js
var month = newInterval(function(date2) {
  date2.setDate(1);
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setMonth(date2.getMonth() + step);
}, function(start2, end) {
  return end.getMonth() - start2.getMonth() + (end.getFullYear() - start2.getFullYear()) * 12;
}, function(date2) {
  return date2.getMonth();
});
var month_default = month;
var months = month.range;

// node_modules/d3-time-format/node_modules/d3-time/src/year.js
var year = newInterval(function(date2) {
  date2.setMonth(0, 1);
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setFullYear(date2.getFullYear() + step);
}, function(start2, end) {
  return end.getFullYear() - start2.getFullYear();
}, function(date2) {
  return date2.getFullYear();
});
year.every = function(k5) {
  return !isFinite(k5 = Math.floor(k5)) || !(k5 > 0) ? null : newInterval(function(date2) {
    date2.setFullYear(Math.floor(date2.getFullYear() / k5) * k5);
    date2.setMonth(0, 1);
    date2.setHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setFullYear(date2.getFullYear() + step * k5);
  });
};
var year_default = year;
var years = year.range;

// node_modules/d3-time-format/node_modules/d3-time/src/utcMinute.js
var utcMinute2 = newInterval(function(date2) {
  date2.setUTCSeconds(0, 0);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationMinute2);
}, function(start2, end) {
  return (end - start2) / durationMinute2;
}, function(date2) {
  return date2.getUTCMinutes();
});
var utcMinute_default = utcMinute2;
var utcMinutes2 = utcMinute2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/utcHour.js
var utcHour2 = newInterval(function(date2) {
  date2.setUTCMinutes(0, 0, 0);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationHour2);
}, function(start2, end) {
  return (end - start2) / durationHour2;
}, function(date2) {
  return date2.getUTCHours();
});
var utcHour_default = utcHour2;
var utcHours2 = utcHour2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/utcDay.js
var utcDay2 = newInterval(function(date2) {
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCDate(date2.getUTCDate() + step);
}, function(start2, end) {
  return (end - start2) / durationDay2;
}, function(date2) {
  return date2.getUTCDate() - 1;
});
var utcDay_default = utcDay2;
var utcDays2 = utcDay2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/utcWeek.js
function utcWeekday2(i6) {
  return newInterval(function(date2) {
    date2.setUTCDate(date2.getUTCDate() - (date2.getUTCDay() + 7 - i6) % 7);
    date2.setUTCHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setUTCDate(date2.getUTCDate() + step * 7);
  }, function(start2, end) {
    return (end - start2) / durationWeek2;
  });
}
var utcSunday2 = utcWeekday2(0);
var utcMonday2 = utcWeekday2(1);
var utcTuesday2 = utcWeekday2(2);
var utcWednesday2 = utcWeekday2(3);
var utcThursday2 = utcWeekday2(4);
var utcFriday2 = utcWeekday2(5);
var utcSaturday2 = utcWeekday2(6);
var utcSundays2 = utcSunday2.range;
var utcMondays2 = utcMonday2.range;
var utcTuesdays2 = utcTuesday2.range;
var utcWednesdays2 = utcWednesday2.range;
var utcThursdays2 = utcThursday2.range;
var utcFridays2 = utcFriday2.range;
var utcSaturdays2 = utcSaturday2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/utcMonth.js
var utcMonth2 = newInterval(function(date2) {
  date2.setUTCDate(1);
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCMonth(date2.getUTCMonth() + step);
}, function(start2, end) {
  return end.getUTCMonth() - start2.getUTCMonth() + (end.getUTCFullYear() - start2.getUTCFullYear()) * 12;
}, function(date2) {
  return date2.getUTCMonth();
});
var utcMonth_default = utcMonth2;
var utcMonths2 = utcMonth2.range;

// node_modules/d3-time-format/node_modules/d3-time/src/utcYear.js
var utcYear2 = newInterval(function(date2) {
  date2.setUTCMonth(0, 1);
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCFullYear(date2.getUTCFullYear() + step);
}, function(start2, end) {
  return end.getUTCFullYear() - start2.getUTCFullYear();
}, function(date2) {
  return date2.getUTCFullYear();
});
utcYear2.every = function(k5) {
  return !isFinite(k5 = Math.floor(k5)) || !(k5 > 0) ? null : newInterval(function(date2) {
    date2.setUTCFullYear(Math.floor(date2.getUTCFullYear() / k5) * k5);
    date2.setUTCMonth(0, 1);
    date2.setUTCHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setUTCFullYear(date2.getUTCFullYear() + step * k5);
  });
};
var utcYear_default = utcYear2;
var utcYears2 = utcYear2.range;

// node_modules/d3-time-format/node_modules/d3-array/src/ascending.js
function ascending_default2(a5, b5) {
  return a5 < b5 ? -1 : a5 > b5 ? 1 : a5 >= b5 ? 0 : NaN;
}

// node_modules/d3-time-format/node_modules/d3-array/src/bisector.js
function bisector_default(f3) {
  let delta = f3;
  let compare = f3;
  if (f3.length === 1) {
    delta = (d3, x4) => f3(d3) - x4;
    compare = ascendingComparator(f3);
  }
  function left(a5, x4, lo, hi) {
    if (lo == null) lo = 0;
    if (hi == null) hi = a5.length;
    while (lo < hi) {
      const mid = lo + hi >>> 1;
      if (compare(a5[mid], x4) < 0) lo = mid + 1;
      else hi = mid;
    }
    return lo;
  }
  function right(a5, x4, lo, hi) {
    if (lo == null) lo = 0;
    if (hi == null) hi = a5.length;
    while (lo < hi) {
      const mid = lo + hi >>> 1;
      if (compare(a5[mid], x4) > 0) hi = mid;
      else lo = mid + 1;
    }
    return lo;
  }
  function center(a5, x4, lo, hi) {
    if (lo == null) lo = 0;
    if (hi == null) hi = a5.length;
    const i6 = left(a5, x4, lo, hi - 1);
    return i6 > lo && delta(a5[i6 - 1], x4) > -delta(a5[i6], x4) ? i6 - 1 : i6;
  }
  return { left, center, right };
}
function ascendingComparator(f3) {
  return (d3, x4) => ascending_default2(f3(d3), x4);
}

// node_modules/d3-time-format/node_modules/d3-array/src/number.js
function number_default2(x4) {
  return x4 === null ? NaN : +x4;
}

// node_modules/d3-time-format/node_modules/d3-array/src/bisect.js
var ascendingBisect2 = bisector_default(ascending_default2);
var bisectRight2 = ascendingBisect2.right;
var bisectLeft2 = ascendingBisect2.left;
var bisectCenter2 = bisector_default(number_default2).center;

// node_modules/d3-time-format/node_modules/d3-array/src/array.js
var array2 = Array.prototype;
var slice3 = array2.slice;
var map5 = array2.map;

// node_modules/d3-time-format/node_modules/d3-array/src/ticks.js
var e102 = Math.sqrt(50);
var e52 = Math.sqrt(10);
var e22 = Math.sqrt(2);
function tickStep2(start2, stop2, count3) {
  var step0 = Math.abs(stop2 - start2) / Math.max(0, count3), step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)), error = step0 / step1;
  if (error >= e102) step1 *= 10;
  else if (error >= e52) step1 *= 5;
  else if (error >= e22) step1 *= 2;
  return stop2 < start2 ? -step1 : step1;
}

// node_modules/d3-time-format/node_modules/d3-array/src/shuffle.js
var shuffle_default2 = shuffler2(Math.random);
function shuffler2(random) {
  return function shuffle(array3, i0 = 0, i1 = array3.length) {
    let m5 = i1 - (i0 = +i0);
    while (m5) {
      const i6 = random() * m5-- | 0, t8 = array3[m5 + i0];
      array3[m5 + i0] = array3[i6 + i0];
      array3[i6 + i0] = t8;
    }
    return array3;
  };
}

// node_modules/d3-time-format/node_modules/d3-time/src/ticks.js
function ticker2(year3, month3, week, day3, hour3, minute3) {
  const tickIntervals = [
    [second_default, 1, durationSecond2],
    [second_default, 5, 5 * durationSecond2],
    [second_default, 15, 15 * durationSecond2],
    [second_default, 30, 30 * durationSecond2],
    [minute3, 1, durationMinute2],
    [minute3, 5, 5 * durationMinute2],
    [minute3, 15, 15 * durationMinute2],
    [minute3, 30, 30 * durationMinute2],
    [hour3, 1, durationHour2],
    [hour3, 3, 3 * durationHour2],
    [hour3, 6, 6 * durationHour2],
    [hour3, 12, 12 * durationHour2],
    [day3, 1, durationDay2],
    [day3, 2, 2 * durationDay2],
    [week, 1, durationWeek2],
    [month3, 1, durationMonth2],
    [month3, 3, 3 * durationMonth2],
    [year3, 1, durationYear2]
  ];
  function ticks2(start2, stop2, count3) {
    const reverse3 = stop2 < start2;
    if (reverse3) [start2, stop2] = [stop2, start2];
    const interval = count3 && typeof count3.range === "function" ? count3 : tickInterval(start2, stop2, count3);
    const ticks3 = interval ? interval.range(start2, +stop2 + 1) : [];
    return reverse3 ? ticks3.reverse() : ticks3;
  }
  function tickInterval(start2, stop2, count3) {
    const target = Math.abs(stop2 - start2) / count3;
    const i6 = bisector_default(([, , step2]) => step2).right(tickIntervals, target);
    if (i6 === tickIntervals.length) return year3.every(tickStep2(start2 / durationYear2, stop2 / durationYear2, count3));
    if (i6 === 0) return millisecond_default.every(Math.max(tickStep2(start2, stop2, count3), 1));
    const [t8, step] = tickIntervals[target / tickIntervals[i6 - 1][2] < tickIntervals[i6][2] / target ? i6 - 1 : i6];
    return t8.every(step);
  }
  return [ticks2, tickInterval];
}
var [utcTicks2, utcTickInterval2] = ticker2(utcYear_default, utcMonth_default, utcSunday2, utcDay_default, utcHour_default, utcMinute_default);
var [timeTicks2, timeTickInterval2] = ticker2(year_default, month_default, sunday, day_default, hour_default, minute_default);

// node_modules/d3-time-format/src/locale.js
function localDate2(d3) {
  if (0 <= d3.y && d3.y < 100) {
    var date2 = new Date(-1, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L);
    date2.setFullYear(d3.y);
    return date2;
  }
  return new Date(d3.y, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L);
}
function utcDate2(d3) {
  if (0 <= d3.y && d3.y < 100) {
    var date2 = new Date(Date.UTC(-1, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L));
    date2.setUTCFullYear(d3.y);
    return date2;
  }
  return new Date(Date.UTC(d3.y, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L));
}
function newDate2(y4, m5, d3) {
  return { y: y4, m: m5, d: d3, H: 0, M: 0, S: 0, L: 0 };
}
function formatLocale2(locale5) {
  var locale_dateTime = locale5.dateTime, locale_date = locale5.date, locale_time = locale5.time, locale_periods = locale5.periods, locale_weekdays = locale5.days, locale_shortWeekdays = locale5.shortDays, locale_months = locale5.months, locale_shortMonths = locale5.shortMonths;
  var periodRe = formatRe2(locale_periods), periodLookup = formatLookup2(locale_periods), weekdayRe = formatRe2(locale_weekdays), weekdayLookup = formatLookup2(locale_weekdays), shortWeekdayRe = formatRe2(locale_shortWeekdays), shortWeekdayLookup = formatLookup2(locale_shortWeekdays), monthRe = formatRe2(locale_months), monthLookup = formatLookup2(locale_months), shortMonthRe = formatRe2(locale_shortMonths), shortMonthLookup = formatLookup2(locale_shortMonths);
  var formats = {
    "a": formatShortWeekday,
    "A": formatWeekday,
    "b": formatShortMonth,
    "B": formatMonth,
    "c": null,
    "d": formatDayOfMonth2,
    "e": formatDayOfMonth2,
    "f": formatMicroseconds2,
    "g": formatYearISO2,
    "G": formatFullYearISO2,
    "H": formatHour242,
    "I": formatHour122,
    "j": formatDayOfYear2,
    "L": formatMilliseconds2,
    "m": formatMonthNumber2,
    "M": formatMinutes2,
    "p": formatPeriod,
    "q": formatQuarter,
    "Q": formatUnixTimestamp2,
    "s": formatUnixTimestampSeconds2,
    "S": formatSeconds2,
    "u": formatWeekdayNumberMonday2,
    "U": formatWeekNumberSunday2,
    "V": formatWeekNumberISO2,
    "w": formatWeekdayNumberSunday2,
    "W": formatWeekNumberMonday2,
    "x": null,
    "X": null,
    "y": formatYear2,
    "Y": formatFullYear2,
    "Z": formatZone2,
    "%": formatLiteralPercent2
  };
  var utcFormats = {
    "a": formatUTCShortWeekday,
    "A": formatUTCWeekday,
    "b": formatUTCShortMonth,
    "B": formatUTCMonth,
    "c": null,
    "d": formatUTCDayOfMonth2,
    "e": formatUTCDayOfMonth2,
    "f": formatUTCMicroseconds2,
    "g": formatUTCYearISO2,
    "G": formatUTCFullYearISO2,
    "H": formatUTCHour242,
    "I": formatUTCHour122,
    "j": formatUTCDayOfYear2,
    "L": formatUTCMilliseconds2,
    "m": formatUTCMonthNumber2,
    "M": formatUTCMinutes2,
    "p": formatUTCPeriod,
    "q": formatUTCQuarter,
    "Q": formatUnixTimestamp2,
    "s": formatUnixTimestampSeconds2,
    "S": formatUTCSeconds2,
    "u": formatUTCWeekdayNumberMonday2,
    "U": formatUTCWeekNumberSunday2,
    "V": formatUTCWeekNumberISO2,
    "w": formatUTCWeekdayNumberSunday2,
    "W": formatUTCWeekNumberMonday2,
    "x": null,
    "X": null,
    "y": formatUTCYear2,
    "Y": formatUTCFullYear2,
    "Z": formatUTCZone2,
    "%": formatLiteralPercent2
  };
  var parses = {
    "a": parseShortWeekday,
    "A": parseWeekday,
    "b": parseShortMonth,
    "B": parseMonth,
    "c": parseLocaleDateTime,
    "d": parseDayOfMonth2,
    "e": parseDayOfMonth2,
    "f": parseMicroseconds2,
    "g": parseYear2,
    "G": parseFullYear2,
    "H": parseHour242,
    "I": parseHour242,
    "j": parseDayOfYear2,
    "L": parseMilliseconds2,
    "m": parseMonthNumber2,
    "M": parseMinutes2,
    "p": parsePeriod,
    "q": parseQuarter2,
    "Q": parseUnixTimestamp2,
    "s": parseUnixTimestampSeconds2,
    "S": parseSeconds2,
    "u": parseWeekdayNumberMonday2,
    "U": parseWeekNumberSunday2,
    "V": parseWeekNumberISO2,
    "w": parseWeekdayNumberSunday2,
    "W": parseWeekNumberMonday2,
    "x": parseLocaleDate,
    "X": parseLocaleTime,
    "y": parseYear2,
    "Y": parseFullYear2,
    "Z": parseZone2,
    "%": parseLiteralPercent2
  };
  formats.x = newFormat(locale_date, formats);
  formats.X = newFormat(locale_time, formats);
  formats.c = newFormat(locale_dateTime, formats);
  utcFormats.x = newFormat(locale_date, utcFormats);
  utcFormats.X = newFormat(locale_time, utcFormats);
  utcFormats.c = newFormat(locale_dateTime, utcFormats);
  function newFormat(specifier, formats2) {
    return function(date2) {
      var string = [], i6 = -1, j3 = 0, n7 = specifier.length, c9, pad3, format3;
      if (!(date2 instanceof Date)) date2 = /* @__PURE__ */ new Date(+date2);
      while (++i6 < n7) {
        if (specifier.charCodeAt(i6) === 37) {
          string.push(specifier.slice(j3, i6));
          if ((pad3 = pads2[c9 = specifier.charAt(++i6)]) != null) c9 = specifier.charAt(++i6);
          else pad3 = c9 === "e" ? " " : "0";
          if (format3 = formats2[c9]) c9 = format3(date2, pad3);
          string.push(c9);
          j3 = i6 + 1;
        }
      }
      string.push(specifier.slice(j3, i6));
      return string.join("");
    };
  }
  function newParse(specifier, Z2) {
    return function(string) {
      var d3 = newDate2(1900, void 0, 1), i6 = parseSpecifier(d3, specifier, string += "", 0), week, day3;
      if (i6 != string.length) return null;
      if ("Q" in d3) return new Date(d3.Q);
      if ("s" in d3) return new Date(d3.s * 1e3 + ("L" in d3 ? d3.L : 0));
      if (Z2 && !("Z" in d3)) d3.Z = 0;
      if ("p" in d3) d3.H = d3.H % 12 + d3.p * 12;
      if (d3.m === void 0) d3.m = "q" in d3 ? d3.q : 0;
      if ("V" in d3) {
        if (d3.V < 1 || d3.V > 53) return null;
        if (!("w" in d3)) d3.w = 1;
        if ("Z" in d3) {
          week = utcDate2(newDate2(d3.y, 0, 1)), day3 = week.getUTCDay();
          week = day3 > 4 || day3 === 0 ? utcMonday2.ceil(week) : utcMonday2(week);
          week = utcDay_default.offset(week, (d3.V - 1) * 7);
          d3.y = week.getUTCFullYear();
          d3.m = week.getUTCMonth();
          d3.d = week.getUTCDate() + (d3.w + 6) % 7;
        } else {
          week = localDate2(newDate2(d3.y, 0, 1)), day3 = week.getDay();
          week = day3 > 4 || day3 === 0 ? monday.ceil(week) : monday(week);
          week = day_default.offset(week, (d3.V - 1) * 7);
          d3.y = week.getFullYear();
          d3.m = week.getMonth();
          d3.d = week.getDate() + (d3.w + 6) % 7;
        }
      } else if ("W" in d3 || "U" in d3) {
        if (!("w" in d3)) d3.w = "u" in d3 ? d3.u % 7 : "W" in d3 ? 1 : 0;
        day3 = "Z" in d3 ? utcDate2(newDate2(d3.y, 0, 1)).getUTCDay() : localDate2(newDate2(d3.y, 0, 1)).getDay();
        d3.m = 0;
        d3.d = "W" in d3 ? (d3.w + 6) % 7 + d3.W * 7 - (day3 + 5) % 7 : d3.w + d3.U * 7 - (day3 + 6) % 7;
      }
      if ("Z" in d3) {
        d3.H += d3.Z / 100 | 0;
        d3.M += d3.Z % 100;
        return utcDate2(d3);
      }
      return localDate2(d3);
    };
  }
  function parseSpecifier(d3, specifier, string, j3) {
    var i6 = 0, n7 = specifier.length, m5 = string.length, c9, parse;
    while (i6 < n7) {
      if (j3 >= m5) return -1;
      c9 = specifier.charCodeAt(i6++);
      if (c9 === 37) {
        c9 = specifier.charAt(i6++);
        parse = parses[c9 in pads2 ? specifier.charAt(i6++) : c9];
        if (!parse || (j3 = parse(d3, string, j3)) < 0) return -1;
      } else if (c9 != string.charCodeAt(j3++)) {
        return -1;
      }
    }
    return j3;
  }
  function parsePeriod(d3, string, i6) {
    var n7 = periodRe.exec(string.slice(i6));
    return n7 ? (d3.p = periodLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseShortWeekday(d3, string, i6) {
    var n7 = shortWeekdayRe.exec(string.slice(i6));
    return n7 ? (d3.w = shortWeekdayLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseWeekday(d3, string, i6) {
    var n7 = weekdayRe.exec(string.slice(i6));
    return n7 ? (d3.w = weekdayLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseShortMonth(d3, string, i6) {
    var n7 = shortMonthRe.exec(string.slice(i6));
    return n7 ? (d3.m = shortMonthLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseMonth(d3, string, i6) {
    var n7 = monthRe.exec(string.slice(i6));
    return n7 ? (d3.m = monthLookup.get(n7[0].toLowerCase()), i6 + n7[0].length) : -1;
  }
  function parseLocaleDateTime(d3, string, i6) {
    return parseSpecifier(d3, locale_dateTime, string, i6);
  }
  function parseLocaleDate(d3, string, i6) {
    return parseSpecifier(d3, locale_date, string, i6);
  }
  function parseLocaleTime(d3, string, i6) {
    return parseSpecifier(d3, locale_time, string, i6);
  }
  function formatShortWeekday(d3) {
    return locale_shortWeekdays[d3.getDay()];
  }
  function formatWeekday(d3) {
    return locale_weekdays[d3.getDay()];
  }
  function formatShortMonth(d3) {
    return locale_shortMonths[d3.getMonth()];
  }
  function formatMonth(d3) {
    return locale_months[d3.getMonth()];
  }
  function formatPeriod(d3) {
    return locale_periods[+(d3.getHours() >= 12)];
  }
  function formatQuarter(d3) {
    return 1 + ~~(d3.getMonth() / 3);
  }
  function formatUTCShortWeekday(d3) {
    return locale_shortWeekdays[d3.getUTCDay()];
  }
  function formatUTCWeekday(d3) {
    return locale_weekdays[d3.getUTCDay()];
  }
  function formatUTCShortMonth(d3) {
    return locale_shortMonths[d3.getUTCMonth()];
  }
  function formatUTCMonth(d3) {
    return locale_months[d3.getUTCMonth()];
  }
  function formatUTCPeriod(d3) {
    return locale_periods[+(d3.getUTCHours() >= 12)];
  }
  function formatUTCQuarter(d3) {
    return 1 + ~~(d3.getUTCMonth() / 3);
  }
  return {
    format: function(specifier) {
      var f3 = newFormat(specifier += "", formats);
      f3.toString = function() {
        return specifier;
      };
      return f3;
    },
    parse: function(specifier) {
      var p4 = newParse(specifier += "", false);
      p4.toString = function() {
        return specifier;
      };
      return p4;
    },
    utcFormat: function(specifier) {
      var f3 = newFormat(specifier += "", utcFormats);
      f3.toString = function() {
        return specifier;
      };
      return f3;
    },
    utcParse: function(specifier) {
      var p4 = newParse(specifier += "", true);
      p4.toString = function() {
        return specifier;
      };
      return p4;
    }
  };
}
var pads2 = { "-": "", "_": " ", "0": "0" };
var numberRe2 = /^\s*\d+/;
var percentRe2 = /^%/;
var requoteRe2 = /[\\^$*+?|[\]().{}]/g;
function pad2(value, fill, width) {
  var sign2 = value < 0 ? "-" : "", string = (sign2 ? -value : value) + "", length = string.length;
  return sign2 + (length < width ? new Array(width - length + 1).join(fill) + string : string);
}
function requote2(s5) {
  return s5.replace(requoteRe2, "\\$&");
}
function formatRe2(names) {
  return new RegExp("^(?:" + names.map(requote2).join("|") + ")", "i");
}
function formatLookup2(names) {
  return new Map(names.map((name, i6) => [name.toLowerCase(), i6]));
}
function parseWeekdayNumberSunday2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 1));
  return n7 ? (d3.w = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekdayNumberMonday2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 1));
  return n7 ? (d3.u = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekNumberSunday2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.U = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekNumberISO2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.V = +n7[0], i6 + n7[0].length) : -1;
}
function parseWeekNumberMonday2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.W = +n7[0], i6 + n7[0].length) : -1;
}
function parseFullYear2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 4));
  return n7 ? (d3.y = +n7[0], i6 + n7[0].length) : -1;
}
function parseYear2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.y = +n7[0] + (+n7[0] > 68 ? 1900 : 2e3), i6 + n7[0].length) : -1;
}
function parseZone2(d3, string, i6) {
  var n7 = /^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(string.slice(i6, i6 + 6));
  return n7 ? (d3.Z = n7[1] ? 0 : -(n7[2] + (n7[3] || "00")), i6 + n7[0].length) : -1;
}
function parseQuarter2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 1));
  return n7 ? (d3.q = n7[0] * 3 - 3, i6 + n7[0].length) : -1;
}
function parseMonthNumber2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.m = n7[0] - 1, i6 + n7[0].length) : -1;
}
function parseDayOfMonth2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.d = +n7[0], i6 + n7[0].length) : -1;
}
function parseDayOfYear2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 3));
  return n7 ? (d3.m = 0, d3.d = +n7[0], i6 + n7[0].length) : -1;
}
function parseHour242(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.H = +n7[0], i6 + n7[0].length) : -1;
}
function parseMinutes2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.M = +n7[0], i6 + n7[0].length) : -1;
}
function parseSeconds2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 2));
  return n7 ? (d3.S = +n7[0], i6 + n7[0].length) : -1;
}
function parseMilliseconds2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 3));
  return n7 ? (d3.L = +n7[0], i6 + n7[0].length) : -1;
}
function parseMicroseconds2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6, i6 + 6));
  return n7 ? (d3.L = Math.floor(n7[0] / 1e3), i6 + n7[0].length) : -1;
}
function parseLiteralPercent2(d3, string, i6) {
  var n7 = percentRe2.exec(string.slice(i6, i6 + 1));
  return n7 ? i6 + n7[0].length : -1;
}
function parseUnixTimestamp2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6));
  return n7 ? (d3.Q = +n7[0], i6 + n7[0].length) : -1;
}
function parseUnixTimestampSeconds2(d3, string, i6) {
  var n7 = numberRe2.exec(string.slice(i6));
  return n7 ? (d3.s = +n7[0], i6 + n7[0].length) : -1;
}
function formatDayOfMonth2(d3, p4) {
  return pad2(d3.getDate(), p4, 2);
}
function formatHour242(d3, p4) {
  return pad2(d3.getHours(), p4, 2);
}
function formatHour122(d3, p4) {
  return pad2(d3.getHours() % 12 || 12, p4, 2);
}
function formatDayOfYear2(d3, p4) {
  return pad2(1 + day_default.count(year_default(d3), d3), p4, 3);
}
function formatMilliseconds2(d3, p4) {
  return pad2(d3.getMilliseconds(), p4, 3);
}
function formatMicroseconds2(d3, p4) {
  return formatMilliseconds2(d3, p4) + "000";
}
function formatMonthNumber2(d3, p4) {
  return pad2(d3.getMonth() + 1, p4, 2);
}
function formatMinutes2(d3, p4) {
  return pad2(d3.getMinutes(), p4, 2);
}
function formatSeconds2(d3, p4) {
  return pad2(d3.getSeconds(), p4, 2);
}
function formatWeekdayNumberMonday2(d3) {
  var day3 = d3.getDay();
  return day3 === 0 ? 7 : day3;
}
function formatWeekNumberSunday2(d3, p4) {
  return pad2(sunday.count(year_default(d3) - 1, d3), p4, 2);
}
function dISO2(d3) {
  var day3 = d3.getDay();
  return day3 >= 4 || day3 === 0 ? thursday(d3) : thursday.ceil(d3);
}
function formatWeekNumberISO2(d3, p4) {
  d3 = dISO2(d3);
  return pad2(thursday.count(year_default(d3), d3) + (year_default(d3).getDay() === 4), p4, 2);
}
function formatWeekdayNumberSunday2(d3) {
  return d3.getDay();
}
function formatWeekNumberMonday2(d3, p4) {
  return pad2(monday.count(year_default(d3) - 1, d3), p4, 2);
}
function formatYear2(d3, p4) {
  return pad2(d3.getFullYear() % 100, p4, 2);
}
function formatYearISO2(d3, p4) {
  d3 = dISO2(d3);
  return pad2(d3.getFullYear() % 100, p4, 2);
}
function formatFullYear2(d3, p4) {
  return pad2(d3.getFullYear() % 1e4, p4, 4);
}
function formatFullYearISO2(d3, p4) {
  var day3 = d3.getDay();
  d3 = day3 >= 4 || day3 === 0 ? thursday(d3) : thursday.ceil(d3);
  return pad2(d3.getFullYear() % 1e4, p4, 4);
}
function formatZone2(d3) {
  var z6 = d3.getTimezoneOffset();
  return (z6 > 0 ? "-" : (z6 *= -1, "+")) + pad2(z6 / 60 | 0, "0", 2) + pad2(z6 % 60, "0", 2);
}
function formatUTCDayOfMonth2(d3, p4) {
  return pad2(d3.getUTCDate(), p4, 2);
}
function formatUTCHour242(d3, p4) {
  return pad2(d3.getUTCHours(), p4, 2);
}
function formatUTCHour122(d3, p4) {
  return pad2(d3.getUTCHours() % 12 || 12, p4, 2);
}
function formatUTCDayOfYear2(d3, p4) {
  return pad2(1 + utcDay_default.count(utcYear_default(d3), d3), p4, 3);
}
function formatUTCMilliseconds2(d3, p4) {
  return pad2(d3.getUTCMilliseconds(), p4, 3);
}
function formatUTCMicroseconds2(d3, p4) {
  return formatUTCMilliseconds2(d3, p4) + "000";
}
function formatUTCMonthNumber2(d3, p4) {
  return pad2(d3.getUTCMonth() + 1, p4, 2);
}
function formatUTCMinutes2(d3, p4) {
  return pad2(d3.getUTCMinutes(), p4, 2);
}
function formatUTCSeconds2(d3, p4) {
  return pad2(d3.getUTCSeconds(), p4, 2);
}
function formatUTCWeekdayNumberMonday2(d3) {
  var dow = d3.getUTCDay();
  return dow === 0 ? 7 : dow;
}
function formatUTCWeekNumberSunday2(d3, p4) {
  return pad2(utcSunday2.count(utcYear_default(d3) - 1, d3), p4, 2);
}
function UTCdISO2(d3) {
  var day3 = d3.getUTCDay();
  return day3 >= 4 || day3 === 0 ? utcThursday2(d3) : utcThursday2.ceil(d3);
}
function formatUTCWeekNumberISO2(d3, p4) {
  d3 = UTCdISO2(d3);
  return pad2(utcThursday2.count(utcYear_default(d3), d3) + (utcYear_default(d3).getUTCDay() === 4), p4, 2);
}
function formatUTCWeekdayNumberSunday2(d3) {
  return d3.getUTCDay();
}
function formatUTCWeekNumberMonday2(d3, p4) {
  return pad2(utcMonday2.count(utcYear_default(d3) - 1, d3), p4, 2);
}
function formatUTCYear2(d3, p4) {
  return pad2(d3.getUTCFullYear() % 100, p4, 2);
}
function formatUTCYearISO2(d3, p4) {
  d3 = UTCdISO2(d3);
  return pad2(d3.getUTCFullYear() % 100, p4, 2);
}
function formatUTCFullYear2(d3, p4) {
  return pad2(d3.getUTCFullYear() % 1e4, p4, 4);
}
function formatUTCFullYearISO2(d3, p4) {
  var day3 = d3.getUTCDay();
  d3 = day3 >= 4 || day3 === 0 ? utcThursday2(d3) : utcThursday2.ceil(d3);
  return pad2(d3.getUTCFullYear() % 1e4, p4, 4);
}
function formatUTCZone2() {
  return "+0000";
}
function formatLiteralPercent2() {
  return "%";
}
function formatUnixTimestamp2(d3) {
  return +d3;
}
function formatUnixTimestampSeconds2(d3) {
  return Math.floor(+d3 / 1e3);
}

// node_modules/d3-time-format/src/defaultLocale.js
var locale4;
var timeFormat2;
var timeParse2;
var utcFormat2;
var utcParse2;
defaultLocale4({
  dateTime: "%x, %X",
  date: "%-m/%-d/%Y",
  time: "%-I:%M:%S %p",
  periods: ["AM", "PM"],
  days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
  shortDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
  shortMonths: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
});
function defaultLocale4(definition) {
  locale4 = formatLocale2(definition);
  timeFormat2 = locale4.format;
  timeParse2 = locale4.parse;
  utcFormat2 = locale4.utcFormat;
  utcParse2 = locale4.utcParse;
  return locale4;
}

// node_modules/d3-time-format/src/isoFormat.js
var isoSpecifier2 = "%Y-%m-%dT%H:%M:%S.%LZ";
function formatIsoNative2(date2) {
  return date2.toISOString();
}
var formatIso2 = Date.prototype.toISOString ? formatIsoNative2 : utcFormat2(isoSpecifier2);

// node_modules/d3-time-format/src/isoParse.js
function parseIsoNative2(string) {
  var date2 = new Date(string);
  return isNaN(date2) ? null : date2;
}
var parseIso2 = +/* @__PURE__ */ new Date("2000-01-01T00:00:00.000Z") ? parseIsoNative2 : utcParse2(isoSpecifier2);

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_isPlainObject = __toESM(require_isPlainObject());
var import_pick = __toESM(require_pick());
var import_isEqual = __toESM(require_isEqual());
var Pr = { background: "transparent", text: { fontFamily: "sans-serif", fontSize: 11, fill: "#333333", outlineWidth: 0, outlineColor: "transparent", outlineOpacity: 1 }, axis: { domain: { line: { stroke: "transparent", strokeWidth: 1 } }, ticks: { line: { stroke: "#777777", strokeWidth: 1 }, text: {} }, legend: { text: { fontSize: 12 } } }, grid: { line: { stroke: "#dddddd", strokeWidth: 1 } }, legends: { hidden: { symbol: { fill: "#333333", opacity: 0.6 }, text: { fill: "#333333", opacity: 0.6 } }, text: {}, ticks: { line: { stroke: "#777777", strokeWidth: 1 }, text: { fontSize: 10 } }, title: { text: {} } }, labels: { text: {} }, markers: { lineColor: "#000000", lineStrokeWidth: 1, text: {} }, dots: { text: {} }, tooltip: { container: { background: "white", color: "inherit", fontSize: "inherit", borderRadius: "2px", boxShadow: "0 1px 2px rgba(0, 0, 0, 0.25)", padding: "5px 9px" }, basic: { whiteSpace: "pre", display: "flex", alignItems: "center" }, chip: { marginRight: 7 }, table: {}, tableCell: { padding: "3px 5px" }, tableCellValue: { fontWeight: "bold" } }, crosshair: { line: { stroke: "#000000", strokeWidth: 1, strokeOpacity: 0.75, strokeDasharray: "6 6" } }, annotations: { text: { fontSize: 13, outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 }, link: { stroke: "#000000", strokeWidth: 1, outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 }, outline: { fill: "none", stroke: "#000000", strokeWidth: 2, outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 }, symbol: { fill: "#000000", outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 } } };
function jr() {
  return jr = Object.assign ? Object.assign.bind() : function(e11) {
    for (var r7 = 1; r7 < arguments.length; r7++) {
      var t8 = arguments[r7];
      for (var n7 in t8) Object.prototype.hasOwnProperty.call(t8, n7) && (e11[n7] = t8[n7]);
    }
    return e11;
  }, jr.apply(this, arguments);
}
function Sr(e11, r7) {
  return Sr = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e12, r8) {
    return e12.__proto__ = r8, e12;
  }, Sr(e11, r7);
}
function Br(e11, r7) {
  if (null == e11) return {};
  var t8, n7, i6 = {}, o5 = Object.keys(e11);
  for (n7 = 0; n7 < o5.length; n7++) t8 = o5[n7], r7.indexOf(t8) >= 0 || (i6[t8] = e11[t8]);
  return i6;
}
var Gr = ["axis.ticks.text", "axis.legend.text", "legends.title.text", "legends.text", "legends.ticks.text", "legends.title.text", "labels.text", "dots.text", "markers.text", "annotations.text"];
var Lr = function(e11, r7) {
  return jr({}, r7, e11);
};
var Ir = function(e11, r7) {
  var t8 = (0, import_merge3.default)({}, e11, r7);
  return Gr.forEach(function(e12) {
    (0, import_set2.default)(t8, e12, Lr((0, import_get.default)(t8, e12), t8.text));
  }), t8;
};
var Yr = (0, import_react16.createContext)();
var Ar = function(e11) {
  var t8 = e11.children, n7 = e11.animate, i6 = void 0 === n7 || n7, o5 = e11.config, l5 = void 0 === o5 ? "default" : o5, a5 = (0, import_react16.useMemo)(function() {
    var e12 = (0, import_isString.default)(l5) ? config[l5] : l5;
    return { animate: i6, config: e12 };
  }, [i6, l5]);
  return (0, import_jsx_runtime2.jsx)(Yr.Provider, { value: a5, children: t8 });
};
var Er = { animate: import_prop_types.default.bool, motionConfig: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(Object.keys(config)), import_prop_types.default.shape({ mass: import_prop_types.default.number, tension: import_prop_types.default.number, friction: import_prop_types.default.number, clamp: import_prop_types.default.bool, precision: import_prop_types.default.number, velocity: import_prop_types.default.number, duration: import_prop_types.default.number, easing: import_prop_types.default.func })]) };
Ar.propTypes = { children: import_prop_types.default.node.isRequired, animate: Er.animate, config: Er.motionConfig };
var Ur = function() {
  return (0, import_react16.useContext)(Yr);
};
var Fr = function(e11) {
  var t8 = Ur(), o5 = t8.animate, l5 = t8.config, a5 = function(e12) {
    var r7 = (0, import_react16.useRef)();
    return (0, import_react16.useEffect)(function() {
      r7.current = e12;
    }, [e12]), r7.current;
  }(e11), d3 = (0, import_react16.useMemo)(function() {
    return string_default(a5, e11);
  }, [a5, e11]), s5 = useSpring({ from: { value: 0 }, to: { value: 1 }, reset: true, config: l5, immediate: !o5 }).value;
  return to2(s5, d3);
};
var Xr = { nivo: ["#d76445", "#f47560", "#e8c1a0", "#97e3d5", "#61cdbb", "#00b0a7"], BrBG: (0, import_last.default)(scheme), PRGn: (0, import_last.default)(scheme2), PiYG: (0, import_last.default)(scheme3), PuOr: (0, import_last.default)(scheme4), RdBu: (0, import_last.default)(scheme5), RdGy: (0, import_last.default)(scheme6), RdYlBu: (0, import_last.default)(scheme7), RdYlGn: (0, import_last.default)(scheme8), spectral: (0, import_last.default)(scheme9), blues: (0, import_last.default)(scheme22), greens: (0, import_last.default)(scheme23), greys: (0, import_last.default)(scheme24), oranges: (0, import_last.default)(scheme27), purples: (0, import_last.default)(scheme25), reds: (0, import_last.default)(scheme26), BuGn: (0, import_last.default)(scheme10), BuPu: (0, import_last.default)(scheme11), GnBu: (0, import_last.default)(scheme12), OrRd: (0, import_last.default)(scheme13), PuBuGn: (0, import_last.default)(scheme14), PuBu: (0, import_last.default)(scheme15), PuRd: (0, import_last.default)(scheme16), RdPu: (0, import_last.default)(scheme17), YlGnBu: (0, import_last.default)(scheme18), YlGn: (0, import_last.default)(scheme19), YlOrBr: (0, import_last.default)(scheme20), YlOrRd: (0, import_last.default)(scheme21) };
var Hr = Object.keys(Xr);
var Nr = { nivo: ["#e8c1a0", "#f47560", "#f1e15b", "#e8a838", "#61cdbb", "#97e3d5"], category10: category10_default, accent: Accent_default, dark2: Dark2_default, paired: Paired_default, pastel1: Pastel1_default, pastel2: Pastel2_default, set1: Set1_default, set2: Set2_default, set3: Set3_default, brown_blueGreen: (0, import_last.default)(scheme), purpleRed_green: (0, import_last.default)(scheme2), pink_yellowGreen: (0, import_last.default)(scheme3), purple_orange: (0, import_last.default)(scheme4), red_blue: (0, import_last.default)(scheme5), red_grey: (0, import_last.default)(scheme6), red_yellow_blue: (0, import_last.default)(scheme7), red_yellow_green: (0, import_last.default)(scheme8), spectral: (0, import_last.default)(scheme9), blues: (0, import_last.default)(scheme22), greens: (0, import_last.default)(scheme23), greys: (0, import_last.default)(scheme24), oranges: (0, import_last.default)(scheme27), purples: (0, import_last.default)(scheme25), reds: (0, import_last.default)(scheme26), blue_green: (0, import_last.default)(scheme10), blue_purple: (0, import_last.default)(scheme11), green_blue: (0, import_last.default)(scheme12), orange_red: (0, import_last.default)(scheme13), purple_blue_green: (0, import_last.default)(scheme14), purple_blue: (0, import_last.default)(scheme15), purple_red: (0, import_last.default)(scheme16), red_purple: (0, import_last.default)(scheme17), yellow_green_blue: (0, import_last.default)(scheme18), yellow_green: (0, import_last.default)(scheme19), yellow_orange_brown: (0, import_last.default)(scheme20), yellow_orange_red: (0, import_last.default)(scheme21) };
var et = import_prop_types.default.oneOfType([import_prop_types.default.oneOf(Hr), import_prop_types.default.func, import_prop_types.default.arrayOf(import_prop_types.default.string)]);
var rt = { basis: basis_default, basisClosed: basisClosed_default, basisOpen: basisOpen_default, bundle: bundle_default, cardinal: cardinal_default, cardinalClosed: cardinalClosed_default, cardinalOpen: cardinalOpen_default, catmullRom: catmullRom_default, catmullRomClosed: catmullRomClosed_default, catmullRomOpen: catmullRomOpen_default, linear: linear_default, linearClosed: linearClosed_default, monotoneX, monotoneY, natural: natural_default, step: step_default, stepAfter, stepBefore };
var tt = Object.keys(rt);
var nt = tt.filter(function(e11) {
  return e11.endsWith("Closed");
});
var it = (0, import_without.default)(tt, "bundle", "basisClosed", "basisOpen", "cardinalClosed", "cardinalOpen", "catmullRomClosed", "catmullRomOpen", "linearClosed");
var ot = (0, import_without.default)(tt, "bundle", "basisClosed", "basisOpen", "cardinalClosed", "cardinalOpen", "catmullRomClosed", "catmullRomOpen", "linearClosed");
var lt = function(e11) {
  if (!rt[e11]) throw new TypeError("'" + e11 + "', is not a valid curve interpolator identifier.");
  return rt[e11];
};
var at = { ascending: ascending_default, descending: descending_default2, insideOut: insideOut_default, none: none_default2, reverse: reverse_default };
var dt = Object.keys(at);
var ut = { expand: expand_default, diverging: diverging_default, none: none_default, silhouette: silhouette_default, wiggle: wiggle_default };
var ct = Object.keys(ut);
var pt = import_prop_types.default.shape({ top: import_prop_types.default.number, right: import_prop_types.default.number, bottom: import_prop_types.default.number, left: import_prop_types.default.number }).isRequired;
var ht = ["normal", "multiply", "screen", "overlay", "darken", "lighten", "color-dodge", "color-burn", "hard-light", "soft-light", "difference", "exclusion", "hue", "saturation", "color", "luminosity"];
var gt = import_prop_types.default.oneOf(ht);
var vt = ordinal(Set3_default);
var _t = { top: 0, right: 0, bottom: 0, left: 0 };
var wt = function(e11, t8, n7) {
  return void 0 === n7 && (n7 = {}), (0, import_react16.useMemo)(function() {
    var r7 = jr({}, _t, n7);
    return { margin: r7, innerWidth: e11 - r7.left - r7.right, innerHeight: t8 - r7.top - r7.bottom, outerWidth: e11, outerHeight: t8 };
  }, [e11, t8, n7.top, n7.right, n7.bottom, n7.left]);
};
var kt = function() {
  var e11 = (0, import_react16.useRef)(null), r7 = (0, import_react16.useState)({ left: 0, top: 0, width: 0, height: 0 }), t8 = r7[0], l5 = r7[1], a5 = (0, import_react16.useState)(function() {
    return "undefined" == typeof ResizeObserver ? null : new ResizeObserver(function(e12) {
      var r8 = e12[0];
      return l5(r8.contentRect);
    });
  })[0];
  return (0, import_react16.useEffect)(function() {
    return e11.current && null !== a5 && a5.observe(e11.current), function() {
      null !== a5 && a5.disconnect();
    };
  }, []), [e11, t8];
};
var Rt = function(e11) {
  return (0, import_react16.useMemo)(function() {
    return Ir(Pr, e11);
  }, [e11]);
};
var xt = function(e11) {
  return "function" == typeof e11 ? e11 : "string" == typeof e11 ? 0 === e11.indexOf("time:") ? timeFormat2(e11.slice("5")) : format2(e11) : function(e12) {
    return "" + e12;
  };
};
var Ot = function(e11) {
  return (0, import_react16.useMemo)(function() {
    return xt(e11);
  }, [e11]);
};
var qt = (0, import_react16.createContext)();
var Ct = {};
var Wt = function(e11) {
  var r7 = e11.theme, t8 = void 0 === r7 ? Ct : r7, n7 = e11.children, i6 = Rt(t8);
  return (0, import_jsx_runtime2.jsx)(qt.Provider, { value: i6, children: n7 });
};
Wt.propTypes = { children: import_prop_types.default.node.isRequired, theme: import_prop_types.default.object };
var zt = function() {
  return (0, import_react16.useContext)(qt);
};
var Tt = ["outlineWidth", "outlineColor", "outlineOpacity"];
var Mt = function(e11) {
  return e11.outlineWidth, e11.outlineColor, e11.outlineOpacity, Br(e11, Tt);
};
var Pt = function(e11) {
  var r7 = e11.children, t8 = e11.condition, n7 = e11.wrapper;
  return t8 ? (0, import_react16.cloneElement)(n7, {}, r7) : r7;
};
Pt.propTypes = { children: import_prop_types.default.node.isRequired, condition: import_prop_types.default.bool.isRequired, wrapper: import_prop_types.default.element.isRequired };
var jt = { position: "relative" };
var St = function(e11) {
  var r7 = e11.children, t8 = e11.theme, i6 = e11.renderWrapper, o5 = void 0 === i6 || i6, l5 = e11.isInteractive, a5 = void 0 === l5 || l5, d3 = e11.animate, s5 = e11.motionConfig, u6 = (0, import_react16.useRef)(null);
  return (0, import_jsx_runtime2.jsx)(Wt, { theme: t8, children: (0, import_jsx_runtime2.jsx)(Ar, { animate: d3, config: s5, children: (0, import_jsx_runtime2.jsx)(M, { container: u6, children: (0, import_jsx_runtime2.jsxs)(Pt, { condition: o5, wrapper: (0, import_jsx_runtime2.jsx)("div", { style: jt, ref: u6 }), children: [r7, a5 && (0, import_jsx_runtime2.jsx)(F, {})] }) }) }) });
};
St.propTypes = { children: import_prop_types.default.element.isRequired, isInteractive: import_prop_types.default.bool, renderWrapper: import_prop_types.default.bool, theme: import_prop_types.default.object, animate: import_prop_types.default.bool, motionConfig: import_prop_types.default.string };
var Bt = function() {
};
var Gt = { position: "relative" };
var Lt = function(e11) {
  var t8 = e11.children, i6 = e11.theme, o5 = e11.isInteractive, l5 = void 0 === o5 || o5, d3 = e11.renderWrapper, s5 = void 0 === d3 || d3, u6 = e11.animate, c9 = e11.motionConfig, f3 = (0, import_react16.useRef)(null), m5 = V(f3), y4 = m5.actions, v6 = m5.state, _3 = (0, import_react16.useCallback)(function(e12, r7) {
    return y4.showTooltipFromEvent(e12, r7);
  }, [y4.showTooltipFromEvent]), w5 = (0, import_react16.useMemo)(function() {
    return { showTooltip: l5 ? _3 : Bt, hideTooltip: l5 ? y4.hideTooltip : Bt };
  }, [y4.hideTooltip, l5, _3]);
  return (0, import_jsx_runtime2.jsx)(Wt, { theme: i6, children: (0, import_jsx_runtime2.jsx)(Ar, { animate: u6, config: c9, children: (0, import_jsx_runtime2.jsx)(j.Provider, { value: y4, children: (0, import_jsx_runtime2.jsx)(O.Provider, { value: v6, children: (0, import_jsx_runtime2.jsxs)(Pt, { condition: s5, wrapper: (0, import_jsx_runtime2.jsx)("div", { style: Gt, ref: f3 }), children: [t8(w5), l5 && (0, import_jsx_runtime2.jsx)(F, {})] }) }) }) }) });
};
Lt.propTypes = { children: import_prop_types.default.func.isRequired, isInteractive: import_prop_types.default.bool, renderWrapper: import_prop_types.default.bool, theme: import_prop_types.default.object.isRequired, animate: import_prop_types.default.bool.isRequired, motionConfig: import_prop_types.default.string };
var It = function(e11) {
  var r7 = e11.children, t8 = kt(), n7 = t8[0], i6 = t8[1], o5 = i6.width > 0 && i6.height > 0;
  return (0, import_jsx_runtime2.jsx)("div", { ref: n7, style: { width: "100%", height: "100%" }, children: o5 && r7({ width: i6.width, height: i6.height }) });
};
It.propTypes = { children: import_prop_types.default.func.isRequired };
var Yt = ["id", "colors"];
var Dt = function(e11) {
  var r7 = e11.id, t8 = e11.colors, n7 = Br(e11, Yt);
  return (0, import_jsx_runtime2.jsx)("linearGradient", jr({ id: r7, x1: 0, x2: 0, y1: 0, y2: 1 }, n7, { children: t8.map(function(e12) {
    var r8 = e12.offset, t9 = e12.color, n8 = e12.opacity;
    return (0, import_jsx_runtime2.jsx)("stop", { offset: r8 + "%", stopColor: t9, stopOpacity: void 0 !== n8 ? n8 : 1 }, r8);
  }) }));
};
Dt.propTypes = { id: import_prop_types.default.string.isRequired, colors: import_prop_types.default.arrayOf(import_prop_types.default.shape({ offset: import_prop_types.default.number.isRequired, color: import_prop_types.default.string.isRequired, opacity: import_prop_types.default.number })).isRequired, gradientTransform: import_prop_types.default.string };
var Et = { linearGradient: Dt };
var Ut = { color: "#000000", background: "#ffffff", size: 4, padding: 4, stagger: false };
var Ft = (0, import_react16.memo)(function(e11) {
  var r7 = e11.id, t8 = e11.background, n7 = void 0 === t8 ? Ut.background : t8, i6 = e11.color, o5 = void 0 === i6 ? Ut.color : i6, l5 = e11.size, a5 = void 0 === l5 ? Ut.size : l5, d3 = e11.padding, s5 = void 0 === d3 ? Ut.padding : d3, u6 = e11.stagger, c9 = void 0 === u6 ? Ut.stagger : u6, f3 = a5 + s5, p4 = a5 / 2, h2 = s5 / 2;
  return true === c9 && (f3 = 2 * a5 + 2 * s5), (0, import_jsx_runtime2.jsxs)("pattern", { id: r7, width: f3, height: f3, patternUnits: "userSpaceOnUse", children: [(0, import_jsx_runtime2.jsx)("rect", { width: f3, height: f3, fill: n7 }), (0, import_jsx_runtime2.jsx)("circle", { cx: h2 + p4, cy: h2 + p4, r: p4, fill: o5 }), c9 && (0, import_jsx_runtime2.jsx)("circle", { cx: 1.5 * s5 + a5 + p4, cy: 1.5 * s5 + a5 + p4, r: p4, fill: o5 })] });
});
Ft.displayName = "PatternDots", Ft.propTypes = { id: import_prop_types.default.string.isRequired, color: import_prop_types.default.string.isRequired, background: import_prop_types.default.string.isRequired, size: import_prop_types.default.number.isRequired, padding: import_prop_types.default.number.isRequired, stagger: import_prop_types.default.bool.isRequired };
var Ht = 2 * Math.PI;
var Kt = function(e11) {
  return e11 * Math.PI / 180;
};
var rn = { svg: { align: { left: "start", center: "middle", right: "end", start: "start", middle: "middle", end: "end" }, baseline: { top: "text-before-edge", center: "central", bottom: "alphabetic" } }, canvas: { align: { left: "left", center: "center", right: "right", start: "left", middle: "center", end: "right" }, baseline: { top: "top", center: "middle", bottom: "bottom" } } };
var nn = { spacing: 5, rotation: 0, background: "#000000", color: "#ffffff", lineWidth: 2 };
var on = (0, import_react16.memo)(function(e11) {
  var r7 = e11.id, t8 = e11.spacing, n7 = void 0 === t8 ? nn.spacing : t8, i6 = e11.rotation, o5 = void 0 === i6 ? nn.rotation : i6, l5 = e11.background, a5 = void 0 === l5 ? nn.background : l5, d3 = e11.color, s5 = void 0 === d3 ? nn.color : d3, u6 = e11.lineWidth, c9 = void 0 === u6 ? nn.lineWidth : u6, f3 = Math.round(o5) % 360, p4 = Math.abs(n7);
  f3 > 180 ? f3 -= 360 : f3 > 90 ? f3 -= 180 : f3 < -180 ? f3 += 360 : f3 < -90 && (f3 += 180);
  var h2, g4 = p4, b5 = p4;
  return 0 === f3 ? h2 = "\n                M 0 0 L " + g4 + " 0\n                M 0 " + b5 + " L " + g4 + " " + b5 + "\n            " : 90 === f3 ? h2 = "\n                M 0 0 L 0 " + b5 + "\n                M " + g4 + " 0 L " + g4 + " " + b5 + "\n            " : (g4 = Math.abs(p4 / Math.sin(Kt(f3))), b5 = p4 / Math.sin(Kt(90 - f3)), h2 = f3 > 0 ? "\n                    M 0 " + -b5 + " L " + 2 * g4 + " " + b5 + "\n                    M " + -g4 + " " + -b5 + " L " + g4 + " " + b5 + "\n                    M " + -g4 + " 0 L " + g4 + " " + 2 * b5 + "\n                " : "\n                    M " + -g4 + " " + b5 + " L " + g4 + " " + -b5 + "\n                    M " + -g4 + " " + 2 * b5 + " L " + 2 * g4 + " " + -b5 + "\n                    M 0 " + 2 * b5 + " L " + 2 * g4 + " 0\n                "), (0, import_jsx_runtime2.jsxs)("pattern", { id: r7, width: g4, height: b5, patternUnits: "userSpaceOnUse", children: [(0, import_jsx_runtime2.jsx)("rect", { width: g4, height: b5, fill: a5, stroke: "rgba(255, 0, 0, 0.1)", strokeWidth: 0 }), (0, import_jsx_runtime2.jsx)("path", { d: h2, strokeWidth: c9, stroke: s5, strokeLinecap: "square" })] });
});
on.displayName = "PatternLines", on.propTypes = { id: import_prop_types.default.string.isRequired, spacing: import_prop_types.default.number.isRequired, rotation: import_prop_types.default.number.isRequired, background: import_prop_types.default.string.isRequired, color: import_prop_types.default.string.isRequired, lineWidth: import_prop_types.default.number.isRequired };
var an = { color: "#000000", background: "#ffffff", size: 4, padding: 4, stagger: false };
var dn = (0, import_react16.memo)(function(e11) {
  var r7 = e11.id, t8 = e11.color, n7 = void 0 === t8 ? an.color : t8, i6 = e11.background, o5 = void 0 === i6 ? an.background : i6, l5 = e11.size, a5 = void 0 === l5 ? an.size : l5, d3 = e11.padding, s5 = void 0 === d3 ? an.padding : d3, u6 = e11.stagger, c9 = void 0 === u6 ? an.stagger : u6, f3 = a5 + s5, p4 = s5 / 2;
  return true === c9 && (f3 = 2 * a5 + 2 * s5), (0, import_jsx_runtime2.jsxs)("pattern", { id: r7, width: f3, height: f3, patternUnits: "userSpaceOnUse", children: [(0, import_jsx_runtime2.jsx)("rect", { width: f3, height: f3, fill: o5 }), (0, import_jsx_runtime2.jsx)("rect", { x: p4, y: p4, width: a5, height: a5, fill: n7 }), c9 && (0, import_jsx_runtime2.jsx)("rect", { x: 1.5 * s5 + a5, y: 1.5 * s5 + a5, width: a5, height: a5, fill: n7 })] });
});
dn.displayName = "PatternSquares", dn.propTypes = { id: import_prop_types.default.string.isRequired, color: import_prop_types.default.string.isRequired, background: import_prop_types.default.string.isRequired, size: import_prop_types.default.number.isRequired, padding: import_prop_types.default.number.isRequired, stagger: import_prop_types.default.bool.isRequired };
var un = { patternDots: Ft, patternLines: on, patternSquares: dn };
var cn = ["type"];
var fn = jr({}, Et, un);
var pn = function(e11) {
  var r7 = e11.defs;
  return !r7 || r7.length < 1 ? null : (0, import_jsx_runtime2.jsx)("defs", { "aria-hidden": true, children: r7.map(function(e12) {
    var r8 = e12.type, t8 = Br(e12, cn);
    return fn[r8] ? (0, import_react16.createElement)(fn[r8], jr({ key: t8.id }, t8)) : null;
  }) });
};
pn.propTypes = { defs: import_prop_types.default.arrayOf(import_prop_types.default.shape({ type: import_prop_types.default.oneOf(Object.keys(fn)).isRequired, id: import_prop_types.default.string.isRequired })) };
var hn = (0, import_react16.memo)(pn);
var gn = function(e11) {
  var r7 = e11.width, t8 = e11.height, n7 = e11.margin, i6 = e11.defs, o5 = e11.children, l5 = e11.role, a5 = e11.ariaLabel, d3 = e11.ariaLabelledBy, s5 = e11.ariaDescribedBy, u6 = e11.isFocusable, c9 = zt();
  return (0, import_jsx_runtime2.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", width: r7, height: t8, role: l5, "aria-label": a5, "aria-labelledby": d3, "aria-describedby": s5, focusable: u6, tabIndex: u6 ? 0 : void 0, children: [(0, import_jsx_runtime2.jsx)(hn, { defs: i6 }), (0, import_jsx_runtime2.jsx)("rect", { width: r7, height: t8, fill: c9.background }), (0, import_jsx_runtime2.jsx)("g", { transform: "translate(" + n7.left + "," + n7.top + ")", children: o5 })] });
};
gn.propTypes = { width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, margin: import_prop_types.default.shape({ top: import_prop_types.default.number.isRequired, left: import_prop_types.default.number.isRequired }).isRequired, defs: import_prop_types.default.array, children: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.node), import_prop_types.default.node]).isRequired, role: import_prop_types.default.string, isFocusable: import_prop_types.default.bool, ariaLabel: import_prop_types.default.string, ariaLabelledBy: import_prop_types.default.string, ariaDescribedBy: import_prop_types.default.string };
var bn = function(e11) {
  var r7 = e11.size, t8 = e11.color, n7 = e11.borderWidth, i6 = e11.borderColor;
  return (0, import_jsx_runtime2.jsx)("circle", { r: r7 / 2, fill: t8, stroke: i6, strokeWidth: n7, style: { pointerEvents: "none" } });
};
bn.propTypes = { size: import_prop_types.default.number.isRequired, color: import_prop_types.default.string.isRequired, borderWidth: import_prop_types.default.number.isRequired, borderColor: import_prop_types.default.string.isRequired };
var mn = (0, import_react16.memo)(bn);
var yn = function(e11) {
  var r7 = e11.x, t8 = e11.y, n7 = e11.symbol, i6 = void 0 === n7 ? mn : n7, o5 = e11.size, l5 = e11.datum, a5 = e11.color, d3 = e11.borderWidth, u6 = e11.borderColor, c9 = e11.label, f3 = e11.labelTextAnchor, p4 = void 0 === f3 ? "middle" : f3, h2 = e11.labelYOffset, g4 = void 0 === h2 ? -12 : h2, b5 = zt(), m5 = Ur(), y4 = m5.animate, v6 = m5.config, _3 = useSpring({ transform: "translate(" + r7 + ", " + t8 + ")", config: v6, immediate: !y4 });
  return (0, import_jsx_runtime2.jsxs)(animated.g, { transform: _3.transform, style: { pointerEvents: "none" }, children: [(0, import_react16.createElement)(i6, { size: o5, color: a5, datum: l5, borderWidth: d3, borderColor: u6 }), c9 && (0, import_jsx_runtime2.jsx)("text", { textAnchor: p4, y: g4, style: Mt(b5.dots.text), children: c9 })] });
};
yn.propTypes = { x: import_prop_types.default.number.isRequired, y: import_prop_types.default.number.isRequired, datum: import_prop_types.default.object.isRequired, size: import_prop_types.default.number.isRequired, color: import_prop_types.default.string.isRequired, borderWidth: import_prop_types.default.number.isRequired, borderColor: import_prop_types.default.string.isRequired, symbol: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]), label: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]), labelTextAnchor: import_prop_types.default.oneOf(["start", "middle", "end"]), labelYOffset: import_prop_types.default.number };
var vn = (0, import_react16.memo)(yn);
var _n = function(e11) {
  var r7 = e11.width, t8 = e11.height, n7 = e11.axis, i6 = e11.scale, o5 = e11.value, l5 = e11.lineStyle, a5 = e11.textStyle, d3 = e11.legend, s5 = e11.legendPosition, u6 = void 0 === s5 ? "top-right" : s5, c9 = e11.legendOffsetX, f3 = void 0 === c9 ? 14 : c9, p4 = e11.legendOffsetY, h2 = void 0 === p4 ? 14 : p4, g4 = e11.legendOrientation, b5 = void 0 === g4 ? "horizontal" : g4, m5 = zt(), y4 = 0, v6 = 0, _3 = 0, w5 = 0;
  "y" === n7 ? (_3 = i6(o5), v6 = r7) : (y4 = i6(o5), w5 = t8);
  var k5 = null;
  if (d3) {
    var R = function(e12) {
      var r8 = e12.axis, t9 = e12.width, n8 = e12.height, i7 = e12.position, o6 = e12.offsetX, l6 = e12.offsetY, a6 = e12.orientation, d4 = 0, s6 = 0, u7 = "vertical" === a6 ? -90 : 0, c10 = "start";
      if ("x" === r8) switch (i7) {
        case "top-left":
          d4 = -o6, s6 = l6, c10 = "end";
          break;
        case "top":
          s6 = -l6, c10 = "horizontal" === a6 ? "middle" : "start";
          break;
        case "top-right":
          d4 = o6, s6 = l6, c10 = "horizontal" === a6 ? "start" : "end";
          break;
        case "right":
          d4 = o6, s6 = n8 / 2, c10 = "horizontal" === a6 ? "start" : "middle";
          break;
        case "bottom-right":
          d4 = o6, s6 = n8 - l6, c10 = "start";
          break;
        case "bottom":
          s6 = n8 + l6, c10 = "horizontal" === a6 ? "middle" : "end";
          break;
        case "bottom-left":
          s6 = n8 - l6, d4 = -o6, c10 = "horizontal" === a6 ? "end" : "start";
          break;
        case "left":
          d4 = -o6, s6 = n8 / 2, c10 = "horizontal" === a6 ? "end" : "middle";
      }
      else switch (i7) {
        case "top-left":
          d4 = o6, s6 = -l6, c10 = "start";
          break;
        case "top":
          d4 = t9 / 2, s6 = -l6, c10 = "horizontal" === a6 ? "middle" : "start";
          break;
        case "top-right":
          d4 = t9 - o6, s6 = -l6, c10 = "horizontal" === a6 ? "end" : "start";
          break;
        case "right":
          d4 = t9 + o6, c10 = "horizontal" === a6 ? "start" : "middle";
          break;
        case "bottom-right":
          d4 = t9 - o6, s6 = l6, c10 = "end";
          break;
        case "bottom":
          d4 = t9 / 2, s6 = l6, c10 = "horizontal" === a6 ? "middle" : "end";
          break;
        case "bottom-left":
          d4 = o6, s6 = l6, c10 = "horizontal" === a6 ? "start" : "end";
          break;
        case "left":
          d4 = -o6, c10 = "horizontal" === a6 ? "end" : "middle";
      }
      return { x: d4, y: s6, rotation: u7, textAnchor: c10 };
    }({ axis: n7, width: r7, height: t8, position: u6, offsetX: f3, offsetY: h2, orientation: b5 });
    k5 = (0, import_jsx_runtime2.jsx)("text", { transform: "translate(" + R.x + ", " + R.y + ") rotate(" + R.rotation + ")", textAnchor: R.textAnchor, dominantBaseline: "central", style: a5, children: d3 });
  }
  return (0, import_jsx_runtime2.jsxs)("g", { transform: "translate(" + y4 + ", " + _3 + ")", children: [(0, import_jsx_runtime2.jsx)("line", { x1: 0, x2: v6, y1: 0, y2: w5, stroke: m5.markers.lineColor, strokeWidth: m5.markers.lineStrokeWidth, style: l5 }), k5] });
};
_n.propTypes = { width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, scale: import_prop_types.default.func.isRequired, value: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, lineStyle: import_prop_types.default.object, textStyle: import_prop_types.default.object, legend: import_prop_types.default.string, legendPosition: import_prop_types.default.oneOf(["top-left", "top", "top-right", "right", "bottom-right", "bottom", "bottom-left", "left"]), legendOffsetX: import_prop_types.default.number.isRequired, legendOffsetY: import_prop_types.default.number.isRequired, legendOrientation: import_prop_types.default.oneOf(["horizontal", "vertical"]).isRequired };
var wn = (0, import_react16.memo)(_n);
var kn = function(e11) {
  var r7 = e11.markers, t8 = e11.width, n7 = e11.height, i6 = e11.xScale, o5 = e11.yScale;
  return r7 && 0 !== r7.length ? r7.map(function(e12, r8) {
    return (0, import_jsx_runtime2.jsx)(wn, jr({}, e12, { width: t8, height: n7, scale: "y" === e12.axis ? o5 : i6 }), r8);
  }) : null;
};
kn.propTypes = { width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, xScale: import_prop_types.default.func.isRequired, yScale: import_prop_types.default.func.isRequired, markers: import_prop_types.default.arrayOf(import_prop_types.default.shape({ axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, value: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, lineStyle: import_prop_types.default.object, textStyle: import_prop_types.default.object })) };
var Rn = (0, import_react16.memo)(kn);
var xn = ["theme", "renderWrapper", "animate", "motionConfig"];
var On = function(e11) {
  return function(r7) {
    var t8, n7;
    function i6() {
      return r7.apply(this, arguments) || this;
    }
    return n7 = r7, (t8 = i6).prototype = Object.create(n7.prototype), t8.prototype.constructor = t8, Sr(t8, n7), i6.prototype.render = function() {
      var r8 = this.props, t9 = r8.theme, n8 = r8.renderWrapper, i7 = r8.animate, o5 = r8.motionConfig, l5 = Br(r8, xn);
      return (0, import_jsx_runtime2.jsx)(St, { theme: t9, renderWrapper: n8, isInteractive: l5.isInteractive, animate: i7, motionConfig: o5, children: (0, import_jsx_runtime2.jsx)(e11, jr({}, l5)) });
    }, i6;
  }(import_react16.Component);
};
var qn = function(e11, r7) {
  var t8, n7 = (0, import_isFunction.default)(e11) ? e11 : function(r8) {
    return (0, import_get.default)(r8, e11);
  };
  return r7 && (t8 = (0, import_isFunction.default)(r7) ? r7 : format2(r7)), t8 ? function(e12) {
    return t8(n7(e12));
  } : n7;
};
var Mn = function(e11, r7, t8, n7) {
  var i6 = t8 - e11, o5 = n7 - r7;
  return i6 *= i6, o5 *= o5, Math.sqrt(i6 + o5);
};
var jn = function(e11, r7, t8, n7, i6, o5) {
  return e11 <= i6 && i6 <= e11 + t8 && r7 <= o5 && o5 <= r7 + n7;
};
var Sn = function(e11, r7) {
  var t8, n7 = "touches" in r7 ? r7.touches[0] : r7, i6 = n7.clientX, o5 = n7.clientY, l5 = e11.getBoundingClientRect(), a5 = (t8 = void 0 !== e11.getBBox ? e11.getBBox() : { width: e11.offsetWidth || 0, height: e11.offsetHeight || 0 }).width === l5.width ? 1 : t8.width / l5.width;
  return [(i6 - l5.left) * a5, (o5 - l5.top) * a5];
};
var Bn = Object.keys(Et);
var Gn = Object.keys(un);
var Ln = function(e11, r7, t8) {
  if ("*" === e11) return true;
  if ((0, import_isFunction.default)(e11)) return e11(r7);
  if ((0, import_isPlainObject.default)(e11)) {
    var n7 = t8 ? (0, import_get.default)(r7, t8) : r7;
    return (0, import_isEqual.default)((0, import_pick.default)(n7, Object.keys(e11)), e11);
  }
  return false;
};
var In = function(e11, r7, t8, n7) {
  var i6 = void 0 === n7 ? {} : n7, o5 = i6.dataKey, l5 = i6.colorKey, a5 = void 0 === l5 ? "color" : l5, d3 = i6.targetKey, s5 = void 0 === d3 ? "fill" : d3, u6 = [], c9 = {};
  return e11.length && r7.length && (u6 = [].concat(e11), r7.forEach(function(r8) {
    for (var n8 = function() {
      var n9 = t8[i7], l6 = n9.id, d4 = n9.match;
      if (Ln(d4, r8, o5)) {
        var f3 = e11.find(function(e12) {
          return e12.id === l6;
        });
        if (f3) {
          if (Gn.includes(f3.type)) if ("inherit" === f3.background || "inherit" === f3.color) {
            var p4 = (0, import_get.default)(r8, a5), h2 = f3.background, g4 = f3.color, b5 = l6;
            "inherit" === f3.background && (b5 = b5 + ".bg." + p4, h2 = p4), "inherit" === f3.color && (b5 = b5 + ".fg." + p4, g4 = p4), (0, import_set2.default)(r8, s5, "url(#" + b5 + ")"), c9[b5] || (u6.push(jr({}, f3, { id: b5, background: h2, color: g4 })), c9[b5] = 1);
          } else (0, import_set2.default)(r8, s5, "url(#" + l6 + ")");
          else if (Bn.includes(f3.type)) {
            if (f3.colors.map(function(e12) {
              return e12.color;
            }).includes("inherit")) {
              var m5 = (0, import_get.default)(r8, a5), _3 = l6, w5 = jr({}, f3, { colors: f3.colors.map(function(e12, r9) {
                return "inherit" !== e12.color ? e12 : (_3 = _3 + "." + r9 + "." + m5, jr({}, e12, { color: "inherit" === e12.color ? m5 : e12.color }));
              }) });
              w5.id = _3, (0, import_set2.default)(r8, s5, "url(#" + _3 + ")"), c9[_3] || (u6.push(w5), c9[_3] = 1);
            } else (0, import_set2.default)(r8, s5, "url(#" + l6 + ")");
          }
        }
        return "break";
      }
    }, i7 = 0; i7 < t8.length; i7++) {
      if ("break" === n8()) break;
    }
  })), u6;
};

// node_modules/@nivo/colors/dist/nivo-colors.es.js
var import_react17 = __toESM(require_react());
var import_get2 = __toESM(require_get());
var import_isPlainObject2 = __toESM(require_isPlainObject());
var import_prop_types2 = __toESM(require_prop_types());
function qe() {
  return qe = Object.assign ? Object.assign.bind() : function(e11) {
    for (var r7 = 1; r7 < arguments.length; r7++) {
      var n7 = arguments[r7];
      for (var t8 in n7) Object.prototype.hasOwnProperty.call(n7, t8) && (e11[t8] = n7[t8]);
    }
    return e11;
  }, qe.apply(this, arguments);
}
function Ce(e11, r7) {
  (null == r7 || r7 > e11.length) && (r7 = e11.length);
  for (var n7 = 0, t8 = new Array(r7); n7 < r7; n7++) t8[n7] = e11[n7];
  return t8;
}
function Ge(e11, r7) {
  var n7 = "undefined" != typeof Symbol && e11[Symbol.iterator] || e11["@@iterator"];
  if (n7) return (n7 = n7.call(e11)).next.bind(n7);
  if (Array.isArray(e11) || (n7 = function(e12, r8) {
    if (e12) {
      if ("string" == typeof e12) return Ce(e12, r8);
      var n8 = Object.prototype.toString.call(e12).slice(8, -1);
      return "Object" === n8 && e12.constructor && (n8 = e12.constructor.name), "Map" === n8 || "Set" === n8 ? Array.from(e12) : "Arguments" === n8 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n8) ? Ce(e12, r8) : void 0;
    }
  }(e11)) || r7 && e11 && "number" == typeof e11.length) {
    n7 && (e11 = n7);
    var t8 = 0;
    return function() {
      return t8 >= e11.length ? { done: true } : { done: false, value: e11[t8++] };
    };
  }
  throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
var Re = { nivo: ["#e8c1a0", "#f47560", "#f1e15b", "#e8a838", "#61cdbb", "#97e3d5"], category10: category10_default, accent: Accent_default, dark2: Dark2_default, paired: Paired_default, pastel1: Pastel1_default, pastel2: Pastel2_default, set1: Set1_default, set2: Set2_default, set3: Set3_default, tableau10: Tableau10_default };
var Ve = Object.keys(Re);
var Pe = { brown_blueGreen: scheme, purpleRed_green: scheme2, pink_yellowGreen: scheme3, purple_orange: scheme4, red_blue: scheme5, red_grey: scheme6, red_yellow_blue: scheme7, red_yellow_green: scheme8, spectral: scheme9 };
var Te = Object.keys(Pe);
var Ue = { brown_blueGreen: BrBG_default, purpleRed_green: PRGn_default, pink_yellowGreen: PiYG_default, purple_orange: PuOr_default, red_blue: RdBu_default, red_grey: RdGy_default, red_yellow_blue: RdYlBu_default, red_yellow_green: RdYlGn_default, spectral: Spectral_default };
var De = { blues: scheme22, greens: scheme23, greys: scheme24, oranges: scheme27, purples: scheme25, reds: scheme26, blue_green: scheme10, blue_purple: scheme11, green_blue: scheme12, orange_red: scheme13, purple_blue_green: scheme14, purple_blue: scheme15, purple_red: scheme16, red_purple: scheme17, yellow_green_blue: scheme18, yellow_green: scheme19, yellow_orange_brown: scheme20, yellow_orange_red: scheme21 };
var Me = Object.keys(De);
var $e = { blues: Blues_default, greens: Greens_default, greys: Greys_default, oranges: Oranges_default, purples: Purples_default, reds: Reds_default, turbo: turbo_default, viridis: viridis_default, inferno, magma, plasma, cividis: cividis_default, warm, cool, cubehelixDefault: cubehelix_default, blue_green: BuGn_default, blue_purple: BuPu_default, green_blue: GnBu_default, orange_red: OrRd_default, purple_blue_green: PuBuGn_default, purple_blue: PuBu_default, purple_red: PuRd_default, red_purple: RdPu_default, yellow_green_blue: YlGnBu_default, yellow_green: YlGn_default, yellow_orange_brown: YlOrBr_default, yellow_orange_red: YlOrRd_default };
var Be = qe({}, Re, Pe, De);
var Fe = Object.keys(Be);
var He = function(e11) {
  return Ve.includes(e11);
};
var Je = function(e11) {
  return Te.includes(e11);
};
var Ke = function(e11) {
  return Me.includes(e11);
};
var Le = { rainbow: rainbow_default, sinebow: sinebow_default };
var Ne = qe({}, Ue, $e, Le);
var Qe2 = Object.keys(Ne);
var We = function(e11, r7) {
  if ("function" == typeof e11) return e11;
  if ((0, import_isPlainObject2.default)(e11)) {
    if (function(e12) {
      return void 0 !== e12.theme;
    }(e11)) {
      if (void 0 === r7) throw new Error("Unable to use color from theme as no theme was provided");
      var n7 = (0, import_get2.default)(r7, e11.theme);
      if (void 0 === n7) throw new Error("Color from theme is undefined at path: '" + e11.theme + "'");
      return function() {
        return n7;
      };
    }
    if (function(e12) {
      return void 0 !== e12.from;
    }(e11)) {
      var t8 = function(r8) {
        return (0, import_get2.default)(r8, e11.from);
      };
      if (Array.isArray(e11.modifiers)) {
        for (var o5, i6 = [], u6 = function() {
          var e12 = o5.value, r8 = e12[0], n8 = e12[1];
          if ("brighter" === r8) i6.push(function(e13) {
            return e13.brighter(n8);
          });
          else if ("darker" === r8) i6.push(function(e13) {
            return e13.darker(n8);
          });
          else {
            if ("opacity" !== r8) throw new Error("Invalid color modifier: '" + r8 + "', must be one of: 'brighter', 'darker', 'opacity'");
            i6.push(function(e13) {
              return e13.opacity = n8, e13;
            });
          }
        }, a5 = Ge(e11.modifiers); !(o5 = a5()).done; ) u6();
        return 0 === i6.length ? t8 : function(e12) {
          return i6.reduce(function(e13, r8) {
            return r8(e13);
          }, rgb(t8(e12))).toString();
        };
      }
      return t8;
    }
    throw new Error("Invalid color spec, you should either specify 'theme' or 'from' when using a config object");
  }
  return function() {
    return e11;
  };
};
var Xe = function(e11, r7) {
  return (0, import_react17.useMemo)(function() {
    return We(e11, r7);
  }, [e11, r7]);
};
var Ye = import_prop_types2.default.oneOfType([import_prop_types2.default.string, import_prop_types2.default.func, import_prop_types2.default.shape({ theme: import_prop_types2.default.string.isRequired }), import_prop_types2.default.shape({ from: import_prop_types2.default.string.isRequired, modifiers: import_prop_types2.default.arrayOf(import_prop_types2.default.array) })]);
var fr = function(e11, r7) {
  if ("function" == typeof e11) return e11;
  var n7 = "function" == typeof r7 ? r7 : function(e12) {
    return (0, import_get2.default)(e12, r7);
  };
  if (Array.isArray(e11)) {
    var t8 = ordinal(e11), o5 = function(e12) {
      return t8(n7(e12));
    };
    return o5.scale = t8, o5;
  }
  if ((0, import_isPlainObject2.default)(e11)) {
    if (function(e12) {
      return void 0 !== e12.datum;
    }(e11)) return function(r8) {
      return (0, import_get2.default)(r8, e11.datum);
    };
    if (function(e12) {
      return void 0 !== e12.scheme;
    }(e11)) {
      if (He(e11.scheme)) {
        var i6 = ordinal(Be[e11.scheme]), u6 = function(e12) {
          return i6(n7(e12));
        };
        return u6.scale = i6, u6;
      }
      if (Je(e11.scheme)) {
        if (void 0 !== e11.size && (e11.size < 3 || e11.size > 11)) throw new Error("Invalid size '" + e11.size + "' for diverging color scheme '" + e11.scheme + "', must be between 3~11");
        var a5 = ordinal(Be[e11.scheme][e11.size || 11]), l5 = function(e12) {
          return a5(n7(e12));
        };
        return l5.scale = a5, l5;
      }
      if (Ke(e11.scheme)) {
        if (void 0 !== e11.size && (e11.size < 3 || e11.size > 9)) throw new Error("Invalid size '" + e11.size + "' for sequential color scheme '" + e11.scheme + "', must be between 3~9");
        var c9 = ordinal(Be[e11.scheme][e11.size || 9]), s5 = function(e12) {
          return c9(n7(e12));
        };
        return s5.scale = c9, s5;
      }
    }
    throw new Error("Invalid colors, when using an object, you should either pass a 'datum' or a 'scheme' property");
  }
  return function() {
    return e11;
  };
};
var pr = function(e11, r7) {
  return (0, import_react17.useMemo)(function() {
    return fr(e11, r7);
  }, [e11, r7]);
};

// node_modules/@nivo/axes/dist/nivo-axes.es.js
var t4 = __toESM(require_react());
var import_react18 = __toESM(require_react());

// node_modules/@nivo/scales/dist/nivo-scales.es.js
var import_uniq = __toESM(require_uniq());
var import_uniqBy = __toESM(require_uniqBy());
var import_sortBy = __toESM(require_sortBy());
var import_last2 = __toESM(require_last());
var import_isDate = __toESM(require_isDate());

// node_modules/@nivo/scales/node_modules/d3-time/src/interval.js
var t03 = /* @__PURE__ */ new Date();
var t13 = /* @__PURE__ */ new Date();
function newInterval2(floori, offseti, count3, field) {
  function interval(date2) {
    return floori(date2 = arguments.length === 0 ? /* @__PURE__ */ new Date() : /* @__PURE__ */ new Date(+date2)), date2;
  }
  interval.floor = function(date2) {
    return floori(date2 = /* @__PURE__ */ new Date(+date2)), date2;
  };
  interval.ceil = function(date2) {
    return floori(date2 = new Date(date2 - 1)), offseti(date2, 1), floori(date2), date2;
  };
  interval.round = function(date2) {
    var d0 = interval(date2), d1 = interval.ceil(date2);
    return date2 - d0 < d1 - date2 ? d0 : d1;
  };
  interval.offset = function(date2, step) {
    return offseti(date2 = /* @__PURE__ */ new Date(+date2), step == null ? 1 : Math.floor(step)), date2;
  };
  interval.range = function(start2, stop2, step) {
    var range2 = [], previous;
    start2 = interval.ceil(start2);
    step = step == null ? 1 : Math.floor(step);
    if (!(start2 < stop2) || !(step > 0)) return range2;
    do
      range2.push(previous = /* @__PURE__ */ new Date(+start2)), offseti(start2, step), floori(start2);
    while (previous < start2 && start2 < stop2);
    return range2;
  };
  interval.filter = function(test) {
    return newInterval2(function(date2) {
      if (date2 >= date2) while (floori(date2), !test(date2)) date2.setTime(date2 - 1);
    }, function(date2, step) {
      if (date2 >= date2) {
        if (step < 0) while (++step <= 0) {
          while (offseti(date2, -1), !test(date2)) {
          }
        }
        else while (--step >= 0) {
          while (offseti(date2, 1), !test(date2)) {
          }
        }
      }
    });
  };
  if (count3) {
    interval.count = function(start2, end) {
      t03.setTime(+start2), t13.setTime(+end);
      floori(t03), floori(t13);
      return Math.floor(count3(t03, t13));
    };
    interval.every = function(step) {
      step = Math.floor(step);
      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? function(d3) {
        return field(d3) % step === 0;
      } : function(d3) {
        return interval.count(0, d3) % step === 0;
      });
    };
  }
  return interval;
}

// node_modules/@nivo/scales/node_modules/d3-time/src/millisecond.js
var millisecond3 = newInterval2(function() {
}, function(date2, step) {
  date2.setTime(+date2 + step);
}, function(start2, end) {
  return end - start2;
});
millisecond3.every = function(k5) {
  k5 = Math.floor(k5);
  if (!isFinite(k5) || !(k5 > 0)) return null;
  if (!(k5 > 1)) return millisecond3;
  return newInterval2(function(date2) {
    date2.setTime(Math.floor(date2 / k5) * k5);
  }, function(date2, step) {
    date2.setTime(+date2 + step * k5);
  }, function(start2, end) {
    return (end - start2) / k5;
  });
};
var millisecond_default2 = millisecond3;
var milliseconds3 = millisecond3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/duration.js
var durationSecond3 = 1e3;
var durationMinute3 = 6e4;
var durationHour3 = 36e5;
var durationDay3 = 864e5;
var durationWeek3 = 6048e5;

// node_modules/@nivo/scales/node_modules/d3-time/src/second.js
var second3 = newInterval2(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds());
}, function(date2, step) {
  date2.setTime(+date2 + step * durationSecond3);
}, function(start2, end) {
  return (end - start2) / durationSecond3;
}, function(date2) {
  return date2.getUTCSeconds();
});
var second_default2 = second3;
var seconds3 = second3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/minute.js
var minute2 = newInterval2(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond3);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationMinute3);
}, function(start2, end) {
  return (end - start2) / durationMinute3;
}, function(date2) {
  return date2.getMinutes();
});
var minute_default2 = minute2;
var minutes2 = minute2.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/hour.js
var hour2 = newInterval2(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond3 - date2.getMinutes() * durationMinute3);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationHour3);
}, function(start2, end) {
  return (end - start2) / durationHour3;
}, function(date2) {
  return date2.getHours();
});
var hour_default2 = hour2;
var hours2 = hour2.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/day.js
var day2 = newInterval2(function(date2) {
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setDate(date2.getDate() + step);
}, function(start2, end) {
  return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute3) / durationDay3;
}, function(date2) {
  return date2.getDate() - 1;
});
var days2 = day2.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/week.js
function weekday2(i6) {
  return newInterval2(function(date2) {
    date2.setDate(date2.getDate() - (date2.getDay() + 7 - i6) % 7);
    date2.setHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setDate(date2.getDate() + step * 7);
  }, function(start2, end) {
    return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute3) / durationWeek3;
  });
}
var sunday2 = weekday2(0);
var monday2 = weekday2(1);
var tuesday2 = weekday2(2);
var wednesday2 = weekday2(3);
var thursday2 = weekday2(4);
var friday2 = weekday2(5);
var saturday2 = weekday2(6);
var sundays2 = sunday2.range;
var mondays2 = monday2.range;
var tuesdays2 = tuesday2.range;
var wednesdays2 = wednesday2.range;
var thursdays2 = thursday2.range;
var fridays2 = friday2.range;
var saturdays2 = saturday2.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/month.js
var month2 = newInterval2(function(date2) {
  date2.setDate(1);
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setMonth(date2.getMonth() + step);
}, function(start2, end) {
  return end.getMonth() - start2.getMonth() + (end.getFullYear() - start2.getFullYear()) * 12;
}, function(date2) {
  return date2.getMonth();
});
var month_default2 = month2;
var months2 = month2.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/year.js
var year2 = newInterval2(function(date2) {
  date2.setMonth(0, 1);
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setFullYear(date2.getFullYear() + step);
}, function(start2, end) {
  return end.getFullYear() - start2.getFullYear();
}, function(date2) {
  return date2.getFullYear();
});
year2.every = function(k5) {
  return !isFinite(k5 = Math.floor(k5)) || !(k5 > 0) ? null : newInterval2(function(date2) {
    date2.setFullYear(Math.floor(date2.getFullYear() / k5) * k5);
    date2.setMonth(0, 1);
    date2.setHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setFullYear(date2.getFullYear() + step * k5);
  });
};
var year_default2 = year2;
var years2 = year2.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/utcMinute.js
var utcMinute3 = newInterval2(function(date2) {
  date2.setUTCSeconds(0, 0);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationMinute3);
}, function(start2, end) {
  return (end - start2) / durationMinute3;
}, function(date2) {
  return date2.getUTCMinutes();
});
var utcMinute_default2 = utcMinute3;
var utcMinutes3 = utcMinute3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/utcHour.js
var utcHour3 = newInterval2(function(date2) {
  date2.setUTCMinutes(0, 0, 0);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationHour3);
}, function(start2, end) {
  return (end - start2) / durationHour3;
}, function(date2) {
  return date2.getUTCHours();
});
var utcHour_default2 = utcHour3;
var utcHours3 = utcHour3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/utcDay.js
var utcDay3 = newInterval2(function(date2) {
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCDate(date2.getUTCDate() + step);
}, function(start2, end) {
  return (end - start2) / durationDay3;
}, function(date2) {
  return date2.getUTCDate() - 1;
});
var utcDays3 = utcDay3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/utcWeek.js
function utcWeekday3(i6) {
  return newInterval2(function(date2) {
    date2.setUTCDate(date2.getUTCDate() - (date2.getUTCDay() + 7 - i6) % 7);
    date2.setUTCHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setUTCDate(date2.getUTCDate() + step * 7);
  }, function(start2, end) {
    return (end - start2) / durationWeek3;
  });
}
var utcSunday3 = utcWeekday3(0);
var utcMonday3 = utcWeekday3(1);
var utcTuesday3 = utcWeekday3(2);
var utcWednesday3 = utcWeekday3(3);
var utcThursday3 = utcWeekday3(4);
var utcFriday3 = utcWeekday3(5);
var utcSaturday3 = utcWeekday3(6);
var utcSundays3 = utcSunday3.range;
var utcMondays3 = utcMonday3.range;
var utcTuesdays3 = utcTuesday3.range;
var utcWednesdays3 = utcWednesday3.range;
var utcThursdays3 = utcThursday3.range;
var utcFridays3 = utcFriday3.range;
var utcSaturdays3 = utcSaturday3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/utcMonth.js
var utcMonth3 = newInterval2(function(date2) {
  date2.setUTCDate(1);
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCMonth(date2.getUTCMonth() + step);
}, function(start2, end) {
  return end.getUTCMonth() - start2.getUTCMonth() + (end.getUTCFullYear() - start2.getUTCFullYear()) * 12;
}, function(date2) {
  return date2.getUTCMonth();
});
var utcMonth_default2 = utcMonth3;
var utcMonths3 = utcMonth3.range;

// node_modules/@nivo/scales/node_modules/d3-time/src/utcYear.js
var utcYear3 = newInterval2(function(date2) {
  date2.setUTCMonth(0, 1);
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCFullYear(date2.getUTCFullYear() + step);
}, function(start2, end) {
  return end.getUTCFullYear() - start2.getUTCFullYear();
}, function(date2) {
  return date2.getUTCFullYear();
});
utcYear3.every = function(k5) {
  return !isFinite(k5 = Math.floor(k5)) || !(k5 > 0) ? null : newInterval2(function(date2) {
    date2.setUTCFullYear(Math.floor(date2.getUTCFullYear() / k5) * k5);
    date2.setUTCMonth(0, 1);
    date2.setUTCHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setUTCFullYear(date2.getUTCFullYear() + step * k5);
  });
};
var utcYear_default2 = utcYear3;
var utcYears3 = utcYear3.range;

// node_modules/@nivo/scales/dist/nivo-scales.es.js
function $() {
  return $ = Object.assign ? Object.assign.bind() : function(n7) {
    for (var t8 = 1; t8 < arguments.length; t8++) {
      var r7 = arguments[t8];
      for (var e11 in r7) Object.prototype.hasOwnProperty.call(r7, e11) && (n7[e11] = r7[e11]);
    }
    return n7;
  }, $.apply(this, arguments);
}
var J = [function(n7) {
  return n7.setMilliseconds(0);
}, function(n7) {
  return n7.setSeconds(0);
}, function(n7) {
  return n7.setMinutes(0);
}, function(n7) {
  return n7.setHours(0);
}, function(n7) {
  return n7.setDate(1);
}, function(n7) {
  return n7.setMonth(0);
}];
var K = { millisecond: [], second: J.slice(0, 1), minute: J.slice(0, 2), hour: J.slice(0, 3), day: J.slice(0, 4), month: J.slice(0, 5), year: J.slice(0, 6) };
var L = function(n7) {
  return function(t8) {
    return K[n7].forEach(function(n8) {
      n8(t8);
    }), t8;
  };
};
var Q = function(n7) {
  var t8 = n7.format, r7 = void 0 === t8 ? "native" : t8, e11 = n7.precision, a5 = void 0 === e11 ? "millisecond" : e11, u6 = n7.useUTC, c9 = void 0 === u6 || u6, s5 = L(a5);
  return function(n8) {
    if (void 0 === n8) return n8;
    if ("native" === r7 || n8 instanceof Date) return s5(n8);
    var t9 = c9 ? utcParse2(r7) : timeParse2(r7);
    return s5(t9(n8));
  };
};
var W2 = function(n7, t8, r7, e11) {
  var a5, i6, o5, c9, s5 = n7.min, d3 = void 0 === s5 ? 0 : s5, f3 = n7.max, l5 = void 0 === f3 ? "auto" : f3, m5 = n7.stacked, v6 = void 0 !== m5 && m5, y4 = n7.reverse, p4 = void 0 !== y4 && y4, h2 = n7.clamp, g4 = void 0 !== h2 && h2, x4 = n7.nice, k5 = void 0 !== x4 && x4;
  "auto" === d3 ? a5 = true === v6 ? null != (i6 = t8.minStacked) ? i6 : 0 : t8.min : a5 = d3;
  "auto" === l5 ? o5 = true === v6 ? null != (c9 = t8.maxStacked) ? c9 : 0 : t8.max : o5 = l5;
  var T4 = linear().rangeRound("x" === e11 ? [0, r7] : [r7, 0]).domain(p4 ? [o5, a5] : [a5, o5]).clamp(g4);
  return true === k5 ? T4.nice() : "number" == typeof k5 && T4.nice(k5), X(T4, v6);
};
var X = function(n7, t8) {
  void 0 === t8 && (t8 = false);
  var r7 = n7;
  return r7.type = "linear", r7.stacked = t8, r7;
};
var Y = function(n7, t8, r7) {
  var e11 = point().range([0, r7]).domain(t8.all);
  return e11.type = "point", e11;
};
var _ = function(n7, t8, r7, e11) {
  var a5 = n7.round, i6 = void 0 === a5 || a5, o5 = band().range("x" === e11 ? [0, r7] : [r7, 0]).domain(t8.all).round(i6);
  return nn2(o5);
};
var nn2 = function(n7) {
  var t8 = n7;
  return t8.type = "band", t8;
};
var tn = function(n7, t8, r7) {
  var e11, a5, i6 = n7.format, o5 = void 0 === i6 ? "native" : i6, u6 = n7.precision, c9 = void 0 === u6 ? "millisecond" : u6, s5 = n7.min, l5 = void 0 === s5 ? "auto" : s5, m5 = n7.max, v6 = void 0 === m5 ? "auto" : m5, y4 = n7.useUTC, p4 = void 0 === y4 || y4, h2 = n7.nice, g4 = void 0 !== h2 && h2, x4 = Q({ format: o5, precision: c9, useUTC: p4 });
  e11 = "auto" === l5 ? x4(t8.min) : "native" !== o5 ? x4(l5) : l5, a5 = "auto" === v6 ? x4(t8.max) : "native" !== o5 ? x4(v6) : v6;
  var k5 = p4 ? utcTime() : time();
  k5.range([0, r7]), e11 && a5 && k5.domain([e11, a5]), true === g4 ? k5.nice() : "object" != typeof g4 && "number" != typeof g4 || k5.nice(g4);
  var T4 = k5;
  return T4.type = "time", T4.useUTC = p4, T4;
};
var rn2 = function(n7, t8, r7, e11) {
  var a5, i6 = n7.base, o5 = void 0 === i6 ? 10 : i6, u6 = n7.min, c9 = void 0 === u6 ? "auto" : u6, s5 = n7.max, d3 = void 0 === s5 ? "auto" : s5;
  if (t8.all.some(function(n8) {
    return 0 === n8;
  })) throw new Error("a log scale domain must not include or cross zero");
  var f3, m5, v6 = false;
  if (t8.all.filter(function(n8) {
    return null != n8;
  }).forEach(function(n8) {
    v6 || (void 0 === a5 ? a5 = Math.sign(n8) : Math.sign(n8) !== a5 && (v6 = true));
  }), v6) throw new Error("a log scale domain must be strictly-positive or strictly-negative");
  f3 = "auto" === c9 ? t8.min : c9, m5 = "auto" === d3 ? t8.max : d3;
  var y4 = log().domain([f3, m5]).rangeRound("x" === e11 ? [0, r7] : [r7, 0]).base(o5).nice();
  return y4.type = "log", y4;
};
var en = function(n7, t8, r7, e11) {
  var a5, i6, o5 = n7.constant, u6 = void 0 === o5 ? 1 : o5, c9 = n7.min, s5 = void 0 === c9 ? "auto" : c9, d3 = n7.max, f3 = void 0 === d3 ? "auto" : d3, l5 = n7.reverse, v6 = void 0 !== l5 && l5;
  a5 = "auto" === s5 ? t8.min : s5, i6 = "auto" === f3 ? t8.max : f3;
  var y4 = symlog().constant(u6).rangeRound("x" === e11 ? [0, r7] : [r7, 0]).nice();
  true === v6 ? y4.domain([i6, a5]) : y4.domain([a5, i6]);
  var p4 = y4;
  return p4.type = "symlog", p4;
};
var an2 = function(n7) {
  return "x" === n7 ? "y" : "x";
};
var on2 = function(n7, t8) {
  return n7 === t8;
};
var un2 = function(n7, t8) {
  return n7.getTime() === t8.getTime();
};
function cn2(n7, t8, r7, e11) {
  switch (n7.type) {
    case "linear":
      return W2(n7, t8, r7, e11);
    case "point":
      return Y(n7, t8, r7);
    case "band":
      return _(n7, t8, r7, e11);
    case "time":
      return tn(n7, t8, r7);
    case "log":
      return rn2(n7, t8, r7, e11);
    case "symlog":
      return en(n7, t8, r7, e11);
    default:
      throw new Error("invalid scale spec");
  }
}
var sn = function(n7, t8, r7) {
  var e11;
  if ("stacked" in r7 && r7.stacked) {
    var a5 = n7.data["x" === t8 ? "xStacked" : "yStacked"];
    return null == a5 ? null : r7(a5);
  }
  return null != (e11 = r7(n7.data[t8])) ? e11 : null;
};
var dn2 = function(n7, t8, r7, e11, a5) {
  var i6 = n7.map(function(n8) {
    return function(n9) {
      return $({}, n9, { data: n9.data.map(function(n10) {
        return { data: $({}, n10) };
      }) });
    }(n8);
  }), o5 = fn2(i6, t8, r7);
  "stacked" in t8 && true === t8.stacked && vn2(o5, i6), "stacked" in r7 && true === r7.stacked && yn2(o5, i6);
  var u6 = cn2(t8, o5.x, e11, "x"), c9 = cn2(r7, o5.y, a5, "y"), s5 = i6.map(function(n8) {
    return $({}, n8, { data: n8.data.map(function(n9) {
      return $({}, n9, { position: { x: sn(n9, "x", u6), y: sn(n9, "y", c9) } });
    }) });
  });
  return $({}, o5, { series: s5, xScale: u6, yScale: c9 });
};
var fn2 = function(n7, t8, r7) {
  return { x: ln(n7, "x", t8), y: ln(n7, "y", r7) };
};
var ln = function(a5, i6, o5, u6) {
  var c9 = void 0 === u6 ? {} : u6, s5 = c9.getValue, d3 = void 0 === s5 ? function(n7) {
    return n7.data[i6];
  } : s5, f3 = c9.setValue, l5 = void 0 === f3 ? function(n7, t8) {
    n7.data[i6] = t8;
  } : f3;
  if ("linear" === o5.type) a5.forEach(function(n7) {
    n7.data.forEach(function(n8) {
      var t8 = d3(n8);
      t8 && l5(n8, parseFloat(String(t8)));
    });
  });
  else if ("time" === o5.type && "native" !== o5.format) {
    var m5 = Q(o5);
    a5.forEach(function(n7) {
      n7.data.forEach(function(n8) {
        var t8 = d3(n8);
        t8 && l5(n8, m5(t8));
      });
    });
  }
  var v6 = [];
  switch (a5.forEach(function(n7) {
    n7.data.forEach(function(n8) {
      v6.push(d3(n8));
    });
  }), o5.type) {
    case "linear":
      var y4 = (0, import_sortBy.default)((0, import_uniq.default)(v6).filter(function(n7) {
        return null !== n7;
      }), function(n7) {
        return n7;
      });
      return { all: y4, min: Math.min.apply(Math, y4), max: Math.max.apply(Math, y4) };
    case "time":
      var p4 = (0, import_uniqBy.default)(v6, function(n7) {
        return n7.getTime();
      }).slice(0).sort(function(n7, t8) {
        return t8.getTime() - n7.getTime();
      }).reverse();
      return { all: p4, min: p4[0], max: (0, import_last2.default)(p4) };
    default:
      var h2 = (0, import_uniq.default)(v6);
      return { all: h2, min: h2[0], max: (0, import_last2.default)(h2) };
  }
};
var mn2 = function(n7, t8, r7) {
  var i6 = an2(n7), o5 = [];
  t8[i6].all.forEach(function(t9) {
    var u6 = (0, import_isDate.default)(t9) ? un2 : on2, c9 = [];
    r7.forEach(function(r8) {
      var a5 = r8.data.find(function(n8) {
        return u6(n8.data[i6], t9);
      }), s5 = null, d3 = null;
      if (void 0 !== a5) {
        if (null !== (s5 = a5.data[n7])) {
          var f3 = (0, import_last2.default)(c9);
          void 0 === f3 ? d3 = s5 : null !== f3 && (d3 = f3 + s5);
        }
        a5.data["x" === n7 ? "xStacked" : "yStacked"] = d3;
      }
      c9.push(d3), null !== d3 && o5.push(d3);
    });
  }), t8[n7].minStacked = Math.min.apply(Math, o5), t8[n7].maxStacked = Math.max.apply(Math, o5);
};
var vn2 = function(n7, t8) {
  return mn2("x", n7, t8);
};
var yn2 = function(n7, t8) {
  return mn2("y", n7, t8);
};
var pn2 = function(n7) {
  var t8 = n7.bandwidth();
  if (0 === t8) return n7;
  var r7 = t8 / 2;
  return n7.round() && (r7 = Math.round(r7)), function(t9) {
    var e11;
    return (null != (e11 = n7(t9)) ? e11 : 0) + r7;
  };
};
var hn2 = { millisecond: [millisecond_default2, millisecond_default2], second: [second_default2, second_default2], minute: [minute_default2, utcMinute_default2], hour: [hour_default2, utcHour_default2], day: [newInterval2(function(n7) {
  return n7.setHours(0, 0, 0, 0);
}, function(n7, t8) {
  return n7.setDate(n7.getDate() + t8);
}, function(n7, t8) {
  return (t8.getTime() - n7.getTime()) / 864e5;
}, function(n7) {
  return Math.floor(n7.getTime() / 864e5);
}), newInterval2(function(n7) {
  return n7.setUTCHours(0, 0, 0, 0);
}, function(n7, t8) {
  return n7.setUTCDate(n7.getUTCDate() + t8);
}, function(n7, t8) {
  return (t8.getTime() - n7.getTime()) / 864e5;
}, function(n7) {
  return Math.floor(n7.getTime() / 864e5);
})], week: [sunday2, utcSunday3], sunday: [sunday2, utcSunday3], monday: [monday2, utcMonday3], tuesday: [tuesday2, utcTuesday3], wednesday: [wednesday2, utcWednesday3], thursday: [thursday2, utcThursday3], friday: [friday2, utcFriday3], saturday: [saturday2, utcSaturday3], month: [month_default2, utcMonth_default2], year: [year_default2, utcYear_default2] };
var gn2 = Object.keys(hn2);
var xn2 = new RegExp("^every\\s*(\\d+)?\\s*(" + gn2.join("|") + ")s?$", "i");
var kn2 = function(n7, t8) {
  if (Array.isArray(t8)) return t8;
  if ("string" == typeof t8 && "useUTC" in n7) {
    var r7 = t8.match(xn2);
    if (r7) {
      var e11 = r7[1], a5 = r7[2], i6 = hn2[a5][n7.useUTC ? 1 : 0];
      if ("day" === a5) {
        var o5, u6, c9 = n7.domain(), s5 = c9[0], d3 = c9[1], f3 = new Date(d3);
        return f3.setDate(f3.getDate() + 1), null != (o5 = null == (u6 = i6.every(Number(null != e11 ? e11 : 1))) ? void 0 : u6.range(s5, f3)) ? o5 : [];
      }
      if (void 0 === e11) return n7.ticks(i6);
      var l5 = i6.every(Number(e11));
      if (l5) return n7.ticks(l5);
    }
    throw new Error("Invalid tickValues: " + t8);
  }
  if ("ticks" in n7) {
    if (void 0 === t8) return n7.ticks();
    if ("number" == typeof (m5 = t8) && isFinite(m5) && Math.floor(m5) === m5) return n7.ticks(t8);
  }
  var m5;
  return n7.domain();
};

// node_modules/@nivo/axes/dist/nivo-axes.es.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
function p2() {
  return p2 = Object.assign ? Object.assign.bind() : function(t8) {
    for (var e11 = 1; e11 < arguments.length; e11++) {
      var i6 = arguments[e11];
      for (var n7 in i6) Object.prototype.hasOwnProperty.call(i6, n7) && (t8[n7] = i6[n7]);
    }
    return t8;
  }, p2.apply(this, arguments);
}
var b2 = function(t8) {
  var e11, i6 = t8.axis, n7 = t8.scale, r7 = t8.ticksPosition, o5 = t8.tickValues, l5 = t8.tickSize, s5 = t8.tickPadding, c9 = t8.tickRotation, f3 = t8.truncateTickAt, u6 = t8.engine, d3 = void 0 === u6 ? "svg" : u6, x4 = kn2(n7, o5), m5 = rn[d3], k5 = "bandwidth" in n7 ? pn2(n7) : n7, g4 = { lineX: 0, lineY: 0 }, v6 = { textX: 0, textY: 0 }, b5 = "object" == typeof document && "rtl" === document.dir, P4 = m5.align.center, T4 = m5.baseline.center;
  "x" === i6 ? (e11 = function(t9) {
    var e12;
    return { x: null != (e12 = k5(t9)) ? e12 : 0, y: 0 };
  }, g4.lineY = l5 * ("after" === r7 ? 1 : -1), v6.textY = (l5 + s5) * ("after" === r7 ? 1 : -1), T4 = "after" === r7 ? m5.baseline.top : m5.baseline.bottom, 0 === c9 ? P4 = m5.align.center : "after" === r7 && c9 < 0 || "before" === r7 && c9 > 0 ? (P4 = m5.align[b5 ? "left" : "right"], T4 = m5.baseline.center) : ("after" === r7 && c9 > 0 || "before" === r7 && c9 < 0) && (P4 = m5.align[b5 ? "right" : "left"], T4 = m5.baseline.center)) : (e11 = function(t9) {
    var e12;
    return { x: 0, y: null != (e12 = k5(t9)) ? e12 : 0 };
  }, g4.lineX = l5 * ("after" === r7 ? 1 : -1), v6.textX = (l5 + s5) * ("after" === r7 ? 1 : -1), P4 = "after" === r7 ? m5.align.left : m5.align.right);
  return { ticks: x4.map(function(t9) {
    var i7 = "string" == typeof t9 ? function(t10) {
      var e12 = String(t10).length;
      return f3 && f3 > 0 && e12 > f3 ? "" + String(t10).slice(0, f3).concat("...") : "" + t10;
    }(t9) : t9;
    return p2({ key: t9 instanceof Date ? "" + t9.valueOf() : "" + t9, value: i7 }, e11(t9), g4, v6);
  }), textAlign: P4, textBaseline: T4 };
};
var P2 = function(t8, e11) {
  if (void 0 === t8 || "function" == typeof t8) return t8;
  if ("time" === e11.type) {
    var i6 = timeFormat2(t8);
    return function(t9) {
      return i6(t9 instanceof Date ? t9 : new Date(t9));
    };
  }
  return format2(t8);
};
var T2 = function(t8) {
  var e11, i6 = t8.width, n7 = t8.height, r7 = t8.scale, a5 = t8.axis, o5 = t8.values, l5 = (e11 = o5, Array.isArray(e11) ? o5 : void 0) || kn2(r7, o5), s5 = "bandwidth" in r7 ? pn2(r7) : r7, c9 = "x" === a5 ? l5.map(function(t9) {
    var e12, i7;
    return { key: t9 instanceof Date ? "" + t9.valueOf() : "" + t9, x1: null != (e12 = s5(t9)) ? e12 : 0, x2: null != (i7 = s5(t9)) ? i7 : 0, y1: 0, y2: n7 };
  }) : l5.map(function(t9) {
    var e12, n8;
    return { key: t9 instanceof Date ? "" + t9.valueOf() : "" + t9, x1: 0, x2: i6, y1: null != (e12 = s5(t9)) ? e12 : 0, y2: null != (n8 = s5(t9)) ? n8 : 0 };
  });
  return c9;
};
var A2 = (0, import_react18.memo)(function(t8) {
  var e11, n7 = t8.value, r7 = t8.format, a5 = t8.lineX, s5 = t8.lineY, c9 = t8.onClick, u6 = t8.textBaseline, d3 = t8.textAnchor, x4 = t8.animatedProps, m5 = zt(), y4 = m5.axis.ticks.line, h2 = m5.axis.ticks.text, v6 = null != (e11 = null == r7 ? void 0 : r7(n7)) ? e11 : n7, b5 = (0, import_react18.useMemo)(function() {
    var t9 = { opacity: x4.opacity };
    return c9 ? { style: p2({}, t9, { cursor: "pointer" }), onClick: function(t10) {
      return c9(t10, v6);
    } } : { style: t9 };
  }, [x4.opacity, c9, v6]);
  return (0, import_jsx_runtime3.jsxs)(animated.g, p2({ transform: x4.transform }, b5, { children: [(0, import_jsx_runtime3.jsx)("line", { x1: 0, x2: a5, y1: 0, y2: s5, style: y4 }), h2.outlineWidth > 0 && (0, import_jsx_runtime3.jsx)(animated.text, { dominantBaseline: u6, textAnchor: d3, transform: x4.textTransform, style: h2, strokeWidth: 2 * h2.outlineWidth, stroke: h2.outlineColor, strokeLinejoin: "round", children: "" + v6 }), (0, import_jsx_runtime3.jsx)(animated.text, { dominantBaseline: u6, textAnchor: d3, transform: x4.textTransform, style: Mt(h2), children: "" + v6 })] }));
});
var S = function(e11) {
  var r7 = e11.axis, a5 = e11.scale, l5 = e11.x, c9 = void 0 === l5 ? 0 : l5, x4 = e11.y, m5 = void 0 === x4 ? 0 : x4, y4 = e11.length, h2 = e11.ticksPosition, T4 = e11.tickValues, S3 = e11.tickSize, W5 = void 0 === S3 ? 5 : S3, w5 = e11.tickPadding, B4 = void 0 === w5 ? 5 : w5, X5 = e11.tickRotation, Y4 = void 0 === X5 ? 0 : X5, C5 = e11.format, O5 = e11.renderTick, j3 = void 0 === O5 ? A2 : O5, z6 = e11.truncateTickAt, V2 = e11.legend, D3 = e11.legendPosition, R = void 0 === D3 ? "end" : D3, E3 = e11.legendOffset, q2 = void 0 === E3 ? 0 : E3, F2 = e11.onClick, L3 = e11.ariaHidden, N2 = zt(), H4 = N2.axis.legend.text, I2 = (0, import_react18.useMemo)(function() {
    return P2(C5, a5);
  }, [C5, a5]), J2 = b2({ axis: r7, scale: a5, ticksPosition: h2, tickValues: T4, tickSize: W5, tickPadding: B4, tickRotation: Y4, truncateTickAt: z6 }), G = J2.ticks, K2 = J2.textAlign, M3 = J2.textBaseline, Q3 = null;
  if (void 0 !== V2) {
    var U2, Z2 = 0, $3 = 0, _3 = 0;
    "y" === r7 ? (_3 = -90, Z2 = q2, "start" === R ? (U2 = "start", $3 = y4) : "middle" === R ? (U2 = "middle", $3 = y4 / 2) : "end" === R && (U2 = "end")) : ($3 = q2, "start" === R ? U2 = "start" : "middle" === R ? (U2 = "middle", Z2 = y4 / 2) : "end" === R && (U2 = "end", Z2 = y4)), Q3 = (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [H4.outlineWidth > 0 && (0, import_jsx_runtime3.jsx)("text", { transform: "translate(" + Z2 + ", " + $3 + ") rotate(" + _3 + ")", textAnchor: U2, style: p2({ dominantBaseline: "central" }, H4), strokeWidth: 2 * H4.outlineWidth, stroke: H4.outlineColor, strokeLinejoin: "round", children: V2 }), (0, import_jsx_runtime3.jsx)("text", { transform: "translate(" + Z2 + ", " + $3 + ") rotate(" + _3 + ")", textAnchor: U2, style: p2({ dominantBaseline: "central" }, H4), children: V2 })] });
  }
  var tt2 = Ur(), et2 = tt2.animate, it2 = tt2.config, nt2 = useSpring({ transform: "translate(" + c9 + "," + m5 + ")", lineX2: "x" === r7 ? y4 : 0, lineY2: "x" === r7 ? 0 : y4, config: it2, immediate: !et2 }), rt2 = (0, import_react18.useCallback)(function(t8) {
    return { opacity: 1, transform: "translate(" + t8.x + "," + t8.y + ")", textTransform: "translate(" + t8.textX + "," + t8.textY + ") rotate(" + Y4 + ")" };
  }, [Y4]), at2 = (0, import_react18.useCallback)(function(t8) {
    return { opacity: 0, transform: "translate(" + t8.x + "," + t8.y + ")", textTransform: "translate(" + t8.textX + "," + t8.textY + ") rotate(" + Y4 + ")" };
  }, [Y4]), ot2 = useTransition(G, { keys: function(t8) {
    return t8.key;
  }, initial: rt2, from: at2, enter: rt2, update: rt2, leave: { opacity: 0 }, config: it2, immediate: !et2 });
  return (0, import_jsx_runtime3.jsxs)(animated.g, { transform: nt2.transform, "aria-hidden": L3, children: [ot2(function(e12, i6, n7, r8) {
    return t4.createElement(j3, p2({ tickIndex: r8, format: I2, rotate: Y4, textBaseline: M3, textAnchor: K2, truncateTickAt: z6, animatedProps: e12 }, i6, F2 ? { onClick: F2 } : {}));
  }), (0, import_jsx_runtime3.jsx)(animated.line, { style: N2.axis.domain.line, x1: 0, x2: nt2.lineX2, y1: 0, y2: nt2.lineY2 }), Q3] });
};
var W3 = (0, import_react18.memo)(S);
var w2 = ["top", "right", "bottom", "left"];
var B = (0, import_react18.memo)(function(t8) {
  var e11 = t8.xScale, i6 = t8.yScale, n7 = t8.width, r7 = t8.height, a5 = { top: t8.top, right: t8.right, bottom: t8.bottom, left: t8.left };
  return (0, import_jsx_runtime3.jsx)(import_jsx_runtime3.Fragment, { children: w2.map(function(t9) {
    var o5 = a5[t9];
    if (!o5) return null;
    var l5 = "top" === t9 || "bottom" === t9;
    return (0, import_jsx_runtime3.jsx)(W3, p2({}, o5, { axis: l5 ? "x" : "y", x: "right" === t9 ? n7 : 0, y: "bottom" === t9 ? r7 : 0, scale: l5 ? e11 : i6, length: l5 ? n7 : r7, ticksPosition: "top" === t9 || "left" === t9 ? "before" : "after", truncateTickAt: o5.truncateTickAt }), t9);
  }) });
});
var X2 = (0, import_react18.memo)(function(t8) {
  var e11 = t8.animatedProps, i6 = zt();
  return (0, import_jsx_runtime3.jsx)(animated.line, p2({}, e11, i6.grid.line));
});
var Y2 = (0, import_react18.memo)(function(t8) {
  var e11 = t8.lines, i6 = Ur(), n7 = i6.animate, a5 = i6.config, o5 = useTransition(e11, { keys: function(t9) {
    return t9.key;
  }, initial: function(t9) {
    return { opacity: 1, x1: t9.x1, x2: t9.x2, y1: t9.y1, y2: t9.y2 };
  }, from: function(t9) {
    return { opacity: 0, x1: t9.x1, x2: t9.x2, y1: t9.y1, y2: t9.y2 };
  }, enter: function(t9) {
    return { opacity: 1, x1: t9.x1, x2: t9.x2, y1: t9.y1, y2: t9.y2 };
  }, update: function(t9) {
    return { opacity: 1, x1: t9.x1, x2: t9.x2, y1: t9.y1, y2: t9.y2 };
  }, leave: { opacity: 0 }, config: a5, immediate: !n7 });
  return (0, import_jsx_runtime3.jsx)("g", { children: o5(function(t9, e12) {
    return (0, import_react18.createElement)(X2, p2({}, e12, { key: e12.key, animatedProps: t9 }));
  }) });
});
var C3 = (0, import_react18.memo)(function(t8) {
  var e11 = t8.width, n7 = t8.height, r7 = t8.xScale, a5 = t8.yScale, o5 = t8.xValues, l5 = t8.yValues, s5 = (0, import_react18.useMemo)(function() {
    return !!r7 && T2({ width: e11, height: n7, scale: r7, axis: "x", values: o5 });
  }, [r7, o5, e11, n7]), c9 = (0, import_react18.useMemo)(function() {
    return !!a5 && T2({ width: e11, height: n7, scale: a5, axis: "y", values: l5 });
  }, [n7, e11, a5, l5]);
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [s5 && (0, import_jsx_runtime3.jsx)(Y2, { lines: s5 }), c9 && (0, import_jsx_runtime3.jsx)(Y2, { lines: c9 })] });
});
var O3 = function(t8, e11) {
  var i6, n7 = e11.axis, r7 = e11.scale, a5 = e11.x, o5 = void 0 === a5 ? 0 : a5, l5 = e11.y, s5 = void 0 === l5 ? 0 : l5, f3 = e11.length, u6 = e11.ticksPosition, d3 = e11.tickValues, x4 = e11.tickSize, m5 = void 0 === x4 ? 5 : x4, y4 = e11.tickPadding, h2 = void 0 === y4 ? 5 : y4, k5 = e11.tickRotation, g4 = void 0 === k5 ? 0 : k5, v6 = e11.format, p4 = e11.legend, P4 = e11.legendPosition, T4 = void 0 === P4 ? "end" : P4, A3 = e11.legendOffset, S3 = void 0 === A3 ? 0 : A3, W5 = e11.theme, w5 = b2({ axis: n7, scale: r7, ticksPosition: u6, tickValues: d3, tickSize: m5, tickPadding: h2, tickRotation: g4, engine: "canvas" }), B4 = w5.ticks, X5 = w5.textAlign, Y4 = w5.textBaseline;
  t8.save(), t8.translate(o5, s5), t8.textAlign = X5, t8.textBaseline = Y4;
  var C5 = W5.axis.ticks.text;
  t8.font = (C5.fontWeight ? C5.fontWeight + " " : "") + C5.fontSize + "px " + C5.fontFamily, (null != (i6 = W5.axis.domain.line.strokeWidth) ? i6 : 0) > 0 && (t8.lineWidth = Number(W5.axis.domain.line.strokeWidth), t8.lineCap = "square", W5.axis.domain.line.stroke && (t8.strokeStyle = W5.axis.domain.line.stroke), t8.beginPath(), t8.moveTo(0, 0), t8.lineTo("x" === n7 ? f3 : 0, "x" === n7 ? 0 : f3), t8.stroke());
  var O5 = "function" == typeof v6 ? v6 : function(t9) {
    return "" + t9;
  };
  if (B4.forEach(function(e12) {
    var i7;
    (null != (i7 = W5.axis.ticks.line.strokeWidth) ? i7 : 0) > 0 && (t8.lineWidth = Number(W5.axis.ticks.line.strokeWidth), t8.lineCap = "square", W5.axis.ticks.line.stroke && (t8.strokeStyle = W5.axis.ticks.line.stroke), t8.beginPath(), t8.moveTo(e12.x, e12.y), t8.lineTo(e12.x + e12.lineX, e12.y + e12.lineY), t8.stroke());
    var n8 = O5(e12.value);
    t8.save(), t8.translate(e12.x + e12.textX, e12.y + e12.textY), t8.rotate(Kt(g4)), C5.outlineWidth > 0 && (t8.strokeStyle = C5.outlineColor, t8.lineWidth = 2 * C5.outlineWidth, t8.lineJoin = "round", t8.strokeText("" + n8, 0, 0)), W5.axis.ticks.text.fill && (t8.fillStyle = C5.fill), t8.fillText("" + n8, 0, 0), t8.restore();
  }), void 0 !== p4) {
    var j3 = 0, z6 = 0, V2 = 0, D3 = "center";
    "y" === n7 ? (V2 = -90, j3 = S3, "start" === T4 ? (D3 = "start", z6 = f3) : "middle" === T4 ? (D3 = "center", z6 = f3 / 2) : "end" === T4 && (D3 = "end")) : (z6 = S3, "start" === T4 ? D3 = "start" : "middle" === T4 ? (D3 = "center", j3 = f3 / 2) : "end" === T4 && (D3 = "end", j3 = f3)), t8.translate(j3, z6), t8.rotate(Kt(V2)), t8.font = (W5.axis.legend.text.fontWeight ? W5.axis.legend.text.fontWeight + " " : "") + W5.axis.legend.text.fontSize + "px " + W5.axis.legend.text.fontFamily, W5.axis.legend.text.fill && (t8.fillStyle = W5.axis.legend.text.fill), t8.textAlign = D3, t8.textBaseline = "middle", t8.fillText(p4, 0, 0);
  }
  t8.restore();
};
var j2 = function(t8, e11) {
  var i6 = e11.xScale, n7 = e11.yScale, r7 = e11.width, a5 = e11.height, o5 = e11.top, l5 = e11.right, s5 = e11.bottom, c9 = e11.left, f3 = e11.theme, u6 = { top: o5, right: l5, bottom: s5, left: c9 };
  w2.forEach(function(e12) {
    var o6 = u6[e12];
    if (!o6) return null;
    var l6 = "top" === e12 || "bottom" === e12, s6 = "top" === e12 || "left" === e12 ? "before" : "after", c10 = l6 ? i6 : n7, d3 = P2(o6.format, c10);
    O3(t8, p2({}, o6, { axis: l6 ? "x" : "y", x: "right" === e12 ? r7 : 0, y: "bottom" === e12 ? a5 : 0, scale: c10, format: d3, length: l6 ? r7 : a5, ticksPosition: s6, theme: f3 }));
  });
};
var z3 = function(t8, e11) {
  var i6 = e11.width, n7 = e11.height, r7 = e11.scale, a5 = e11.axis, o5 = e11.values;
  T2({ width: i6, height: n7, scale: r7, axis: a5, values: o5 }).forEach(function(e12) {
    t8.beginPath(), t8.moveTo(e12.x1, e12.y1), t8.lineTo(e12.x2, e12.y2), t8.stroke();
  });
};

// node_modules/@nivo/legends/dist/nivo-legends.es.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var c8 = __toESM(require_react());
var import_react19 = __toESM(require_react());
var f2 = function(e11) {
  var i6 = e11.x, n7 = e11.y, o5 = e11.size, r7 = e11.fill, l5 = e11.opacity, a5 = void 0 === l5 ? 1 : l5, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s5 = e11.borderColor;
  return (0, import_jsx_runtime4.jsx)("circle", { r: o5 / 2, cx: i6 + o5 / 2, cy: n7 + o5 / 2, fill: r7, opacity: a5, strokeWidth: d3, stroke: void 0 === s5 ? "transparent" : s5, style: { pointerEvents: "none" } });
};
var m3 = function(e11) {
  var i6 = e11.x, n7 = e11.y, o5 = e11.size, r7 = e11.fill, l5 = e11.opacity, a5 = void 0 === l5 ? 1 : l5, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s5 = e11.borderColor;
  return (0, import_jsx_runtime4.jsx)("g", { transform: "translate(" + i6 + "," + n7 + ")", children: (0, import_jsx_runtime4.jsx)("path", { d: "\n                    M" + o5 / 2 + " 0\n                    L" + 0.8 * o5 + " " + o5 / 2 + "\n                    L" + o5 / 2 + " " + o5 + "\n                    L" + 0.2 * o5 + " " + o5 / 2 + "\n                    L" + o5 / 2 + " 0\n                ", fill: r7, opacity: a5, strokeWidth: d3, stroke: void 0 === s5 ? "transparent" : s5, style: { pointerEvents: "none" } }) });
};
var v4 = function(e11) {
  var i6 = e11.x, n7 = e11.y, o5 = e11.size, r7 = e11.fill, l5 = e11.opacity, a5 = void 0 === l5 ? 1 : l5, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s5 = e11.borderColor;
  return (0, import_jsx_runtime4.jsx)("rect", { x: i6, y: n7, fill: r7, opacity: a5, strokeWidth: d3, stroke: void 0 === s5 ? "transparent" : s5, width: o5, height: o5, style: { pointerEvents: "none" } });
};
var u2 = function(e11) {
  var i6 = e11.x, n7 = e11.y, o5 = e11.size, r7 = e11.fill, l5 = e11.opacity, a5 = void 0 === l5 ? 1 : l5, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s5 = e11.borderColor;
  return (0, import_jsx_runtime4.jsx)("g", { transform: "translate(" + i6 + "," + n7 + ")", children: (0, import_jsx_runtime4.jsx)("path", { d: "\n                M" + o5 / 2 + " 0\n                L" + o5 + " " + o5 + "\n                L0 " + o5 + "\n                L" + o5 / 2 + " 0\n            ", fill: r7, opacity: a5, strokeWidth: d3, stroke: void 0 === s5 ? "transparent" : s5, style: { pointerEvents: "none" } }) });
};
function p3() {
  return p3 = Object.assign ? Object.assign.bind() : function(t8) {
    for (var e11 = 1; e11 < arguments.length; e11++) {
      var i6 = arguments[e11];
      for (var n7 in i6) Object.prototype.hasOwnProperty.call(i6, n7) && (t8[n7] = i6[n7]);
    }
    return t8;
  }, p3.apply(this, arguments);
}
var k4 = { top: 0, right: 0, bottom: 0, left: 0 };
var x3 = function(t8) {
  var e11, i6 = t8.direction, n7 = t8.itemsSpacing, o5 = t8.padding, r7 = t8.itemCount, l5 = t8.itemWidth, a5 = t8.itemHeight;
  if ("number" != typeof o5 && ("object" != typeof (e11 = o5) || Array.isArray(e11) || null === e11)) throw new Error("Invalid property padding, must be one of: number, object");
  var c9 = "number" == typeof o5 ? { top: o5, right: o5, bottom: o5, left: o5 } : p3({}, k4, o5), d3 = c9.left + c9.right, s5 = c9.top + c9.bottom, h2 = l5 + d3, g4 = a5 + s5, f3 = (r7 - 1) * n7;
  return "row" === i6 ? h2 = l5 * r7 + f3 + d3 : "column" === i6 && (g4 = a5 * r7 + f3 + s5), { width: h2, height: g4, padding: c9 };
};
var b3 = function(t8) {
  var e11 = t8.anchor, i6 = t8.translateX, n7 = t8.translateY, o5 = t8.containerWidth, r7 = t8.containerHeight, l5 = t8.width, a5 = t8.height, c9 = i6, d3 = n7;
  switch (e11) {
    case "top":
      c9 += (o5 - l5) / 2;
      break;
    case "top-right":
      c9 += o5 - l5;
      break;
    case "right":
      c9 += o5 - l5, d3 += (r7 - a5) / 2;
      break;
    case "bottom-right":
      c9 += o5 - l5, d3 += r7 - a5;
      break;
    case "bottom":
      c9 += (o5 - l5) / 2, d3 += r7 - a5;
      break;
    case "bottom-left":
      d3 += r7 - a5;
      break;
    case "left":
      d3 += (r7 - a5) / 2;
      break;
    case "center":
      c9 += (o5 - l5) / 2, d3 += (r7 - a5) / 2;
  }
  return { x: c9, y: d3 };
};
var S2 = function(t8) {
  var e11, i6, n7, o5, r7, l5, a5 = t8.direction, c9 = t8.justify, d3 = t8.symbolSize, s5 = t8.symbolSpacing, h2 = t8.width, g4 = t8.height;
  switch (a5) {
    case "left-to-right":
      e11 = 0, i6 = (g4 - d3) / 2, o5 = g4 / 2, l5 = "central", c9 ? (n7 = h2, r7 = "end") : (n7 = d3 + s5, r7 = "start");
      break;
    case "right-to-left":
      e11 = h2 - d3, i6 = (g4 - d3) / 2, o5 = g4 / 2, l5 = "central", c9 ? (n7 = 0, r7 = "start") : (n7 = h2 - d3 - s5, r7 = "end");
      break;
    case "top-to-bottom":
      e11 = (h2 - d3) / 2, i6 = 0, n7 = h2 / 2, r7 = "middle", c9 ? (o5 = g4, l5 = "alphabetic") : (o5 = d3 + s5, l5 = "text-before-edge");
      break;
    case "bottom-to-top":
      e11 = (h2 - d3) / 2, i6 = g4 - d3, n7 = h2 / 2, r7 = "middle", c9 ? (o5 = 0, l5 = "text-before-edge") : (o5 = g4 - d3 - s5, l5 = "alphabetic");
  }
  return { symbolX: e11, symbolY: i6, labelX: n7, labelY: o5, labelAnchor: r7, labelAlignment: l5 };
};
var w3 = { circle: f2, diamond: m3, square: v4, triangle: u2 };
var X3 = function(i6) {
  var n7, l5, a5, d3, g4, f3, m5, v6, u6, y4, k5, x4 = i6.x, b5 = i6.y, A3 = i6.width, W5 = i6.height, z6 = i6.data, C5 = i6.direction, X5 = void 0 === C5 ? "left-to-right" : C5, Y4 = i6.justify, O5 = void 0 !== Y4 && Y4, B4 = i6.textColor, H4 = i6.background, E3 = void 0 === H4 ? "transparent" : H4, j3 = i6.opacity, L3 = void 0 === j3 ? 1 : j3, M3 = i6.symbolShape, F2 = void 0 === M3 ? "square" : M3, T4 = i6.symbolSize, P4 = void 0 === T4 ? 16 : T4, V2 = i6.symbolSpacing, R = void 0 === V2 ? 8 : V2, D3 = i6.symbolBorderWidth, q2 = void 0 === D3 ? 0 : D3, G = i6.symbolBorderColor, I2 = void 0 === G ? "transparent" : G, N2 = i6.onClick, _3 = i6.onMouseEnter, J2 = i6.onMouseLeave, K2 = i6.toggleSerie, Q3 = i6.effects, U2 = (0, import_react19.useState)({}), Z2 = U2[0], $3 = U2[1], tt2 = zt(), et2 = (0, import_react19.useCallback)(function(t8) {
    if (Q3) {
      var e11 = Q3.filter(function(t9) {
        return "hover" === t9.on;
      }).reduce(function(t9, e12) {
        return p3({}, t9, e12.style);
      }, {});
      $3(e11);
    }
    null == _3 || _3(z6, t8);
  }, [_3, z6, Q3]), it2 = (0, import_react19.useCallback)(function(t8) {
    if (Q3) {
      var e11 = Q3.filter(function(t9) {
        return "hover" !== t9.on;
      }).reduce(function(t9, e12) {
        return p3({}, t9, e12.style);
      }, {});
      $3(e11);
    }
    null == J2 || J2(z6, t8);
  }, [J2, z6, Q3]), nt2 = S2({ direction: X5, justify: O5, symbolSize: null != (n7 = Z2.symbolSize) ? n7 : P4, symbolSpacing: R, width: A3, height: W5 }), ot2 = nt2.symbolX, rt2 = nt2.symbolY, lt2 = nt2.labelX, at2 = nt2.labelY, ct2 = nt2.labelAnchor, dt2 = nt2.labelAlignment, st = [N2, _3, J2, K2].some(function(t8) {
    return void 0 !== t8;
  }), ht2 = "function" == typeof F2 ? F2 : w3[F2];
  return (0, import_jsx_runtime4.jsxs)("g", { transform: "translate(" + x4 + "," + b5 + ")", style: { opacity: null != (l5 = Z2.itemOpacity) ? l5 : L3 }, children: [(0, import_jsx_runtime4.jsx)("rect", { width: A3, height: W5, fill: null != (a5 = Z2.itemBackground) ? a5 : E3, style: { cursor: st ? "pointer" : "auto" }, onClick: function(t8) {
    null == N2 || N2(z6, t8), null == K2 || K2(z6.id);
  }, onMouseEnter: et2, onMouseLeave: it2 }), c8.createElement(ht2, p3({ id: z6.id, x: ot2, y: rt2, size: null != (d3 = Z2.symbolSize) ? d3 : P4, fill: null != (g4 = null != (f3 = z6.fill) ? f3 : z6.color) ? g4 : "black", borderWidth: null != (m5 = Z2.symbolBorderWidth) ? m5 : q2, borderColor: null != (v6 = Z2.symbolBorderColor) ? v6 : I2 }, z6.hidden ? tt2.legends.hidden.symbol : void 0)), (0, import_jsx_runtime4.jsx)("text", { textAnchor: ct2, style: p3({}, Mt(tt2.legends.text), { fill: null != (u6 = null != (y4 = null != (k5 = Z2.itemTextColor) ? k5 : B4) ? y4 : tt2.legends.text.fill) ? u6 : "black", dominantBaseline: dt2, pointerEvents: "none", userSelect: "none" }, z6.hidden ? tt2.legends.hidden.text : void 0), x: lt2, y: at2, children: z6.label })] });
};
var Y3 = function(e11) {
  var i6 = e11.data, n7 = e11.x, o5 = e11.y, r7 = e11.direction, l5 = e11.padding, a5 = void 0 === l5 ? 0 : l5, c9 = e11.justify, d3 = e11.effects, s5 = e11.itemWidth, h2 = e11.itemHeight, g4 = e11.itemDirection, f3 = void 0 === g4 ? "left-to-right" : g4, m5 = e11.itemsSpacing, v6 = void 0 === m5 ? 0 : m5, u6 = e11.itemTextColor, p4 = e11.itemBackground, y4 = void 0 === p4 ? "transparent" : p4, k5 = e11.itemOpacity, b5 = void 0 === k5 ? 1 : k5, S3 = e11.symbolShape, A3 = e11.symbolSize, W5 = e11.symbolSpacing, z6 = e11.symbolBorderWidth, C5 = e11.symbolBorderColor, w5 = e11.onClick, Y4 = e11.onMouseEnter, O5 = e11.onMouseLeave, B4 = e11.toggleSerie, H4 = x3({ itemCount: i6.length, itemWidth: s5, itemHeight: h2, itemsSpacing: v6, direction: r7, padding: a5 }).padding, E3 = "row" === r7 ? s5 + v6 : 0, j3 = "column" === r7 ? h2 + v6 : 0;
  return (0, import_jsx_runtime4.jsx)("g", { transform: "translate(" + n7 + "," + o5 + ")", children: i6.map(function(e12, i7) {
    return (0, import_jsx_runtime4.jsx)(X3, { data: e12, x: i7 * E3 + H4.left, y: i7 * j3 + H4.top, width: s5, height: h2, direction: f3, justify: c9, effects: d3, textColor: u6, background: y4, opacity: b5, symbolShape: S3, symbolSize: A3, symbolSpacing: W5, symbolBorderWidth: z6, symbolBorderColor: C5, onClick: w5, onMouseEnter: Y4, onMouseLeave: O5, toggleSerie: B4 }, i7);
  }) });
};
var O4 = function(e11) {
  var i6 = e11.data, n7 = e11.containerWidth, o5 = e11.containerHeight, r7 = e11.translateX, l5 = void 0 === r7 ? 0 : r7, a5 = e11.translateY, c9 = void 0 === a5 ? 0 : a5, d3 = e11.anchor, s5 = e11.direction, h2 = e11.padding, g4 = void 0 === h2 ? 0 : h2, f3 = e11.justify, m5 = e11.itemsSpacing, v6 = void 0 === m5 ? 0 : m5, u6 = e11.itemWidth, p4 = e11.itemHeight, y4 = e11.itemDirection, k5 = e11.itemTextColor, S3 = e11.itemBackground, A3 = e11.itemOpacity, W5 = e11.symbolShape, z6 = e11.symbolSize, C5 = e11.symbolSpacing, w5 = e11.symbolBorderWidth, X5 = e11.symbolBorderColor, O5 = e11.onClick, B4 = e11.onMouseEnter, H4 = e11.onMouseLeave, E3 = e11.toggleSerie, j3 = e11.effects, L3 = x3({ itemCount: i6.length, itemsSpacing: v6, itemWidth: u6, itemHeight: p4, direction: s5, padding: g4 }), M3 = L3.width, F2 = L3.height, T4 = b3({ anchor: d3, translateX: l5, translateY: c9, containerWidth: n7, containerHeight: o5, width: M3, height: F2 }), P4 = T4.x, V2 = T4.y;
  return (0, import_jsx_runtime4.jsx)(Y3, { data: i6, x: P4, y: V2, direction: s5, padding: g4, justify: f3, effects: j3, itemsSpacing: v6, itemWidth: u6, itemHeight: p4, itemDirection: y4, itemTextColor: k5, itemBackground: S3, itemOpacity: A3, symbolShape: W5, symbolSize: z6, symbolSpacing: C5, symbolBorderWidth: w5, symbolBorderColor: X5, onClick: O5, onMouseEnter: B4, onMouseLeave: H4, toggleSerie: "boolean" == typeof E3 ? void 0 : E3 });
};
var B2 = { start: "left", middle: "center", end: "right" };
var H = function(t8, e11) {
  var i6 = e11.data, n7 = e11.containerWidth, o5 = e11.containerHeight, r7 = e11.translateX, l5 = void 0 === r7 ? 0 : r7, a5 = e11.translateY, c9 = void 0 === a5 ? 0 : a5, d3 = e11.anchor, s5 = e11.direction, h2 = e11.padding, g4 = void 0 === h2 ? 0 : h2, f3 = e11.justify, m5 = void 0 !== f3 && f3, v6 = e11.itemsSpacing, u6 = void 0 === v6 ? 0 : v6, p4 = e11.itemWidth, y4 = e11.itemHeight, k5 = e11.itemDirection, A3 = void 0 === k5 ? "left-to-right" : k5, W5 = e11.itemTextColor, z6 = e11.symbolSize, C5 = void 0 === z6 ? 16 : z6, w5 = e11.symbolSpacing, X5 = void 0 === w5 ? 8 : w5, Y4 = e11.theme, O5 = x3({ itemCount: i6.length, itemWidth: p4, itemHeight: y4, itemsSpacing: u6, direction: s5, padding: g4 }), H4 = O5.width, E3 = O5.height, j3 = O5.padding, L3 = b3({ anchor: d3, translateX: l5, translateY: c9, containerWidth: n7, containerHeight: o5, width: H4, height: E3 }), M3 = L3.x, F2 = L3.y, T4 = "row" === s5 ? p4 + u6 : 0, P4 = "column" === s5 ? y4 + u6 : 0;
  t8.save(), t8.translate(M3, F2), t8.font = Y4.legends.text.fontSize + "px " + (Y4.legends.text.fontFamily || "sans-serif"), i6.forEach(function(e12, i7) {
    var n8, o6, r8 = i7 * T4 + j3.left, l6 = i7 * P4 + j3.top, a6 = S2({ direction: A3, justify: m5, symbolSize: C5, symbolSpacing: X5, width: p4, height: y4 }), c10 = a6.symbolX, d4 = a6.symbolY, s6 = a6.labelX, h3 = a6.labelY, g5 = a6.labelAnchor, f4 = a6.labelAlignment;
    t8.fillStyle = null != (n8 = e12.color) ? n8 : "black", t8.fillRect(r8 + c10, l6 + d4, C5, C5), t8.textAlign = B2[g5], "central" === f4 && (t8.textBaseline = "middle"), t8.fillStyle = null != (o6 = null != W5 ? W5 : Y4.legends.text.fill) ? o6 : "black", t8.fillText(String(e12.label), r8 + s6, l6 + h3);
  }), t8.restore();
};

// node_modules/@nivo/line/dist/nivo-line.es.js
var import_uniqueId = __toESM(require_uniqueId());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());

// node_modules/@nivo/voronoi/dist/nivo-voronoi.es.js
var import_react20 = __toESM(require_react());

// node_modules/robust-predicates/esm/util.js
var epsilon3 = 11102230246251565e-32;
var splitter = 134217729;
var resulterrbound = (3 + 8 * epsilon3) * epsilon3;
function sum4(elen, e11, flen, f3, h2) {
  let Q3, Qnew, hh, bvirt;
  let enow = e11[0];
  let fnow = f3[0];
  let eindex = 0;
  let findex = 0;
  if (fnow > enow === fnow > -enow) {
    Q3 = enow;
    enow = e11[++eindex];
  } else {
    Q3 = fnow;
    fnow = f3[++findex];
  }
  let hindex = 0;
  if (eindex < elen && findex < flen) {
    if (fnow > enow === fnow > -enow) {
      Qnew = enow + Q3;
      hh = Q3 - (Qnew - enow);
      enow = e11[++eindex];
    } else {
      Qnew = fnow + Q3;
      hh = Q3 - (Qnew - fnow);
      fnow = f3[++findex];
    }
    Q3 = Qnew;
    if (hh !== 0) {
      h2[hindex++] = hh;
    }
    while (eindex < elen && findex < flen) {
      if (fnow > enow === fnow > -enow) {
        Qnew = Q3 + enow;
        bvirt = Qnew - Q3;
        hh = Q3 - (Qnew - bvirt) + (enow - bvirt);
        enow = e11[++eindex];
      } else {
        Qnew = Q3 + fnow;
        bvirt = Qnew - Q3;
        hh = Q3 - (Qnew - bvirt) + (fnow - bvirt);
        fnow = f3[++findex];
      }
      Q3 = Qnew;
      if (hh !== 0) {
        h2[hindex++] = hh;
      }
    }
  }
  while (eindex < elen) {
    Qnew = Q3 + enow;
    bvirt = Qnew - Q3;
    hh = Q3 - (Qnew - bvirt) + (enow - bvirt);
    enow = e11[++eindex];
    Q3 = Qnew;
    if (hh !== 0) {
      h2[hindex++] = hh;
    }
  }
  while (findex < flen) {
    Qnew = Q3 + fnow;
    bvirt = Qnew - Q3;
    hh = Q3 - (Qnew - bvirt) + (fnow - bvirt);
    fnow = f3[++findex];
    Q3 = Qnew;
    if (hh !== 0) {
      h2[hindex++] = hh;
    }
  }
  if (Q3 !== 0 || hindex === 0) {
    h2[hindex++] = Q3;
  }
  return hindex;
}
function estimate(elen, e11) {
  let Q3 = e11[0];
  for (let i6 = 1; i6 < elen; i6++) Q3 += e11[i6];
  return Q3;
}
function vec(n7) {
  return new Float64Array(n7);
}

// node_modules/robust-predicates/esm/orient2d.js
var ccwerrboundA = (3 + 16 * epsilon3) * epsilon3;
var ccwerrboundB = (2 + 12 * epsilon3) * epsilon3;
var ccwerrboundC = (9 + 64 * epsilon3) * epsilon3 * epsilon3;
var B3 = vec(4);
var C1 = vec(8);
var C22 = vec(12);
var D = vec(16);
var u3 = vec(4);
function orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {
  let acxtail, acytail, bcxtail, bcytail;
  let bvirt, c9, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t14, t04, u32;
  const acx = ax - cx;
  const bcx = bx - cx;
  const acy = ay - cy;
  const bcy = by - cy;
  s1 = acx * bcy;
  c9 = splitter * acx;
  ahi = c9 - (c9 - acx);
  alo = acx - ahi;
  c9 = splitter * bcy;
  bhi = c9 - (c9 - bcy);
  blo = bcy - bhi;
  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);
  t14 = acy * bcx;
  c9 = splitter * acy;
  ahi = c9 - (c9 - acy);
  alo = acy - ahi;
  c9 = splitter * bcx;
  bhi = c9 - (c9 - bcx);
  blo = bcx - bhi;
  t04 = alo * blo - (t14 - ahi * bhi - alo * bhi - ahi * blo);
  _i = s0 - t04;
  bvirt = s0 - _i;
  B3[0] = s0 - (_i + bvirt) + (bvirt - t04);
  _j = s1 + _i;
  bvirt = _j - s1;
  _0 = s1 - (_j - bvirt) + (_i - bvirt);
  _i = _0 - t14;
  bvirt = _0 - _i;
  B3[1] = _0 - (_i + bvirt) + (bvirt - t14);
  u32 = _j + _i;
  bvirt = u32 - _j;
  B3[2] = _j - (u32 - bvirt) + (_i - bvirt);
  B3[3] = u32;
  let det = estimate(4, B3);
  let errbound = ccwerrboundB * detsum;
  if (det >= errbound || -det >= errbound) {
    return det;
  }
  bvirt = ax - acx;
  acxtail = ax - (acx + bvirt) + (bvirt - cx);
  bvirt = bx - bcx;
  bcxtail = bx - (bcx + bvirt) + (bvirt - cx);
  bvirt = ay - acy;
  acytail = ay - (acy + bvirt) + (bvirt - cy);
  bvirt = by - bcy;
  bcytail = by - (bcy + bvirt) + (bvirt - cy);
  if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {
    return det;
  }
  errbound = ccwerrboundC * detsum + resulterrbound * Math.abs(det);
  det += acx * bcytail + bcy * acxtail - (acy * bcxtail + bcx * acytail);
  if (det >= errbound || -det >= errbound) return det;
  s1 = acxtail * bcy;
  c9 = splitter * acxtail;
  ahi = c9 - (c9 - acxtail);
  alo = acxtail - ahi;
  c9 = splitter * bcy;
  bhi = c9 - (c9 - bcy);
  blo = bcy - bhi;
  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);
  t14 = acytail * bcx;
  c9 = splitter * acytail;
  ahi = c9 - (c9 - acytail);
  alo = acytail - ahi;
  c9 = splitter * bcx;
  bhi = c9 - (c9 - bcx);
  blo = bcx - bhi;
  t04 = alo * blo - (t14 - ahi * bhi - alo * bhi - ahi * blo);
  _i = s0 - t04;
  bvirt = s0 - _i;
  u3[0] = s0 - (_i + bvirt) + (bvirt - t04);
  _j = s1 + _i;
  bvirt = _j - s1;
  _0 = s1 - (_j - bvirt) + (_i - bvirt);
  _i = _0 - t14;
  bvirt = _0 - _i;
  u3[1] = _0 - (_i + bvirt) + (bvirt - t14);
  u32 = _j + _i;
  bvirt = u32 - _j;
  u3[2] = _j - (u32 - bvirt) + (_i - bvirt);
  u3[3] = u32;
  const C1len = sum4(4, B3, 4, u3, C1);
  s1 = acx * bcytail;
  c9 = splitter * acx;
  ahi = c9 - (c9 - acx);
  alo = acx - ahi;
  c9 = splitter * bcytail;
  bhi = c9 - (c9 - bcytail);
  blo = bcytail - bhi;
  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);
  t14 = acy * bcxtail;
  c9 = splitter * acy;
  ahi = c9 - (c9 - acy);
  alo = acy - ahi;
  c9 = splitter * bcxtail;
  bhi = c9 - (c9 - bcxtail);
  blo = bcxtail - bhi;
  t04 = alo * blo - (t14 - ahi * bhi - alo * bhi - ahi * blo);
  _i = s0 - t04;
  bvirt = s0 - _i;
  u3[0] = s0 - (_i + bvirt) + (bvirt - t04);
  _j = s1 + _i;
  bvirt = _j - s1;
  _0 = s1 - (_j - bvirt) + (_i - bvirt);
  _i = _0 - t14;
  bvirt = _0 - _i;
  u3[1] = _0 - (_i + bvirt) + (bvirt - t14);
  u32 = _j + _i;
  bvirt = u32 - _j;
  u3[2] = _j - (u32 - bvirt) + (_i - bvirt);
  u3[3] = u32;
  const C2len = sum4(C1len, C1, 4, u3, C22);
  s1 = acxtail * bcytail;
  c9 = splitter * acxtail;
  ahi = c9 - (c9 - acxtail);
  alo = acxtail - ahi;
  c9 = splitter * bcytail;
  bhi = c9 - (c9 - bcytail);
  blo = bcytail - bhi;
  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);
  t14 = acytail * bcxtail;
  c9 = splitter * acytail;
  ahi = c9 - (c9 - acytail);
  alo = acytail - ahi;
  c9 = splitter * bcxtail;
  bhi = c9 - (c9 - bcxtail);
  blo = bcxtail - bhi;
  t04 = alo * blo - (t14 - ahi * bhi - alo * bhi - ahi * blo);
  _i = s0 - t04;
  bvirt = s0 - _i;
  u3[0] = s0 - (_i + bvirt) + (bvirt - t04);
  _j = s1 + _i;
  bvirt = _j - s1;
  _0 = s1 - (_j - bvirt) + (_i - bvirt);
  _i = _0 - t14;
  bvirt = _0 - _i;
  u3[1] = _0 - (_i + bvirt) + (bvirt - t14);
  u32 = _j + _i;
  bvirt = u32 - _j;
  u3[2] = _j - (u32 - bvirt) + (_i - bvirt);
  u3[3] = u32;
  const Dlen = sum4(C2len, C22, 4, u3, D);
  return D[Dlen - 1];
}
function orient2d(ax, ay, bx, by, cx, cy) {
  const detleft = (ay - cy) * (bx - cx);
  const detright = (ax - cx) * (by - cy);
  const det = detleft - detright;
  const detsum = Math.abs(detleft + detright);
  if (Math.abs(det) >= ccwerrboundA * detsum) return det;
  return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);
}

// node_modules/robust-predicates/esm/orient3d.js
var o3derrboundA = (7 + 56 * epsilon3) * epsilon3;
var o3derrboundB = (3 + 28 * epsilon3) * epsilon3;
var o3derrboundC = (26 + 288 * epsilon3) * epsilon3 * epsilon3;
var bc = vec(4);
var ca = vec(4);
var ab = vec(4);
var at_b = vec(4);
var at_c = vec(4);
var bt_c = vec(4);
var bt_a = vec(4);
var ct_a = vec(4);
var ct_b = vec(4);
var bct = vec(8);
var cat = vec(8);
var abt = vec(8);
var u4 = vec(4);
var _8 = vec(8);
var _8b = vec(8);
var _16 = vec(8);
var _12 = vec(12);
var fin = vec(192);
var fin2 = vec(192);

// node_modules/robust-predicates/esm/incircle.js
var iccerrboundA = (10 + 96 * epsilon3) * epsilon3;
var iccerrboundB = (4 + 48 * epsilon3) * epsilon3;
var iccerrboundC = (44 + 576 * epsilon3) * epsilon3 * epsilon3;
var bc2 = vec(4);
var ca2 = vec(4);
var ab2 = vec(4);
var aa = vec(4);
var bb = vec(4);
var cc = vec(4);
var u5 = vec(4);
var v5 = vec(4);
var axtbc = vec(8);
var aytbc = vec(8);
var bxtca = vec(8);
var bytca = vec(8);
var cxtab = vec(8);
var cytab = vec(8);
var abt2 = vec(8);
var bct2 = vec(8);
var cat2 = vec(8);
var abtt = vec(4);
var bctt = vec(4);
var catt = vec(4);
var _82 = vec(8);
var _162 = vec(16);
var _16b = vec(16);
var _16c = vec(16);
var _32 = vec(32);
var _32b = vec(32);
var _48 = vec(48);
var _64 = vec(64);
var fin3 = vec(1152);
var fin22 = vec(1152);

// node_modules/robust-predicates/esm/insphere.js
var isperrboundA = (16 + 224 * epsilon3) * epsilon3;
var isperrboundB = (5 + 72 * epsilon3) * epsilon3;
var isperrboundC = (71 + 1408 * epsilon3) * epsilon3 * epsilon3;
var ab3 = vec(4);
var bc3 = vec(4);
var cd = vec(4);
var de = vec(4);
var ea = vec(4);
var ac = vec(4);
var bd = vec(4);
var ce = vec(4);
var da = vec(4);
var eb = vec(4);
var abc = vec(24);
var bcd = vec(24);
var cde = vec(24);
var dea = vec(24);
var eab = vec(24);
var abd = vec(24);
var bce = vec(24);
var cda = vec(24);
var deb = vec(24);
var eac = vec(24);
var adet = vec(1152);
var bdet = vec(1152);
var cdet = vec(1152);
var ddet = vec(1152);
var edet = vec(1152);
var abdet = vec(2304);
var cddet = vec(2304);
var cdedet = vec(3456);
var deter = vec(5760);
var _83 = vec(8);
var _8b2 = vec(8);
var _8c = vec(8);
var _163 = vec(16);
var _24 = vec(24);
var _482 = vec(48);
var _48b = vec(48);
var _96 = vec(96);
var _192 = vec(192);
var _384x = vec(384);
var _384y = vec(384);
var _384z = vec(384);
var _768 = vec(768);
var xdet = vec(96);
var ydet = vec(96);
var zdet = vec(96);
var fin4 = vec(1152);

// node_modules/delaunator/index.js
var EPSILON = Math.pow(2, -52);
var EDGE_STACK = new Uint32Array(512);
var Delaunator = class _Delaunator {
  static from(points, getX = defaultGetX, getY = defaultGetY) {
    const n7 = points.length;
    const coords = new Float64Array(n7 * 2);
    for (let i6 = 0; i6 < n7; i6++) {
      const p4 = points[i6];
      coords[2 * i6] = getX(p4);
      coords[2 * i6 + 1] = getY(p4);
    }
    return new _Delaunator(coords);
  }
  constructor(coords) {
    const n7 = coords.length >> 1;
    if (n7 > 0 && typeof coords[0] !== "number") throw new Error("Expected coords to contain numbers.");
    this.coords = coords;
    const maxTriangles = Math.max(2 * n7 - 5, 0);
    this._triangles = new Uint32Array(maxTriangles * 3);
    this._halfedges = new Int32Array(maxTriangles * 3);
    this._hashSize = Math.ceil(Math.sqrt(n7));
    this._hullPrev = new Uint32Array(n7);
    this._hullNext = new Uint32Array(n7);
    this._hullTri = new Uint32Array(n7);
    this._hullHash = new Int32Array(this._hashSize);
    this._ids = new Uint32Array(n7);
    this._dists = new Float64Array(n7);
    this.update();
  }
  update() {
    const { coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash } = this;
    const n7 = coords.length >> 1;
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
    for (let i6 = 0; i6 < n7; i6++) {
      const x4 = coords[2 * i6];
      const y4 = coords[2 * i6 + 1];
      if (x4 < minX) minX = x4;
      if (y4 < minY) minY = y4;
      if (x4 > maxX) maxX = x4;
      if (y4 > maxY) maxY = y4;
      this._ids[i6] = i6;
    }
    const cx = (minX + maxX) / 2;
    const cy = (minY + maxY) / 2;
    let i0, i1, i22;
    for (let i6 = 0, minDist = Infinity; i6 < n7; i6++) {
      const d3 = dist(cx, cy, coords[2 * i6], coords[2 * i6 + 1]);
      if (d3 < minDist) {
        i0 = i6;
        minDist = d3;
      }
    }
    const i0x = coords[2 * i0];
    const i0y = coords[2 * i0 + 1];
    for (let i6 = 0, minDist = Infinity; i6 < n7; i6++) {
      if (i6 === i0) continue;
      const d3 = dist(i0x, i0y, coords[2 * i6], coords[2 * i6 + 1]);
      if (d3 < minDist && d3 > 0) {
        i1 = i6;
        minDist = d3;
      }
    }
    let i1x = coords[2 * i1];
    let i1y = coords[2 * i1 + 1];
    let minRadius = Infinity;
    for (let i6 = 0; i6 < n7; i6++) {
      if (i6 === i0 || i6 === i1) continue;
      const r7 = circumradius(i0x, i0y, i1x, i1y, coords[2 * i6], coords[2 * i6 + 1]);
      if (r7 < minRadius) {
        i22 = i6;
        minRadius = r7;
      }
    }
    let i2x = coords[2 * i22];
    let i2y = coords[2 * i22 + 1];
    if (minRadius === Infinity) {
      for (let i6 = 0; i6 < n7; i6++) {
        this._dists[i6] = coords[2 * i6] - coords[0] || coords[2 * i6 + 1] - coords[1];
      }
      quicksort(this._ids, this._dists, 0, n7 - 1);
      const hull = new Uint32Array(n7);
      let j3 = 0;
      for (let i6 = 0, d0 = -Infinity; i6 < n7; i6++) {
        const id = this._ids[i6];
        const d3 = this._dists[id];
        if (d3 > d0) {
          hull[j3++] = id;
          d0 = d3;
        }
      }
      this.hull = hull.subarray(0, j3);
      this.triangles = new Uint32Array(0);
      this.halfedges = new Uint32Array(0);
      return;
    }
    if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {
      const i6 = i1;
      const x4 = i1x;
      const y4 = i1y;
      i1 = i22;
      i1x = i2x;
      i1y = i2y;
      i22 = i6;
      i2x = x4;
      i2y = y4;
    }
    const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);
    this._cx = center.x;
    this._cy = center.y;
    for (let i6 = 0; i6 < n7; i6++) {
      this._dists[i6] = dist(coords[2 * i6], coords[2 * i6 + 1], center.x, center.y);
    }
    quicksort(this._ids, this._dists, 0, n7 - 1);
    this._hullStart = i0;
    let hullSize = 3;
    hullNext[i0] = hullPrev[i22] = i1;
    hullNext[i1] = hullPrev[i0] = i22;
    hullNext[i22] = hullPrev[i1] = i0;
    hullTri[i0] = 0;
    hullTri[i1] = 1;
    hullTri[i22] = 2;
    hullHash.fill(-1);
    hullHash[this._hashKey(i0x, i0y)] = i0;
    hullHash[this._hashKey(i1x, i1y)] = i1;
    hullHash[this._hashKey(i2x, i2y)] = i22;
    this.trianglesLen = 0;
    this._addTriangle(i0, i1, i22, -1, -1, -1);
    for (let k5 = 0, xp, yp; k5 < this._ids.length; k5++) {
      const i6 = this._ids[k5];
      const x4 = coords[2 * i6];
      const y4 = coords[2 * i6 + 1];
      if (k5 > 0 && Math.abs(x4 - xp) <= EPSILON && Math.abs(y4 - yp) <= EPSILON) continue;
      xp = x4;
      yp = y4;
      if (i6 === i0 || i6 === i1 || i6 === i22) continue;
      let start2 = 0;
      for (let j3 = 0, key = this._hashKey(x4, y4); j3 < this._hashSize; j3++) {
        start2 = hullHash[(key + j3) % this._hashSize];
        if (start2 !== -1 && start2 !== hullNext[start2]) break;
      }
      start2 = hullPrev[start2];
      let e11 = start2, q2;
      while (q2 = hullNext[e11], orient2d(x4, y4, coords[2 * e11], coords[2 * e11 + 1], coords[2 * q2], coords[2 * q2 + 1]) >= 0) {
        e11 = q2;
        if (e11 === start2) {
          e11 = -1;
          break;
        }
      }
      if (e11 === -1) continue;
      let t8 = this._addTriangle(e11, i6, hullNext[e11], -1, -1, hullTri[e11]);
      hullTri[i6] = this._legalize(t8 + 2);
      hullTri[e11] = t8;
      hullSize++;
      let n8 = hullNext[e11];
      while (q2 = hullNext[n8], orient2d(x4, y4, coords[2 * n8], coords[2 * n8 + 1], coords[2 * q2], coords[2 * q2 + 1]) < 0) {
        t8 = this._addTriangle(n8, i6, q2, hullTri[i6], -1, hullTri[n8]);
        hullTri[i6] = this._legalize(t8 + 2);
        hullNext[n8] = n8;
        hullSize--;
        n8 = q2;
      }
      if (e11 === start2) {
        while (q2 = hullPrev[e11], orient2d(x4, y4, coords[2 * q2], coords[2 * q2 + 1], coords[2 * e11], coords[2 * e11 + 1]) < 0) {
          t8 = this._addTriangle(q2, i6, e11, -1, hullTri[e11], hullTri[q2]);
          this._legalize(t8 + 2);
          hullTri[q2] = t8;
          hullNext[e11] = e11;
          hullSize--;
          e11 = q2;
        }
      }
      this._hullStart = hullPrev[i6] = e11;
      hullNext[e11] = hullPrev[n8] = i6;
      hullNext[i6] = n8;
      hullHash[this._hashKey(x4, y4)] = i6;
      hullHash[this._hashKey(coords[2 * e11], coords[2 * e11 + 1])] = e11;
    }
    this.hull = new Uint32Array(hullSize);
    for (let i6 = 0, e11 = this._hullStart; i6 < hullSize; i6++) {
      this.hull[i6] = e11;
      e11 = hullNext[e11];
    }
    this.triangles = this._triangles.subarray(0, this.trianglesLen);
    this.halfedges = this._halfedges.subarray(0, this.trianglesLen);
  }
  _hashKey(x4, y4) {
    return Math.floor(pseudoAngle(x4 - this._cx, y4 - this._cy) * this._hashSize) % this._hashSize;
  }
  _legalize(a5) {
    const { _triangles: triangles, _halfedges: halfedges, coords } = this;
    let i6 = 0;
    let ar = 0;
    while (true) {
      const b5 = halfedges[a5];
      const a0 = a5 - a5 % 3;
      ar = a0 + (a5 + 2) % 3;
      if (b5 === -1) {
        if (i6 === 0) break;
        a5 = EDGE_STACK[--i6];
        continue;
      }
      const b0 = b5 - b5 % 3;
      const al = a0 + (a5 + 1) % 3;
      const bl = b0 + (b5 + 2) % 3;
      const p0 = triangles[ar];
      const pr2 = triangles[a5];
      const pl = triangles[al];
      const p1 = triangles[bl];
      const illegal = inCircle(
        coords[2 * p0],
        coords[2 * p0 + 1],
        coords[2 * pr2],
        coords[2 * pr2 + 1],
        coords[2 * pl],
        coords[2 * pl + 1],
        coords[2 * p1],
        coords[2 * p1 + 1]
      );
      if (illegal) {
        triangles[a5] = p1;
        triangles[b5] = p0;
        const hbl = halfedges[bl];
        if (hbl === -1) {
          let e11 = this._hullStart;
          do {
            if (this._hullTri[e11] === bl) {
              this._hullTri[e11] = a5;
              break;
            }
            e11 = this._hullPrev[e11];
          } while (e11 !== this._hullStart);
        }
        this._link(a5, hbl);
        this._link(b5, halfedges[ar]);
        this._link(ar, bl);
        const br = b0 + (b5 + 1) % 3;
        if (i6 < EDGE_STACK.length) {
          EDGE_STACK[i6++] = br;
        }
      } else {
        if (i6 === 0) break;
        a5 = EDGE_STACK[--i6];
      }
    }
    return ar;
  }
  _link(a5, b5) {
    this._halfedges[a5] = b5;
    if (b5 !== -1) this._halfedges[b5] = a5;
  }
  // add a new triangle given vertex indices and adjacent half-edge ids
  _addTriangle(i0, i1, i22, a5, b5, c9) {
    const t8 = this.trianglesLen;
    this._triangles[t8] = i0;
    this._triangles[t8 + 1] = i1;
    this._triangles[t8 + 2] = i22;
    this._link(t8, a5);
    this._link(t8 + 1, b5);
    this._link(t8 + 2, c9);
    this.trianglesLen += 3;
    return t8;
  }
};
function pseudoAngle(dx, dy) {
  const p4 = dx / (Math.abs(dx) + Math.abs(dy));
  return (dy > 0 ? 3 - p4 : 1 + p4) / 4;
}
function dist(ax, ay, bx, by) {
  const dx = ax - bx;
  const dy = ay - by;
  return dx * dx + dy * dy;
}
function inCircle(ax, ay, bx, by, cx, cy, px, py) {
  const dx = ax - px;
  const dy = ay - py;
  const ex = bx - px;
  const ey = by - py;
  const fx = cx - px;
  const fy = cy - py;
  const ap = dx * dx + dy * dy;
  const bp = ex * ex + ey * ey;
  const cp = fx * fx + fy * fy;
  return dx * (ey * cp - bp * fy) - dy * (ex * cp - bp * fx) + ap * (ex * fy - ey * fx) < 0;
}
function circumradius(ax, ay, bx, by, cx, cy) {
  const dx = bx - ax;
  const dy = by - ay;
  const ex = cx - ax;
  const ey = cy - ay;
  const bl = dx * dx + dy * dy;
  const cl = ex * ex + ey * ey;
  const d3 = 0.5 / (dx * ey - dy * ex);
  const x4 = (ey * bl - dy * cl) * d3;
  const y4 = (dx * cl - ex * bl) * d3;
  return x4 * x4 + y4 * y4;
}
function circumcenter(ax, ay, bx, by, cx, cy) {
  const dx = bx - ax;
  const dy = by - ay;
  const ex = cx - ax;
  const ey = cy - ay;
  const bl = dx * dx + dy * dy;
  const cl = ex * ex + ey * ey;
  const d3 = 0.5 / (dx * ey - dy * ex);
  const x4 = ax + (ey * bl - dy * cl) * d3;
  const y4 = ay + (dx * cl - ex * bl) * d3;
  return { x: x4, y: y4 };
}
function quicksort(ids, dists, left, right) {
  if (right - left <= 20) {
    for (let i6 = left + 1; i6 <= right; i6++) {
      const temp = ids[i6];
      const tempDist = dists[temp];
      let j3 = i6 - 1;
      while (j3 >= left && dists[ids[j3]] > tempDist) ids[j3 + 1] = ids[j3--];
      ids[j3 + 1] = temp;
    }
  } else {
    const median2 = left + right >> 1;
    let i6 = left + 1;
    let j3 = right;
    swap(ids, median2, i6);
    if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);
    if (dists[ids[i6]] > dists[ids[right]]) swap(ids, i6, right);
    if (dists[ids[left]] > dists[ids[i6]]) swap(ids, left, i6);
    const temp = ids[i6];
    const tempDist = dists[temp];
    while (true) {
      do
        i6++;
      while (dists[ids[i6]] < tempDist);
      do
        j3--;
      while (dists[ids[j3]] > tempDist);
      if (j3 < i6) break;
      swap(ids, i6, j3);
    }
    ids[left + 1] = ids[j3];
    ids[j3] = temp;
    if (right - i6 + 1 >= j3 - left) {
      quicksort(ids, dists, i6, right);
      quicksort(ids, dists, left, j3 - 1);
    } else {
      quicksort(ids, dists, left, j3 - 1);
      quicksort(ids, dists, i6, right);
    }
  }
}
function swap(arr, i6, j3) {
  const tmp = arr[i6];
  arr[i6] = arr[j3];
  arr[j3] = tmp;
}
function defaultGetX(p4) {
  return p4[0];
}
function defaultGetY(p4) {
  return p4[1];
}

// node_modules/d3-delaunay/src/path.js
var epsilon4 = 1e-6;
var Path2 = class {
  constructor() {
    this._x0 = this._y0 = // start of current subpath
    this._x1 = this._y1 = null;
    this._ = "";
  }
  moveTo(x4, y4) {
    this._ += `M${this._x0 = this._x1 = +x4},${this._y0 = this._y1 = +y4}`;
  }
  closePath() {
    if (this._x1 !== null) {
      this._x1 = this._x0, this._y1 = this._y0;
      this._ += "Z";
    }
  }
  lineTo(x4, y4) {
    this._ += `L${this._x1 = +x4},${this._y1 = +y4}`;
  }
  arc(x4, y4, r7) {
    x4 = +x4, y4 = +y4, r7 = +r7;
    const x0 = x4 + r7;
    const y0 = y4;
    if (r7 < 0) throw new Error("negative radius");
    if (this._x1 === null) this._ += `M${x0},${y0}`;
    else if (Math.abs(this._x1 - x0) > epsilon4 || Math.abs(this._y1 - y0) > epsilon4) this._ += "L" + x0 + "," + y0;
    if (!r7) return;
    this._ += `A${r7},${r7},0,1,1,${x4 - r7},${y4}A${r7},${r7},0,1,1,${this._x1 = x0},${this._y1 = y0}`;
  }
  rect(x4, y4, w5, h2) {
    this._ += `M${this._x0 = this._x1 = +x4},${this._y0 = this._y1 = +y4}h${+w5}v${+h2}h${-w5}Z`;
  }
  value() {
    return this._ || null;
  }
};

// node_modules/d3-delaunay/src/polygon.js
var Polygon = class {
  constructor() {
    this._ = [];
  }
  moveTo(x4, y4) {
    this._.push([x4, y4]);
  }
  closePath() {
    this._.push(this._[0].slice());
  }
  lineTo(x4, y4) {
    this._.push([x4, y4]);
  }
  value() {
    return this._.length ? this._ : null;
  }
};

// node_modules/d3-delaunay/src/voronoi.js
var Voronoi = class {
  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {
    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error("invalid bounds");
    this.delaunay = delaunay;
    this._circumcenters = new Float64Array(delaunay.points.length * 2);
    this.vectors = new Float64Array(delaunay.points.length * 2);
    this.xmax = xmax, this.xmin = xmin;
    this.ymax = ymax, this.ymin = ymin;
    this._init();
  }
  update() {
    this.delaunay.update();
    this._init();
    return this;
  }
  _init() {
    const { delaunay: { points, hull, triangles }, vectors } = this;
    let bx, by;
    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);
    for (let i6 = 0, j3 = 0, n7 = triangles.length, x4, y4; i6 < n7; i6 += 3, j3 += 2) {
      const t14 = triangles[i6] * 2;
      const t22 = triangles[i6 + 1] * 2;
      const t32 = triangles[i6 + 2] * 2;
      const x12 = points[t14];
      const y12 = points[t14 + 1];
      const x22 = points[t22];
      const y22 = points[t22 + 1];
      const x32 = points[t32];
      const y32 = points[t32 + 1];
      const dx = x22 - x12;
      const dy = y22 - y12;
      const ex = x32 - x12;
      const ey = y32 - y12;
      const ab4 = (dx * ey - dy * ex) * 2;
      if (Math.abs(ab4) < 1e-9) {
        if (bx === void 0) {
          bx = by = 0;
          for (const i7 of hull) bx += points[i7 * 2], by += points[i7 * 2 + 1];
          bx /= hull.length, by /= hull.length;
        }
        const a5 = 1e9 * Math.sign((bx - x12) * ey - (by - y12) * ex);
        x4 = (x12 + x32) / 2 - a5 * ey;
        y4 = (y12 + y32) / 2 + a5 * ex;
      } else {
        const d3 = 1 / ab4;
        const bl = dx * dx + dy * dy;
        const cl = ex * ex + ey * ey;
        x4 = x12 + (ey * bl - dy * cl) * d3;
        y4 = y12 + (dx * cl - ex * bl) * d3;
      }
      circumcenters[j3] = x4;
      circumcenters[j3 + 1] = y4;
    }
    let h2 = hull[hull.length - 1];
    let p0, p1 = h2 * 4;
    let x0, x1 = points[2 * h2];
    let y0, y1 = points[2 * h2 + 1];
    vectors.fill(0);
    for (let i6 = 0; i6 < hull.length; ++i6) {
      h2 = hull[i6];
      p0 = p1, x0 = x1, y0 = y1;
      p1 = h2 * 4, x1 = points[2 * h2], y1 = points[2 * h2 + 1];
      vectors[p0 + 2] = vectors[p1] = y0 - y1;
      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;
    }
  }
  render(context) {
    const buffer = context == null ? context = new Path2() : void 0;
    const { delaunay: { halfedges, inedges, hull }, circumcenters, vectors } = this;
    if (hull.length <= 1) return null;
    for (let i6 = 0, n7 = halfedges.length; i6 < n7; ++i6) {
      const j3 = halfedges[i6];
      if (j3 < i6) continue;
      const ti = Math.floor(i6 / 3) * 2;
      const tj = Math.floor(j3 / 3) * 2;
      const xi = circumcenters[ti];
      const yi = circumcenters[ti + 1];
      const xj = circumcenters[tj];
      const yj = circumcenters[tj + 1];
      this._renderSegment(xi, yi, xj, yj, context);
    }
    let h0, h1 = hull[hull.length - 1];
    for (let i6 = 0; i6 < hull.length; ++i6) {
      h0 = h1, h1 = hull[i6];
      const t8 = Math.floor(inedges[h1] / 3) * 2;
      const x4 = circumcenters[t8];
      const y4 = circumcenters[t8 + 1];
      const v6 = h0 * 4;
      const p4 = this._project(x4, y4, vectors[v6 + 2], vectors[v6 + 3]);
      if (p4) this._renderSegment(x4, y4, p4[0], p4[1], context);
    }
    return buffer && buffer.value();
  }
  renderBounds(context) {
    const buffer = context == null ? context = new Path2() : void 0;
    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);
    return buffer && buffer.value();
  }
  renderCell(i6, context) {
    const buffer = context == null ? context = new Path2() : void 0;
    const points = this._clip(i6);
    if (points === null || !points.length) return;
    context.moveTo(points[0], points[1]);
    let n7 = points.length;
    while (points[0] === points[n7 - 2] && points[1] === points[n7 - 1] && n7 > 1) n7 -= 2;
    for (let i7 = 2; i7 < n7; i7 += 2) {
      if (points[i7] !== points[i7 - 2] || points[i7 + 1] !== points[i7 - 1])
        context.lineTo(points[i7], points[i7 + 1]);
    }
    context.closePath();
    return buffer && buffer.value();
  }
  *cellPolygons() {
    const { delaunay: { points } } = this;
    for (let i6 = 0, n7 = points.length / 2; i6 < n7; ++i6) {
      const cell = this.cellPolygon(i6);
      if (cell) cell.index = i6, yield cell;
    }
  }
  cellPolygon(i6) {
    const polygon = new Polygon();
    this.renderCell(i6, polygon);
    return polygon.value();
  }
  _renderSegment(x0, y0, x1, y1, context) {
    let S3;
    const c0 = this._regioncode(x0, y0);
    const c12 = this._regioncode(x1, y1);
    if (c0 === 0 && c12 === 0) {
      context.moveTo(x0, y0);
      context.lineTo(x1, y1);
    } else if (S3 = this._clipSegment(x0, y0, x1, y1, c0, c12)) {
      context.moveTo(S3[0], S3[1]);
      context.lineTo(S3[2], S3[3]);
    }
  }
  contains(i6, x4, y4) {
    if ((x4 = +x4, x4 !== x4) || (y4 = +y4, y4 !== y4)) return false;
    return this.delaunay._step(i6, x4, y4) === i6;
  }
  *neighbors(i6) {
    const ci = this._clip(i6);
    if (ci) for (const j3 of this.delaunay.neighbors(i6)) {
      const cj = this._clip(j3);
      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {
        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {
          if (ci[ai] === cj[aj] && ci[ai + 1] === cj[aj + 1] && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj] && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {
            yield j3;
            break loop;
          }
        }
      }
    }
  }
  _cell(i6) {
    const { circumcenters, delaunay: { inedges, halfedges, triangles } } = this;
    const e0 = inedges[i6];
    if (e0 === -1) return null;
    const points = [];
    let e11 = e0;
    do {
      const t8 = Math.floor(e11 / 3);
      points.push(circumcenters[t8 * 2], circumcenters[t8 * 2 + 1]);
      e11 = e11 % 3 === 2 ? e11 - 2 : e11 + 1;
      if (triangles[e11] !== i6) break;
      e11 = halfedges[e11];
    } while (e11 !== e0 && e11 !== -1);
    return points;
  }
  _clip(i6) {
    if (i6 === 0 && this.delaunay.hull.length === 1) {
      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];
    }
    const points = this._cell(i6);
    if (points === null) return null;
    const { vectors: V2 } = this;
    const v6 = i6 * 4;
    return this._simplify(V2[v6] || V2[v6 + 1] ? this._clipInfinite(i6, points, V2[v6], V2[v6 + 1], V2[v6 + 2], V2[v6 + 3]) : this._clipFinite(i6, points));
  }
  _clipFinite(i6, points) {
    const n7 = points.length;
    let P4 = null;
    let x0, y0, x1 = points[n7 - 2], y1 = points[n7 - 1];
    let c0, c12 = this._regioncode(x1, y1);
    let e0, e1 = 0;
    for (let j3 = 0; j3 < n7; j3 += 2) {
      x0 = x1, y0 = y1, x1 = points[j3], y1 = points[j3 + 1];
      c0 = c12, c12 = this._regioncode(x1, y1);
      if (c0 === 0 && c12 === 0) {
        e0 = e1, e1 = 0;
        if (P4) P4.push(x1, y1);
        else P4 = [x1, y1];
      } else {
        let S3, sx0, sy0, sx1, sy1;
        if (c0 === 0) {
          if ((S3 = this._clipSegment(x0, y0, x1, y1, c0, c12)) === null) continue;
          [sx0, sy0, sx1, sy1] = S3;
        } else {
          if ((S3 = this._clipSegment(x1, y1, x0, y0, c12, c0)) === null) continue;
          [sx1, sy1, sx0, sy0] = S3;
          e0 = e1, e1 = this._edgecode(sx0, sy0);
          if (e0 && e1) this._edge(i6, e0, e1, P4, P4.length);
          if (P4) P4.push(sx0, sy0);
          else P4 = [sx0, sy0];
        }
        e0 = e1, e1 = this._edgecode(sx1, sy1);
        if (e0 && e1) this._edge(i6, e0, e1, P4, P4.length);
        if (P4) P4.push(sx1, sy1);
        else P4 = [sx1, sy1];
      }
    }
    if (P4) {
      e0 = e1, e1 = this._edgecode(P4[0], P4[1]);
      if (e0 && e1) this._edge(i6, e0, e1, P4, P4.length);
    } else if (this.contains(i6, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {
      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];
    }
    return P4;
  }
  _clipSegment(x0, y0, x1, y1, c0, c12) {
    const flip = c0 < c12;
    if (flip) [x0, y0, x1, y1, c0, c12] = [x1, y1, x0, y0, c12, c0];
    while (true) {
      if (c0 === 0 && c12 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];
      if (c0 & c12) return null;
      let x4, y4, c9 = c0 || c12;
      if (c9 & 8) x4 = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y4 = this.ymax;
      else if (c9 & 4) x4 = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y4 = this.ymin;
      else if (c9 & 2) y4 = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x4 = this.xmax;
      else y4 = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x4 = this.xmin;
      if (c0) x0 = x4, y0 = y4, c0 = this._regioncode(x0, y0);
      else x1 = x4, y1 = y4, c12 = this._regioncode(x1, y1);
    }
  }
  _clipInfinite(i6, points, vx0, vy0, vxn, vyn) {
    let P4 = Array.from(points), p4;
    if (p4 = this._project(P4[0], P4[1], vx0, vy0)) P4.unshift(p4[0], p4[1]);
    if (p4 = this._project(P4[P4.length - 2], P4[P4.length - 1], vxn, vyn)) P4.push(p4[0], p4[1]);
    if (P4 = this._clipFinite(i6, P4)) {
      for (let j3 = 0, n7 = P4.length, c0, c12 = this._edgecode(P4[n7 - 2], P4[n7 - 1]); j3 < n7; j3 += 2) {
        c0 = c12, c12 = this._edgecode(P4[j3], P4[j3 + 1]);
        if (c0 && c12) j3 = this._edge(i6, c0, c12, P4, j3), n7 = P4.length;
      }
    } else if (this.contains(i6, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {
      P4 = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];
    }
    return P4;
  }
  _edge(i6, e0, e1, P4, j3) {
    while (e0 !== e1) {
      let x4, y4;
      switch (e0) {
        case 5:
          e0 = 4;
          continue;
        // top-left
        case 4:
          e0 = 6, x4 = this.xmax, y4 = this.ymin;
          break;
        // top
        case 6:
          e0 = 2;
          continue;
        // top-right
        case 2:
          e0 = 10, x4 = this.xmax, y4 = this.ymax;
          break;
        // right
        case 10:
          e0 = 8;
          continue;
        // bottom-right
        case 8:
          e0 = 9, x4 = this.xmin, y4 = this.ymax;
          break;
        // bottom
        case 9:
          e0 = 1;
          continue;
        // bottom-left
        case 1:
          e0 = 5, x4 = this.xmin, y4 = this.ymin;
          break;
      }
      if ((P4[j3] !== x4 || P4[j3 + 1] !== y4) && this.contains(i6, x4, y4)) {
        P4.splice(j3, 0, x4, y4), j3 += 2;
      }
    }
    return j3;
  }
  _project(x0, y0, vx, vy) {
    let t8 = Infinity, c9, x4, y4;
    if (vy < 0) {
      if (y0 <= this.ymin) return null;
      if ((c9 = (this.ymin - y0) / vy) < t8) y4 = this.ymin, x4 = x0 + (t8 = c9) * vx;
    } else if (vy > 0) {
      if (y0 >= this.ymax) return null;
      if ((c9 = (this.ymax - y0) / vy) < t8) y4 = this.ymax, x4 = x0 + (t8 = c9) * vx;
    }
    if (vx > 0) {
      if (x0 >= this.xmax) return null;
      if ((c9 = (this.xmax - x0) / vx) < t8) x4 = this.xmax, y4 = y0 + (t8 = c9) * vy;
    } else if (vx < 0) {
      if (x0 <= this.xmin) return null;
      if ((c9 = (this.xmin - x0) / vx) < t8) x4 = this.xmin, y4 = y0 + (t8 = c9) * vy;
    }
    return [x4, y4];
  }
  _edgecode(x4, y4) {
    return (x4 === this.xmin ? 1 : x4 === this.xmax ? 2 : 0) | (y4 === this.ymin ? 4 : y4 === this.ymax ? 8 : 0);
  }
  _regioncode(x4, y4) {
    return (x4 < this.xmin ? 1 : x4 > this.xmax ? 2 : 0) | (y4 < this.ymin ? 4 : y4 > this.ymax ? 8 : 0);
  }
  _simplify(P4) {
    if (P4 && P4.length > 4) {
      for (let i6 = 0; i6 < P4.length; i6 += 2) {
        const j3 = (i6 + 2) % P4.length, k5 = (i6 + 4) % P4.length;
        if (P4[i6] === P4[j3] && P4[j3] === P4[k5] || P4[i6 + 1] === P4[j3 + 1] && P4[j3 + 1] === P4[k5 + 1]) {
          P4.splice(j3, 2), i6 -= 2;
        }
      }
      if (!P4.length) P4 = null;
    }
    return P4;
  }
};

// node_modules/d3-delaunay/src/delaunay.js
var tau3 = 2 * Math.PI;
var pow2 = Math.pow;
function pointX(p4) {
  return p4[0];
}
function pointY(p4) {
  return p4[1];
}
function collinear(d3) {
  const { triangles, coords } = d3;
  for (let i6 = 0; i6 < triangles.length; i6 += 3) {
    const a5 = 2 * triangles[i6], b5 = 2 * triangles[i6 + 1], c9 = 2 * triangles[i6 + 2], cross3 = (coords[c9] - coords[a5]) * (coords[b5 + 1] - coords[a5 + 1]) - (coords[b5] - coords[a5]) * (coords[c9 + 1] - coords[a5 + 1]);
    if (cross3 > 1e-10) return false;
  }
  return true;
}
function jitter(x4, y4, r7) {
  return [x4 + Math.sin(x4 + y4) * r7, y4 + Math.cos(x4 - y4) * r7];
}
var Delaunay = class _Delaunay {
  static from(points, fx = pointX, fy = pointY, that) {
    return new _Delaunay("length" in points ? flatArray(points, fx, fy, that) : Float64Array.from(flatIterable(points, fx, fy, that)));
  }
  constructor(points) {
    this._delaunator = new Delaunator(points);
    this.inedges = new Int32Array(points.length / 2);
    this._hullIndex = new Int32Array(points.length / 2);
    this.points = this._delaunator.coords;
    this._init();
  }
  update() {
    this._delaunator.update();
    this._init();
    return this;
  }
  _init() {
    const d3 = this._delaunator, points = this.points;
    if (d3.hull && d3.hull.length > 2 && collinear(d3)) {
      this.collinear = Int32Array.from({ length: points.length / 2 }, (_3, i6) => i6).sort((i6, j3) => points[2 * i6] - points[2 * j3] || points[2 * i6 + 1] - points[2 * j3 + 1]);
      const e11 = this.collinear[0], f3 = this.collinear[this.collinear.length - 1], bounds = [points[2 * e11], points[2 * e11 + 1], points[2 * f3], points[2 * f3 + 1]], r7 = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);
      for (let i6 = 0, n7 = points.length / 2; i6 < n7; ++i6) {
        const p4 = jitter(points[2 * i6], points[2 * i6 + 1], r7);
        points[2 * i6] = p4[0];
        points[2 * i6 + 1] = p4[1];
      }
      this._delaunator = new Delaunator(points);
    } else {
      delete this.collinear;
    }
    const halfedges = this.halfedges = this._delaunator.halfedges;
    const hull = this.hull = this._delaunator.hull;
    const triangles = this.triangles = this._delaunator.triangles;
    const inedges = this.inedges.fill(-1);
    const hullIndex = this._hullIndex.fill(-1);
    for (let e11 = 0, n7 = halfedges.length; e11 < n7; ++e11) {
      const p4 = triangles[e11 % 3 === 2 ? e11 - 2 : e11 + 1];
      if (halfedges[e11] === -1 || inedges[p4] === -1) inedges[p4] = e11;
    }
    for (let i6 = 0, n7 = hull.length; i6 < n7; ++i6) {
      hullIndex[hull[i6]] = i6;
    }
    if (hull.length <= 2 && hull.length > 0) {
      this.triangles = new Int32Array(3).fill(-1);
      this.halfedges = new Int32Array(3).fill(-1);
      this.triangles[0] = hull[0];
      inedges[hull[0]] = 1;
      if (hull.length === 2) {
        inedges[hull[1]] = 0;
        this.triangles[1] = hull[1];
        this.triangles[2] = hull[1];
      }
    }
  }
  voronoi(bounds) {
    return new Voronoi(this, bounds);
  }
  *neighbors(i6) {
    const { inedges, hull, _hullIndex, halfedges, triangles, collinear: collinear2 } = this;
    if (collinear2) {
      const l5 = collinear2.indexOf(i6);
      if (l5 > 0) yield collinear2[l5 - 1];
      if (l5 < collinear2.length - 1) yield collinear2[l5 + 1];
      return;
    }
    const e0 = inedges[i6];
    if (e0 === -1) return;
    let e11 = e0, p0 = -1;
    do {
      yield p0 = triangles[e11];
      e11 = e11 % 3 === 2 ? e11 - 2 : e11 + 1;
      if (triangles[e11] !== i6) return;
      e11 = halfedges[e11];
      if (e11 === -1) {
        const p4 = hull[(_hullIndex[i6] + 1) % hull.length];
        if (p4 !== p0) yield p4;
        return;
      }
    } while (e11 !== e0);
  }
  find(x4, y4, i6 = 0) {
    if ((x4 = +x4, x4 !== x4) || (y4 = +y4, y4 !== y4)) return -1;
    const i0 = i6;
    let c9;
    while ((c9 = this._step(i6, x4, y4)) >= 0 && c9 !== i6 && c9 !== i0) i6 = c9;
    return c9;
  }
  _step(i6, x4, y4) {
    const { inedges, hull, _hullIndex, halfedges, triangles, points } = this;
    if (inedges[i6] === -1 || !points.length) return (i6 + 1) % (points.length >> 1);
    let c9 = i6;
    let dc = pow2(x4 - points[i6 * 2], 2) + pow2(y4 - points[i6 * 2 + 1], 2);
    const e0 = inedges[i6];
    let e11 = e0;
    do {
      let t8 = triangles[e11];
      const dt2 = pow2(x4 - points[t8 * 2], 2) + pow2(y4 - points[t8 * 2 + 1], 2);
      if (dt2 < dc) dc = dt2, c9 = t8;
      e11 = e11 % 3 === 2 ? e11 - 2 : e11 + 1;
      if (triangles[e11] !== i6) break;
      e11 = halfedges[e11];
      if (e11 === -1) {
        e11 = hull[(_hullIndex[i6] + 1) % hull.length];
        if (e11 !== t8) {
          if (pow2(x4 - points[e11 * 2], 2) + pow2(y4 - points[e11 * 2 + 1], 2) < dc) return e11;
        }
        break;
      }
    } while (e11 !== e0);
    return c9;
  }
  render(context) {
    const buffer = context == null ? context = new Path2() : void 0;
    const { points, halfedges, triangles } = this;
    for (let i6 = 0, n7 = halfedges.length; i6 < n7; ++i6) {
      const j3 = halfedges[i6];
      if (j3 < i6) continue;
      const ti = triangles[i6] * 2;
      const tj = triangles[j3] * 2;
      context.moveTo(points[ti], points[ti + 1]);
      context.lineTo(points[tj], points[tj + 1]);
    }
    this.renderHull(context);
    return buffer && buffer.value();
  }
  renderPoints(context, r7) {
    if (r7 === void 0 && (!context || typeof context.moveTo !== "function")) r7 = context, context = null;
    r7 = r7 == void 0 ? 2 : +r7;
    const buffer = context == null ? context = new Path2() : void 0;
    const { points } = this;
    for (let i6 = 0, n7 = points.length; i6 < n7; i6 += 2) {
      const x4 = points[i6], y4 = points[i6 + 1];
      context.moveTo(x4 + r7, y4);
      context.arc(x4, y4, r7, 0, tau3);
    }
    return buffer && buffer.value();
  }
  renderHull(context) {
    const buffer = context == null ? context = new Path2() : void 0;
    const { hull, points } = this;
    const h2 = hull[0] * 2, n7 = hull.length;
    context.moveTo(points[h2], points[h2 + 1]);
    for (let i6 = 1; i6 < n7; ++i6) {
      const h3 = 2 * hull[i6];
      context.lineTo(points[h3], points[h3 + 1]);
    }
    context.closePath();
    return buffer && buffer.value();
  }
  hullPolygon() {
    const polygon = new Polygon();
    this.renderHull(polygon);
    return polygon.value();
  }
  renderTriangle(i6, context) {
    const buffer = context == null ? context = new Path2() : void 0;
    const { points, triangles } = this;
    const t04 = triangles[i6 *= 3] * 2;
    const t14 = triangles[i6 + 1] * 2;
    const t22 = triangles[i6 + 2] * 2;
    context.moveTo(points[t04], points[t04 + 1]);
    context.lineTo(points[t14], points[t14 + 1]);
    context.lineTo(points[t22], points[t22 + 1]);
    context.closePath();
    return buffer && buffer.value();
  }
  *trianglePolygons() {
    const { triangles } = this;
    for (let i6 = 0, n7 = triangles.length / 3; i6 < n7; ++i6) {
      yield this.trianglePolygon(i6);
    }
  }
  trianglePolygon(i6) {
    const polygon = new Polygon();
    this.renderTriangle(i6, polygon);
    return polygon.value();
  }
};
function flatArray(points, fx, fy, that) {
  const n7 = points.length;
  const array3 = new Float64Array(n7 * 2);
  for (let i6 = 0; i6 < n7; ++i6) {
    const p4 = points[i6];
    array3[i6 * 2] = fx.call(that, p4, i6, points);
    array3[i6 * 2 + 1] = fy.call(that, p4, i6, points);
  }
  return array3;
}
function* flatIterable(points, fx, fy, that) {
  let i6 = 0;
  for (const p4 of points) {
    yield fx.call(that, p4, i6, points);
    yield fy.call(that, p4, i6, points);
    ++i6;
  }
}

// node_modules/@nivo/voronoi/dist/nivo-voronoi.es.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var C4 = function(n7) {
  return [n7.x, n7.y];
};
var L2 = _t;
var T3 = "cursor";
var P3 = "top";
var w4 = function(n7) {
  var o5 = n7.points, e11 = n7.getNodePosition, i6 = void 0 === e11 ? C4 : e11, t8 = n7.margin, r7 = void 0 === t8 ? L2 : t8;
  return o5.map(function(n8) {
    var o6 = i6(n8), e12 = o6[0], t9 = o6[1];
    return [e12 + r7.left, t9 + r7.top];
  });
};
var E2 = function(n7) {
  var o5 = n7.points, e11 = n7.width, i6 = n7.height, t8 = n7.margin, r7 = void 0 === t8 ? L2 : t8, l5 = n7.debug, u6 = Delaunay.from(o5), a5 = l5 ? u6.voronoi([0, 0, r7.left + e11 + r7.right, r7.top + i6 + r7.bottom]) : void 0;
  return { points: o5, delaunay: u6, voronoi: a5 };
};
var W4 = function(o5) {
  var e11 = o5.points, i6 = o5.getNodePosition, t8 = void 0 === i6 ? C4 : i6, r7 = o5.width, l5 = o5.height, u6 = o5.margin, a5 = void 0 === u6 ? L2 : u6, d3 = o5.debug;
  return (0, import_react20.useMemo)(function() {
    return E2({ points: w4({ points: e11, margin: a5, getNodePosition: t8 }), width: r7, height: l5, margin: a5, debug: d3 });
  }, [e11, r7, l5, a5, d3]);
};
var D2 = function(r7) {
  var l5 = r7.elementRef, u6 = r7.nodes, s5 = r7.getNodePosition, c9 = void 0 === s5 ? C4 : s5, h2 = r7.delaunay, v6 = r7.setCurrent, f3 = r7.margin, p4 = void 0 === f3 ? L2 : f3, m5 = r7.detectionRadius, M3 = void 0 === m5 ? 1 / 0 : m5, b5 = r7.isInteractive, k5 = void 0 === b5 || b5, y4 = r7.onMouseEnter, w5 = r7.onMouseMove, E3 = r7.onMouseLeave, W5 = r7.onClick, x4 = r7.onTouchStart, S3 = r7.onTouchMove, D3 = r7.onTouchEnd, R = r7.enableTouchCrosshair, A3 = void 0 !== R && R, N2 = r7.tooltip, O5 = r7.tooltipPosition, j3 = void 0 === O5 ? T3 : O5, I2 = r7.tooltipAnchor, z6 = void 0 === I2 ? P3 : I2, F2 = (0, import_react20.useState)(null), H4 = F2[0], B4 = F2[1], q2 = (0, import_react20.useRef)(null);
  (0, import_react20.useEffect)(function() {
    q2.current = H4;
  }, [q2, H4]);
  var G = (0, import_react20.useCallback)(function(n7) {
    if (!l5.current) return null;
    var o5 = Sn(l5.current, n7), e11 = o5[0], i6 = o5[1], t8 = h2.find(e11, i6), r8 = void 0 !== t8 ? u6[t8] : null;
    if (r8 && M3 !== 1 / 0) {
      var s6 = c9(r8), v7 = s6[0], f4 = s6[1];
      Mn(e11, i6, v7 + p4.left, f4 + p4.top) > M3 && (t8 = null, r8 = null);
    }
    return null === t8 || null === r8 ? null : [t8, r8];
  }, [l5, h2, u6, c9, p4, M3]), J2 = k(), K2 = J2.showTooltipAt, Q3 = J2.showTooltipFromEvent, U2 = J2.hideTooltip, V2 = (0, import_react20.useMemo)(function() {
    if (N2) return "cursor" === j3 ? function(n7, o5) {
      Q3(N2(n7), o5, z6);
    } : function(n7) {
      var o5 = c9(n7), e11 = o5[0], i6 = o5[1];
      K2(N2(n7), [e11 + p4.left, i6 + p4.top], z6);
    };
  }, [K2, Q3, N2, j3, z6, c9, p4]), X5 = (0, import_react20.useCallback)(function(n7) {
    var o5 = G(n7);
    if (B4(o5), null == v6 || v6(o5 ? o5[1] : null), o5) {
      var e11 = o5[1];
      null == V2 || V2(e11, n7), null == y4 || y4(o5[1], n7);
    }
  }, [G, B4, v6, V2, y4]), Y4 = (0, import_react20.useCallback)(function(n7) {
    var o5 = G(n7);
    if (B4(o5), o5) {
      var e11 = o5[0], i6 = o5[1];
      if (null == v6 || v6(i6), null == V2 || V2(i6, n7), q2.current) {
        var t8 = q2.current, r8 = t8[0], l6 = t8[1];
        e11 !== r8 ? null == E3 || E3(l6, n7) : null == w5 || w5(i6, n7);
      } else null == y4 || y4(i6, n7);
    } else null == v6 || v6(null), null == U2 || U2(), q2.current && (null == E3 || E3(q2.current[1], n7));
  }, [G, B4, q2, y4, w5, E3, V2, U2]), Z2 = (0, import_react20.useCallback)(function(n7) {
    B4(null), null == v6 || v6(null), U2(), E3 && q2.current && E3(q2.current[1], n7);
  }, [B4, v6, q2, U2, E3]), $3 = (0, import_react20.useCallback)(function(n7) {
    var o5 = G(n7);
    B4(o5), o5 && (null == W5 || W5(o5[1], n7));
  }, [G, B4, W5]), _3 = (0, import_react20.useCallback)(function(n7) {
    var o5 = G(n7);
    A3 && (B4(o5), null == v6 || v6(o5 ? o5[1] : null)), o5 && (null == x4 || x4(o5[1], n7));
  }, [G, B4, v6, A3, x4]), nn3 = (0, import_react20.useCallback)(function(n7) {
    var o5 = G(n7);
    A3 && (B4(o5), null == v6 || v6(o5 ? o5[1] : null)), o5 && (null == S3 || S3(o5[1], n7));
  }, [G, B4, v6, A3, S3]), on3 = (0, import_react20.useCallback)(function(n7) {
    A3 && (B4(null), null == v6 || v6(null)), D3 && q2.current && D3(q2.current[1], n7);
  }, [A3, B4, v6, D3, q2]);
  return { current: H4, handleMouseEnter: k5 ? X5 : void 0, handleMouseMove: k5 ? Y4 : void 0, handleMouseLeave: k5 ? Z2 : void 0, handleClick: k5 ? $3 : void 0, handleTouchStart: k5 ? _3 : void 0, handleTouchMove: k5 ? nn3 : void 0, handleTouchEnd: k5 ? on3 : void 0 };
};
var I = function(o5) {
  var i6 = o5.nodes, t8 = o5.width, r7 = o5.height, l5 = o5.margin, u6 = void 0 === l5 ? L2 : l5, a5 = o5.getNodePosition, d3 = o5.setCurrent, s5 = o5.onMouseEnter, c9 = o5.onMouseMove, h2 = o5.onMouseLeave, v6 = o5.onClick, f3 = o5.onTouchStart, p4 = o5.onTouchMove, g4 = o5.onTouchEnd, k5 = o5.enableTouchCrosshair, y4 = void 0 !== k5 && k5, C5 = o5.detectionRadius, w5 = void 0 === C5 ? 1 / 0 : C5, E3 = o5.tooltip, x4 = o5.tooltipPosition, S3 = void 0 === x4 ? T3 : x4, R = o5.tooltipAnchor, A3 = void 0 === R ? P3 : R, N2 = o5.debug, O5 = (0, import_react20.useRef)(null), j3 = W4({ points: i6, getNodePosition: a5, width: t8, height: r7, margin: u6, debug: N2 }), I2 = j3.delaunay, z6 = j3.voronoi, F2 = D2({ elementRef: O5, nodes: i6, delaunay: I2, margin: u6, detectionRadius: w5, setCurrent: d3, onMouseEnter: s5, onMouseMove: c9, onMouseLeave: h2, onClick: v6, onTouchStart: f3, onTouchMove: p4, onTouchEnd: g4, enableTouchCrosshair: y4, tooltip: E3, tooltipPosition: S3, tooltipAnchor: A3 }), H4 = F2.current, B4 = F2.handleMouseEnter, q2 = F2.handleMouseMove, G = F2.handleMouseLeave, J2 = F2.handleClick, K2 = F2.handleTouchStart, Q3 = F2.handleTouchMove, U2 = F2.handleTouchEnd, V2 = (0, import_react20.useMemo)(function() {
    if (N2 && z6) return z6.render();
  }, [N2, z6]);
  return (0, import_jsx_runtime5.jsxs)("g", { ref: O5, transform: "translate(" + -u6.left + "," + -u6.top + ")", children: [N2 && z6 && (0, import_jsx_runtime5.jsxs)(import_jsx_runtime5.Fragment, { children: [(0, import_jsx_runtime5.jsx)("path", { d: V2, stroke: "red", strokeWidth: 1, opacity: 0.75 }), w5 < 1 / 0 && (0, import_jsx_runtime5.jsx)("path", { stroke: "red", strokeWidth: 0.35, fill: "none", d: I2.renderPoints(void 0, w5) }), H4 && (0, import_jsx_runtime5.jsx)("path", { fill: "pink", opacity: 0.35, d: z6.renderCell(H4[0]) })] }), (0, import_jsx_runtime5.jsx)("rect", { "data-ref": "mesh-interceptor", width: u6.left + t8 + u6.right, height: u6.top + r7 + u6.bottom, fill: "red", opacity: 0, style: { cursor: "auto" }, onMouseEnter: B4, onMouseMove: q2, onMouseLeave: G, onTouchStart: K2, onTouchMove: Q3, onTouchEnd: U2, onClick: J2 })] });
};
var z4 = function(n7, o5) {
  n7.save(), n7.globalAlpha = 0.75, n7.beginPath(), o5.render(n7), n7.strokeStyle = "red", n7.lineWidth = 1, n7.stroke(), n7.restore();
};
var H2 = function(n7, o5, e11) {
  n7.save(), n7.globalAlpha = 0.35, n7.beginPath(), o5.renderCell(e11, n7), n7.fillStyle = "pink", n7.fill(), n7.restore();
};

// node_modules/@nivo/line/dist/nivo-line.es.js
function Q2() {
  return Q2 = Object.assign ? Object.assign.bind() : function(e11) {
    for (var o5 = 1; o5 < arguments.length; o5++) {
      var i6 = arguments[o5];
      for (var t8 in i6) Object.prototype.hasOwnProperty.call(i6, t8) && (e11[t8] = i6[t8]);
    }
    return e11;
  }, Q2.apply(this, arguments);
}
var U = (0, import_react21.memo)(function(e11) {
  var o5 = e11.point;
  return (0, import_jsx_runtime6.jsx)(w, { id: (0, import_jsx_runtime6.jsxs)("span", { children: ["x: ", (0, import_jsx_runtime6.jsx)("strong", { children: o5.data.xFormatted }), ", y:", " ", (0, import_jsx_runtime6.jsx)("strong", { children: o5.data.yFormatted })] }), enableChip: true, color: o5.serieColor });
});
var Z = (0, import_react21.memo)(function(e11) {
  var o5 = e11.slice, i6 = e11.axis, t8 = zt(), n7 = "x" === i6 ? "y" : "x";
  return (0, import_jsx_runtime6.jsx)(C, { rows: o5.points.map(function(e12) {
    return [(0, import_jsx_runtime6.jsx)(g, { color: e12.serieColor, style: t8.tooltip.chip }, "chip"), e12.serieId, (0, import_jsx_runtime6.jsx)("span", { style: t8.tooltip.tableCellValue, children: e12.data[n7 + "Formatted"] }, "value")];
  }) });
});
var $2 = { curve: "linear", xScale: { type: "point" }, yScale: { type: "linear", min: 0, max: "auto" }, layers: ["grid", "markers", "axes", "areas", "crosshair", "lines", "points", "slices", "mesh", "legends"], axisBottom: {}, axisLeft: {}, enableGridX: true, enableGridY: true, enablePoints: true, pointSize: 6, pointColor: { from: "color" }, pointBorderWidth: 0, pointBorderColor: { theme: "background" }, enablePointLabel: false, pointLabel: "yFormatted", colors: { scheme: "nivo" }, enableArea: false, areaBaselineValue: 0, areaOpacity: 0.2, areaBlendMode: "normal", lineWidth: 2, legends: [], isInteractive: true, tooltip: U, enableSlices: false, debugSlices: false, sliceTooltip: Z, debugMesh: false, enableCrosshair: true, crosshairType: "bottom-left" };
var _2 = Q2({}, $2, { enablePointLabel: false, useMesh: false, enableTouchCrosshair: false, animate: true, motionConfig: "gentle", defs: [], fill: [], role: "img" });
var ee = Q2({}, $2, { pixelRatio: "undefined" != typeof window && window.devicePixelRatio || 1 });
var oe = function(e11) {
  var i6 = e11.curve;
  return (0, import_react21.useMemo)(function() {
    return line_default().defined(function(e12) {
      return null !== e12.x && null !== e12.y;
    }).x(function(e12) {
      return e12.x;
    }).y(function(e12) {
      return e12.y;
    }).curve(lt(i6));
  }, [i6]);
};
var ie = function(e11) {
  var i6 = e11.curve, t8 = e11.yScale, n7 = e11.areaBaselineValue;
  return (0, import_react21.useMemo)(function() {
    return area_default().defined(function(e12) {
      return null !== e12.x && null !== e12.y;
    }).x(function(e12) {
      return e12.x;
    }).y1(function(e12) {
      return e12.y;
    }).curve(lt(i6)).y0(t8(n7));
  }, [i6, t8, n7]);
};
var te = function(e11) {
  var i6 = e11.componentId, t8 = e11.enableSlices, n7 = e11.points, r7 = e11.width, a5 = e11.height;
  return (0, import_react21.useMemo)(function() {
    if (false === t8) return [];
    if ("x" === t8) {
      var e12 = /* @__PURE__ */ new Map();
      return n7.forEach(function(o6) {
        null !== o6.data.x && null !== o6.data.y && (e12.has(o6.x) ? e12.get(o6.x).push(o6) : e12.set(o6.x, [o6]));
      }), Array.from(e12.entries()).sort(function(e13, o6) {
        return e13[0] - o6[0];
      }).map(function(e13, o6, t9) {
        var n8, l5, s5 = e13[0], c9 = e13[1], u6 = t9[o6 - 1], d3 = t9[o6 + 1];
        return n8 = u6 ? s5 - (s5 - u6[0]) / 2 : s5, l5 = d3 ? s5 - n8 + (d3[0] - s5) / 2 : r7 - n8, { id: "slice:" + i6 + ":" + s5, x0: n8, x: s5, y0: 0, y: 0, width: l5, height: a5, points: c9.reverse() };
      });
    }
    if ("y" === t8) {
      var o5 = /* @__PURE__ */ new Map();
      return n7.forEach(function(e13) {
        null !== e13.data.x && null !== e13.data.y && (o5.has(e13.y) ? o5.get(e13.y).push(e13) : o5.set(e13.y, [e13]));
      }), Array.from(o5.entries()).sort(function(e13, o6) {
        return e13[0] - o6[0];
      }).map(function(e13, o6, i7) {
        var t9, n8, l5 = e13[0], s5 = e13[1], c9 = i7[o6 - 1], u6 = i7[o6 + 1];
        return t9 = c9 ? l5 - (l5 - c9[0]) / 2 : l5, n8 = u6 ? l5 - t9 + (u6[0] - l5) / 2 : a5 - t9, { id: l5, x0: 0, x: 0, y0: t9, y: l5, width: r7, height: n8, points: s5.reverse() };
      });
    }
  }, [i6, t8, a5, n7, r7]);
};
var ne = "line";
var re3 = function(e11) {
  var n7 = e11.data, r7 = e11.xScale, a5 = void 0 === r7 ? _2.xScale : r7, l5 = e11.xFormat, s5 = e11.yScale, u6 = void 0 === s5 ? _2.yScale : s5, h2 = e11.yFormat, f3 = e11.width, v6 = e11.height, p4 = e11.colors, m5 = void 0 === p4 ? _2.colors : p4, g4 = e11.curve, y4 = void 0 === g4 ? _2.curve : g4, x4 = e11.areaBaselineValue, b5 = void 0 === x4 ? _2.areaBaselineValue : x4, S3 = e11.pointColor, M3 = void 0 === S3 ? _2.pointColor : S3, C5 = e11.pointBorderColor, k5 = void 0 === C5 ? _2.pointBorderColor : C5, B4 = e11.enableSlices, W5 = void 0 === B4 ? _2.enableSlicesTooltip : B4, E3 = (0, import_react21.useState)((0, import_uniqueId.default)(ne))[0], L3 = Ot(l5), G = Ot(h2), P4 = pr(m5, "id"), F2 = zt(), O5 = Xe(M3, F2), V2 = Xe(k5, F2), Y4 = (0, import_react21.useState)([]), R = Y4[0], I2 = Y4[1], X5 = (0, import_react21.useMemo)(function() {
    return dn2(n7.filter(function(e12) {
      return -1 === R.indexOf(e12.id);
    }), a5, u6, f3, v6);
  }, [n7, R, a5, u6, f3, v6]), z6 = X5.xScale, j3 = X5.yScale, D3 = X5.series, q2 = (0, import_react21.useMemo)(function() {
    var e12 = n7.map(function(e13) {
      return { id: e13.id, label: e13.id, color: P4(e13) };
    }), o5 = e12.map(function(e13) {
      return Q2({}, D3.find(function(o6) {
        return o6.id === e13.id;
      }), { color: e13.color });
    }).filter(function(e13) {
      return Boolean(e13.id);
    });
    return { legendData: e12.map(function(e13) {
      return Q2({}, e13, { hidden: !o5.find(function(o6) {
        return o6.id === e13.id;
      }) });
    }).reverse(), series: o5 };
  }, [n7, D3, P4]), J2 = q2.legendData, K2 = q2.series, N2 = (0, import_react21.useCallback)(function(e12) {
    I2(function(o5) {
      return o5.indexOf(e12) > -1 ? o5.filter(function(o6) {
        return o6 !== e12;
      }) : [].concat(o5, [e12]);
    });
  }, []), U2 = function(e12) {
    var i6 = e12.series, t8 = e12.getPointColor, n8 = e12.getPointBorderColor, r8 = e12.formatX, a6 = e12.formatY;
    return (0, import_react21.useMemo)(function() {
      return i6.reduce(function(e13, o5) {
        return [].concat(e13, o5.data.filter(function(e14) {
          return null !== e14.position.x && null !== e14.position.y;
        }).map(function(i7, l6) {
          var s6 = { id: o5.id + "." + l6, index: e13.length + l6, serieId: o5.id, serieColor: o5.color, x: i7.position.x, y: i7.position.y };
          return s6.color = t8(o5), s6.borderColor = n8(s6), s6.data = Q2({}, i7.data, { xFormatted: r8(i7.data.x), yFormatted: a6(i7.data.y) }), s6;
        }));
      }, []);
    }, [i6, t8, n8, r8, a6]);
  }({ series: K2, getPointColor: O5, getPointBorderColor: V2, formatX: L3, formatY: G }), Z2 = te({ componentId: E3, enableSlices: W5, points: U2, width: f3, height: v6 });
  return { legendData: J2, toggleSerie: N2, lineGenerator: oe({ curve: y4 }), areaGenerator: ie({ curve: y4, yScale: j3, areaBaselineValue: b5 }), getColor: P4, series: K2, xScale: z6, yScale: j3, slices: Z2, points: U2 };
};
var ae = function(e11) {
  var o5 = e11.areaBlendMode, i6 = e11.areaOpacity, t8 = e11.color, n7 = e11.fill, r7 = e11.path, a5 = Ur(), l5 = a5.animate, s5 = a5.config, c9 = Fr(r7), u6 = useSpring({ color: t8, config: s5, immediate: !l5 });
  return (0, import_jsx_runtime6.jsx)(animated.path, { d: c9, fill: n7 || u6.color, fillOpacity: i6, strokeWidth: 0, style: { mixBlendMode: o5 } });
};
var le = (0, import_react21.memo)(function(e11) {
  var o5 = e11.areaGenerator, i6 = e11.areaOpacity, t8 = e11.areaBlendMode, n7 = e11.lines.slice(0).reverse();
  return (0, import_jsx_runtime6.jsx)("g", { children: n7.map(function(e12) {
    return (0, import_jsx_runtime6.jsx)(ae, Q2({ path: o5(e12.data.map(function(e13) {
      return e13.position;
    })) }, Q2({ areaOpacity: i6, areaBlendMode: t8 }, e12)), e12.id);
  }) });
});
var se = (0, import_react21.memo)(function(e11) {
  var i6 = e11.lineGenerator, t8 = e11.points, n7 = e11.color, r7 = e11.thickness, a5 = (0, import_react21.useMemo)(function() {
    return i6(t8);
  }, [i6, t8]), l5 = Fr(a5);
  return (0, import_jsx_runtime6.jsx)(animated.path, { d: l5, fill: "none", strokeWidth: r7, stroke: n7 });
});
var ce2 = (0, import_react21.memo)(function(e11) {
  var o5 = e11.lines, i6 = e11.lineGenerator, t8 = e11.lineWidth;
  return o5.slice(0).reverse().map(function(e12) {
    var o6 = e12.id, n7 = e12.data, r7 = e12.color;
    return (0, import_jsx_runtime6.jsx)(se, { id: o6, points: n7.map(function(e13) {
      return e13.position;
    }), lineGenerator: i6, color: r7, thickness: t8 }, o6);
  });
});
var ue = (0, import_react21.memo)(function(e11) {
  var o5 = e11.slice, i6 = e11.slices, r7 = e11.axis, a5 = e11.debug, l5 = e11.tooltip, s5 = e11.isCurrent, c9 = e11.setCurrent, u6 = e11.onMouseEnter, d3 = e11.onMouseMove, h2 = e11.onMouseLeave, f3 = e11.onClick, v6 = e11.onTouchStart, p4 = e11.onTouchMove, m5 = e11.onTouchEnd, g4 = k(), y4 = g4.showTooltipFromEvent, x4 = g4.hideTooltip, b5 = (0, import_react21.useCallback)(function(e12) {
    y4((0, import_react21.createElement)(l5, { slice: o5, axis: r7 }), e12, "right"), c9(o5), u6 && u6(o5, e12);
  }, [y4, l5, o5, r7, c9, u6]), S3 = (0, import_react21.useCallback)(function(e12) {
    y4((0, import_react21.createElement)(l5, { slice: o5, axis: r7 }), e12, "right"), d3 && d3(o5, e12);
  }, [y4, l5, o5, r7, d3]), M3 = (0, import_react21.useCallback)(function(e12) {
    x4(), c9(null), h2 && h2(o5, e12);
  }, [x4, c9, h2, o5]), C5 = (0, import_react21.useCallback)(function(e12) {
    f3 && f3(o5, e12);
  }, [o5, f3]), w5 = (0, import_react21.useCallback)(function(e12) {
    y4((0, import_react21.createElement)(l5, { slice: o5, axis: r7 }), e12, "right"), c9(o5), v6 && v6(o5, e12);
  }, [r7, v6, c9, y4, o5, l5]), T4 = (0, import_react21.useCallback)(function(e12) {
    var t8 = e12.touches[0], a6 = document.elementFromPoint(t8.clientX, t8.clientY), s6 = null == a6 ? void 0 : a6.getAttribute("data-ref");
    if (s6) {
      var u7 = i6.find(function(e13) {
        return e13.id === s6;
      });
      u7 && (y4((0, import_react21.createElement)(l5, { slice: u7, axis: r7 }), e12, "right"), c9(u7));
    }
    p4 && p4(o5, e12);
  }, [r7, p4, c9, y4, o5, i6, l5]), k5 = (0, import_react21.useCallback)(function(e12) {
    x4(), c9(null), m5 && m5(o5, e12);
  }, [x4, c9, m5, o5]);
  return (0, import_jsx_runtime6.jsx)("rect", { x: o5.x0, y: o5.y0, width: o5.width, height: o5.height, stroke: "red", strokeWidth: a5 ? 1 : 0, strokeOpacity: 0.75, fill: "red", fillOpacity: s5 && a5 ? 0.35 : 0, onMouseEnter: b5, onMouseMove: S3, onMouseLeave: M3, onClick: C5, onTouchStart: w5, onTouchMove: T4, onTouchEnd: k5, "data-ref": o5.id });
});
var de2 = (0, import_react21.memo)(function(e11) {
  var o5 = e11.slices, i6 = e11.axis, t8 = e11.debug, n7 = e11.height, r7 = e11.tooltip, a5 = e11.current, l5 = e11.setCurrent, s5 = e11.onMouseEnter, c9 = e11.onMouseMove, u6 = e11.onMouseLeave, d3 = e11.onClick, h2 = e11.onTouchStart, f3 = e11.onTouchMove, v6 = e11.onTouchEnd;
  return o5.map(function(e12) {
    return (0, import_jsx_runtime6.jsx)(ue, { slice: e12, slices: o5, axis: i6, debug: t8, height: n7, tooltip: r7, setCurrent: l5, isCurrent: null !== a5 && a5.id === e12.id, onMouseEnter: s5, onMouseMove: c9, onMouseLeave: u6, onClick: d3, onTouchStart: h2, onTouchMove: f3, onTouchEnd: v6 }, e12.id);
  });
});
var he = (0, import_react21.memo)(function(e11) {
  var o5 = e11.points, i6 = e11.symbol, t8 = e11.size, n7 = e11.borderWidth, r7 = e11.enableLabel, a5 = e11.label, l5 = e11.labelYOffset, s5 = zt(), u6 = qn(a5), d3 = o5.slice(0).reverse().map(function(e12) {
    return { id: e12.id, x: e12.x, y: e12.y, datum: e12.data, fill: e12.color, stroke: e12.borderColor, label: r7 ? u6(e12) : null };
  });
  return (0, import_jsx_runtime6.jsx)("g", { children: d3.map(function(e12) {
    return (0, import_jsx_runtime6.jsx)(vn, { x: e12.x, y: e12.y, datum: e12.datum, symbol: i6, size: t8, color: e12.fill, borderWidth: n7, borderColor: e12.stroke, label: e12.label, labelYOffset: l5, theme: s5 }, e12.id);
  }) });
});
var fe = (0, import_react21.memo)(function(e11) {
  var o5 = e11.points, i6 = e11.width, r7 = e11.height, a5 = e11.margin, l5 = e11.setCurrent, s5 = e11.onMouseEnter, c9 = e11.onMouseMove, u6 = e11.onMouseLeave, d3 = e11.onClick, h2 = e11.onTouchStart, f3 = e11.onTouchMove, v6 = e11.onTouchEnd, p4 = e11.tooltip, m5 = e11.debug, g4 = e11.enableTouchCrosshair, y4 = k(), x4 = y4.showTooltipAt, b5 = y4.hideTooltip, S3 = (0, import_react21.useCallback)(function(e12, o6) {
    x4((0, import_react21.createElement)(p4, { point: e12 }), [e12.x + a5.left, e12.y + a5.top], "top"), s5 && s5(e12, o6);
  }, [x4, p4, s5, a5]), M3 = (0, import_react21.useCallback)(function(e12, o6) {
    x4((0, import_react21.createElement)(p4, { point: e12 }), [e12.x + a5.left, e12.y + a5.top], "top"), c9 && c9(e12, o6);
  }, [x4, p4, a5.left, a5.top, c9]), C5 = (0, import_react21.useCallback)(function(e12, o6) {
    b5(), u6 && u6(e12, o6);
  }, [b5, u6]), w5 = (0, import_react21.useCallback)(function(e12, o6) {
    d3 && d3(e12, o6);
  }, [d3]), T4 = (0, import_react21.useCallback)(function(e12, o6) {
    x4((0, import_react21.createElement)(p4, { point: e12 }), [e12.x + a5.left, e12.y + a5.top], "top"), h2 && h2(e12, o6);
  }, [a5.left, a5.top, h2, x4, p4]), k5 = (0, import_react21.useCallback)(function(e12, o6) {
    x4((0, import_react21.createElement)(p4, { point: e12 }), [e12.x + a5.left, e12.y + a5.top], "top"), f3 && f3(e12, o6);
  }, [a5.left, a5.top, f3, x4, p4]), B4 = (0, import_react21.useCallback)(function(e12, o6) {
    b5(), v6 && v6(e12, o6);
  }, [v6, b5]);
  return (0, import_jsx_runtime6.jsx)(I, { nodes: o5, width: i6, height: r7, setCurrent: l5, onMouseEnter: S3, onMouseMove: M3, onMouseLeave: C5, onClick: w5, onTouchStart: T4, onTouchMove: k5, onTouchEnd: B4, enableTouchCrosshair: g4, debug: m5 });
});
var ve = On(function(e11) {
  var o5 = e11.data, t8 = e11.xScale, n7 = void 0 === t8 ? { type: "point" } : t8, a5 = e11.xFormat, l5 = e11.yScale, s5 = void 0 === l5 ? { type: "linear", min: 0, max: "auto" } : l5, u6 = e11.yFormat, d3 = e11.layers, h2 = void 0 === d3 ? ["grid", "markers", "axes", "areas", "crosshair", "lines", "points", "slices", "mesh", "legends"] : d3, f3 = e11.curve, v6 = void 0 === f3 ? "linear" : f3, p4 = e11.areaBaselineValue, m5 = void 0 === p4 ? 0 : p4, S3 = e11.colors, M3 = void 0 === S3 ? { scheme: "nivo" } : S3, C5 = e11.margin, w5 = e11.width, W5 = e11.height, E3 = e11.axisTop, G = e11.axisRight, P4 = e11.axisBottom, F2 = void 0 === P4 ? {} : P4, O5 = e11.axisLeft, V2 = void 0 === O5 ? {} : O5, R = e11.enableGridX, I2 = void 0 === R || R, A3 = e11.enableGridY, H4 = void 0 === A3 || A3, z6 = e11.gridXValues, j3 = e11.gridYValues, D3 = e11.lineWidth, q2 = void 0 === D3 ? 2 : D3, J2 = e11.enableArea, K2 = void 0 !== J2 && J2, N2 = e11.areaOpacity, $3 = void 0 === N2 ? 0.2 : N2, _3 = e11.areaBlendMode, ee2 = void 0 === _3 ? "normal" : _3, oe2 = e11.enablePoints, ie2 = void 0 === oe2 || oe2, te2 = e11.pointSymbol, ne2 = e11.pointSize, ae2 = void 0 === ne2 ? 6 : ne2, se2 = e11.pointColor, ue2 = void 0 === se2 ? { from: "color" } : se2, ve2 = e11.pointBorderWidth, pe2 = void 0 === ve2 ? 0 : ve2, me2 = e11.pointBorderColor, ge2 = void 0 === me2 ? { theme: "background" } : me2, ye2 = e11.enablePointLabel, xe = void 0 !== ye2 && ye2, be = e11.pointLabel, Se = void 0 === be ? "data.yFormatted" : be, Me2 = e11.pointLabelYOffset, Ce2 = e11.defs, we2 = void 0 === Ce2 ? [] : Ce2, Te2 = e11.fill, ke2 = void 0 === Te2 ? [] : Te2, Be2 = e11.markers, We2 = e11.legends, Ee = void 0 === We2 ? [] : We2, Le2 = e11.isInteractive, Ge2 = void 0 === Le2 || Le2, Pe2 = e11.useMesh, Fe2 = void 0 !== Pe2 && Pe2, Oe2 = e11.debugMesh, Ve2 = void 0 !== Oe2 && Oe2, Ye2 = e11.onMouseEnter, Re2 = e11.onMouseMove, Ie = e11.onMouseLeave, Ae = e11.onClick, He2 = e11.onTouchStart, Xe2 = e11.onTouchMove, ze = e11.onTouchEnd, je2 = e11.tooltip, De2 = void 0 === je2 ? U : je2, qe2 = e11.enableSlices, Je2 = void 0 !== qe2 && qe2, Ke2 = e11.debugSlices, Ne2 = void 0 !== Ke2 && Ke2, Qe3 = e11.sliceTooltip, Ue2 = void 0 === Qe3 ? Z : Qe3, Ze2 = e11.enableCrosshair, $e2 = void 0 === Ze2 || Ze2, _e = e11.crosshairType, eo = void 0 === _e ? "bottom-left" : _e, oo = e11.enableTouchCrosshair, io = void 0 !== oo && oo, to3 = e11.role, no = void 0 === to3 ? "img" : to3, ro = wt(w5, W5, C5), ao = ro.margin, lo = ro.innerWidth, so = ro.innerHeight, co = ro.outerWidth, uo = ro.outerHeight, ho = re3({ data: o5, xScale: n7, xFormat: a5, yScale: s5, yFormat: u6, width: lo, height: so, colors: M3, curve: v6, areaBaselineValue: m5, pointColor: ue2, pointBorderColor: ge2, enableSlices: Je2 }), fo = ho.legendData, vo = ho.toggleSerie, po = ho.lineGenerator, mo = ho.areaGenerator, go = ho.series, yo = ho.xScale, xo = ho.yScale, bo = ho.slices, So = ho.points, Mo = zt(), Co = Xe(ue2, Mo), wo = Xe(ge2, Mo), To = (0, import_react21.useState)(null), ko = To[0], Bo = To[1], Wo = (0, import_react21.useState)(null), Eo = Wo[0], Lo = Wo[1], Go = { grid: (0, import_jsx_runtime6.jsx)(C3, { theme: Mo, width: lo, height: so, xScale: I2 ? yo : null, yScale: H4 ? xo : null, xValues: z6, yValues: j3 }, "grid"), markers: (0, import_jsx_runtime6.jsx)(Rn, { markers: Be2, width: lo, height: so, xScale: yo, yScale: xo, theme: Mo }, "markers"), axes: (0, import_jsx_runtime6.jsx)(B, { xScale: yo, yScale: xo, width: lo, height: so, theme: Mo, top: E3, right: G, bottom: F2, left: V2 }, "axes"), areas: null, lines: (0, import_jsx_runtime6.jsx)(ce2, { lines: go, lineGenerator: po, lineWidth: q2 }, "lines"), slices: null, points: null, crosshair: null, mesh: null, legends: Ee.map(function(e12, o6) {
    return (0, import_jsx_runtime6.jsx)(O4, Q2({}, e12, { containerWidth: lo, containerHeight: so, data: e12.data || fo, theme: Mo, toggleSerie: e12.toggleSerie ? vo : void 0 }), "legend." + o6);
  }) }, Po = In(we2, go, ke2);
  return K2 && (Go.areas = (0, import_jsx_runtime6.jsx)(le, { areaGenerator: mo, areaOpacity: $3, areaBlendMode: ee2, lines: go }, "areas")), Ge2 && false !== Je2 && (Go.slices = (0, import_jsx_runtime6.jsx)(de2, { slices: bo, axis: Je2, debug: Ne2, height: so, tooltip: Ue2, current: Eo, setCurrent: Lo, onMouseEnter: Ye2, onMouseMove: Re2, onMouseLeave: Ie, onClick: Ae, onTouchStart: He2, onTouchMove: Xe2, onTouchEnd: ze }, "slices")), ie2 && (Go.points = (0, import_jsx_runtime6.jsx)(he, { points: So, symbol: te2, size: ae2, color: Co, borderWidth: pe2, borderColor: wo, enableLabel: xe, label: Se, labelYOffset: Me2 }, "points")), Ge2 && $e2 && (null !== ko && (Go.crosshair = (0, import_jsx_runtime6.jsx)(P, { width: lo, height: so, x: ko.x, y: ko.y, type: eo }, "crosshair")), null !== Eo && (Go.crosshair = (0, import_jsx_runtime6.jsx)(P, { width: lo, height: so, x: Eo.x, y: Eo.y, type: Je2 }, "crosshair"))), Ge2 && Fe2 && false === Je2 && (Go.mesh = (0, import_jsx_runtime6.jsx)(fe, { points: So, width: lo, height: so, margin: ao, current: ko, setCurrent: Bo, onMouseEnter: Ye2, onMouseMove: Re2, onMouseLeave: Ie, onClick: Ae, onTouchStart: He2, onTouchMove: Xe2, onTouchEnd: ze, tooltip: De2, enableTouchCrosshair: io, debug: Ve2 }, "mesh")), (0, import_jsx_runtime6.jsx)(gn, { defs: Po, width: co, height: uo, margin: ao, role: no, children: h2.map(function(o6, i6) {
    return "function" == typeof o6 ? (0, import_jsx_runtime6.jsx)(import_react21.Fragment, { children: o6(Q2({}, e11, { innerWidth: lo, innerHeight: so, series: go, slices: bo, points: So, xScale: yo, yScale: xo, lineGenerator: po, areaGenerator: mo, currentPoint: ko, setCurrentPoint: Bo, currentSlice: Eo, setCurrentSlice: Lo })) }, i6) : Go[o6];
  }) });
});
var pe = function(e11) {
  return (0, import_jsx_runtime6.jsx)(It, { children: function(o5) {
    var i6 = o5.width, t8 = o5.height;
    return (0, import_jsx_runtime6.jsx)(ve, Q2({ width: i6, height: t8 }, e11));
  } });
};
var me = On(function(e11) {
  var o5 = (0, import_react21.useRef)(null), r7 = e11.width, a5 = e11.height, u6 = e11.margin, d3 = e11.pixelRatio, h2 = void 0 === d3 ? "undefined" != typeof window && window.devicePixelRatio || 1 : d3, f3 = e11.data, v6 = e11.xScale, p4 = void 0 === v6 ? { type: "point" } : v6, m5 = e11.xFormat, y4 = e11.yScale, x4 = void 0 === y4 ? { type: "linear", min: 0, max: "auto" } : y4, b5 = e11.yFormat, S3 = e11.curve, w5 = void 0 === S3 ? "linear" : S3, T4 = e11.layers, k5 = void 0 === T4 ? ["grid", "markers", "axes", "areas", "crosshair", "lines", "points", "slices", "mesh", "legends"] : T4, B4 = e11.colors, L3 = void 0 === B4 ? { scheme: "nivo" } : B4, P4 = e11.lineWidth, F2 = void 0 === P4 ? 2 : P4, O5 = e11.enableArea, Y4 = void 0 !== O5 && O5, R = e11.areaBaselineValue, I2 = void 0 === R ? 0 : R, A3 = e11.areaOpacity, H4 = void 0 === A3 ? 0.2 : A3, z6 = e11.enablePoints, j3 = void 0 === z6 || z6, D3 = e11.pointSize, q2 = void 0 === D3 ? 6 : D3, Z2 = e11.pointColor, $3 = void 0 === Z2 ? { from: "color" } : Z2, _3 = e11.pointBorderWidth, ee2 = void 0 === _3 ? 0 : _3, oe2 = e11.pointBorderColor, ie2 = void 0 === oe2 ? { theme: "background" } : oe2, te2 = e11.enableGridX, ne2 = void 0 === te2 || te2, ae2 = e11.gridXValues, le2 = e11.enableGridY, se2 = void 0 === le2 || le2, ce3 = e11.gridYValues, ue2 = e11.axisTop, de3 = e11.axisRight, he2 = e11.axisBottom, fe2 = void 0 === he2 ? {} : he2, ve2 = e11.axisLeft, pe2 = void 0 === ve2 ? {} : ve2, me2 = e11.legends, ge2 = void 0 === me2 ? [] : me2, ye2 = e11.isInteractive, xe = void 0 === ye2 || ye2, be = e11.debugMesh, Se = void 0 !== be && be, Me2 = e11.onMouseLeave, Ce2 = e11.onClick, we2 = e11.tooltip, Te2 = void 0 === we2 ? U : we2, ke2 = e11.canvasRef, Be2 = wt(r7, a5, u6), We2 = Be2.margin, Ee = Be2.innerWidth, Le2 = Be2.innerHeight, Ge2 = Be2.outerWidth, Pe2 = Be2.outerHeight, Fe2 = zt(), Oe2 = (0, import_react21.useState)(null), Ve2 = Oe2[0], Ye2 = Oe2[1], Re2 = re3({ data: f3, xScale: p4, xFormat: m5, yScale: x4, yFormat: b5, width: Ee, height: Le2, colors: L3, curve: w5, areaBaselineValue: I2, pointColor: $3, pointBorderColor: ie2 }), Ie = Re2.lineGenerator, Ae = Re2.areaGenerator, He2 = Re2.series, Xe2 = Re2.xScale, ze = Re2.yScale, je2 = Re2.points, De2 = W4({ points: je2, width: Ee, height: Le2, debug: Se }), qe2 = De2.delaunay, Je2 = De2.voronoi;
  (0, import_react21.useEffect)(function() {
    ke2 && (ke2.current = o5.current), o5.current.width = Ge2 * h2, o5.current.height = Pe2 * h2;
    var e12 = o5.current.getContext("2d");
    e12.scale(h2, h2), e12.fillStyle = Fe2.background, e12.fillRect(0, 0, Ge2, Pe2), e12.translate(We2.left, We2.top), k5.forEach(function(o6) {
      if ("function" == typeof o6 && o6({ ctx: e12, innerWidth: Ee, innerHeight: Le2, series: He2, points: je2, xScale: Xe2, yScale: ze, lineWidth: F2, lineGenerator: Ie, areaGenerator: Ae, currentPoint: Ve2, setCurrentPoint: Ye2 }), "grid" === o6 && Fe2.grid.line.strokeWidth > 0 && (e12.lineWidth = Fe2.grid.line.strokeWidth, e12.strokeStyle = Fe2.grid.line.stroke, ne2 && z3(e12, { width: Ee, height: Le2, scale: Xe2, axis: "x", values: ae2 }), se2 && z3(e12, { width: Ee, height: Le2, scale: ze, axis: "y", values: ce3 })), "axes" === o6 && j2(e12, { xScale: Xe2, yScale: ze, width: Ee, height: Le2, top: ue2, right: de3, bottom: fe2, left: pe2, theme: Fe2 }), "areas" === o6 && true === Y4) {
        e12.save(), e12.globalAlpha = H4, Ae.context(e12);
        for (var i6 = He2.length - 1; i6 >= 0; i6--) e12.fillStyle = He2[i6].color, e12.beginPath(), Ae(He2[i6].data.map(function(e13) {
          return e13.position;
        })), e12.fill();
        e12.restore();
      }
      if ("lines" === o6 && (Ie.context(e12), He2.forEach(function(o7) {
        e12.strokeStyle = o7.color, e12.lineWidth = F2, e12.beginPath(), Ie(o7.data.map(function(e13) {
          return e13.position;
        })), e12.stroke();
      })), "points" === o6 && true === j3 && q2 > 0 && je2.forEach(function(o7) {
        e12.fillStyle = o7.color, e12.beginPath(), e12.arc(o7.x, o7.y, q2 / 2, 0, 2 * Math.PI), e12.fill(), ee2 > 0 && (e12.strokeStyle = o7.borderColor, e12.lineWidth = ee2, e12.stroke());
      }), "mesh" === o6 && true === Se && (z4(e12, Je2), Ve2 && H2(e12, Je2, Ve2.index)), "legends" === o6) {
        var t8 = He2.map(function(e13) {
          return { id: e13.id, label: e13.id, color: e13.color };
        }).reverse();
        ge2.forEach(function(o7) {
          H(e12, Q2({}, o7, { data: o7.data || t8, containerWidth: Ee, containerHeight: Le2, theme: Fe2 }));
        });
      }
    });
  }, [o5, Ge2, Pe2, k5, Fe2, Ie, He2, Xe2, ze, ne2, ae2, se2, ce3, ue2, de3, fe2, pe2, ge2, je2, j3, q2, Ve2]);
  var Ke2 = (0, import_react21.useCallback)(function(e12) {
    var i6 = Sn(o5.current, e12), t8 = i6[0], n7 = i6[1];
    if (!jn(We2.left, We2.top, Ee, Le2, t8, n7)) return null;
    var r8 = qe2.find(t8 - We2.left, n7 - We2.top);
    return je2[r8];
  }, [o5, We2, Ee, Le2, qe2]), Ne2 = k(), Qe3 = Ne2.showTooltipFromEvent, Ue2 = Ne2.hideTooltip, Ze2 = (0, import_react21.useCallback)(function(e12) {
    var o6 = Ke2(e12);
    Ye2(o6), o6 ? Qe3((0, import_react21.createElement)(Te2, { point: o6 }), e12) : Ue2();
  }, [Ke2, Ye2, Qe3, Ue2, Te2]), $e2 = (0, import_react21.useCallback)(function(e12) {
    Ue2(), Ye2(null), Ve2 && Me2 && Me2(Ve2, e12);
  }, [Ue2, Ye2, Me2]), _e = (0, import_react21.useCallback)(function(e12) {
    if (Ce2) {
      var o6 = Ke2(e12);
      o6 && Ce2(o6, e12);
    }
  }, [Ke2, Ce2]);
  return (0, import_jsx_runtime6.jsx)("canvas", { ref: o5, width: Ge2 * h2, height: Pe2 * h2, style: { width: Ge2, height: Pe2, cursor: xe ? "auto" : "normal" }, onMouseEnter: xe ? Ze2 : void 0, onMouseMove: xe ? Ze2 : void 0, onMouseLeave: xe ? $e2 : void 0, onClick: xe ? _e : void 0 });
});
var ge = (0, import_react21.forwardRef)(function(e11, o5) {
  return (0, import_jsx_runtime6.jsx)(me, Q2({}, e11, { canvasRef: o5 }));
});
var ye = (0, import_react21.forwardRef)(function(e11, o5) {
  return (0, import_jsx_runtime6.jsx)(It, { children: function(i6) {
    var t8 = i6.width, n7 = i6.height;
    return (0, import_jsx_runtime6.jsx)(ge, Q2({ width: t8, height: n7 }, e11, { ref: o5 }));
  } });
});
export {
  ne as LINE_UNIQUE_ID_PREFIX,
  ve as Line,
  ge as LineCanvas,
  ee as LineCanvasDefaultProps,
  _2 as LineDefaultProps,
  pe as ResponsiveLine,
  ye as ResponsiveLineCanvas,
  ie as useAreaGenerator,
  re3 as useLine,
  oe as useLineGenerator,
  te as useSlices
};
//# sourceMappingURL=@nivo_line.js.map
