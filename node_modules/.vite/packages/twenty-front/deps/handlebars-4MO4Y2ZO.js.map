{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/handlebars/handlebars.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{{!--\", \"--}}\"]\n  },\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/\\{\\{!--/, \"comment.block.start.handlebars\", \"@commentBlock\"],\n      [/\\{\\{!/, \"comment.start.handlebars\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@handlebarsInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@commentHtml\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    doctype: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.comment\"\n        }\n      ],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/\\}\\}/, \"comment.end.handlebars\", \"@pop\"],\n      [/./, \"comment.content.handlebars\"]\n    ],\n    commentBlock: [\n      [/--\\}\\}/, \"comment.block.end.handlebars\", \"@pop\"],\n      [/./, \"comment.content.handlebars\"]\n    ],\n    commentHtml: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.comment\"\n        }\n      ],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.script\"\n        }\n      ],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.style\"\n        }\n      ],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    handlebarsInSimpleState: [\n      [/\\{\\{\\{?/, \"delimiter.handlebars\"],\n      [/\\}\\}\\}?/, { token: \"delimiter.handlebars\", switchTo: \"@$S2.$S3\" }],\n      { include: \"handlebarsRoot\" }\n    ],\n    handlebarsInEmbeddedState: [\n      [/\\{\\{\\{?/, \"delimiter.handlebars\"],\n      [\n        /\\}\\}\\}?/,\n        {\n          token: \"delimiter.handlebars\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"handlebarsRoot\" }\n    ],\n    handlebarsRoot: [\n      [/\"[^\"]*\"/, \"string.handlebars\"],\n      [/[#/][^\\s}]+/, \"keyword.helper.handlebars\"],\n      [/else\\b/, \"keyword.helper.handlebars\"],\n      [/[\\s]+/],\n      [/[^}]/, \"variable.parameter.handlebars\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;;;;AAAA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,gBAkBA,MA4CA;AAzFJ;AAAA;AAwBA;AAjBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,SAAS,MAAM;AAAA,MAChC;AAAA,MACA,UAAU;AAAA,QACR,CAAC,QAAQ,KAAK;AAAA,QACd,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,MAAM,IAAI;AAAA,QACX,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,UACE,YAAY,IAAI;AAAA,YACd,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,UACA,WAAW;AAAA,UACX,QAAQ;AAAA,YACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,UAClE;AAAA,QACF;AAAA,QACA;AAAA,UACE,YAAY,IAAI;AAAA,YACd,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,UACA,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,QACnF;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA;AAAA;AAAA,MAGd,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,CAAC,WAAW,kCAAkC,eAAe;AAAA,UAC7D,CAAC,SAAS,4BAA4B,UAAU;AAAA,UAChD,CAAC,QAAQ,EAAE,OAAO,YAAY,UAAU,gCAAgC,CAAC;AAAA,UACzE,CAAC,aAAa,gBAAgB,UAAU;AAAA,UACxC,CAAC,QAAQ,gBAAgB,cAAc;AAAA,UACvC,CAAC,iBAAiB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,UAClE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,UAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,UACxE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,UAC5E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,UAC3E,CAAC,KAAK,gBAAgB;AAAA,UACtB,CAAC,MAAM,gBAAgB;AAAA,UACvB,CAAC,QAAQ;AAAA;AAAA,QAEX;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,SAAS,sBAAsB;AAAA,UAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,QAC9B;AAAA,QACA,SAAS;AAAA,UACP,CAAC,QAAQ,0BAA0B,MAAM;AAAA,UACzC,CAAC,KAAK,4BAA4B;AAAA,QACpC;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,UAAU,gCAAgC,MAAM;AAAA,UACjD,CAAC,KAAK,4BAA4B;AAAA,QACpC;AAAA,QACA,aAAa;AAAA,UACX;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,OAAO,gBAAgB,MAAM;AAAA,UAC9B,CAAC,SAAS,sBAAsB;AAAA,UAChC,CAAC,KAAK,sBAAsB;AAAA,QAC9B;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,kBAAkB,MAAM;AAAA,UACjC,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY;AAAA;AAAA,QAEf;AAAA;AAAA;AAAA,QAGA,QAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,UAC7C,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY;AAAA;AAAA,UAEb;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA;AAAA,QAEA,iBAAiB;AAAA,UACf;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,UAC3C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA;AAAA,QAEA,uBAAuB;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA;AAAA,QAEA,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA,QACA,gBAAgB;AAAA,UACd;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,QACzE;AAAA;AAAA;AAAA;AAAA,QAIA,OAAO;AAAA,UACL;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,UAC5C,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY;AAAA;AAAA,UAEb;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA;AAAA,QAEA,gBAAgB;AAAA,UACd;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,UAC1C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA;AAAA,QAEA,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA;AAAA,QAEA,qBAAqB;AAAA,UACnB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA,QACA,eAAe;AAAA,UACb;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,QACxE;AAAA;AAAA,QAEA,yBAAyB;AAAA,UACvB,CAAC,WAAW,sBAAsB;AAAA,UAClC,CAAC,WAAW,EAAE,OAAO,wBAAwB,UAAU,WAAW,CAAC;AAAA,UACnE,EAAE,SAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,2BAA2B;AAAA,UACzB,CAAC,WAAW,sBAAsB;AAAA,UAClC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,EAAE,SAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,WAAW,mBAAmB;AAAA,UAC/B,CAAC,eAAe,2BAA2B;AAAA,UAC3C,CAAC,UAAU,2BAA2B;AAAA,UACtC,CAAC,OAAO;AAAA,UACR,CAAC,QAAQ,+BAA+B;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}