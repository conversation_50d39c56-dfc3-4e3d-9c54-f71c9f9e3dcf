{"version": 3, "sources": ["../../../../@tiptap/extension-hard-break/src/hard-break.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean,\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [\n      { tag: 'br' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak: () => ({\n        commands,\n        chain,\n        state,\n        editor,\n      }) => {\n        return commands.first([\n          () => commands.exitCode(),\n          () => commands.command(() => {\n            const { selection, storedMarks } = state\n\n            if (selection.$from.parent.type.spec.isolating) {\n              return false\n            }\n\n            const { keepMarks } = this.options\n            const { splittableMarks } = editor.extensionManager\n            const marks = storedMarks\n              || (selection.$to.parentOffset && selection.$from.marks())\n\n            return chain()\n              .insertContent({ type: this.name })\n              .command(({ tr, dispatch }) => {\n                if (dispatch && marks && keepMarks) {\n                  const filteredMarks = marks\n                    .filter(mark => splittableMarks.includes(mark.type.name))\n\n                  tr.ensureMarks(filteredMarks)\n                }\n\n                return true\n              })\n              .run()\n          }),\n        ])\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;AAkCa,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,WAAW;MACX,gBAAgB,CAAA;;;EAIpB,QAAQ;EAER,OAAO;EAEP,YAAY;EAEZ,sBAAsB;EAEtB,YAAS;AACP,WAAO;MACL,EAAE,KAAK,KAAI;;;EAIf,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;;EAG5E,aAAU;AACR,WAAO;;EAGT,cAAW;AACT,WAAO;MACL,cAAc,MAAM,CAAC,EACnB,UACA,OACA,OACA,OAAM,MACH;AACH,eAAO,SAAS,MAAM;UACpB,MAAM,SAAS,SAAQ;UACvB,MAAM,SAAS,QAAQ,MAAK;AAC1B,kBAAM,EAAE,WAAW,YAAW,IAAK;AAEnC,gBAAI,UAAU,MAAM,OAAO,KAAK,KAAK,WAAW;AAC9C,qBAAO;;AAGT,kBAAM,EAAE,UAAS,IAAK,KAAK;AAC3B,kBAAM,EAAE,gBAAe,IAAK,OAAO;AACnC,kBAAM,QAAQ,eACR,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAK;AAEzD,mBAAO,MAAK,EACT,cAAc,EAAE,MAAM,KAAK,KAAI,CAAE,EACjC,QAAQ,CAAC,EAAE,IAAI,SAAQ,MAAM;AAC5B,kBAAI,YAAY,SAAS,WAAW;AAClC,sBAAM,gBAAgB,MACnB,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAE1D,mBAAG,YAAY,aAAa;;AAG9B,qBAAO;YACT,CAAC,EACA,IAAG;UACR,CAAC;QACF,CAAA;;;;EAKP,uBAAoB;AAClB,WAAO;MACL,aAAa,MAAM,KAAK,OAAO,SAAS,aAAY;MACpD,eAAe,MAAM,KAAK,OAAO,SAAS,aAAY;;;AAG3D,CAAA;", "names": []}