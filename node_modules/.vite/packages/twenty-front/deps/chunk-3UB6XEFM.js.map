{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/Sidebar/Actions/EditSidebarListElement.vue.js"], "sourcesContent": ["import { defineComponent as a, ref as i, openBlock as u, createBlock as s, withCtx as d, createVNode as p, unref as f } from \"vue\";\nimport { ScalarTextField as c } from \"@scalar/components\";\nimport v from \"./SidebarListElementForm.vue.js\";\nconst C = /* @__PURE__ */ a({\n  __name: \"EditSidebarListElement\",\n  props: {\n    name: {}\n  },\n  emits: [\"close\", \"edit\"],\n  setup(l, { emit: m }) {\n    const r = l, n = m, t = i(r.name);\n    return (x, e) => (u(), s(v, {\n      onCancel: e[1] || (e[1] = (o) => n(\"close\")),\n      onSubmit: e[2] || (e[2] = (o) => n(\"edit\", t.value))\n    }, {\n      default: d(() => [\n        p(f(c), {\n          modelValue: t.value,\n          \"onUpdate:modelValue\": e[0] || (e[0] = (o) => t.value = o),\n          autofocus: \"\"\n        }, null, 8, [\"modelValue\"])\n      ]),\n      _: 1\n    }));\n  }\n});\nexport {\n  C as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAGA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM,CAAC;AAAA,EACT;AAAA,EACA,OAAO,CAAC,SAAS,MAAM;AAAA,EACvB,MAAM,GAAG,EAAE,MAAMA,GAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAIA,IAAG,IAAI,IAAE,EAAE,IAAI;AAChC,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG;AAAA,MAC1B,UAAU,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;AAAA,MAC1C,UAAU,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK;AAAA,IACpD,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,YAAE,MAAE,CAAC,GAAG;AAAA,UACN,YAAY,EAAE;AAAA,UACd,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,UACxD,WAAW;AAAA,QACb,GAAG,MAAM,GAAG,CAAC,YAAY,CAAC;AAAA,MAC5B,CAAC;AAAA,MACD,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": ["m"]}