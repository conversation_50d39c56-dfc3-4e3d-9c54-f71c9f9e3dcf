{"version": 3, "sources": ["../../../../unraw/dist/errors.js", "../../../../unraw/dist/index.js", "../../../../moo/moo.js", "../../../../@messageformat/parser/lib/lexer.js", "../../../../@messageformat/parser/lib/parser.js", "../../../../@lingui/core/dist/index.mjs", "../../../../@lingui/message-utils/dist/compileMessage.mjs"], "sourcesContent": ["\"use strict\";\n// NOTE: don't construct errors here or they'll have the wrong stack trace.\n// NOTE: don't make custom error class; the JS engines use `SyntaxError`\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.errorMessages = exports.ErrorType = void 0;\n/**\n * Keys for possible error messages used by `unraw`.\n * Note: These do _not_ map to actual error object types. All errors thrown\n * are `SyntaxError`.\n */\n// Don't use const enum or JS users won't be able to access the enum values\nvar ErrorType;\n(function (ErrorType) {\n    /**\n     * Thrown when a badly formed Unicode escape sequence is found. Possible\n     * reasons include the code being too short (`\"\\u25\"`) or having invalid\n     * characters (`\"\\u2$A5\"`).\n     */\n    ErrorType[\"MalformedUnicode\"] = \"MALFORMED_UNICODE\";\n    /**\n     * Thrown when a badly formed hexadecimal escape sequence is found. Possible\n     * reasons include the code being too short (`\"\\x2\"`) or having invalid\n     * characters (`\"\\x2$\"`).\n     */\n    ErrorType[\"MalformedHexadecimal\"] = \"MALFORMED_HEXADECIMAL\";\n    /**\n     * Thrown when a Unicode code point escape sequence has too high of a code\n     * point. The maximum code point allowed is `\\u{10FFFF}`, so `\\u{110000}` and\n     * higher will throw this error.\n     */\n    ErrorType[\"CodePointLimit\"] = \"CODE_POINT_LIMIT\";\n    /**\n     * Thrown when an octal escape sequences is encountered and `allowOctals` is\n     * `false`. For example, `unraw(\"\\234\", false)`.\n     */\n    ErrorType[\"OctalDeprecation\"] = \"OCTAL_DEPRECATION\";\n    /**\n     * Thrown only when a single backslash is found at the end of a string. For\n     * example, `\"\\\\\"` or `\"test\\\\x24\\\\\"`.\n     */\n    ErrorType[\"EndOfString\"] = \"END_OF_STRING\";\n})(ErrorType = exports.ErrorType || (exports.ErrorType = {}));\n/** Map of error message names to the full text of the message. */\nexports.errorMessages = new Map([\n    [ErrorType.MalformedUnicode, \"malformed Unicode character escape sequence\"],\n    [\n        ErrorType.MalformedHexadecimal,\n        \"malformed hexadecimal character escape sequence\"\n    ],\n    [\n        ErrorType.CodePointLimit,\n        \"Unicode codepoint must not be greater than 0x10FFFF in escape sequence\"\n    ],\n    [\n        ErrorType.OctalDeprecation,\n        '\"0\"-prefixed octal literals and octal escape sequences are deprecated; ' +\n            'for octal literals use the \"0o\" prefix instead'\n    ],\n    [ErrorType.EndOfString, \"malformed escape sequence at end of string\"]\n]);\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unraw = exports.errorMessages = exports.ErrorType = void 0;\nconst errors_1 = require(\"./errors\");\nObject.defineProperty(exports, \"ErrorType\", { enumerable: true, get: function () { return errors_1.ErrorType; } });\nObject.defineProperty(exports, \"errorMessages\", { enumerable: true, get: function () { return errors_1.errorMessages; } });\n/**\n * Parse a string as a base-16 number. This is more strict than `parseInt` as it\n * will not allow any other characters, including (for example) \"+\", \"-\", and\n * \".\".\n * @param hex A string containing a hexadecimal number.\n * @returns The parsed integer, or `NaN` if the string is not a valid hex\n * number.\n */\nfunction parseHexToInt(hex) {\n    const isOnlyHexChars = !hex.match(/[^a-f0-9]/i);\n    return isOnlyHexChars ? parseInt(hex, 16) : NaN;\n}\n/**\n * Check the validity and length of a hexadecimal code and optionally enforces\n * a specific number of hex digits.\n * @param hex The string to validate and parse.\n * @param errorName The name of the error message to throw a `SyntaxError` with\n * if `hex` is invalid. This is used to index `errorMessages`.\n * @param enforcedLength If provided, will throw an error if `hex` is not\n * exactly this many characters.\n * @returns The parsed hex number as a normal number.\n * @throws {SyntaxError} If the code is not valid.\n */\nfunction validateAndParseHex(hex, errorName, enforcedLength) {\n    const parsedHex = parseHexToInt(hex);\n    if (Number.isNaN(parsedHex) ||\n        (enforcedLength !== undefined && enforcedLength !== hex.length)) {\n        throw new SyntaxError(errors_1.errorMessages.get(errorName));\n    }\n    return parsedHex;\n}\n/**\n * Parse a two-digit hexadecimal character escape code.\n * @param code The two-digit hexadecimal number that represents the character to\n * output.\n * @returns The single character represented by the code.\n * @throws {SyntaxError} If the code is not valid hex or is not the right\n * length.\n */\nfunction parseHexadecimalCode(code) {\n    const parsedCode = validateAndParseHex(code, errors_1.ErrorType.MalformedHexadecimal, 2);\n    return String.fromCharCode(parsedCode);\n}\n/**\n * Parse a four-digit Unicode character escape code.\n * @param code The four-digit unicode number that represents the character to\n * output.\n * @param surrogateCode Optional four-digit unicode surrogate that represents\n * the other half of the character to output.\n * @returns The single character represented by the code.\n * @throws {SyntaxError} If the codes are not valid hex or are not the right\n * length.\n */\nfunction parseUnicodeCode(code, surrogateCode) {\n    const parsedCode = validateAndParseHex(code, errors_1.ErrorType.MalformedUnicode, 4);\n    if (surrogateCode !== undefined) {\n        const parsedSurrogateCode = validateAndParseHex(surrogateCode, errors_1.ErrorType.MalformedUnicode, 4);\n        return String.fromCharCode(parsedCode, parsedSurrogateCode);\n    }\n    return String.fromCharCode(parsedCode);\n}\n/**\n * Test if the text is surrounded by curly braces (`{}`).\n * @param text Text to check.\n * @returns `true` if the text is in the form `{*}`.\n */\nfunction isCurlyBraced(text) {\n    return text.charAt(0) === \"{\" && text.charAt(text.length - 1) === \"}\";\n}\n/**\n * Parse a Unicode code point character escape code.\n * @param codePoint A unicode escape code point, including the surrounding curly\n * braces.\n * @returns The single character represented by the code.\n * @throws {SyntaxError} If the code is not valid hex or does not have the\n * surrounding curly braces.\n */\nfunction parseUnicodeCodePointCode(codePoint) {\n    if (!isCurlyBraced(codePoint)) {\n        throw new SyntaxError(errors_1.errorMessages.get(errors_1.ErrorType.MalformedUnicode));\n    }\n    const withoutBraces = codePoint.slice(1, -1);\n    const parsedCode = validateAndParseHex(withoutBraces, errors_1.ErrorType.MalformedUnicode);\n    try {\n        return String.fromCodePoint(parsedCode);\n    }\n    catch (err) {\n        throw err instanceof RangeError\n            ? new SyntaxError(errors_1.errorMessages.get(errors_1.ErrorType.CodePointLimit))\n            : err;\n    }\n}\n// Have to give overload that takes boolean for when compiler doesn't know if\n// true or false\nfunction parseOctalCode(code, error = false) {\n    if (error) {\n        throw new SyntaxError(errors_1.errorMessages.get(errors_1.ErrorType.OctalDeprecation));\n    }\n    // The original regex only allows digits so we don't need to have a strict\n    // octal parser like hexToInt. Length is not enforced for octals.\n    const parsedCode = parseInt(code, 8);\n    return String.fromCharCode(parsedCode);\n}\n/**\n * Map of unescaped letters to their corresponding special JS escape characters.\n * Intentionally does not include characters that map to themselves like \"\\'\".\n */\nconst singleCharacterEscapes = new Map([\n    [\"b\", \"\\b\"],\n    [\"f\", \"\\f\"],\n    [\"n\", \"\\n\"],\n    [\"r\", \"\\r\"],\n    [\"t\", \"\\t\"],\n    [\"v\", \"\\v\"],\n    [\"0\", \"\\0\"]\n]);\n/**\n * Parse a single character escape sequence and return the matching character.\n * If none is matched, defaults to `code`.\n * @param code A single character code.\n */\nfunction parseSingleCharacterCode(code) {\n    return singleCharacterEscapes.get(code) || code;\n}\n/**\n * Matches every escape sequence possible, including invalid ones.\n *\n * All capture groups (described below) are unique (only one will match), except\n * for 4, which can only potentially match if 3 does.\n *\n * **Capture Groups:**\n * 0. A single backslash\n * 1. Hexadecimal code\n * 2. Unicode code point code with surrounding curly braces\n * 3. Unicode escape code with surrogate\n * 4. Surrogate code\n * 5. Unicode escape code without surrogate\n * 6. Octal code _NOTE: includes \"0\"._\n * 7. A single character (will never be \\, x, u, or 0-3)\n */\nconst escapeMatch = /\\\\(?:(\\\\)|x([\\s\\S]{0,2})|u(\\{[^}]*\\}?)|u([\\s\\S]{4})\\\\u([^{][\\s\\S]{0,3})|u([\\s\\S]{0,4})|([0-3]?[0-7]{1,2})|([\\s\\S])|$)/g;\n/**\n * Replace raw escape character strings with their escape characters.\n * @param raw A string where escape characters are represented as raw string\n * values like `\\'` rather than `'`.\n * @param allowOctals If `true`, will process the now-deprecated octal escape\n * sequences (ie, `\\111`).\n * @returns The processed string, with escape characters replaced by their\n * respective actual Unicode characters.\n */\nfunction unraw(raw, allowOctals = false) {\n    return raw.replace(escapeMatch, function (_, backslash, hex, codePoint, unicodeWithSurrogate, surrogate, unicode, octal, singleCharacter) {\n        // Compare groups to undefined because empty strings mean different errors\n        // Otherwise, `\\u` would fail the same as `\\` which is wrong.\n        if (backslash !== undefined) {\n            return \"\\\\\";\n        }\n        if (hex !== undefined) {\n            return parseHexadecimalCode(hex);\n        }\n        if (codePoint !== undefined) {\n            return parseUnicodeCodePointCode(codePoint);\n        }\n        if (unicodeWithSurrogate !== undefined) {\n            return parseUnicodeCode(unicodeWithSurrogate, surrogate);\n        }\n        if (unicode !== undefined) {\n            return parseUnicodeCode(unicode);\n        }\n        if (octal === \"0\") {\n            return \"\\0\";\n        }\n        if (octal !== undefined) {\n            return parseOctalCode(octal, !allowOctals);\n        }\n        if (singleCharacter !== undefined) {\n            return parseSingleCharacterCode(singleCharacter);\n        }\n        throw new SyntaxError(errors_1.errorMessages.get(errors_1.ErrorType.EndOfString));\n    });\n}\nexports.unraw = unraw;\nexports.default = unraw;\n", "(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    define([], factory) /* global define */\n  } else if (typeof module === 'object' && module.exports) {\n    module.exports = factory()\n  } else {\n    root.moo = factory()\n  }\n}(this, function() {\n  'use strict';\n\n  var hasOwnProperty = Object.prototype.hasOwnProperty\n  var toString = Object.prototype.toString\n  var hasSticky = typeof new RegExp().sticky === 'boolean'\n\n  /***************************************************************************/\n\n  function isRegExp(o) { return o && toString.call(o) === '[object RegExp]' }\n  function isObject(o) { return o && typeof o === 'object' && !isRegExp(o) && !Array.isArray(o) }\n\n  function reEscape(s) {\n    return s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n  }\n  function reGroups(s) {\n    var re = new RegExp('|' + s)\n    return re.exec('').length - 1\n  }\n  function reCapture(s) {\n    return '(' + s + ')'\n  }\n  function reUnion(regexps) {\n    if (!regexps.length) return '(?!)'\n    var source =  regexps.map(function(s) {\n      return \"(?:\" + s + \")\"\n    }).join('|')\n    return \"(?:\" + source + \")\"\n  }\n\n  function regexpOrLiteral(obj) {\n    if (typeof obj === 'string') {\n      return '(?:' + reEscape(obj) + ')'\n\n    } else if (isRegExp(obj)) {\n      // TODO: consider /u support\n      if (obj.ignoreCase) throw new Error('RegExp /i flag not allowed')\n      if (obj.global) throw new Error('RegExp /g flag is implied')\n      if (obj.sticky) throw new Error('RegExp /y flag is implied')\n      if (obj.multiline) throw new Error('RegExp /m flag is implied')\n      return obj.source\n\n    } else {\n      throw new Error('Not a pattern: ' + obj)\n    }\n  }\n\n  function pad(s, length) {\n    if (s.length > length) {\n      return s\n    }\n    return Array(length - s.length + 1).join(\" \") + s\n  }\n\n  function lastNLines(string, numLines) {\n    var position = string.length\n    var lineBreaks = 0;\n    while (true) {\n      var idx = string.lastIndexOf(\"\\n\", position - 1)\n      if (idx === -1) {\n        break;\n      } else {\n        lineBreaks++\n      }\n      position = idx\n      if (lineBreaks === numLines) {\n        break;\n      }\n      if (position === 0) {\n        break;\n      }\n    }\n    var startPosition = \n      lineBreaks < numLines ?\n      0 : \n      position + 1\n    return string.substring(startPosition).split(\"\\n\")\n  }\n\n  function objectToRules(object) {\n    var keys = Object.getOwnPropertyNames(object)\n    var result = []\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      var thing = object[key]\n      var rules = [].concat(thing)\n      if (key === 'include') {\n        for (var j = 0; j < rules.length; j++) {\n          result.push({include: rules[j]})\n        }\n        continue\n      }\n      var match = []\n      rules.forEach(function(rule) {\n        if (isObject(rule)) {\n          if (match.length) result.push(ruleOptions(key, match))\n          result.push(ruleOptions(key, rule))\n          match = []\n        } else {\n          match.push(rule)\n        }\n      })\n      if (match.length) result.push(ruleOptions(key, match))\n    }\n    return result\n  }\n\n  function arrayToRules(array) {\n    var result = []\n    for (var i = 0; i < array.length; i++) {\n      var obj = array[i]\n      if (obj.include) {\n        var include = [].concat(obj.include)\n        for (var j = 0; j < include.length; j++) {\n          result.push({include: include[j]})\n        }\n        continue\n      }\n      if (!obj.type) {\n        throw new Error('Rule has no type: ' + JSON.stringify(obj))\n      }\n      result.push(ruleOptions(obj.type, obj))\n    }\n    return result\n  }\n\n  function ruleOptions(type, obj) {\n    if (!isObject(obj)) {\n      obj = { match: obj }\n    }\n    if (obj.include) {\n      throw new Error('Matching rules cannot also include states')\n    }\n\n    // nb. error and fallback imply lineBreaks\n    var options = {\n      defaultType: type,\n      lineBreaks: !!obj.error || !!obj.fallback,\n      pop: false,\n      next: null,\n      push: null,\n      error: false,\n      fallback: false,\n      value: null,\n      type: null,\n      shouldThrow: false,\n    }\n\n    // Avoid Object.assign(), so we support IE9+\n    for (var key in obj) {\n      if (hasOwnProperty.call(obj, key)) {\n        options[key] = obj[key]\n      }\n    }\n\n    // type transform cannot be a string\n    if (typeof options.type === 'string' && type !== options.type) {\n      throw new Error(\"Type transform cannot be a string (type '\" + options.type + \"' for token '\" + type + \"')\")\n    }\n\n    // convert to array\n    var match = options.match\n    options.match = Array.isArray(match) ? match : match ? [match] : []\n    options.match.sort(function(a, b) {\n      return isRegExp(a) && isRegExp(b) ? 0\n           : isRegExp(b) ? -1 : isRegExp(a) ? +1 : b.length - a.length\n    })\n    return options\n  }\n\n  function toRules(spec) {\n    return Array.isArray(spec) ? arrayToRules(spec) : objectToRules(spec)\n  }\n\n  var defaultErrorRule = ruleOptions('error', {lineBreaks: true, shouldThrow: true})\n  function compileRules(rules, hasStates) {\n    var errorRule = null\n    var fast = Object.create(null)\n    var fastAllowed = true\n    var unicodeFlag = null\n    var groups = []\n    var parts = []\n\n    // If there is a fallback rule, then disable fast matching\n    for (var i = 0; i < rules.length; i++) {\n      if (rules[i].fallback) {\n        fastAllowed = false\n      }\n    }\n\n    for (var i = 0; i < rules.length; i++) {\n      var options = rules[i]\n\n      if (options.include) {\n        // all valid inclusions are removed by states() preprocessor\n        throw new Error('Inheritance is not allowed in stateless lexers')\n      }\n\n      if (options.error || options.fallback) {\n        // errorRule can only be set once\n        if (errorRule) {\n          if (!options.fallback === !errorRule.fallback) {\n            throw new Error(\"Multiple \" + (options.fallback ? \"fallback\" : \"error\") + \" rules not allowed (for token '\" + options.defaultType + \"')\")\n          } else {\n            throw new Error(\"fallback and error are mutually exclusive (for token '\" + options.defaultType + \"')\")\n          }\n        }\n        errorRule = options\n      }\n\n      var match = options.match.slice()\n      if (fastAllowed) {\n        while (match.length && typeof match[0] === 'string' && match[0].length === 1) {\n          var word = match.shift()\n          fast[word.charCodeAt(0)] = options\n        }\n      }\n\n      // Warn about inappropriate state-switching options\n      if (options.pop || options.push || options.next) {\n        if (!hasStates) {\n          throw new Error(\"State-switching options are not allowed in stateless lexers (for token '\" + options.defaultType + \"')\")\n        }\n        if (options.fallback) {\n          throw new Error(\"State-switching options are not allowed on fallback tokens (for token '\" + options.defaultType + \"')\")\n        }\n      }\n\n      // Only rules with a .match are included in the RegExp\n      if (match.length === 0) {\n        continue\n      }\n      fastAllowed = false\n\n      groups.push(options)\n\n      // Check unicode flag is used everywhere or nowhere\n      for (var j = 0; j < match.length; j++) {\n        var obj = match[j]\n        if (!isRegExp(obj)) {\n          continue\n        }\n\n        if (unicodeFlag === null) {\n          unicodeFlag = obj.unicode\n        } else if (unicodeFlag !== obj.unicode && options.fallback === false) {\n          throw new Error('If one rule is /u then all must be')\n        }\n      }\n\n      // convert to RegExp\n      var pat = reUnion(match.map(regexpOrLiteral))\n\n      // validate\n      var regexp = new RegExp(pat)\n      if (regexp.test(\"\")) {\n        throw new Error(\"RegExp matches empty string: \" + regexp)\n      }\n      var groupCount = reGroups(pat)\n      if (groupCount > 0) {\n        throw new Error(\"RegExp has capture groups: \" + regexp + \"\\nUse (?: … ) instead\")\n      }\n\n      // try and detect rules matching newlines\n      if (!options.lineBreaks && regexp.test('\\n')) {\n        throw new Error('Rule should declare lineBreaks: ' + regexp)\n      }\n\n      // store regex\n      parts.push(reCapture(pat))\n    }\n\n\n    // If there's no fallback rule, use the sticky flag so we only look for\n    // matches at the current index.\n    //\n    // If we don't support the sticky flag, then fake it using an irrefutable\n    // match (i.e. an empty pattern).\n    var fallbackRule = errorRule && errorRule.fallback\n    var flags = hasSticky && !fallbackRule ? 'ym' : 'gm'\n    var suffix = hasSticky || fallbackRule ? '' : '|'\n\n    if (unicodeFlag === true) flags += \"u\"\n    var combined = new RegExp(reUnion(parts) + suffix, flags)\n    return {regexp: combined, groups: groups, fast: fast, error: errorRule || defaultErrorRule}\n  }\n\n  function compile(rules) {\n    var result = compileRules(toRules(rules))\n    return new Lexer({start: result}, 'start')\n  }\n\n  function checkStateGroup(g, name, map) {\n    var state = g && (g.push || g.next)\n    if (state && !map[state]) {\n      throw new Error(\"Missing state '\" + state + \"' (in token '\" + g.defaultType + \"' of state '\" + name + \"')\")\n    }\n    if (g && g.pop && +g.pop !== 1) {\n      throw new Error(\"pop must be 1 (in token '\" + g.defaultType + \"' of state '\" + name + \"')\")\n    }\n  }\n  function compileStates(states, start) {\n    var all = states.$all ? toRules(states.$all) : []\n    delete states.$all\n\n    var keys = Object.getOwnPropertyNames(states)\n    if (!start) start = keys[0]\n\n    var ruleMap = Object.create(null)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      ruleMap[key] = toRules(states[key]).concat(all)\n    }\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      var rules = ruleMap[key]\n      var included = Object.create(null)\n      for (var j = 0; j < rules.length; j++) {\n        var rule = rules[j]\n        if (!rule.include) continue\n        var splice = [j, 1]\n        if (rule.include !== key && !included[rule.include]) {\n          included[rule.include] = true\n          var newRules = ruleMap[rule.include]\n          if (!newRules) {\n            throw new Error(\"Cannot include nonexistent state '\" + rule.include + \"' (in state '\" + key + \"')\")\n          }\n          for (var k = 0; k < newRules.length; k++) {\n            var newRule = newRules[k]\n            if (rules.indexOf(newRule) !== -1) continue\n            splice.push(newRule)\n          }\n        }\n        rules.splice.apply(rules, splice)\n        j--\n      }\n    }\n\n    var map = Object.create(null)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      map[key] = compileRules(ruleMap[key], true)\n    }\n\n    for (var i = 0; i < keys.length; i++) {\n      var name = keys[i]\n      var state = map[name]\n      var groups = state.groups\n      for (var j = 0; j < groups.length; j++) {\n        checkStateGroup(groups[j], name, map)\n      }\n      var fastKeys = Object.getOwnPropertyNames(state.fast)\n      for (var j = 0; j < fastKeys.length; j++) {\n        checkStateGroup(state.fast[fastKeys[j]], name, map)\n      }\n    }\n\n    return new Lexer(map, start)\n  }\n\n  function keywordTransform(map) {\n\n    // Use a JavaScript Map to map keywords to their corresponding token type\n    // unless Map is unsupported, then fall back to using an Object:\n    var isMap = typeof Map !== 'undefined'\n    var reverseMap = isMap ? new Map : Object.create(null)\n\n    var types = Object.getOwnPropertyNames(map)\n    for (var i = 0; i < types.length; i++) {\n      var tokenType = types[i]\n      var item = map[tokenType]\n      var keywordList = Array.isArray(item) ? item : [item]\n      keywordList.forEach(function(keyword) {\n        if (typeof keyword !== 'string') {\n          throw new Error(\"keyword must be string (in keyword '\" + tokenType + \"')\")\n        }\n        if (isMap) {\n          reverseMap.set(keyword, tokenType)\n        } else {\n          reverseMap[keyword] = tokenType\n        }\n      })\n    }\n    return function(k) {\n      return isMap ? reverseMap.get(k) : reverseMap[k]\n    }\n  }\n\n  /***************************************************************************/\n\n  var Lexer = function(states, state) {\n    this.startState = state\n    this.states = states\n    this.buffer = ''\n    this.stack = []\n    this.reset()\n  }\n\n  Lexer.prototype.reset = function(data, info) {\n    this.buffer = data || ''\n    this.index = 0\n    this.line = info ? info.line : 1\n    this.col = info ? info.col : 1\n    this.queuedToken = info ? info.queuedToken : null\n    this.queuedText = info ? info.queuedText: \"\";\n    this.queuedThrow = info ? info.queuedThrow : null\n    this.setState(info ? info.state : this.startState)\n    this.stack = info && info.stack ? info.stack.slice() : []\n    return this\n  }\n\n  Lexer.prototype.save = function() {\n    return {\n      line: this.line,\n      col: this.col,\n      state: this.state,\n      stack: this.stack.slice(),\n      queuedToken: this.queuedToken,\n      queuedText: this.queuedText,\n      queuedThrow: this.queuedThrow,\n    }\n  }\n\n  Lexer.prototype.setState = function(state) {\n    if (!state || this.state === state) return\n    this.state = state\n    var info = this.states[state]\n    this.groups = info.groups\n    this.error = info.error\n    this.re = info.regexp\n    this.fast = info.fast\n  }\n\n  Lexer.prototype.popState = function() {\n    this.setState(this.stack.pop())\n  }\n\n  Lexer.prototype.pushState = function(state) {\n    this.stack.push(this.state)\n    this.setState(state)\n  }\n\n  var eat = hasSticky ? function(re, buffer) { // assume re is /y\n    return re.exec(buffer)\n  } : function(re, buffer) { // assume re is /g\n    var match = re.exec(buffer)\n    // will always match, since we used the |(?:) trick\n    if (match[0].length === 0) {\n      return null\n    }\n    return match\n  }\n\n  Lexer.prototype._getGroup = function(match) {\n    var groupCount = this.groups.length\n    for (var i = 0; i < groupCount; i++) {\n      if (match[i + 1] !== undefined) {\n        return this.groups[i]\n      }\n    }\n    throw new Error('Cannot find token type for matched text')\n  }\n\n  function tokenToString() {\n    return this.value\n  }\n\n  Lexer.prototype.next = function() {\n    var index = this.index\n\n    // If a fallback token matched, we don't need to re-run the RegExp\n    if (this.queuedGroup) {\n      var token = this._token(this.queuedGroup, this.queuedText, index)\n      this.queuedGroup = null\n      this.queuedText = \"\"\n      return token\n    }\n\n    var buffer = this.buffer\n    if (index === buffer.length) {\n      return // EOF\n    }\n\n    // Fast matching for single characters\n    var group = this.fast[buffer.charCodeAt(index)]\n    if (group) {\n      return this._token(group, buffer.charAt(index), index)\n    }\n\n    // Execute RegExp\n    var re = this.re\n    re.lastIndex = index\n    var match = eat(re, buffer)\n\n    // Error tokens match the remaining buffer\n    var error = this.error\n    if (match == null) {\n      return this._token(error, buffer.slice(index, buffer.length), index)\n    }\n\n    var group = this._getGroup(match)\n    var text = match[0]\n\n    if (error.fallback && match.index !== index) {\n      this.queuedGroup = group\n      this.queuedText = text\n\n      // Fallback tokens contain the unmatched portion of the buffer\n      return this._token(error, buffer.slice(index, match.index), index)\n    }\n\n    return this._token(group, text, index)\n  }\n\n  Lexer.prototype._token = function(group, text, offset) {\n    // count line breaks\n    var lineBreaks = 0\n    if (group.lineBreaks) {\n      var matchNL = /\\n/g\n      var nl = 1\n      if (text === '\\n') {\n        lineBreaks = 1\n      } else {\n        while (matchNL.exec(text)) { lineBreaks++; nl = matchNL.lastIndex }\n      }\n    }\n\n    var token = {\n      type: (typeof group.type === 'function' && group.type(text)) || group.defaultType,\n      value: typeof group.value === 'function' ? group.value(text) : text,\n      text: text,\n      toString: tokenToString,\n      offset: offset,\n      lineBreaks: lineBreaks,\n      line: this.line,\n      col: this.col,\n    }\n    // nb. adding more props to token object will make V8 sad!\n\n    var size = text.length\n    this.index += size\n    this.line += lineBreaks\n    if (lineBreaks !== 0) {\n      this.col = size - nl + 1\n    } else {\n      this.col += size\n    }\n\n    // throw, if no rule with {error: true}\n    if (group.shouldThrow) {\n      var err = new Error(this.formatError(token, \"invalid syntax\"))\n      throw err;\n    }\n\n    if (group.pop) this.popState()\n    else if (group.push) this.pushState(group.push)\n    else if (group.next) this.setState(group.next)\n\n    return token\n  }\n\n  if (typeof Symbol !== 'undefined' && Symbol.iterator) {\n    var LexerIterator = function(lexer) {\n      this.lexer = lexer\n    }\n\n    LexerIterator.prototype.next = function() {\n      var token = this.lexer.next()\n      return {value: token, done: !token}\n    }\n\n    LexerIterator.prototype[Symbol.iterator] = function() {\n      return this\n    }\n\n    Lexer.prototype[Symbol.iterator] = function() {\n      return new LexerIterator(this)\n    }\n  }\n\n  Lexer.prototype.formatError = function(token, message) {\n    if (token == null) {\n      // An undefined token indicates EOF\n      var text = this.buffer.slice(this.index)\n      var token = {\n        text: text,\n        offset: this.index,\n        lineBreaks: text.indexOf('\\n') === -1 ? 0 : 1,\n        line: this.line,\n        col: this.col,\n      }\n    }\n    \n    var numLinesAround = 2\n    var firstDisplayedLine = Math.max(token.line - numLinesAround, 1)\n    var lastDisplayedLine = token.line + numLinesAround\n    var lastLineDigits = String(lastDisplayedLine).length\n    var displayedLines = lastNLines(\n        this.buffer, \n        (this.line - token.line) + numLinesAround + 1\n      )\n      .slice(0, 5)\n    var errorLines = []\n    errorLines.push(message + \" at line \" + token.line + \" col \" + token.col + \":\")\n    errorLines.push(\"\")\n    for (var i = 0; i < displayedLines.length; i++) {\n      var line = displayedLines[i]\n      var lineNo = firstDisplayedLine + i\n      errorLines.push(pad(String(lineNo), lastLineDigits) + \"  \" + line);\n      if (lineNo === token.line) {\n        errorLines.push(pad(\"\", lastLineDigits + token.col + 1) + \"^\")\n      }\n    }\n    return errorLines.join(\"\\n\")\n  }\n\n  Lexer.prototype.clone = function() {\n    return new Lexer(this.states, this.state)\n  }\n\n  Lexer.prototype.has = function(tokenType) {\n    return true\n  }\n\n\n  return {\n    compile: compile,\n    states: compileStates,\n    error: Object.freeze({error: true}),\n    fallback: Object.freeze({fallback: true}),\n    keywords: keywordTransform,\n  }\n\n}));\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.lexer = exports.states = void 0;\nconst moo_1 = __importDefault(require(\"moo\"));\nexports.states = {\n    body: {\n        doubleapos: { match: \"''\", value: () => \"'\" },\n        quoted: {\n            lineBreaks: true,\n            match: /'[{}#](?:[^']|'')*'(?!')/u,\n            value: src => src.slice(1, -1).replace(/''/g, \"'\")\n        },\n        argument: {\n            lineBreaks: true,\n            match: /\\{\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*/u,\n            push: 'arg',\n            value: src => src.substring(1).trim()\n        },\n        octothorpe: '#',\n        end: { match: '}', pop: 1 },\n        content: { lineBreaks: true, match: /[^][^{}#']*/u }\n    },\n    arg: {\n        select: {\n            lineBreaks: true,\n            match: /,\\s*(?:plural|select|selectordinal)\\s*,\\s*/u,\n            next: 'select',\n            value: src => src.split(',')[1].trim()\n        },\n        'func-args': {\n            lineBreaks: true,\n            match: /,\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*,/u,\n            next: 'body',\n            value: src => src.split(',')[1].trim()\n        },\n        'func-simple': {\n            lineBreaks: true,\n            match: /,\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*/u,\n            value: src => src.substring(1).trim()\n        },\n        end: { match: '}', pop: 1 }\n    },\n    select: {\n        offset: {\n            lineBreaks: true,\n            match: /\\s*offset\\s*:\\s*\\d+\\s*/u,\n            value: src => src.split(':')[1].trim()\n        },\n        case: {\n            lineBreaks: true,\n            match: /\\s*(?:=\\d+|[^\\p{Pat_Syn}\\p{Pat_WS}]+)\\s*\\{/u,\n            push: 'body',\n            value: src => src.substring(0, src.indexOf('{')).trim()\n        },\n        end: { match: /\\s*\\}/u, pop: 1 }\n    }\n};\nexports.lexer = moo_1.default.states(exports.states);\n", "\"use strict\";\n/**\n * An AST parser for ICU MessageFormat strings\n *\n * @packageDocumentation\n * @example\n * ```\n * import { parse } from '@messageformat/parser\n *\n * parse('So {wow}.')\n * [ { type: 'content', value: 'So ' },\n *   { type: 'argument', arg: 'wow' },\n *   { type: 'content', value: '.' } ]\n *\n *\n * parse('Such { thing }. { count, selectordinal, one {First} two {Second}' +\n *       '                  few {Third} other {#th} } word.')\n * [ { type: 'content', value: 'Such ' },\n *   { type: 'argument', arg: 'thing' },\n *   { type: 'content', value: '. ' },\n *   { type: 'selectordinal',\n *     arg: 'count',\n *     cases: [\n *       { key: 'one', tokens: [ { type: 'content', value: 'First' } ] },\n *       { key: 'two', tokens: [ { type: 'content', value: 'Second' } ] },\n *       { key: 'few', tokens: [ { type: 'content', value: 'Third' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'octothorpe' }, { type: 'content', value: 'th' } ] }\n *     ] },\n *   { type: 'content', value: ' word.' } ]\n *\n *\n * parse('Many{type,select,plural{ numbers}selectordinal{ counting}' +\n *                          'select{ choices}other{ some {type}}}.')\n * [ { type: 'content', value: 'Many' },\n *   { type: 'select',\n *     arg: 'type',\n *     cases: [\n *       { key: 'plural', tokens: [ { type: 'content', value: 'numbers' } ] },\n *       { key: 'selectordinal', tokens: [ { type: 'content', value: 'counting' } ] },\n *       { key: 'select', tokens: [ { type: 'content', value: 'choices' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'content', value: 'some ' }, { type: 'argument', arg: 'type' } ] }\n *     ] },\n *   { type: 'content', value: '.' } ]\n *\n *\n * parse('{Such compliance')\n * // ParseError: invalid syntax at line 1 col 7:\n * //\n * //  {Such compliance\n * //        ^\n *\n *\n * const msg = '{words, plural, zero{No words} one{One word} other{# words}}'\n * parse(msg)\n * [ { type: 'plural',\n *     arg: 'words',\n *     cases: [\n *       { key: 'zero', tokens: [ { type: 'content', value: 'No words' } ] },\n *       { key: 'one', tokens: [ { type: 'content', value: 'One word' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'octothorpe' }, { type: 'content', value: ' words' } ] }\n *     ] } ]\n *\n *\n * parse(msg, { cardinal: [ 'one', 'other' ], ordinal: [ 'one', 'two', 'few', 'other' ] })\n * // ParseError: The plural case zero is not valid in this locale at line 1 col 17:\n * //\n * //   {words, plural, zero{\n * //                   ^\n * ```\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ParseError = void 0;\nexports.parse = parse;\nconst lexer_js_1 = require(\"./lexer.js\");\nconst getContext = (lt) => ({\n    offset: lt.offset,\n    line: lt.line,\n    col: lt.col,\n    text: lt.text,\n    lineBreaks: lt.lineBreaks\n});\nconst isSelectType = (type) => type === 'plural' || type === 'select' || type === 'selectordinal';\nfunction strictArgStyleParam(lt, param) {\n    let value = '';\n    let text = '';\n    for (const p of param) {\n        const pText = p.ctx.text;\n        text += pText;\n        switch (p.type) {\n            case 'content':\n                value += p.value;\n                break;\n            case 'argument':\n            case 'function':\n            case 'octothorpe':\n                value += pText;\n                break;\n            default:\n                throw new ParseError(lt, `Unsupported part in strict mode function arg style: ${pText}`);\n        }\n    }\n    const c = {\n        type: 'content',\n        value: value.trim(),\n        ctx: Object.assign({}, param[0].ctx, { text })\n    };\n    return [c];\n}\nconst strictArgTypes = [\n    'number',\n    'date',\n    'time',\n    'spellout',\n    'ordinal',\n    'duration'\n];\nconst defaultPluralKeys = ['zero', 'one', 'two', 'few', 'many', 'other'];\n/**\n * Thrown by {@link parse} on error\n *\n * @public\n */\nclass ParseError extends Error {\n    /** @internal */\n    constructor(lt, msg) {\n        super(lexer_js_1.lexer.formatError(lt, msg));\n    }\n}\nexports.ParseError = ParseError;\nclass Parser {\n    constructor(src, opt) {\n        var _a, _b, _c, _d;\n        this.lexer = lexer_js_1.lexer.reset(src);\n        this.cardinalKeys = (_a = opt === null || opt === void 0 ? void 0 : opt.cardinal) !== null && _a !== void 0 ? _a : defaultPluralKeys;\n        this.ordinalKeys = (_b = opt === null || opt === void 0 ? void 0 : opt.ordinal) !== null && _b !== void 0 ? _b : defaultPluralKeys;\n        this.strict = (_c = opt === null || opt === void 0 ? void 0 : opt.strict) !== null && _c !== void 0 ? _c : false;\n        this.strictPluralKeys = (_d = opt === null || opt === void 0 ? void 0 : opt.strictPluralKeys) !== null && _d !== void 0 ? _d : true;\n    }\n    parse() {\n        return this.parseBody(false, true);\n    }\n    checkSelectKey(lt, type, key) {\n        if (key[0] === '=') {\n            if (type === 'select') {\n                throw new ParseError(lt, `The case ${key} is not valid with select`);\n            }\n        }\n        else if (type !== 'select') {\n            const keys = type === 'plural' ? this.cardinalKeys : this.ordinalKeys;\n            if (this.strictPluralKeys && keys.length > 0 && !keys.includes(key)) {\n                const msg = `The ${type} case ${key} is not valid in this locale`;\n                throw new ParseError(lt, msg);\n            }\n        }\n    }\n    parseSelect({ value: arg }, inPlural, ctx, type) {\n        const sel = { type, arg, cases: [], ctx };\n        if (type === 'plural' || type === 'selectordinal')\n            inPlural = true;\n        else if (this.strict)\n            inPlural = false;\n        for (const lt of this.lexer) {\n            switch (lt.type) {\n                case 'offset':\n                    if (type === 'select') {\n                        throw new ParseError(lt, 'Unexpected plural offset for select');\n                    }\n                    if (sel.cases.length > 0) {\n                        throw new ParseError(lt, 'Plural offset must be set before cases');\n                    }\n                    sel.pluralOffset = Number(lt.value);\n                    ctx.text += lt.text;\n                    ctx.lineBreaks += lt.lineBreaks;\n                    break;\n                case 'case': {\n                    this.checkSelectKey(lt, type, lt.value);\n                    sel.cases.push({\n                        key: lt.value,\n                        tokens: this.parseBody(inPlural),\n                        ctx: getContext(lt)\n                    });\n                    break;\n                }\n                case 'end':\n                    return sel;\n                /* istanbul ignore next: never happens */\n                default:\n                    throw new ParseError(lt, `Unexpected lexer token: ${lt.type}`);\n            }\n        }\n        throw new ParseError(null, 'Unexpected message end');\n    }\n    parseArgToken(lt, inPlural) {\n        const ctx = getContext(lt);\n        const argType = this.lexer.next();\n        if (!argType)\n            throw new ParseError(null, 'Unexpected message end');\n        ctx.text += argType.text;\n        ctx.lineBreaks += argType.lineBreaks;\n        if (this.strict &&\n            (argType.type === 'func-simple' || argType.type === 'func-args') &&\n            !strictArgTypes.includes(argType.value)) {\n            const msg = `Invalid strict mode function arg type: ${argType.value}`;\n            throw new ParseError(lt, msg);\n        }\n        switch (argType.type) {\n            case 'end':\n                return { type: 'argument', arg: lt.value, ctx };\n            case 'func-simple': {\n                const end = this.lexer.next();\n                if (!end)\n                    throw new ParseError(null, 'Unexpected message end');\n                /* istanbul ignore if: never happens */\n                if (end.type !== 'end') {\n                    throw new ParseError(end, `Unexpected lexer token: ${end.type}`);\n                }\n                ctx.text += end.text;\n                if (isSelectType(argType.value.toLowerCase())) {\n                    throw new ParseError(argType, `Invalid type identifier: ${argType.value}`);\n                }\n                return {\n                    type: 'function',\n                    arg: lt.value,\n                    key: argType.value,\n                    ctx\n                };\n            }\n            case 'func-args': {\n                if (isSelectType(argType.value.toLowerCase())) {\n                    const msg = `Invalid type identifier: ${argType.value}`;\n                    throw new ParseError(argType, msg);\n                }\n                let param = this.parseBody(this.strict ? false : inPlural);\n                if (this.strict && param.length > 0) {\n                    param = strictArgStyleParam(lt, param);\n                }\n                return {\n                    type: 'function',\n                    arg: lt.value,\n                    key: argType.value,\n                    param,\n                    ctx\n                };\n            }\n            case 'select':\n                /* istanbul ignore else: never happens */\n                if (isSelectType(argType.value)) {\n                    return this.parseSelect(lt, inPlural, ctx, argType.value);\n                }\n                else {\n                    throw new ParseError(argType, `Unexpected select type ${argType.value}`);\n                }\n            /* istanbul ignore next: never happens */\n            default:\n                throw new ParseError(argType, `Unexpected lexer token: ${argType.type}`);\n        }\n    }\n    parseBody(inPlural, atRoot) {\n        const tokens = [];\n        let content = null;\n        for (const lt of this.lexer) {\n            if (lt.type === 'argument') {\n                if (content)\n                    content = null;\n                tokens.push(this.parseArgToken(lt, inPlural));\n            }\n            else if (lt.type === 'octothorpe' && inPlural) {\n                if (content)\n                    content = null;\n                tokens.push({ type: 'octothorpe', ctx: getContext(lt) });\n            }\n            else if (lt.type === 'end' && !atRoot) {\n                return tokens;\n            }\n            else {\n                let value = lt.value;\n                if (!inPlural && lt.type === 'quoted' && value[0] === '#') {\n                    if (value.includes('{')) {\n                        const errMsg = `Unsupported escape pattern: ${value}`;\n                        throw new ParseError(lt, errMsg);\n                    }\n                    value = lt.text;\n                }\n                if (content) {\n                    content.value += value;\n                    content.ctx.text += lt.text;\n                    content.ctx.lineBreaks += lt.lineBreaks;\n                }\n                else {\n                    content = { type: 'content', value, ctx: getContext(lt) };\n                    tokens.push(content);\n                }\n            }\n        }\n        if (atRoot)\n            return tokens;\n        throw new ParseError(null, 'Unexpected message end');\n    }\n}\n/**\n * Parse an input string into an array of tokens\n *\n * @public\n * @remarks\n * The parser only supports the default `DOUBLE_OPTIONAL`\n * {@link http://www.icu-project.org/apiref/icu4c/messagepattern_8h.html#af6e0757e0eb81c980b01ee5d68a9978b | apostrophe mode}.\n */\nfunction parse(src, options = {}) {\n    const parser = new Parser(src, options);\n    return parser.parse();\n}\n", "import { unraw } from 'unraw';\nimport { compileMessage } from '@lingui/message-utils/compileMessage';\n\nconst isString = (s) => typeof s === \"string\";\nconst isFunction = (f) => typeof f === \"function\";\n\nconst cache = /* @__PURE__ */ new Map();\nconst defaultLocale = \"en\";\nfunction normalizeLocales(locales) {\n  const out = Array.isArray(locales) ? locales : [locales];\n  return [...out, defaultLocale];\n}\nfunction date(locales, value, format) {\n  const _locales = normalizeLocales(locales);\n  const formatter = getMemoized(\n    () => cacheKey(\"date\", _locales, format),\n    () => new Intl.DateTimeFormat(_locales, format)\n  );\n  return formatter.format(isString(value) ? new Date(value) : value);\n}\nfunction number(locales, value, format) {\n  const _locales = normalizeLocales(locales);\n  const formatter = getMemoized(\n    () => cacheKey(\"number\", _locales, format),\n    () => new Intl.NumberFormat(_locales, format)\n  );\n  return formatter.format(value);\n}\nfunction plural(locales, ordinal, value, { offset = 0, ...rules }) {\n  const _locales = normalizeLocales(locales);\n  const plurals = ordinal ? getMemoized(\n    () => cacheKey(\"plural-ordinal\", _locales),\n    () => new Intl.PluralRules(_locales, { type: \"ordinal\" })\n  ) : getMemoized(\n    () => cacheKey(\"plural-cardinal\", _locales),\n    () => new Intl.PluralRules(_locales, { type: \"cardinal\" })\n  );\n  return rules[value] ?? rules[plurals.select(value - offset)] ?? rules.other;\n}\nfunction getMemoized(getKey, construct) {\n  const key = getKey();\n  let formatter = cache.get(key);\n  if (!formatter) {\n    formatter = construct();\n    cache.set(key, formatter);\n  }\n  return formatter;\n}\nfunction cacheKey(type, locales, options) {\n  const localeKey = locales.join(\"-\");\n  return `${type}-${localeKey}-${JSON.stringify(options)}`;\n}\n\nconst formats = {\n  __proto__: null,\n  date: date,\n  defaultLocale: defaultLocale,\n  number: number,\n  plural: plural\n};\n\nconst UNICODE_REGEX = /\\\\u[a-fA-F0-9]{4}|\\\\x[a-fA-F0-9]{2}/;\nconst OCTOTHORPE_PH = \"%__lingui_octothorpe__%\";\nconst getDefaultFormats = (locale, passedLocales, formats = {}) => {\n  const locales = passedLocales || locale;\n  const style = (format) => {\n    return typeof format === \"object\" ? format : formats[format] || { style: format };\n  };\n  const replaceOctothorpe = (value, message) => {\n    const numberFormat = Object.keys(formats).length ? style(\"number\") : void 0;\n    const valueStr = number(locales, value, numberFormat);\n    return message.replace(new RegExp(OCTOTHORPE_PH, \"g\"), valueStr);\n  };\n  return {\n    plural: (value, cases) => {\n      const { offset = 0 } = cases;\n      const message = plural(locales, false, value, cases);\n      return replaceOctothorpe(value - offset, message);\n    },\n    selectordinal: (value, cases) => {\n      const { offset = 0 } = cases;\n      const message = plural(locales, true, value, cases);\n      return replaceOctothorpe(value - offset, message);\n    },\n    select: selectFormatter,\n    number: (value, format) => number(locales, value, style(format)),\n    date: (value, format) => date(locales, value, style(format))\n  };\n};\nconst selectFormatter = (value, rules) => rules[value] ?? rules.other;\nfunction interpolate(translation, locale, locales) {\n  return (values = {}, formats) => {\n    const formatters = getDefaultFormats(locale, locales, formats);\n    const formatMessage = (tokens, replaceOctothorpe = false) => {\n      if (!Array.isArray(tokens))\n        return tokens;\n      return tokens.reduce((message, token) => {\n        if (token === \"#\" && replaceOctothorpe) {\n          return message + OCTOTHORPE_PH;\n        }\n        if (isString(token)) {\n          return message + token;\n        }\n        const [name, type, format] = token;\n        let interpolatedFormat = {};\n        if (type === \"plural\" || type === \"selectordinal\" || type === \"select\") {\n          Object.entries(format).forEach(\n            ([key, value2]) => {\n              interpolatedFormat[key] = formatMessage(\n                value2,\n                type === \"plural\" || type === \"selectordinal\"\n              );\n            }\n          );\n        } else {\n          interpolatedFormat = format;\n        }\n        let value;\n        if (type) {\n          const formatter = formatters[type];\n          value = formatter(values[name], interpolatedFormat);\n        } else {\n          value = values[name];\n        }\n        if (value == null) {\n          return message;\n        }\n        return message + value;\n      }, \"\");\n    };\n    const result = formatMessage(translation);\n    if (isString(result) && UNICODE_REGEX.test(result)) {\n      return unraw(result.trim());\n    }\n    if (isString(result))\n      return result.trim();\n    return result ? String(result) : \"\";\n  };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => {\n  __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass EventEmitter {\n  constructor() {\n    __publicField$1(this, \"_events\", {});\n  }\n  on(event, listener) {\n    var _a;\n    (_a = this._events)[event] ?? (_a[event] = []);\n    this._events[event].push(listener);\n    return () => this.removeListener(event, listener);\n  }\n  removeListener(event, listener) {\n    const maybeListeners = this._getListeners(event);\n    if (!maybeListeners)\n      return;\n    const index = maybeListeners.indexOf(listener);\n    if (~index)\n      maybeListeners.splice(index, 1);\n  }\n  emit(event, ...args) {\n    const maybeListeners = this._getListeners(event);\n    if (!maybeListeners)\n      return;\n    maybeListeners.map((listener) => listener.apply(this, args));\n  }\n  _getListeners(event) {\n    const maybeListeners = this._events[event];\n    return Array.isArray(maybeListeners) ? maybeListeners : false;\n  }\n}\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass I18n extends EventEmitter {\n  constructor(params) {\n    super();\n    __publicField(this, \"_locale\", \"\");\n    __publicField(this, \"_locales\");\n    __publicField(this, \"_localeData\", {});\n    __publicField(this, \"_messages\", {});\n    __publicField(this, \"_missing\");\n    __publicField(this, \"_messageCompiler\");\n    /**\n     * Alias for {@see I18n._}\n     */\n    __publicField(this, \"t\", this._.bind(this));\n    if (process.env.NODE_ENV !== \"production\") {\n      this.setMessagesCompiler(compileMessage);\n    }\n    if (params.missing != null)\n      this._missing = params.missing;\n    if (params.messages != null)\n      this.load(params.messages);\n    if (params.localeData != null)\n      this.loadLocaleData(params.localeData);\n    if (typeof params.locale === \"string\" || params.locales) {\n      this.activate(params.locale ?? defaultLocale, params.locales);\n    }\n  }\n  get locale() {\n    return this._locale;\n  }\n  get locales() {\n    return this._locales;\n  }\n  get messages() {\n    return this._messages[this._locale] ?? {};\n  }\n  /**\n   * @deprecated this has no effect. Please remove this from the code. Deprecated in v4\n   */\n  get localeData() {\n    return this._localeData[this._locale] ?? {};\n  }\n  _loadLocaleData(locale, localeData) {\n    const maybeLocaleData = this._localeData[locale];\n    if (!maybeLocaleData) {\n      this._localeData[locale] = localeData;\n    } else {\n      Object.assign(maybeLocaleData, localeData);\n    }\n  }\n  /**\n   * Registers a `MessageCompiler` to enable the use of uncompiled catalogs at runtime.\n   *\n   * In production builds, the `MessageCompiler` is typically excluded to reduce bundle size.\n   * By default, message catalogs should be precompiled during the build process. However,\n   * if you need to compile catalogs at runtime, you can use this method to set a message compiler.\n   *\n   * Example usage:\n   *\n   * ```ts\n   * import { compileMessage } from \"@lingui/message-utils/compileMessage\";\n   *\n   * i18n.setMessagesCompiler(compileMessage);\n   * ```\n   */\n  setMessagesCompiler(compiler) {\n    this._messageCompiler = compiler;\n    return this;\n  }\n  /**\n   * @deprecated Plurals automatically used from Intl.PluralRules you can safely remove this call. Deprecated in v4\n   */\n  // @ts-ignore deprecated, so ignore the reported error\n  loadLocaleData(localeOrAllData, localeData) {\n    if (localeData != null) {\n      this._loadLocaleData(localeOrAllData, localeData);\n    } else {\n      Object.keys(localeOrAllData).forEach(\n        (locale) => this._loadLocaleData(locale, localeOrAllData[locale])\n      );\n    }\n    this.emit(\"change\");\n  }\n  _load(locale, messages) {\n    const maybeMessages = this._messages[locale];\n    if (!maybeMessages) {\n      this._messages[locale] = messages;\n    } else {\n      Object.assign(maybeMessages, messages);\n    }\n  }\n  load(localeOrMessages, messages) {\n    if (typeof localeOrMessages == \"string\" && typeof messages === \"object\") {\n      this._load(localeOrMessages, messages);\n    } else {\n      Object.entries(localeOrMessages).forEach(\n        ([locale, messages2]) => this._load(locale, messages2)\n      );\n    }\n    this.emit(\"change\");\n  }\n  /**\n   * @param options {@link LoadAndActivateOptions}\n   */\n  loadAndActivate({ locale, locales, messages }) {\n    this._locale = locale;\n    this._locales = locales || void 0;\n    this._messages[this._locale] = messages;\n    this.emit(\"change\");\n  }\n  activate(locale, locales) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!this._messages[locale]) {\n        console.warn(`Messages for locale \"${locale}\" not loaded.`);\n      }\n    }\n    this._locale = locale;\n    this._locales = locales;\n    this.emit(\"change\");\n  }\n  _(id, values, options) {\n    let message = options?.message;\n    if (!id) {\n      id = \"\";\n    }\n    if (!isString(id)) {\n      values = id.values || values;\n      message = id.message;\n      id = id.id;\n    }\n    const messageForId = this.messages[id];\n    const messageMissing = messageForId === void 0;\n    const missing = this._missing;\n    if (missing && messageMissing) {\n      return isFunction(missing) ? missing(this._locale, id) : missing;\n    }\n    if (messageMissing) {\n      this.emit(\"missing\", { id, locale: this._locale });\n    }\n    let translation = messageForId || message || id;\n    if (isString(translation)) {\n      if (this._messageCompiler) {\n        translation = this._messageCompiler(translation);\n      } else {\n        console.warn(`Uncompiled message detected! Message:\n\n> ${translation}\n\nThat means you use raw catalog or your catalog doesn't have a translation for the message and fallback was used.\nICU features such as interpolation and plurals will not work properly for that message. \n\nPlease compile your catalog first. \n`);\n      }\n    }\n    if (isString(translation) && UNICODE_REGEX.test(translation))\n      return JSON.parse(`\"${translation}\"`);\n    if (isString(translation))\n      return translation;\n    return interpolate(\n      translation,\n      this._locale,\n      this._locales\n    )(values, options?.formats);\n  }\n  date(value, format) {\n    return date(this._locales || this._locale, value, format);\n  }\n  number(value, format) {\n    return number(this._locales || this._locale, value, format);\n  }\n}\nfunction setupI18n(params = {}) {\n  return new I18n(params);\n}\n\nconst i18n = setupI18n();\n\nexport { I18n, formats, i18n, setupI18n };\n", "import { parse } from '@messageformat/parser';\n\nfunction processTokens(tokens, mapText) {\n  if (!tokens.filter((token) => token.type !== \"content\").length) {\n    return tokens.map((token) => mapText(token.value));\n  }\n  return tokens.map((token) => {\n    if (token.type === \"content\") {\n      return mapText(token.value);\n    } else if (token.type === \"octothorpe\") {\n      return \"#\";\n    } else if (token.type === \"argument\") {\n      return [token.arg];\n    } else if (token.type === \"function\") {\n      const _param = token?.param?.[0];\n      if (_param) {\n        return [token.arg, token.key, _param.value.trim()];\n      } else {\n        return [token.arg, token.key];\n      }\n    }\n    const offset = token.pluralOffset;\n    const formatProps = {};\n    token.cases.forEach(({ key, tokens: tokens2 }) => {\n      const prop = key[0] === \"=\" ? key.slice(1) : key;\n      formatProps[prop] = processTokens(tokens2, mapText);\n    });\n    return [\n      token.arg,\n      token.type,\n      {\n        offset,\n        ...formatProps\n      }\n    ];\n  });\n}\nfunction compileMessage(message, mapText = (v) => v) {\n  try {\n    return processTokens(parse(message), mapText);\n  } catch (e) {\n    console.error(`${e.message} \n\nMessage: ${message}`);\n    return [message];\n  }\n}\n\nexport { compileMessage };\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAGA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gBAAgB,QAAQ,YAAY;AAO5C,QAAI;AACJ,KAAC,SAAUA,YAAW;AAMlB,MAAAA,WAAU,kBAAkB,IAAI;AAMhC,MAAAA,WAAU,sBAAsB,IAAI;AAMpC,MAAAA,WAAU,gBAAgB,IAAI;AAK9B,MAAAA,WAAU,kBAAkB,IAAI;AAKhC,MAAAA,WAAU,aAAa,IAAI;AAAA,IAC/B,GAAG,YAAY,QAAQ,cAAc,QAAQ,YAAY,CAAC,EAAE;AAE5D,YAAQ,gBAAgB,oBAAI,IAAI;AAAA,MAC5B,CAAC,UAAU,kBAAkB,6CAA6C;AAAA,MAC1E;AAAA,QACI,UAAU;AAAA,QACV;AAAA,MACJ;AAAA,MACA;AAAA,QACI,UAAU;AAAA,QACV;AAAA,MACJ;AAAA,MACA;AAAA,QACI,UAAU;AAAA,QACV;AAAA,MAEJ;AAAA,MACA,CAAC,UAAU,aAAa,4CAA4C;AAAA,IACxE,CAAC;AAAA;AAAA;;;AC3DD;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,QAAQ,QAAQ,gBAAgB,QAAQ,YAAY;AAC5D,QAAM,WAAW;AACjB,WAAO,eAAe,SAAS,aAAa,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,SAAS;AAAA,IAAW,EAAE,CAAC;AACjH,WAAO,eAAe,SAAS,iBAAiB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,SAAS;AAAA,IAAe,EAAE,CAAC;AASzH,aAAS,cAAc,KAAK;AACxB,YAAM,iBAAiB,CAAC,IAAI,MAAM,YAAY;AAC9C,aAAO,iBAAiB,SAAS,KAAK,EAAE,IAAI;AAAA,IAChD;AAYA,aAAS,oBAAoB,KAAK,WAAW,gBAAgB;AACzD,YAAM,YAAY,cAAc,GAAG;AACnC,UAAI,OAAO,MAAM,SAAS,KACrB,mBAAmB,UAAa,mBAAmB,IAAI,QAAS;AACjE,cAAM,IAAI,YAAY,SAAS,cAAc,IAAI,SAAS,CAAC;AAAA,MAC/D;AACA,aAAO;AAAA,IACX;AASA,aAAS,qBAAqB,MAAM;AAChC,YAAM,aAAa,oBAAoB,MAAM,SAAS,UAAU,sBAAsB,CAAC;AACvF,aAAO,OAAO,aAAa,UAAU;AAAA,IACzC;AAWA,aAAS,iBAAiB,MAAM,eAAe;AAC3C,YAAM,aAAa,oBAAoB,MAAM,SAAS,UAAU,kBAAkB,CAAC;AACnF,UAAI,kBAAkB,QAAW;AAC7B,cAAM,sBAAsB,oBAAoB,eAAe,SAAS,UAAU,kBAAkB,CAAC;AACrG,eAAO,OAAO,aAAa,YAAY,mBAAmB;AAAA,MAC9D;AACA,aAAO,OAAO,aAAa,UAAU;AAAA,IACzC;AAMA,aAAS,cAAc,MAAM;AACzB,aAAO,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,KAAK,SAAS,CAAC,MAAM;AAAA,IACtE;AASA,aAAS,0BAA0B,WAAW;AAC1C,UAAI,CAAC,cAAc,SAAS,GAAG;AAC3B,cAAM,IAAI,YAAY,SAAS,cAAc,IAAI,SAAS,UAAU,gBAAgB,CAAC;AAAA,MACzF;AACA,YAAM,gBAAgB,UAAU,MAAM,GAAG,EAAE;AAC3C,YAAM,aAAa,oBAAoB,eAAe,SAAS,UAAU,gBAAgB;AACzF,UAAI;AACA,eAAO,OAAO,cAAc,UAAU;AAAA,MAC1C,SACO,KAAK;AACR,cAAM,eAAe,aACf,IAAI,YAAY,SAAS,cAAc,IAAI,SAAS,UAAU,cAAc,CAAC,IAC7E;AAAA,MACV;AAAA,IACJ;AAGA,aAAS,eAAe,MAAM,QAAQ,OAAO;AACzC,UAAI,OAAO;AACP,cAAM,IAAI,YAAY,SAAS,cAAc,IAAI,SAAS,UAAU,gBAAgB,CAAC;AAAA,MACzF;AAGA,YAAM,aAAa,SAAS,MAAM,CAAC;AACnC,aAAO,OAAO,aAAa,UAAU;AAAA,IACzC;AAKA,QAAM,yBAAyB,oBAAI,IAAI;AAAA,MACnC,CAAC,KAAK,IAAI;AAAA,MACV,CAAC,KAAK,IAAI;AAAA,MACV,CAAC,KAAK,IAAI;AAAA,MACV,CAAC,KAAK,IAAI;AAAA,MACV,CAAC,KAAK,GAAI;AAAA,MACV,CAAC,KAAK,IAAI;AAAA,MACV,CAAC,KAAK,IAAI;AAAA,IACd,CAAC;AAMD,aAAS,yBAAyB,MAAM;AACpC,aAAO,uBAAuB,IAAI,IAAI,KAAK;AAAA,IAC/C;AAiBA,QAAM,cAAc;AAUpB,aAASC,OAAM,KAAK,cAAc,OAAO;AACrC,aAAO,IAAI,QAAQ,aAAa,SAAU,GAAG,WAAW,KAAK,WAAW,sBAAsB,WAAW,SAAS,OAAO,iBAAiB;AAGtI,YAAI,cAAc,QAAW;AACzB,iBAAO;AAAA,QACX;AACA,YAAI,QAAQ,QAAW;AACnB,iBAAO,qBAAqB,GAAG;AAAA,QACnC;AACA,YAAI,cAAc,QAAW;AACzB,iBAAO,0BAA0B,SAAS;AAAA,QAC9C;AACA,YAAI,yBAAyB,QAAW;AACpC,iBAAO,iBAAiB,sBAAsB,SAAS;AAAA,QAC3D;AACA,YAAI,YAAY,QAAW;AACvB,iBAAO,iBAAiB,OAAO;AAAA,QACnC;AACA,YAAI,UAAU,KAAK;AACf,iBAAO;AAAA,QACX;AACA,YAAI,UAAU,QAAW;AACrB,iBAAO,eAAe,OAAO,CAAC,WAAW;AAAA,QAC7C;AACA,YAAI,oBAAoB,QAAW;AAC/B,iBAAO,yBAAyB,eAAe;AAAA,QACnD;AACA,cAAM,IAAI,YAAY,SAAS,cAAc,IAAI,SAAS,UAAU,WAAW,CAAC;AAAA,MACpF,CAAC;AAAA,IACL;AACA,YAAQ,QAAQA;AAChB,YAAQ,UAAUA;AAAA;AAAA;;;AC5LlB;AAAA;AAAA,KAAC,SAAS,MAAM,SAAS;AACvB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,MACpB,WAAW,OAAO,WAAW,YAAY,OAAO,SAAS;AACvD,eAAO,UAAU,QAAQ;AAAA,MAC3B,OAAO;AACL,aAAK,MAAM,QAAQ;AAAA,MACrB;AAAA,IACF,GAAE,SAAM,WAAW;AACjB;AAEA,UAAI,iBAAiB,OAAO,UAAU;AACtC,UAAI,WAAW,OAAO,UAAU;AAChC,UAAI,YAAY,OAAO,IAAI,OAAO,EAAE,WAAW;AAI/C,eAAS,SAAS,GAAG;AAAE,eAAO,KAAK,SAAS,KAAK,CAAC,MAAM;AAAA,MAAkB;AAC1E,eAAS,SAAS,GAAG;AAAE,eAAO,KAAK,OAAO,MAAM,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC;AAAA,MAAE;AAE9F,eAAS,SAAS,GAAG;AACnB,eAAO,EAAE,QAAQ,0BAA0B,MAAM;AAAA,MACnD;AACA,eAAS,SAAS,GAAG;AACnB,YAAI,KAAK,IAAI,OAAO,MAAM,CAAC;AAC3B,eAAO,GAAG,KAAK,EAAE,EAAE,SAAS;AAAA,MAC9B;AACA,eAAS,UAAU,GAAG;AACpB,eAAO,MAAM,IAAI;AAAA,MACnB;AACA,eAAS,QAAQ,SAAS;AACxB,YAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,YAAI,SAAU,QAAQ,IAAI,SAAS,GAAG;AACpC,iBAAO,QAAQ,IAAI;AAAA,QACrB,CAAC,EAAE,KAAK,GAAG;AACX,eAAO,QAAQ,SAAS;AAAA,MAC1B;AAEA,eAAS,gBAAgB,KAAK;AAC5B,YAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAO,QAAQ,SAAS,GAAG,IAAI;AAAA,QAEjC,WAAW,SAAS,GAAG,GAAG;AAExB,cAAI,IAAI,WAAY,OAAM,IAAI,MAAM,4BAA4B;AAChE,cAAI,IAAI,OAAQ,OAAM,IAAI,MAAM,2BAA2B;AAC3D,cAAI,IAAI,OAAQ,OAAM,IAAI,MAAM,2BAA2B;AAC3D,cAAI,IAAI,UAAW,OAAM,IAAI,MAAM,2BAA2B;AAC9D,iBAAO,IAAI;AAAA,QAEb,OAAO;AACL,gBAAM,IAAI,MAAM,oBAAoB,GAAG;AAAA,QACzC;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,QAAQ;AACtB,YAAI,EAAE,SAAS,QAAQ;AACrB,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI;AAAA,MAClD;AAEA,eAAS,WAAW,QAAQ,UAAU;AACpC,YAAI,WAAW,OAAO;AACtB,YAAI,aAAa;AACjB,eAAO,MAAM;AACX,cAAI,MAAM,OAAO,YAAY,MAAM,WAAW,CAAC;AAC/C,cAAI,QAAQ,IAAI;AACd;AAAA,UACF,OAAO;AACL;AAAA,UACF;AACA,qBAAW;AACX,cAAI,eAAe,UAAU;AAC3B;AAAA,UACF;AACA,cAAI,aAAa,GAAG;AAClB;AAAA,UACF;AAAA,QACF;AACA,YAAI,gBACF,aAAa,WACb,IACA,WAAW;AACb,eAAO,OAAO,UAAU,aAAa,EAAE,MAAM,IAAI;AAAA,MACnD;AAEA,eAAS,cAAc,QAAQ;AAC7B,YAAI,OAAO,OAAO,oBAAoB,MAAM;AAC5C,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,QAAQ,OAAO,GAAG;AACtB,cAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AAC3B,cAAI,QAAQ,WAAW;AACrB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,qBAAO,KAAK,EAAC,SAAS,MAAM,CAAC,EAAC,CAAC;AAAA,YACjC;AACA;AAAA,UACF;AACA,cAAI,QAAQ,CAAC;AACb,gBAAM,QAAQ,SAAS,MAAM;AAC3B,gBAAI,SAAS,IAAI,GAAG;AAClB,kBAAI,MAAM,OAAQ,QAAO,KAAK,YAAY,KAAK,KAAK,CAAC;AACrD,qBAAO,KAAK,YAAY,KAAK,IAAI,CAAC;AAClC,sBAAQ,CAAC;AAAA,YACX,OAAO;AACL,oBAAM,KAAK,IAAI;AAAA,YACjB;AAAA,UACF,CAAC;AACD,cAAI,MAAM,OAAQ,QAAO,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,QACvD;AACA,eAAO;AAAA,MACT;AAEA,eAAS,aAAa,OAAO;AAC3B,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,MAAM,MAAM,CAAC;AACjB,cAAI,IAAI,SAAS;AACf,gBAAI,UAAU,CAAC,EAAE,OAAO,IAAI,OAAO;AACnC,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,qBAAO,KAAK,EAAC,SAAS,QAAQ,CAAC,EAAC,CAAC;AAAA,YACnC;AACA;AAAA,UACF;AACA,cAAI,CAAC,IAAI,MAAM;AACb,kBAAM,IAAI,MAAM,uBAAuB,KAAK,UAAU,GAAG,CAAC;AAAA,UAC5D;AACA,iBAAO,KAAK,YAAY,IAAI,MAAM,GAAG,CAAC;AAAA,QACxC;AACA,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,MAAM,KAAK;AAC9B,YAAI,CAAC,SAAS,GAAG,GAAG;AAClB,gBAAM,EAAE,OAAO,IAAI;AAAA,QACrB;AACA,YAAI,IAAI,SAAS;AACf,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC7D;AAGA,YAAI,UAAU;AAAA,UACZ,aAAa;AAAA,UACb,YAAY,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,IAAI;AAAA,UACjC,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AAGA,iBAAS,OAAO,KAAK;AACnB,cAAI,eAAe,KAAK,KAAK,GAAG,GAAG;AACjC,oBAAQ,GAAG,IAAI,IAAI,GAAG;AAAA,UACxB;AAAA,QACF;AAGA,YAAI,OAAO,QAAQ,SAAS,YAAY,SAAS,QAAQ,MAAM;AAC7D,gBAAM,IAAI,MAAM,8CAA8C,QAAQ,OAAO,kBAAkB,OAAO,IAAI;AAAA,QAC5G;AAGA,YAAI,QAAQ,QAAQ;AACpB,gBAAQ,QAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,QAAQ,CAAC,KAAK,IAAI,CAAC;AAClE,gBAAQ,MAAM,KAAK,SAAS,GAAG,GAAG;AAChC,iBAAO,SAAS,CAAC,KAAK,SAAS,CAAC,IAAI,IAC7B,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,IAAK,EAAE,SAAS,EAAE;AAAA,QAC5D,CAAC;AACD,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,MAAM;AACrB,eAAO,MAAM,QAAQ,IAAI,IAAI,aAAa,IAAI,IAAI,cAAc,IAAI;AAAA,MACtE;AAEA,UAAI,mBAAmB,YAAY,SAAS,EAAC,YAAY,MAAM,aAAa,KAAI,CAAC;AACjF,eAAS,aAAa,OAAO,WAAW;AACtC,YAAI,YAAY;AAChB,YAAI,OAAO,uBAAO,OAAO,IAAI;AAC7B,YAAI,cAAc;AAClB,YAAI,cAAc;AAClB,YAAI,SAAS,CAAC;AACd,YAAI,QAAQ,CAAC;AAGb,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,MAAM,CAAC,EAAE,UAAU;AACrB,0BAAc;AAAA,UAChB;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,UAAU,MAAM,CAAC;AAErB,cAAI,QAAQ,SAAS;AAEnB,kBAAM,IAAI,MAAM,gDAAgD;AAAA,UAClE;AAEA,cAAI,QAAQ,SAAS,QAAQ,UAAU;AAErC,gBAAI,WAAW;AACb,kBAAI,CAAC,QAAQ,aAAa,CAAC,UAAU,UAAU;AAC7C,sBAAM,IAAI,MAAM,eAAe,QAAQ,WAAW,aAAa,WAAW,oCAAoC,QAAQ,cAAc,IAAI;AAAA,cAC1I,OAAO;AACL,sBAAM,IAAI,MAAM,2DAA2D,QAAQ,cAAc,IAAI;AAAA,cACvG;AAAA,YACF;AACA,wBAAY;AAAA,UACd;AAEA,cAAI,QAAQ,QAAQ,MAAM,MAAM;AAChC,cAAI,aAAa;AACf,mBAAO,MAAM,UAAU,OAAO,MAAM,CAAC,MAAM,YAAY,MAAM,CAAC,EAAE,WAAW,GAAG;AAC5E,kBAAI,OAAO,MAAM,MAAM;AACvB,mBAAK,KAAK,WAAW,CAAC,CAAC,IAAI;AAAA,YAC7B;AAAA,UACF;AAGA,cAAI,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,MAAM;AAC/C,gBAAI,CAAC,WAAW;AACd,oBAAM,IAAI,MAAM,6EAA6E,QAAQ,cAAc,IAAI;AAAA,YACzH;AACA,gBAAI,QAAQ,UAAU;AACpB,oBAAM,IAAI,MAAM,4EAA4E,QAAQ,cAAc,IAAI;AAAA,YACxH;AAAA,UACF;AAGA,cAAI,MAAM,WAAW,GAAG;AACtB;AAAA,UACF;AACA,wBAAc;AAEd,iBAAO,KAAK,OAAO;AAGnB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,MAAM,MAAM,CAAC;AACjB,gBAAI,CAAC,SAAS,GAAG,GAAG;AAClB;AAAA,YACF;AAEA,gBAAI,gBAAgB,MAAM;AACxB,4BAAc,IAAI;AAAA,YACpB,WAAW,gBAAgB,IAAI,WAAW,QAAQ,aAAa,OAAO;AACpE,oBAAM,IAAI,MAAM,oCAAoC;AAAA,YACtD;AAAA,UACF;AAGA,cAAI,MAAM,QAAQ,MAAM,IAAI,eAAe,CAAC;AAG5C,cAAI,SAAS,IAAI,OAAO,GAAG;AAC3B,cAAI,OAAO,KAAK,EAAE,GAAG;AACnB,kBAAM,IAAI,MAAM,kCAAkC,MAAM;AAAA,UAC1D;AACA,cAAI,aAAa,SAAS,GAAG;AAC7B,cAAI,aAAa,GAAG;AAClB,kBAAM,IAAI,MAAM,gCAAgC,SAAS,uBAAuB;AAAA,UAClF;AAGA,cAAI,CAAC,QAAQ,cAAc,OAAO,KAAK,IAAI,GAAG;AAC5C,kBAAM,IAAI,MAAM,qCAAqC,MAAM;AAAA,UAC7D;AAGA,gBAAM,KAAK,UAAU,GAAG,CAAC;AAAA,QAC3B;AAQA,YAAI,eAAe,aAAa,UAAU;AAC1C,YAAI,QAAQ,aAAa,CAAC,eAAe,OAAO;AAChD,YAAI,SAAS,aAAa,eAAe,KAAK;AAE9C,YAAI,gBAAgB,KAAM,UAAS;AACnC,YAAI,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,QAAQ,KAAK;AACxD,eAAO,EAAC,QAAQ,UAAU,QAAgB,MAAY,OAAO,aAAa,iBAAgB;AAAA,MAC5F;AAEA,eAAS,QAAQ,OAAO;AACtB,YAAI,SAAS,aAAa,QAAQ,KAAK,CAAC;AACxC,eAAO,IAAI,MAAM,EAAC,OAAO,OAAM,GAAG,OAAO;AAAA,MAC3C;AAEA,eAAS,gBAAgB,GAAG,MAAM,KAAK;AACrC,YAAI,QAAQ,MAAM,EAAE,QAAQ,EAAE;AAC9B,YAAI,SAAS,CAAC,IAAI,KAAK,GAAG;AACxB,gBAAM,IAAI,MAAM,oBAAoB,QAAQ,kBAAkB,EAAE,cAAc,iBAAiB,OAAO,IAAI;AAAA,QAC5G;AACA,YAAI,KAAK,EAAE,OAAO,CAAC,EAAE,QAAQ,GAAG;AAC9B,gBAAM,IAAI,MAAM,8BAA8B,EAAE,cAAc,iBAAiB,OAAO,IAAI;AAAA,QAC5F;AAAA,MACF;AACA,eAAS,cAAc,QAAQ,OAAO;AACpC,YAAI,MAAM,OAAO,OAAO,QAAQ,OAAO,IAAI,IAAI,CAAC;AAChD,eAAO,OAAO;AAEd,YAAI,OAAO,OAAO,oBAAoB,MAAM;AAC5C,YAAI,CAAC,MAAO,SAAQ,KAAK,CAAC;AAE1B,YAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,MAAM,KAAK,CAAC;AAChB,kBAAQ,GAAG,IAAI,QAAQ,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG;AAAA,QAChD;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,QAAQ,QAAQ,GAAG;AACvB,cAAI,WAAW,uBAAO,OAAO,IAAI;AACjC,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,OAAO,MAAM,CAAC;AAClB,gBAAI,CAAC,KAAK,QAAS;AACnB,gBAAI,SAAS,CAAC,GAAG,CAAC;AAClB,gBAAI,KAAK,YAAY,OAAO,CAAC,SAAS,KAAK,OAAO,GAAG;AACnD,uBAAS,KAAK,OAAO,IAAI;AACzB,kBAAI,WAAW,QAAQ,KAAK,OAAO;AACnC,kBAAI,CAAC,UAAU;AACb,sBAAM,IAAI,MAAM,uCAAuC,KAAK,UAAU,kBAAkB,MAAM,IAAI;AAAA,cACpG;AACA,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,UAAU,SAAS,CAAC;AACxB,oBAAI,MAAM,QAAQ,OAAO,MAAM,GAAI;AACnC,uBAAO,KAAK,OAAO;AAAA,cACrB;AAAA,YACF;AACA,kBAAM,OAAO,MAAM,OAAO,MAAM;AAChC;AAAA,UACF;AAAA,QACF;AAEA,YAAI,MAAM,uBAAO,OAAO,IAAI;AAC5B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,GAAG,IAAI,aAAa,QAAQ,GAAG,GAAG,IAAI;AAAA,QAC5C;AAEA,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,OAAO,KAAK,CAAC;AACjB,cAAI,QAAQ,IAAI,IAAI;AACpB,cAAI,SAAS,MAAM;AACnB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,4BAAgB,OAAO,CAAC,GAAG,MAAM,GAAG;AAAA,UACtC;AACA,cAAI,WAAW,OAAO,oBAAoB,MAAM,IAAI;AACpD,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,4BAAgB,MAAM,KAAK,SAAS,CAAC,CAAC,GAAG,MAAM,GAAG;AAAA,UACpD;AAAA,QACF;AAEA,eAAO,IAAI,MAAM,KAAK,KAAK;AAAA,MAC7B;AAEA,eAAS,iBAAiB,KAAK;AAI7B,YAAI,QAAQ,OAAO,QAAQ;AAC3B,YAAI,aAAa,QAAQ,oBAAI,QAAM,uBAAO,OAAO,IAAI;AAErD,YAAI,QAAQ,OAAO,oBAAoB,GAAG;AAC1C,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,YAAY,MAAM,CAAC;AACvB,cAAI,OAAO,IAAI,SAAS;AACxB,cAAI,cAAc,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACpD,sBAAY,QAAQ,SAAS,SAAS;AACpC,gBAAI,OAAO,YAAY,UAAU;AAC/B,oBAAM,IAAI,MAAM,yCAAyC,YAAY,IAAI;AAAA,YAC3E;AACA,gBAAI,OAAO;AACT,yBAAW,IAAI,SAAS,SAAS;AAAA,YACnC,OAAO;AACL,yBAAW,OAAO,IAAI;AAAA,YACxB;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO,SAAS,GAAG;AACjB,iBAAO,QAAQ,WAAW,IAAI,CAAC,IAAI,WAAW,CAAC;AAAA,QACjD;AAAA,MACF;AAIA,UAAI,QAAQ,SAAS,QAAQ,OAAO;AAClC,aAAK,aAAa;AAClB,aAAK,SAAS;AACd,aAAK,SAAS;AACd,aAAK,QAAQ,CAAC;AACd,aAAK,MAAM;AAAA,MACb;AAEA,YAAM,UAAU,QAAQ,SAAS,MAAM,MAAM;AAC3C,aAAK,SAAS,QAAQ;AACtB,aAAK,QAAQ;AACb,aAAK,OAAO,OAAO,KAAK,OAAO;AAC/B,aAAK,MAAM,OAAO,KAAK,MAAM;AAC7B,aAAK,cAAc,OAAO,KAAK,cAAc;AAC7C,aAAK,aAAa,OAAO,KAAK,aAAY;AAC1C,aAAK,cAAc,OAAO,KAAK,cAAc;AAC7C,aAAK,SAAS,OAAO,KAAK,QAAQ,KAAK,UAAU;AACjD,aAAK,QAAQ,QAAQ,KAAK,QAAQ,KAAK,MAAM,MAAM,IAAI,CAAC;AACxD,eAAO;AAAA,MACT;AAEA,YAAM,UAAU,OAAO,WAAW;AAChC,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,KAAK,KAAK;AAAA,UACV,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK,MAAM,MAAM;AAAA,UACxB,aAAa,KAAK;AAAA,UAClB,YAAY,KAAK;AAAA,UACjB,aAAa,KAAK;AAAA,QACpB;AAAA,MACF;AAEA,YAAM,UAAU,WAAW,SAAS,OAAO;AACzC,YAAI,CAAC,SAAS,KAAK,UAAU,MAAO;AACpC,aAAK,QAAQ;AACb,YAAI,OAAO,KAAK,OAAO,KAAK;AAC5B,aAAK,SAAS,KAAK;AACnB,aAAK,QAAQ,KAAK;AAClB,aAAK,KAAK,KAAK;AACf,aAAK,OAAO,KAAK;AAAA,MACnB;AAEA,YAAM,UAAU,WAAW,WAAW;AACpC,aAAK,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,MAChC;AAEA,YAAM,UAAU,YAAY,SAAS,OAAO;AAC1C,aAAK,MAAM,KAAK,KAAK,KAAK;AAC1B,aAAK,SAAS,KAAK;AAAA,MACrB;AAEA,UAAI,MAAM,YAAY,SAAS,IAAI,QAAQ;AACzC,eAAO,GAAG,KAAK,MAAM;AAAA,MACvB,IAAI,SAAS,IAAI,QAAQ;AACvB,YAAI,QAAQ,GAAG,KAAK,MAAM;AAE1B,YAAI,MAAM,CAAC,EAAE,WAAW,GAAG;AACzB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,YAAM,UAAU,YAAY,SAAS,OAAO;AAC1C,YAAI,aAAa,KAAK,OAAO;AAC7B,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAI,MAAM,IAAI,CAAC,MAAM,QAAW;AAC9B,mBAAO,KAAK,OAAO,CAAC;AAAA,UACtB;AAAA,QACF;AACA,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAEA,eAAS,gBAAgB;AACvB,eAAO,KAAK;AAAA,MACd;AAEA,YAAM,UAAU,OAAO,WAAW;AAChC,YAAI,QAAQ,KAAK;AAGjB,YAAI,KAAK,aAAa;AACpB,cAAI,QAAQ,KAAK,OAAO,KAAK,aAAa,KAAK,YAAY,KAAK;AAChE,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,KAAK;AAClB,YAAI,UAAU,OAAO,QAAQ;AAC3B;AAAA,QACF;AAGA,YAAI,QAAQ,KAAK,KAAK,OAAO,WAAW,KAAK,CAAC;AAC9C,YAAI,OAAO;AACT,iBAAO,KAAK,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,KAAK;AAAA,QACvD;AAGA,YAAI,KAAK,KAAK;AACd,WAAG,YAAY;AACf,YAAI,QAAQ,IAAI,IAAI,MAAM;AAG1B,YAAI,QAAQ,KAAK;AACjB,YAAI,SAAS,MAAM;AACjB,iBAAO,KAAK,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,MAAM,GAAG,KAAK;AAAA,QACrE;AAEA,YAAI,QAAQ,KAAK,UAAU,KAAK;AAChC,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,MAAM,YAAY,MAAM,UAAU,OAAO;AAC3C,eAAK,cAAc;AACnB,eAAK,aAAa;AAGlB,iBAAO,KAAK,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,KAAK,GAAG,KAAK;AAAA,QACnE;AAEA,eAAO,KAAK,OAAO,OAAO,MAAM,KAAK;AAAA,MACvC;AAEA,YAAM,UAAU,SAAS,SAAS,OAAO,MAAM,QAAQ;AAErD,YAAI,aAAa;AACjB,YAAI,MAAM,YAAY;AACpB,cAAI,UAAU;AACd,cAAI,KAAK;AACT,cAAI,SAAS,MAAM;AACjB,yBAAa;AAAA,UACf,OAAO;AACL,mBAAO,QAAQ,KAAK,IAAI,GAAG;AAAE;AAAc,mBAAK,QAAQ;AAAA,YAAU;AAAA,UACpE;AAAA,QACF;AAEA,YAAI,QAAQ;AAAA,UACV,MAAO,OAAO,MAAM,SAAS,cAAc,MAAM,KAAK,IAAI,KAAM,MAAM;AAAA,UACtE,OAAO,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,IAAI,IAAI;AAAA,UAC/D;AAAA,UACA,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA,MAAM,KAAK;AAAA,UACX,KAAK,KAAK;AAAA,QACZ;AAGA,YAAI,OAAO,KAAK;AAChB,aAAK,SAAS;AACd,aAAK,QAAQ;AACb,YAAI,eAAe,GAAG;AACpB,eAAK,MAAM,OAAO,KAAK;AAAA,QACzB,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AAGA,YAAI,MAAM,aAAa;AACrB,cAAI,MAAM,IAAI,MAAM,KAAK,YAAY,OAAO,gBAAgB,CAAC;AAC7D,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAK,MAAK,SAAS;AAAA,iBACpB,MAAM,KAAM,MAAK,UAAU,MAAM,IAAI;AAAA,iBACrC,MAAM,KAAM,MAAK,SAAS,MAAM,IAAI;AAE7C,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,YAAI,gBAAgB,SAAS,OAAO;AAClC,eAAK,QAAQ;AAAA,QACf;AAEA,sBAAc,UAAU,OAAO,WAAW;AACxC,cAAI,QAAQ,KAAK,MAAM,KAAK;AAC5B,iBAAO,EAAC,OAAO,OAAO,MAAM,CAAC,MAAK;AAAA,QACpC;AAEA,sBAAc,UAAU,OAAO,QAAQ,IAAI,WAAW;AACpD,iBAAO;AAAA,QACT;AAEA,cAAM,UAAU,OAAO,QAAQ,IAAI,WAAW;AAC5C,iBAAO,IAAI,cAAc,IAAI;AAAA,QAC/B;AAAA,MACF;AAEA,YAAM,UAAU,cAAc,SAAS,OAAO,SAAS;AACrD,YAAI,SAAS,MAAM;AAEjB,cAAI,OAAO,KAAK,OAAO,MAAM,KAAK,KAAK;AACvC,cAAI,QAAQ;AAAA,YACV;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI;AAAA,YAC5C,MAAM,KAAK;AAAA,YACX,KAAK,KAAK;AAAA,UACZ;AAAA,QACF;AAEA,YAAI,iBAAiB;AACrB,YAAI,qBAAqB,KAAK,IAAI,MAAM,OAAO,gBAAgB,CAAC;AAChE,YAAI,oBAAoB,MAAM,OAAO;AACrC,YAAI,iBAAiB,OAAO,iBAAiB,EAAE;AAC/C,YAAI,iBAAiB;AAAA,UACjB,KAAK;AAAA,UACJ,KAAK,OAAO,MAAM,OAAQ,iBAAiB;AAAA,QAC9C,EACC,MAAM,GAAG,CAAC;AACb,YAAI,aAAa,CAAC;AAClB,mBAAW,KAAK,UAAU,cAAc,MAAM,OAAO,UAAU,MAAM,MAAM,GAAG;AAC9E,mBAAW,KAAK,EAAE;AAClB,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAI,OAAO,eAAe,CAAC;AAC3B,cAAI,SAAS,qBAAqB;AAClC,qBAAW,KAAK,IAAI,OAAO,MAAM,GAAG,cAAc,IAAI,OAAO,IAAI;AACjE,cAAI,WAAW,MAAM,MAAM;AACzB,uBAAW,KAAK,IAAI,IAAI,iBAAiB,MAAM,MAAM,CAAC,IAAI,GAAG;AAAA,UAC/D;AAAA,QACF;AACA,eAAO,WAAW,KAAK,IAAI;AAAA,MAC7B;AAEA,YAAM,UAAU,QAAQ,WAAW;AACjC,eAAO,IAAI,MAAM,KAAK,QAAQ,KAAK,KAAK;AAAA,MAC1C;AAEA,YAAM,UAAU,MAAM,SAAS,WAAW;AACxC,eAAO;AAAA,MACT;AAGA,aAAO;AAAA,QACL;AAAA,QACA,QAAQ;AAAA,QACR,OAAO,OAAO,OAAO,EAAC,OAAO,KAAI,CAAC;AAAA,QAClC,UAAU,OAAO,OAAO,EAAC,UAAU,KAAI,CAAC;AAAA,QACxC,UAAU;AAAA,MACZ;AAAA,IAEF,CAAC;AAAA;AAAA;;;ACjoBD;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,QAAQ,QAAQ,SAAS;AACjC,QAAM,QAAQ,gBAAgB,aAAc;AAC5C,YAAQ,SAAS;AAAA,MACb,MAAM;AAAA,QACF,YAAY,EAAE,OAAO,MAAM,OAAO,MAAM,IAAI;AAAA,QAC5C,QAAQ;AAAA,UACJ,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,OAAO,SAAO,IAAI,MAAM,GAAG,EAAE,EAAE,QAAQ,OAAO,GAAG;AAAA,QACrD;AAAA,QACA,UAAU;AAAA,UACN,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,SAAO,IAAI,UAAU,CAAC,EAAE,KAAK;AAAA,QACxC;AAAA,QACA,YAAY;AAAA,QACZ,KAAK,EAAE,OAAO,KAAK,KAAK,EAAE;AAAA,QAC1B,SAAS,EAAE,YAAY,MAAM,OAAO,eAAe;AAAA,MACvD;AAAA,MACA,KAAK;AAAA,QACD,QAAQ;AAAA,UACJ,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK;AAAA,QACzC;AAAA,QACA,aAAa;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK;AAAA,QACzC;AAAA,QACA,eAAe;AAAA,UACX,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,OAAO,SAAO,IAAI,UAAU,CAAC,EAAE,KAAK;AAAA,QACxC;AAAA,QACA,KAAK,EAAE,OAAO,KAAK,KAAK,EAAE;AAAA,MAC9B;AAAA,MACA,QAAQ;AAAA,QACJ,QAAQ;AAAA,UACJ,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,OAAO,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK;AAAA,QACzC;AAAA,QACA,MAAM;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,SAAO,IAAI,UAAU,GAAG,IAAI,QAAQ,GAAG,CAAC,EAAE,KAAK;AAAA,QAC1D;AAAA,QACA,KAAK,EAAE,OAAO,UAAU,KAAK,EAAE;AAAA,MACnC;AAAA,IACJ;AACA,YAAQ,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM;AAAA;AAAA;;;AC5DnD;AAAA;AAAA;AAyEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa;AACrB,YAAQ,QAAQC;AAChB,QAAM,aAAa;AACnB,QAAM,aAAa,CAAC,QAAQ;AAAA,MACxB,QAAQ,GAAG;AAAA,MACX,MAAM,GAAG;AAAA,MACT,KAAK,GAAG;AAAA,MACR,MAAM,GAAG;AAAA,MACT,YAAY,GAAG;AAAA,IACnB;AACA,QAAM,eAAe,CAAC,SAAS,SAAS,YAAY,SAAS,YAAY,SAAS;AAClF,aAAS,oBAAoB,IAAI,OAAO;AACpC,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,iBAAW,KAAK,OAAO;AACnB,cAAM,QAAQ,EAAE,IAAI;AACpB,gBAAQ;AACR,gBAAQ,EAAE,MAAM;AAAA,UACZ,KAAK;AACD,qBAAS,EAAE;AACX;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,qBAAS;AACT;AAAA,UACJ;AACI,kBAAM,IAAI,WAAW,IAAI,uDAAuD,KAAK,EAAE;AAAA,QAC/F;AAAA,MACJ;AACA,YAAM,IAAI;AAAA,QACN,MAAM;AAAA,QACN,OAAO,MAAM,KAAK;AAAA,QAClB,KAAK,OAAO,OAAO,CAAC,GAAG,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAAA,MACjD;AACA,aAAO,CAAC,CAAC;AAAA,IACb;AACA,QAAM,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAM,oBAAoB,CAAC,QAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO;AAMvE,QAAM,aAAN,cAAyB,MAAM;AAAA;AAAA,MAE3B,YAAY,IAAI,KAAK;AACjB,cAAM,WAAW,MAAM,YAAY,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACJ;AACA,YAAQ,aAAa;AACrB,QAAM,SAAN,MAAa;AAAA,MACT,YAAY,KAAK,KAAK;AAClB,YAAI,IAAI,IAAI,IAAI;AAChB,aAAK,QAAQ,WAAW,MAAM,MAAM,GAAG;AACvC,aAAK,gBAAgB,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,cAAc,QAAQ,OAAO,SAAS,KAAK;AACnH,aAAK,eAAe,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK;AACjH,aAAK,UAAU,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,YAAY,QAAQ,OAAO,SAAS,KAAK;AAC3G,aAAK,oBAAoB,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,sBAAsB,QAAQ,OAAO,SAAS,KAAK;AAAA,MACnI;AAAA,MACA,QAAQ;AACJ,eAAO,KAAK,UAAU,OAAO,IAAI;AAAA,MACrC;AAAA,MACA,eAAe,IAAI,MAAM,KAAK;AAC1B,YAAI,IAAI,CAAC,MAAM,KAAK;AAChB,cAAI,SAAS,UAAU;AACnB,kBAAM,IAAI,WAAW,IAAI,YAAY,GAAG,2BAA2B;AAAA,UACvE;AAAA,QACJ,WACS,SAAS,UAAU;AACxB,gBAAM,OAAO,SAAS,WAAW,KAAK,eAAe,KAAK;AAC1D,cAAI,KAAK,oBAAoB,KAAK,SAAS,KAAK,CAAC,KAAK,SAAS,GAAG,GAAG;AACjE,kBAAM,MAAM,OAAO,IAAI,SAAS,GAAG;AACnC,kBAAM,IAAI,WAAW,IAAI,GAAG;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,YAAY,EAAE,OAAO,IAAI,GAAG,UAAU,KAAK,MAAM;AAC7C,cAAM,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC,GAAG,IAAI;AACxC,YAAI,SAAS,YAAY,SAAS;AAC9B,qBAAW;AAAA,iBACN,KAAK;AACV,qBAAW;AACf,mBAAW,MAAM,KAAK,OAAO;AACzB,kBAAQ,GAAG,MAAM;AAAA,YACb,KAAK;AACD,kBAAI,SAAS,UAAU;AACnB,sBAAM,IAAI,WAAW,IAAI,qCAAqC;AAAA,cAClE;AACA,kBAAI,IAAI,MAAM,SAAS,GAAG;AACtB,sBAAM,IAAI,WAAW,IAAI,wCAAwC;AAAA,cACrE;AACA,kBAAI,eAAe,OAAO,GAAG,KAAK;AAClC,kBAAI,QAAQ,GAAG;AACf,kBAAI,cAAc,GAAG;AACrB;AAAA,YACJ,KAAK,QAAQ;AACT,mBAAK,eAAe,IAAI,MAAM,GAAG,KAAK;AACtC,kBAAI,MAAM,KAAK;AAAA,gBACX,KAAK,GAAG;AAAA,gBACR,QAAQ,KAAK,UAAU,QAAQ;AAAA,gBAC/B,KAAK,WAAW,EAAE;AAAA,cACtB,CAAC;AACD;AAAA,YACJ;AAAA,YACA,KAAK;AACD,qBAAO;AAAA;AAAA,YAEX;AACI,oBAAM,IAAI,WAAW,IAAI,2BAA2B,GAAG,IAAI,EAAE;AAAA,UACrE;AAAA,QACJ;AACA,cAAM,IAAI,WAAW,MAAM,wBAAwB;AAAA,MACvD;AAAA,MACA,cAAc,IAAI,UAAU;AACxB,cAAM,MAAM,WAAW,EAAE;AACzB,cAAM,UAAU,KAAK,MAAM,KAAK;AAChC,YAAI,CAAC;AACD,gBAAM,IAAI,WAAW,MAAM,wBAAwB;AACvD,YAAI,QAAQ,QAAQ;AACpB,YAAI,cAAc,QAAQ;AAC1B,YAAI,KAAK,WACJ,QAAQ,SAAS,iBAAiB,QAAQ,SAAS,gBACpD,CAAC,eAAe,SAAS,QAAQ,KAAK,GAAG;AACzC,gBAAM,MAAM,0CAA0C,QAAQ,KAAK;AACnE,gBAAM,IAAI,WAAW,IAAI,GAAG;AAAA,QAChC;AACA,gBAAQ,QAAQ,MAAM;AAAA,UAClB,KAAK;AACD,mBAAO,EAAE,MAAM,YAAY,KAAK,GAAG,OAAO,IAAI;AAAA,UAClD,KAAK,eAAe;AAChB,kBAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,gBAAI,CAAC;AACD,oBAAM,IAAI,WAAW,MAAM,wBAAwB;AAEvD,gBAAI,IAAI,SAAS,OAAO;AACpB,oBAAM,IAAI,WAAW,KAAK,2BAA2B,IAAI,IAAI,EAAE;AAAA,YACnE;AACA,gBAAI,QAAQ,IAAI;AAChB,gBAAI,aAAa,QAAQ,MAAM,YAAY,CAAC,GAAG;AAC3C,oBAAM,IAAI,WAAW,SAAS,4BAA4B,QAAQ,KAAK,EAAE;AAAA,YAC7E;AACA,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,GAAG;AAAA,cACR,KAAK,QAAQ;AAAA,cACb;AAAA,YACJ;AAAA,UACJ;AAAA,UACA,KAAK,aAAa;AACd,gBAAI,aAAa,QAAQ,MAAM,YAAY,CAAC,GAAG;AAC3C,oBAAM,MAAM,4BAA4B,QAAQ,KAAK;AACrD,oBAAM,IAAI,WAAW,SAAS,GAAG;AAAA,YACrC;AACA,gBAAI,QAAQ,KAAK,UAAU,KAAK,SAAS,QAAQ,QAAQ;AACzD,gBAAI,KAAK,UAAU,MAAM,SAAS,GAAG;AACjC,sBAAQ,oBAAoB,IAAI,KAAK;AAAA,YACzC;AACA,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,GAAG;AAAA,cACR,KAAK,QAAQ;AAAA,cACb;AAAA,cACA;AAAA,YACJ;AAAA,UACJ;AAAA,UACA,KAAK;AAED,gBAAI,aAAa,QAAQ,KAAK,GAAG;AAC7B,qBAAO,KAAK,YAAY,IAAI,UAAU,KAAK,QAAQ,KAAK;AAAA,YAC5D,OACK;AACD,oBAAM,IAAI,WAAW,SAAS,0BAA0B,QAAQ,KAAK,EAAE;AAAA,YAC3E;AAAA;AAAA,UAEJ;AACI,kBAAM,IAAI,WAAW,SAAS,2BAA2B,QAAQ,IAAI,EAAE;AAAA,QAC/E;AAAA,MACJ;AAAA,MACA,UAAU,UAAU,QAAQ;AACxB,cAAM,SAAS,CAAC;AAChB,YAAI,UAAU;AACd,mBAAW,MAAM,KAAK,OAAO;AACzB,cAAI,GAAG,SAAS,YAAY;AACxB,gBAAI;AACA,wBAAU;AACd,mBAAO,KAAK,KAAK,cAAc,IAAI,QAAQ,CAAC;AAAA,UAChD,WACS,GAAG,SAAS,gBAAgB,UAAU;AAC3C,gBAAI;AACA,wBAAU;AACd,mBAAO,KAAK,EAAE,MAAM,cAAc,KAAK,WAAW,EAAE,EAAE,CAAC;AAAA,UAC3D,WACS,GAAG,SAAS,SAAS,CAAC,QAAQ;AACnC,mBAAO;AAAA,UACX,OACK;AACD,gBAAI,QAAQ,GAAG;AACf,gBAAI,CAAC,YAAY,GAAG,SAAS,YAAY,MAAM,CAAC,MAAM,KAAK;AACvD,kBAAI,MAAM,SAAS,GAAG,GAAG;AACrB,sBAAM,SAAS,+BAA+B,KAAK;AACnD,sBAAM,IAAI,WAAW,IAAI,MAAM;AAAA,cACnC;AACA,sBAAQ,GAAG;AAAA,YACf;AACA,gBAAI,SAAS;AACT,sBAAQ,SAAS;AACjB,sBAAQ,IAAI,QAAQ,GAAG;AACvB,sBAAQ,IAAI,cAAc,GAAG;AAAA,YACjC,OACK;AACD,wBAAU,EAAE,MAAM,WAAW,OAAO,KAAK,WAAW,EAAE,EAAE;AACxD,qBAAO,KAAK,OAAO;AAAA,YACvB;AAAA,UACJ;AAAA,QACJ;AACA,YAAI;AACA,iBAAO;AACX,cAAM,IAAI,WAAW,MAAM,wBAAwB;AAAA,MACvD;AAAA,IACJ;AASA,aAASA,OAAM,KAAK,UAAU,CAAC,GAAG;AAC9B,YAAM,SAAS,IAAI,OAAO,KAAK,OAAO;AACtC,aAAO,OAAO,MAAM;AAAA,IACxB;AAAA;AAAA;;;ACzTA,mBAAsB;;;ACAtB,oBAAsB;AAEtB,SAAS,cAAc,QAAQ,SAAS;AACtC,MAAI,CAAC,OAAO,OAAO,CAAC,UAAU,MAAM,SAAS,SAAS,EAAE,QAAQ;AAC9D,WAAO,OAAO,IAAI,CAAC,UAAU,QAAQ,MAAM,KAAK,CAAC;AAAA,EACnD;AACA,SAAO,OAAO,IAAI,CAAC,UAAU;AAN/B;AAOI,QAAI,MAAM,SAAS,WAAW;AAC5B,aAAO,QAAQ,MAAM,KAAK;AAAA,IAC5B,WAAW,MAAM,SAAS,cAAc;AACtC,aAAO;AAAA,IACT,WAAW,MAAM,SAAS,YAAY;AACpC,aAAO,CAAC,MAAM,GAAG;AAAA,IACnB,WAAW,MAAM,SAAS,YAAY;AACpC,YAAM,UAAS,oCAAO,UAAP,mBAAe;AAC9B,UAAI,QAAQ;AACV,eAAO,CAAC,MAAM,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,CAAC;AAAA,MACnD,OAAO;AACL,eAAO,CAAC,MAAM,KAAK,MAAM,GAAG;AAAA,MAC9B;AAAA,IACF;AACA,UAAM,SAAS,MAAM;AACrB,UAAM,cAAc,CAAC;AACrB,UAAM,MAAM,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI;AAC7C,kBAAY,IAAI,IAAI,cAAc,SAAS,OAAO;AAAA,IACpD,CAAC;AACD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,QACE;AAAA,QACA,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,SAAS,eAAe,SAAS,UAAU,CAAC,MAAM,GAAG;AACnD,MAAI;AACF,WAAO,kBAAc,qBAAM,OAAO,GAAG,OAAO;AAAA,EAC9C,SAAS,GAAG;AACV,YAAQ,MAAM,GAAG,EAAE,OAAO;AAAA;AAAA,WAEnB,OAAO,EAAE;AAChB,WAAO,CAAC,OAAO;AAAA,EACjB;AACF;;;AD3CA,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM;AACrC,IAAM,aAAa,CAAC,MAAM,OAAO,MAAM;AAEvC,IAAM,QAAwB,oBAAI,IAAI;AACtC,IAAM,gBAAgB;AACtB,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACvD,SAAO,CAAC,GAAG,KAAK,aAAa;AAC/B;AACA,SAAS,KAAK,SAAS,OAAO,QAAQ;AACpC,QAAM,WAAW,iBAAiB,OAAO;AACzC,QAAM,YAAY;AAAA,IAChB,MAAM,SAAS,QAAQ,UAAU,MAAM;AAAA,IACvC,MAAM,IAAI,KAAK,eAAe,UAAU,MAAM;AAAA,EAChD;AACA,SAAO,UAAU,OAAO,SAAS,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK;AACnE;AACA,SAAS,OAAO,SAAS,OAAO,QAAQ;AACtC,QAAM,WAAW,iBAAiB,OAAO;AACzC,QAAM,YAAY;AAAA,IAChB,MAAM,SAAS,UAAU,UAAU,MAAM;AAAA,IACzC,MAAM,IAAI,KAAK,aAAa,UAAU,MAAM;AAAA,EAC9C;AACA,SAAO,UAAU,OAAO,KAAK;AAC/B;AACA,SAAS,OAAO,SAAS,SAAS,OAAO,EAAE,SAAS,GAAG,GAAG,MAAM,GAAG;AACjE,QAAM,WAAW,iBAAiB,OAAO;AACzC,QAAM,UAAU,UAAU;AAAA,IACxB,MAAM,SAAS,kBAAkB,QAAQ;AAAA,IACzC,MAAM,IAAI,KAAK,YAAY,UAAU,EAAE,MAAM,UAAU,CAAC;AAAA,EAC1D,IAAI;AAAA,IACF,MAAM,SAAS,mBAAmB,QAAQ;AAAA,IAC1C,MAAM,IAAI,KAAK,YAAY,UAAU,EAAE,MAAM,WAAW,CAAC;AAAA,EAC3D;AACA,SAAO,MAAM,KAAK,KAAK,MAAM,QAAQ,OAAO,QAAQ,MAAM,CAAC,KAAK,MAAM;AACxE;AACA,SAAS,YAAY,QAAQ,WAAW;AACtC,QAAM,MAAM,OAAO;AACnB,MAAI,YAAY,MAAM,IAAI,GAAG;AAC7B,MAAI,CAAC,WAAW;AACd,gBAAY,UAAU;AACtB,UAAM,IAAI,KAAK,SAAS;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,SAAS,MAAM,SAAS,SAAS;AACxC,QAAM,YAAY,QAAQ,KAAK,GAAG;AAClC,SAAO,GAAG,IAAI,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO,CAAC;AACxD;AAEA,IAAM,UAAU;AAAA,EACd,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB,CAAC,QAAQ,eAAeC,WAAU,CAAC,MAAM;AACjE,QAAM,UAAU,iBAAiB;AACjC,QAAM,QAAQ,CAAC,WAAW;AACxB,WAAO,OAAO,WAAW,WAAW,SAASA,SAAQ,MAAM,KAAK,EAAE,OAAO,OAAO;AAAA,EAClF;AACA,QAAM,oBAAoB,CAAC,OAAO,YAAY;AAC5C,UAAM,eAAe,OAAO,KAAKA,QAAO,EAAE,SAAS,MAAM,QAAQ,IAAI;AACrE,UAAM,WAAW,OAAO,SAAS,OAAO,YAAY;AACpD,WAAO,QAAQ,QAAQ,IAAI,OAAO,eAAe,GAAG,GAAG,QAAQ;AAAA,EACjE;AACA,SAAO;AAAA,IACL,QAAQ,CAAC,OAAO,UAAU;AACxB,YAAM,EAAE,SAAS,EAAE,IAAI;AACvB,YAAM,UAAU,OAAO,SAAS,OAAO,OAAO,KAAK;AACnD,aAAO,kBAAkB,QAAQ,QAAQ,OAAO;AAAA,IAClD;AAAA,IACA,eAAe,CAAC,OAAO,UAAU;AAC/B,YAAM,EAAE,SAAS,EAAE,IAAI;AACvB,YAAM,UAAU,OAAO,SAAS,MAAM,OAAO,KAAK;AAClD,aAAO,kBAAkB,QAAQ,QAAQ,OAAO;AAAA,IAClD;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,CAAC,OAAO,WAAW,OAAO,SAAS,OAAO,MAAM,MAAM,CAAC;AAAA,IAC/D,MAAM,CAAC,OAAO,WAAW,KAAK,SAAS,OAAO,MAAM,MAAM,CAAC;AAAA,EAC7D;AACF;AACA,IAAM,kBAAkB,CAAC,OAAO,UAAU,MAAM,KAAK,KAAK,MAAM;AAChE,SAAS,YAAY,aAAa,QAAQ,SAAS;AACjD,SAAO,CAAC,SAAS,CAAC,GAAGA,aAAY;AAC/B,UAAM,aAAa,kBAAkB,QAAQ,SAASA,QAAO;AAC7D,UAAM,gBAAgB,CAAC,QAAQ,oBAAoB,UAAU;AAC3D,UAAI,CAAC,MAAM,QAAQ,MAAM;AACvB,eAAO;AACT,aAAO,OAAO,OAAO,CAAC,SAAS,UAAU;AACvC,YAAI,UAAU,OAAO,mBAAmB;AACtC,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,SAAS,KAAK,GAAG;AACnB,iBAAO,UAAU;AAAA,QACnB;AACA,cAAM,CAAC,MAAM,MAAM,MAAM,IAAI;AAC7B,YAAI,qBAAqB,CAAC;AAC1B,YAAI,SAAS,YAAY,SAAS,mBAAmB,SAAS,UAAU;AACtE,iBAAO,QAAQ,MAAM,EAAE;AAAA,YACrB,CAAC,CAAC,KAAK,MAAM,MAAM;AACjB,iCAAmB,GAAG,IAAI;AAAA,gBACxB;AAAA,gBACA,SAAS,YAAY,SAAS;AAAA,cAChC;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,+BAAqB;AAAA,QACvB;AACA,YAAI;AACJ,YAAI,MAAM;AACR,gBAAM,YAAY,WAAW,IAAI;AACjC,kBAAQ,UAAU,OAAO,IAAI,GAAG,kBAAkB;AAAA,QACpD,OAAO;AACL,kBAAQ,OAAO,IAAI;AAAA,QACrB;AACA,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,eAAO,UAAU;AAAA,MACnB,GAAG,EAAE;AAAA,IACP;AACA,UAAM,SAAS,cAAc,WAAW;AACxC,QAAI,SAAS,MAAM,KAAK,cAAc,KAAK,MAAM,GAAG;AAClD,iBAAO,oBAAM,OAAO,KAAK,CAAC;AAAA,IAC5B;AACA,QAAI,SAAS,MAAM;AACjB,aAAO,OAAO,KAAK;AACrB,WAAO,SAAS,OAAO,MAAM,IAAI;AAAA,EACnC;AACF;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU;AACzC,oBAAkB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACtE,SAAO;AACT;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,oBAAgB,MAAM,WAAW,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,OAAO,UAAU;AAClB,QAAI;AACJ,KAAC,KAAK,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC;AAC5C,SAAK,QAAQ,KAAK,EAAE,KAAK,QAAQ;AACjC,WAAO,MAAM,KAAK,eAAe,OAAO,QAAQ;AAAA,EAClD;AAAA,EACA,eAAe,OAAO,UAAU;AAC9B,UAAM,iBAAiB,KAAK,cAAc,KAAK;AAC/C,QAAI,CAAC;AACH;AACF,UAAM,QAAQ,eAAe,QAAQ,QAAQ;AAC7C,QAAI,CAAC;AACH,qBAAe,OAAO,OAAO,CAAC;AAAA,EAClC;AAAA,EACA,KAAK,UAAU,MAAM;AACnB,UAAM,iBAAiB,KAAK,cAAc,KAAK;AAC/C,QAAI,CAAC;AACH;AACF,mBAAe,IAAI,CAAC,aAAa,SAAS,MAAM,MAAM,IAAI,CAAC;AAAA,EAC7D;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,iBAAiB,KAAK,QAAQ,KAAK;AACzC,WAAO,MAAM,QAAQ,cAAc,IAAI,iBAAiB;AAAA,EAC1D;AACF;AAEA,IAAI,YAAY,OAAO;AACvB,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,kBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,SAAO;AACT;AACA,IAAM,OAAN,cAAmB,aAAa;AAAA,EAC9B,YAAY,QAAQ;AAClB,UAAM;AACN,kBAAc,MAAM,WAAW,EAAE;AACjC,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,eAAe,CAAC,CAAC;AACrC,kBAAc,MAAM,aAAa,CAAC,CAAC;AACnC,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,kBAAkB;AAItC,kBAAc,MAAM,KAAK,KAAK,EAAE,KAAK,IAAI,CAAC;AAC1C,QAAI,MAAuC;AACzC,WAAK,oBAAoB,cAAc;AAAA,IACzC;AACA,QAAI,OAAO,WAAW;AACpB,WAAK,WAAW,OAAO;AACzB,QAAI,OAAO,YAAY;AACrB,WAAK,KAAK,OAAO,QAAQ;AAC3B,QAAI,OAAO,cAAc;AACvB,WAAK,eAAe,OAAO,UAAU;AACvC,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO,SAAS;AACvD,WAAK,SAAS,OAAO,UAAU,eAAe,OAAO,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,KAAK,OAAO,KAAK,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACf,WAAO,KAAK,YAAY,KAAK,OAAO,KAAK,CAAC;AAAA,EAC5C;AAAA,EACA,gBAAgB,QAAQ,YAAY;AAClC,UAAM,kBAAkB,KAAK,YAAY,MAAM;AAC/C,QAAI,CAAC,iBAAiB;AACpB,WAAK,YAAY,MAAM,IAAI;AAAA,IAC7B,OAAO;AACL,aAAO,OAAO,iBAAiB,UAAU;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,oBAAoB,UAAU;AAC5B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,iBAAiB,YAAY;AAC1C,QAAI,cAAc,MAAM;AACtB,WAAK,gBAAgB,iBAAiB,UAAU;AAAA,IAClD,OAAO;AACL,aAAO,KAAK,eAAe,EAAE;AAAA,QAC3B,CAAC,WAAW,KAAK,gBAAgB,QAAQ,gBAAgB,MAAM,CAAC;AAAA,MAClE;AAAA,IACF;AACA,SAAK,KAAK,QAAQ;AAAA,EACpB;AAAA,EACA,MAAM,QAAQ,UAAU;AACtB,UAAM,gBAAgB,KAAK,UAAU,MAAM;AAC3C,QAAI,CAAC,eAAe;AAClB,WAAK,UAAU,MAAM,IAAI;AAAA,IAC3B,OAAO;AACL,aAAO,OAAO,eAAe,QAAQ;AAAA,IACvC;AAAA,EACF;AAAA,EACA,KAAK,kBAAkB,UAAU;AAC/B,QAAI,OAAO,oBAAoB,YAAY,OAAO,aAAa,UAAU;AACvE,WAAK,MAAM,kBAAkB,QAAQ;AAAA,IACvC,OAAO;AACL,aAAO,QAAQ,gBAAgB,EAAE;AAAA,QAC/B,CAAC,CAAC,QAAQ,SAAS,MAAM,KAAK,MAAM,QAAQ,SAAS;AAAA,MACvD;AAAA,IACF;AACA,SAAK,KAAK,QAAQ;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,EAAE,QAAQ,SAAS,SAAS,GAAG;AAC7C,SAAK,UAAU;AACf,SAAK,WAAW,WAAW;AAC3B,SAAK,UAAU,KAAK,OAAO,IAAI;AAC/B,SAAK,KAAK,QAAQ;AAAA,EACpB;AAAA,EACA,SAAS,QAAQ,SAAS;AACxB,QAAI,MAAuC;AACzC,UAAI,CAAC,KAAK,UAAU,MAAM,GAAG;AAC3B,gBAAQ,KAAK,wBAAwB,MAAM,eAAe;AAAA,MAC5D;AAAA,IACF;AACA,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,KAAK,QAAQ;AAAA,EACpB;AAAA,EACA,EAAE,IAAI,QAAQ,SAAS;AACrB,QAAI,UAAU,mCAAS;AACvB,QAAI,CAAC,IAAI;AACP,WAAK;AAAA,IACP;AACA,QAAI,CAAC,SAAS,EAAE,GAAG;AACjB,eAAS,GAAG,UAAU;AACtB,gBAAU,GAAG;AACb,WAAK,GAAG;AAAA,IACV;AACA,UAAM,eAAe,KAAK,SAAS,EAAE;AACrC,UAAM,iBAAiB,iBAAiB;AACxC,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,gBAAgB;AAC7B,aAAO,WAAW,OAAO,IAAI,QAAQ,KAAK,SAAS,EAAE,IAAI;AAAA,IAC3D;AACA,QAAI,gBAAgB;AAClB,WAAK,KAAK,WAAW,EAAE,IAAI,QAAQ,KAAK,QAAQ,CAAC;AAAA,IACnD;AACA,QAAI,cAAc,gBAAgB,WAAW;AAC7C,QAAI,SAAS,WAAW,GAAG;AACzB,UAAI,KAAK,kBAAkB;AACzB,sBAAc,KAAK,iBAAiB,WAAW;AAAA,MACjD,OAAO;AACL,gBAAQ,KAAK;AAAA;AAAA,IAEjB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAMd;AAAA,MACK;AAAA,IACF;AACA,QAAI,SAAS,WAAW,KAAK,cAAc,KAAK,WAAW;AACzD,aAAO,KAAK,MAAM,IAAI,WAAW,GAAG;AACtC,QAAI,SAAS,WAAW;AACtB,aAAO;AACT,WAAO;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP,EAAE,QAAQ,mCAAS,OAAO;AAAA,EAC5B;AAAA,EACA,KAAK,OAAO,QAAQ;AAClB,WAAO,KAAK,KAAK,YAAY,KAAK,SAAS,OAAO,MAAM;AAAA,EAC1D;AAAA,EACA,OAAO,OAAO,QAAQ;AACpB,WAAO,OAAO,KAAK,YAAY,KAAK,SAAS,OAAO,MAAM;AAAA,EAC5D;AACF;AACA,SAAS,UAAU,SAAS,CAAC,GAAG;AAC9B,SAAO,IAAI,KAAK,MAAM;AACxB;AAEA,IAAM,OAAO,UAAU;", "names": ["ErrorType", "unraw", "parse", "formats"]}