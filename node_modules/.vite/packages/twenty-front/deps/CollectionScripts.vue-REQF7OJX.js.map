{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/CollectionScripts.vue2.js"], "sourcesContent": ["import { defineComponent as e, openBlock as o, createBlock as r, withCtx as n, createTextVNode as i } from \"vue\";\nimport p from \"../../components/ViewLayout/ViewLayoutSection.vue.js\";\nconst s = /* @__PURE__ */ e({\n  __name: \"CollectionScripts\",\n  setup(c) {\n    return (_, t) => (o(), r(p, null, {\n      title: n(() => t[0] || (t[0] = [\n        i(\"Scripts\")\n      ])),\n      _: 1\n    }));\n  }\n});\nexport {\n  s as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM,GAAG;AACP,WAAO,CAACA,IAAG,OAAO,UAAE,GAAG,YAAE,GAAG,MAAM;AAAA,MAChC,OAAO,QAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,QAC7B,gBAAE,SAAS;AAAA,MACb,EAAE;AAAA,MACF,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": ["_"]}