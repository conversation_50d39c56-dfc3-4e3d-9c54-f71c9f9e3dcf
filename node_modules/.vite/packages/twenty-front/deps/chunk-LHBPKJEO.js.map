{"version": 3, "sources": ["../../../../@scalar/api-client/dist/hooks/useSidebar.js", "../../../../@scalar/use-tooltip/dist/isMacOS.js"], "sourcesContent": ["import { inject as S, readonly as a, reactive as c, ref as b } from \"vue\";\nconst O = ({ layout: t }) => {\n  const e = c({}), r = b(t !== \"modal\");\n  return {\n    collapsedSidebarFolders: e,\n    isSidebarOpen: r\n  };\n}, p = Symbol(), m = () => {\n  const t = S(p);\n  if (!t) throw new Error(\"useSidebar must have injected SIDEBAR_SYMBOL\");\n  const { collapsedSidebarFolders: e, isSidebarOpen: r } = t, s = (o, l) => e[o] = l, d = (o) => e[o] = !e[o], i = (o) => r.value = o, n = () => r.value = !r.value;\n  return {\n    /** State */\n    collapsedSidebarFolders: a(e),\n    isSidebarOpen: a(r),\n    /** Actions */\n    setCollapsedSidebarFolder: s,\n    toggleSidebarFolder: d,\n    setSidebarOpen: i,\n    toggleSidebarOpen: n\n  };\n};\nexport {\n  p as SIDEBAR_SYMBOL,\n  O as createSidebarState,\n  m as useSidebar\n};\n", "/**\n * Check if the current platform is macOS.\n */\nfunction isMacOS() {\n    return typeof navigator !== 'undefined' ? /Mac/.test(navigator.platform) : false;\n}\n\nexport { isMacOS };\n"], "mappings": ";;;;;;;;AACA,IAAM,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM;AAC3B,QAAM,IAAI,SAAE,CAAC,CAAC,GAAG,IAAI,IAAE,MAAM,OAAO;AACpC,SAAO;AAAA,IACL,yBAAyB;AAAA,IACzB,eAAe;AAAA,EACjB;AACF;AANA,IAMG,IAAI,OAAO;AANd,IAMiB,IAAI,MAAM;AACzB,QAAM,IAAI,OAAE,CAAC;AACb,MAAI,CAAC,EAAG,OAAM,IAAI,MAAM,8CAA8C;AACtE,QAAM,EAAE,yBAAyB,GAAG,eAAe,EAAE,IAAI,GAAG,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC5J,SAAO;AAAA;AAAA,IAEL,yBAAyB,SAAE,CAAC;AAAA,IAC5B,eAAe,SAAE,CAAC;AAAA;AAAA,IAElB,2BAA2B;AAAA,IAC3B,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACrB;AACF;;;AClBA,SAAS,UAAU;AACf,SAAO,OAAO,cAAc,cAAc,MAAM,KAAK,UAAU,QAAQ,IAAI;AAC/E;", "names": []}