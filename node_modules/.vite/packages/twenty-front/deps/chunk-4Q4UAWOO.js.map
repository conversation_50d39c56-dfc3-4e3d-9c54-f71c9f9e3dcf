{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/ScalarHotkey.vue.js"], "sourcesContent": ["import { defineComponent as m, computed as r, openBlock as a, createElementBlock as c, normalizeProps as p, guardReactiveProps as s, unref as u, toDisplayString as f } from \"vue\";\nimport { useBindCx as l } from \"@scalar/components\";\nimport { isMacOS as x } from \"@scalar/use-tooltip\";\nconst b = /* @__PURE__ */ m({\n  __name: \"ScalarHotkey\",\n  props: {\n    hotkey: {},\n    modifier: {}\n  },\n  setup(t) {\n    const e = t, { cx: i } = l(), o = r(() => e.modifier || \"meta\"), n = r(() => `${o.value === \"meta\" ? x() ? \"⌘\" : \"^\" : o.value} ${e.hotkey}`);\n    return (d, y) => (a(), c(\"div\", p(s(\n      u(i)(\n        \"border-b-3 inline-block overflow-hidden rounded border-1/2 text-xxs rounded-b px-1 font-medium uppercase\"\n      )\n    )), f(n.value), 17));\n  }\n});\nexport {\n  b as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,EACb;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,EAAE,IAAIA,GAAE,IAAI,EAAE,GAAG,IAAI,SAAE,MAAM,EAAE,YAAY,MAAM,GAAG,IAAI,SAAE,MAAM,GAAG,EAAE,UAAU,SAAS,QAAE,IAAI,MAAM,MAAM,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE;AAC5I,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO,eAAE;AAAA,MAChC,MAAEA,EAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF,CAAC,GAAG,gBAAE,EAAE,KAAK,GAAG,EAAE;AAAA,EACpB;AACF,CAAC;", "names": ["i"]}