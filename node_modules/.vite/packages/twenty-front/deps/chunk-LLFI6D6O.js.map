{"version": 3, "sources": ["../../../../@scalar/api-client/dist/assets/keycap.ascii.js", "../../../../@scalar/api-client/dist/components/EmptyState.vue2.js", "../../../../@scalar/api-client/dist/components/EmptyState.vue.js"], "sourcesContent": ["const n = `          .:=+++++=================-\n         .--#*                       :.\n        .-:-**                        -.\n      .:-::-+*                        =:\n     .:-:::-=#                         ::\n    .-:::----**                        ..\n   .-:::::---=#                         ..\n  :-::::::----**                         ..\n.:-::::::::----*=                        ..\n.-::::::::------+-                        ..\n..::::::::-------=                         ..\n .:::::----------++                        ..\n   .:::----------+**+*++*+*++*+++*++++++++++:.\n   ------------+*+=-=======================.\n   .----------+*+=========================:.\n    :--------+*+=-========================.\n    .=------+*+=-========================:.\n     .=----+*+=-------=================+-.\n     .----+*+=----------================.\n     .:=-+*+=----------=-==============-\n      .-+*+=----------------===========.\n       .-+=------------------====-====:.`;\nexport {\n  n as default\n};\n", "import { defineComponent as o, openBlock as c, createElementBlock as n, createElementVNode as e, createVNode as t, unref as a } from \"vue\";\nimport l from \"../assets/keycap.ascii.js\";\nimport s from \"./ScalarAsciiArt.vue.js\";\nimport i from \"./ScalarHotkey.vue.js\";\nconst p = { class: \"flex-center flex w-full scale-75\" }, m = { class: \"relative\" }, d = { class: \"relative -ml-12\" }, h = /* @__PURE__ */ o({\n  __name: \"EmptyState\",\n  setup(_) {\n    return (f, r) => (c(), n(\"div\", p, [\n      e(\"div\", m, [\n        t(i, {\n          class: \"keycap-hotkey right-14 border-transparent text-xl\",\n          hotkey: \"\"\n        }),\n        t(s, {\n          art: a(l),\n          class: \"text-c-3 !leading-[6px]\"\n        }, null, 8, [\"art\"])\n      ]),\n      e(\"div\", d, [\n        r[0] || (r[0] = e(\"div\", { class: \"keycap-hotkey right-16 text-xl\" }, \"K\", -1)),\n        t(s, {\n          art: a(l),\n          class: \"keycap-n !leading-[6px]\"\n        }, null, 8, [\"art\"])\n      ])\n    ]));\n  }\n});\nexport {\n  h as default\n};\n", "import t from \"./EmptyState.vue2.js\";\n/* empty css                */\nimport o from \"../_virtual/_plugin-vue_export-helper.js\";\nconst r = /* @__PURE__ */ o(t, [[\"__scopeId\", \"data-v-6e1f579f\"]]);\nexport {\n  r as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACIV,IAAM,IAAI,EAAE,OAAO,mCAAmC;AAAtD,IAAyD,IAAI,EAAE,OAAO,WAAW;AAAjF,IAAoF,IAAI,EAAE,OAAO,kBAAkB;AAAnH,IAAsH,IAAoB,gBAAE;AAAA,EAC1I,QAAQ;AAAA,EACR,MAAM,GAAG;AACP,WAAO,CAAC,GAAGA,QAAO,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,MACjC,gBAAE,OAAO,GAAG;AAAA,QACV,YAAE,GAAG;AAAA,UACH,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,CAAC;AAAA,QACD,YAAE,GAAG;AAAA,UACH,KAAK,MAAE,CAAC;AAAA,UACR,OAAO;AAAA,QACT,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC;AAAA,MACrB,CAAC;AAAA,MACD,gBAAE,OAAO,GAAG;AAAA,QACVA,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,gBAAE,OAAO,EAAE,OAAO,iCAAiC,GAAG,KAAK,EAAE;AAAA,QAC7E,YAAE,GAAG;AAAA,UACH,KAAK,MAAE,CAAC;AAAA,UACR,OAAO;AAAA,QACT,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;;;ACxBD,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;", "names": ["r"]}