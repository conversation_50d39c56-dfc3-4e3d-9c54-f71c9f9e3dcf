import {
  whitespaceSensitiveTagNames
} from "./chunk-LG6Z3D2E.js";
import {
  embedded,
  isElement,
  phrasing
} from "./chunk-M2KGN5WX.js";
import {
  whitespace
} from "./chunk-4IFNTA3D.js";
import {
  SKIP,
  visitParents
} from "./chunk-OBJQZ5YF.js";
import "./chunk-XPZLJQLW.js";

// node_modules/hast-util-minify-whitespace/node_modules/unist-util-is/lib/index.js
var convert = (
  // Note: overloads in JSDoc can’t yet use different `@template`s.
  /**
   * @type {(
   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &
   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &
   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &
   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &
   *   ((test?: Test) => Check)
   * )}
   */
  /**
   * @param {Test} [test]
   * @returns {Check}
   */
  function(test) {
    if (test === null || test === void 0) {
      return ok;
    }
    if (typeof test === "function") {
      return castFactory(test);
    }
    if (typeof test === "object") {
      return Array.isArray(test) ? anyFactory(test) : propsFactory(test);
    }
    if (typeof test === "string") {
      return typeFactory(test);
    }
    throw new Error("Expected function, string, or object as test");
  }
);
function anyFactory(tests) {
  const checks = [];
  let index = -1;
  while (++index < tests.length) {
    checks[index] = convert(tests[index]);
  }
  return castFactory(any);
  function any(...parameters) {
    let index2 = -1;
    while (++index2 < checks.length) {
      if (checks[index2].apply(this, parameters)) return true;
    }
    return false;
  }
}
function propsFactory(check) {
  const checkAsRecord = (
    /** @type {Record<string, unknown>} */
    check
  );
  return castFactory(all2);
  function all2(node) {
    const nodeAsRecord = (
      /** @type {Record<string, unknown>} */
      /** @type {unknown} */
      node
    );
    let key;
    for (key in check) {
      if (nodeAsRecord[key] !== checkAsRecord[key]) return false;
    }
    return true;
  }
}
function typeFactory(check) {
  return castFactory(type);
  function type(node) {
    return node && node.type === check;
  }
}
function castFactory(testFunction) {
  return check;
  function check(value, index, parent) {
    return Boolean(
      looksLikeANode(value) && testFunction.call(
        this,
        value,
        typeof index === "number" ? index : void 0,
        parent || void 0
      )
    );
  }
}
function ok() {
  return true;
}
function looksLikeANode(value) {
  return value !== null && typeof value === "object" && "type" in value;
}

// node_modules/hast-util-minify-whitespace/lib/block.js
var blocks = [
  "address",
  // Flow content.
  "article",
  // Sections and headings.
  "aside",
  // Sections and headings.
  "blockquote",
  // Flow content.
  "body",
  // Page.
  "br",
  // Contribute whitespace intrinsically.
  "caption",
  // Similar to block.
  "center",
  // Flow content, legacy.
  "col",
  // Similar to block.
  "colgroup",
  // Similar to block.
  "dd",
  // Lists.
  "dialog",
  // Flow content.
  "dir",
  // Lists, legacy.
  "div",
  // Flow content.
  "dl",
  // Lists.
  "dt",
  // Lists.
  "figcaption",
  // Flow content.
  "figure",
  // Flow content.
  "footer",
  // Flow content.
  "form",
  // Flow content.
  "h1",
  // Sections and headings.
  "h2",
  // Sections and headings.
  "h3",
  // Sections and headings.
  "h4",
  // Sections and headings.
  "h5",
  // Sections and headings.
  "h6",
  // Sections and headings.
  "head",
  // Page.
  "header",
  // Flow content.
  "hgroup",
  // Sections and headings.
  "hr",
  // Flow content.
  "html",
  // Page.
  "legend",
  // Flow content.
  "li",
  // Block-like.
  "li",
  // Similar to block.
  "listing",
  // Flow content, legacy
  "main",
  // Flow content.
  "menu",
  // Lists.
  "nav",
  // Sections and headings.
  "ol",
  // Lists.
  "optgroup",
  // Similar to block.
  "option",
  // Similar to block.
  "p",
  // Flow content.
  "plaintext",
  // Flow content, legacy
  "pre",
  // Flow content.
  "section",
  // Sections and headings.
  "summary",
  // Similar to block.
  "table",
  // Similar to block.
  "tbody",
  // Similar to block.
  "td",
  // Block-like.
  "td",
  // Similar to block.
  "tfoot",
  // Similar to block.
  "th",
  // Block-like.
  "th",
  // Similar to block.
  "thead",
  // Similar to block.
  "tr",
  // Similar to block.
  "ul",
  // Lists.
  "wbr",
  // Contribute whitespace intrinsically.
  "xmp"
  // Flow content, legacy
];

// node_modules/hast-util-minify-whitespace/lib/content.js
var content = [
  // Form.
  "button",
  "input",
  "select",
  "textarea"
];

// node_modules/hast-util-minify-whitespace/lib/skippable.js
var skippable = [
  "area",
  "base",
  "basefont",
  "dialog",
  "datalist",
  "head",
  "link",
  "meta",
  "noembed",
  "noframes",
  "param",
  "rp",
  "script",
  "source",
  "style",
  "template",
  "track",
  "title"
];

// node_modules/hast-util-minify-whitespace/lib/index.js
var emptyOptions = {};
var ignorableNode = convert(["comment", "doctype"]);
function minifyWhitespace(tree, options) {
  const settings = options || emptyOptions;
  minify(tree, {
    collapse: collapseFactory(
      settings.newlines ? replaceNewlines : replaceWhitespace
    ),
    whitespace: "normal"
  });
}
function minify(node, state) {
  if ("children" in node) {
    const settings = { ...state };
    if (node.type === "root" || blocklike(node)) {
      settings.before = true;
      settings.after = true;
    }
    settings.whitespace = inferWhiteSpace(node, state);
    return all(node, settings);
  }
  if (node.type === "text") {
    if (state.whitespace === "normal") {
      return minifyText(node, state);
    }
    if (state.whitespace === "nowrap") {
      node.value = state.collapse(node.value);
    }
  }
  return { ignore: ignorableNode(node), stripAtStart: false, remove: false };
}
function minifyText(node, state) {
  const value = state.collapse(node.value);
  const result = { ignore: false, stripAtStart: false, remove: false };
  let start = 0;
  let end = value.length;
  if (state.before && removable(value.charAt(0))) {
    start++;
  }
  if (start !== end && removable(value.charAt(end - 1))) {
    if (state.after) {
      end--;
    } else {
      result.stripAtStart = true;
    }
  }
  if (start === end) {
    result.remove = true;
  } else {
    node.value = value.slice(start, end);
  }
  return result;
}
function all(parent, state) {
  let before = state.before;
  const after = state.after;
  const children = parent.children;
  let length = children.length;
  let index = -1;
  while (++index < length) {
    const result = minify(children[index], {
      ...state,
      after: collapsableAfter(children, index, after),
      before
    });
    if (result.remove) {
      children.splice(index, 1);
      index--;
      length--;
    } else if (!result.ignore) {
      before = result.stripAtStart;
    }
    if (content2(children[index])) {
      before = false;
    }
  }
  return { ignore: false, stripAtStart: Boolean(before || after), remove: false };
}
function collapsableAfter(nodes, index, after) {
  while (++index < nodes.length) {
    const node = nodes[index];
    let result = inferBoundary(node);
    if (result === void 0 && "children" in node && !skippable2(node)) {
      result = collapsableAfter(node.children, -1);
    }
    if (typeof result === "boolean") {
      return result;
    }
  }
  return after;
}
function inferBoundary(node) {
  if (node.type === "element") {
    if (content2(node)) {
      return false;
    }
    if (blocklike(node)) {
      return true;
    }
  } else if (node.type === "text") {
    if (!whitespace(node)) {
      return false;
    }
  } else if (!ignorableNode(node)) {
    return false;
  }
}
function content2(node) {
  return embedded(node) || isElement(node, content);
}
function blocklike(node) {
  return isElement(node, blocks);
}
function skippable2(node) {
  return Boolean(node.type === "element" && node.properties.hidden) || ignorableNode(node) || isElement(node, skippable);
}
function removable(character) {
  return character === " " || character === "\n";
}
function replaceNewlines(value) {
  const match = /\r?\n|\r/.exec(value);
  return match ? match[0] : " ";
}
function replaceWhitespace() {
  return " ";
}
function collapseFactory(replace) {
  return collapse;
  function collapse(value) {
    return String(value).replace(/[\t\n\v\f\r ]+/g, replace);
  }
}
function inferWhiteSpace(node, state) {
  if ("tagName" in node && node.properties) {
    switch (node.tagName) {
      // Whitespace in script/style, while not displayed by CSS as significant,
      // could have some meaning in JS/CSS, so we can’t touch them.
      case "listing":
      case "plaintext":
      case "script":
      case "style":
      case "xmp": {
        return "pre";
      }
      case "nobr": {
        return "nowrap";
      }
      case "pre": {
        return node.properties.wrap ? "pre-wrap" : "pre";
      }
      case "td":
      case "th": {
        return node.properties.noWrap ? "nowrap" : state.whitespace;
      }
      case "textarea": {
        return "pre-wrap";
      }
      default:
    }
  }
  return state.whitespace;
}

// node_modules/hast-util-format/lib/index.js
var emptyOptions2 = {};
function format(tree, options) {
  const settings = options || emptyOptions2;
  const state = {
    blanks: settings.blanks || [],
    head: false,
    indentInitial: settings.indentInitial !== false,
    indent: typeof settings.indent === "number" ? " ".repeat(settings.indent) : typeof settings.indent === "string" ? settings.indent : "  "
  };
  minifyWhitespace(tree, { newlines: true });
  visitParents(tree, visitor);
  function visitor(node, parents) {
    if (!("children" in node)) {
      return;
    }
    if (node.type === "element" && node.tagName === "head") {
      state.head = true;
    }
    if (state.head && node.type === "element" && node.tagName === "body") {
      state.head = false;
    }
    if (node.type === "element" && whitespaceSensitiveTagNames.includes(node.tagName)) {
      return SKIP;
    }
    if (node.children.length === 0 || !padding(state, node)) {
      return;
    }
    let level = parents.length;
    if (!state.indentInitial) {
      level--;
    }
    let eol = false;
    for (const child of node.children) {
      if (child.type === "comment" || child.type === "text") {
        if (child.value.includes("\n")) {
          eol = true;
        }
        child.value = child.value.replace(
          / *\n/g,
          "$&" + state.indent.repeat(level)
        );
      }
    }
    const result = [];
    let previous;
    for (const child of node.children) {
      if (padding(state, child) || eol && !previous) {
        addBreak(result, level, child);
        eol = true;
      }
      previous = child;
      result.push(child);
    }
    if (previous && (eol || padding(state, previous))) {
      if (whitespace(previous)) {
        result.pop();
        previous = result[result.length - 1];
      }
      addBreak(result, level - 1);
    }
    node.children = result;
  }
  function addBreak(list, level, next) {
    const tail = list[list.length - 1];
    const previous = tail && whitespace(tail) ? list[list.length - 2] : tail;
    const replace = (blank(state, previous) && blank(state, next) ? "\n\n" : "\n") + state.indent.repeat(Math.max(level, 0));
    if (tail && tail.type === "text") {
      tail.value = whitespace(tail) ? replace : tail.value + replace;
    } else {
      list.push({ type: "text", value: replace });
    }
  }
}
function blank(state, node) {
  return Boolean(
    node && node.type === "element" && state.blanks.length > 0 && state.blanks.includes(node.tagName)
  );
}
function padding(state, node) {
  return node.type === "root" || (node.type === "element" ? state.head || node.tagName === "script" || embedded(node) || !phrasing(node) : false);
}

// node_modules/rehype-format/lib/index.js
function rehypeFormat(options) {
  return function(tree) {
    format(tree, options);
  };
}
export {
  rehypeFormat as default
};
//# sourceMappingURL=rehype-format-GGS5EMU7.js.map
