import {
  _ as _2
} from "./chunk-TG5SUGVT.js";
import "./chunk-OFOUQLHF.js";
import "./chunk-Z6VJM5ZM.js";
import {
  F,
  je,
  s
} from "./chunk-MFP3SY2Q.js";
import {
  _
} from "./chunk-TACAB4LM.js";
import {
  $,
  Fragment,
  c,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  nextTick,
  openBlock,
  ref,
  unref,
  w3 as w,
  watch,
  withCtx
} from "./chunk-NPL3RUXR.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-4DUSDZ2B.js";
import "./chunk-BVF5FUF3.js";
import "./chunk-YRBF5NWE.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-IX2KTD5L.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-URP2UYTW.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-MR4E537R.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CAP4CFCM.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-O3EO7ESF.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-2S2EUIOI.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Collection/components/MarkdownInput.vue2.js
var z = { class: "flex h-full w-full flex-col gap-2 pt-8" };
var C = { class: "flex min-h-8 items-center justify-between gap-2 pl-1.5" };
var I = { class: "has-[:focus-visible]:bg-b-1 z-1 group relative flex flex-col rounded-lg" };
var B = { class: "h-full min-h-[calc(1rem*4)]" };
var $2 = {
  key: 1,
  class: "text-c-3 flex h-full items-center justify-center rounded-lg border p-4"
};
var D = defineComponent({
  __name: "MarkdownInput",
  props: {
    modelValue: {},
    environment: {},
    envVariables: {},
    workspace: {}
  },
  emits: ["update:modelValue"],
  setup(E, { emit: k }) {
    const V = k, t = ref("preview"), p = ref(null);
    return watch(t, (l) => {
      l === "edit" && nextTick(() => {
        var e;
        (e = p.value) == null || e.focus();
      });
    }), (l, e) => (openBlock(), createElementBlock("div", z, [
      createBaseVNode("div", C, [
        e[6] || (e[6] = createBaseVNode("h3", { class: "font-bold" }, "Description", -1)),
        t.value === "preview" ? (openBlock(), createBlock(unref($), {
          key: 0,
          class: "text-c-2 hover:text-c-1 flex items-center gap-2",
          type: "button",
          size: "sm",
          variant: "outlined",
          onClick: e[0] || (e[0] = (r) => t.value = "edit")
        }, {
          default: withCtx(() => [
            createVNode(unref(c), {
              icon: "Pencil",
              size: "sm",
              thickness: "1.5"
            }),
            e[5] || (e[5] = createBaseVNode("span", null, "Edit", -1))
          ]),
          _: 1
        })) : createCommentVNode("", true)
      ]),
      createBaseVNode("div", I, [
        createBaseVNode("div", B, [
          t.value === "preview" ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
            l.modelValue ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
              l.modelValue ? (openBlock(), createBlock(unref(w), {
                key: 0,
                withImages: "",
                class: "hover:border-b-3 h-full rounded border border-transparent p-1.5",
                value: l.modelValue,
                onDblclick: e[1] || (e[1] = (r) => t.value = "edit")
              }, null, 8, ["value"])) : createCommentVNode("", true),
              e[7] || (e[7] = createBaseVNode("div", { class: "brightness-lifted -z-1 bg-b-1 absolute inset-0 hidden rounded group-hover:block group-has-[:focus-visible]:hidden" }, null, -1))
            ], 64)) : (openBlock(), createElementBlock("div", $2, [
              createVNode(unref($), {
                class: "hover:bg-b-2 hover:text-c-1 text-c-2 flex items-center gap-2",
                variant: "ghost",
                size: "sm",
                onClick: e[2] || (e[2] = (r) => t.value = "edit")
              }, {
                default: withCtx(() => [
                  createVNode(unref(c), {
                    icon: "Pencil",
                    size: "sm",
                    thickness: "1.5"
                  }),
                  e[8] || (e[8] = createBaseVNode("span", null, "Write a description", -1))
                ]),
                _: 1
              })
            ]))
          ], 64)) : createCommentVNode("", true),
          t.value === "edit" ? (openBlock(), createBlock(_2, {
            key: 1,
            ref_key: "codeInputRef",
            ref: p,
            class: "h-full border px-0.5 py-0",
            modelValue: l.modelValue,
            environment: l.environment,
            envVariables: l.envVariables,
            workspace: l.workspace,
            onBlur: e[3] || (e[3] = (r) => t.value = "preview"),
            "onUpdate:modelValue": e[4] || (e[4] = (r) => V("update:modelValue", r))
          }, null, 8, ["modelValue", "environment", "envVariables", "workspace"])) : createCommentVNode("", true)
        ])
      ])
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Collection/components/MarkdownInput.vue.js
var m = s(D, [["__scopeId", "data-v-139eac35"]]);

// node_modules/@scalar/api-client/dist/views/Collection/CollectionOverview.vue2.js
var M = defineComponent({
  __name: "CollectionOverview",
  setup(C2) {
    const {
      activeCollection: o,
      activeEnvironment: t,
      activeEnvVariables: s2,
      activeWorkspace: n
    } = F(), { collectionMutators: m2 } = je(), p = (i) => {
      o.value && m2.edit(o.value.uid, "info.description", i);
    };
    return (i, w2) => (openBlock(), createBlock(_, null, {
      default: withCtx(() => {
        var r, a;
        return [
          unref(t) && unref(n) ? (openBlock(), createBlock(m, {
            key: 0,
            environment: unref(t),
            envVariables: unref(s2),
            workspace: unref(n),
            modelValue: ((a = (r = unref(o)) == null ? void 0 : r.info) == null ? void 0 : a.description) ?? "",
            "onUpdate:modelValue": p
          }, null, 8, ["environment", "envVariables", "workspace", "modelValue"])) : createCommentVNode("", true)
        ];
      }),
      _: 1
    }));
  }
});
export {
  M as default
};
//# sourceMappingURL=CollectionOverview.vue-TVOMUVUX.js.map
