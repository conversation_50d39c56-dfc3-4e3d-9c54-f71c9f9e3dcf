import {
  createElementBlock,
  defineComponent,
  guardReactiveProps,
  i2 as i,
  normalizeProps,
  openBlock,
  renderSlot,
  unref
} from "./chunk-NPL3RUXR.js";

// node_modules/@scalar/api-client/dist/components/ViewLayout/ViewLayout.vue.js
var u = defineComponent({
  __name: "ViewLayout",
  setup(m) {
    const { cx: e } = i();
    return (r, s) => (openBlock(), createElementBlock("div", normalizeProps(guardReactiveProps(
      unref(e)(
        "flex flex-col min-h-0 flex-1 *:border-t-1/2 first:*:border-t-0 md:*:border-t-0 xl:overflow-hidden md:flex-row leading-3"
      )
    )), [
      renderSlot(r.$slots, "default")
    ], 16));
  }
});

export {
  u
};
//# sourceMappingURL=chunk-5HYEUVPS.js.map
