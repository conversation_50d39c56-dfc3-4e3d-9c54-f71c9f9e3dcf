import {
  require_lib
} from "./chunk-W35CJXQD.js";
import "./chunk-QR6HC4NR.js";
import {
  core_exports,
  init_core
} from "./chunk-6Q2IFXCX.js";
import "./chunk-JB6CC7ZF.js";
import "./chunk-Z7KI3FL7.js";
import "./chunk-37DL3DMY.js";
import {
  init_utilities,
  utilities_exports
} from "./chunk-R5Y5ZCXM.js";
import "./chunk-I7RZVVY3.js";
import "./chunk-LQCADWFL.js";
import "./chunk-I4HVGP7M.js";
import {
  __commonJS,
  __toCommonJS
} from "./chunk-XPZLJQLW.js";

// node_modules/apollo-link-rest/bundle.umd.js
var require_bundle_umd = __commonJS({
  "node_modules/apollo-link-rest/bundle.umd.js"(exports, module) {
    (function(global2, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports, (init_core(), __toCommonJS(core_exports)), (init_utilities(), __toCommonJS(utilities_exports)), require_lib()) : typeof define === "function" && define.amd ? define(["exports", "@apollo/client/core", "@apollo/client/utilities", "qs"], factory) : factory(global2["apollo-link-rest"] = {}, global2.apolloClient.core, global2.apolloClient.utilities, global2.qs);
    })(exports, function(exports2, core, utilities, qs) {
      "use strict";
      var __awaiter = function(thisArg, _arguments, P, generator) {
        function adopt(value) {
          return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
          });
        }
        return new (P || (P = Promise))(function(resolve, reject) {
          function fulfilled(value) {
            try {
              step(generator.next(value));
            } catch (e) {
              reject(e);
            }
          }
          function rejected(value) {
            try {
              step(generator["throw"](value));
            } catch (e) {
              reject(e);
            }
          }
          function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
          }
          step((generator = generator.apply(thisArg, _arguments || [])).next());
        });
      };
      var __generator = function(thisArg, body) {
        var _ = { label: 0, sent: function() {
          if (t[0] & 1) throw t[1];
          return t[1];
        }, trys: [], ops: [] }, f, y, t, g;
        return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
          return this;
        }), g;
        function verb(n) {
          return function(v) {
            return step([n, v]);
          };
        }
        function step(op) {
          if (f) throw new TypeError("Generator is already executing.");
          while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;
              case 4:
                _.label++;
                return { value: op[1], done: false };
              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;
              case 7:
                op = _.ops.pop();
                _.trys.pop();
                continue;
              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }
                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }
                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }
                if (t && _.label < t[2]) {
                  _.label = t[2];
                  _.ops.push(op);
                  break;
                }
                if (t[2]) _.ops.pop();
                _.trys.pop();
                continue;
            }
            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }
          if (op[0] & 5) throw op[1];
          return { value: op[0] ? op[1] : void 0, done: true };
        }
      };
      function getDirectiveInfoFromField(field, variables) {
        if (field.directives && field.directives.length) {
          var directiveObj_1 = {};
          field.directives.forEach(function(directive) {
            directiveObj_1[directive.name.value] = utilities.argumentsObjectFromField(directive, variables);
          });
          return directiveObj_1;
        }
        return null;
      }
      var hasOwn = Object.prototype.hasOwnProperty;
      function merge(dest, src) {
        if (src !== null && typeof src === "object") {
          Object.keys(src).forEach(function(key) {
            var srcVal = src[key];
            if (!hasOwn.call(dest, key)) {
              dest[key] = srcVal;
            } else {
              merge(dest[key], srcVal);
            }
          });
        }
      }
      function graphql(resolver2, document, rootValue, contextValue, variableValues, execOptions) {
        if (execOptions === void 0) {
          execOptions = {};
        }
        var mainDefinition = utilities.getMainDefinition(document);
        var fragments = utilities.getFragmentDefinitions(document);
        var fragmentMap = utilities.createFragmentMap(fragments);
        var resultMapper = execOptions.resultMapper;
        var fragmentMatcher = execOptions.fragmentMatcher || function() {
          return true;
        };
        var execContext = {
          fragmentMap,
          contextValue,
          variableValues,
          resultMapper,
          resolver: resolver2,
          fragmentMatcher
        };
        return executeSelectionSet(mainDefinition.selectionSet, rootValue, execContext);
      }
      function executeSelectionSet(selectionSet, rootValue, execContext) {
        return __awaiter(this, void 0, void 0, function() {
          var fragmentMap, contextValue, variables, result, execute;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
              case 0:
                fragmentMap = execContext.fragmentMap, contextValue = execContext.contextValue, variables = execContext.variableValues;
                result = {};
                execute = function(selection) {
                  return __awaiter(_this, void 0, void 0, function() {
                    var fieldResult, resultFieldKey, fragment, typeCondition, fragmentResult;
                    return __generator(this, function(_a2) {
                      switch (_a2.label) {
                        case 0:
                          if (!utilities.shouldInclude(selection, variables)) {
                            return [
                              2
                              /*return*/
                            ];
                          }
                          if (!utilities.isField(selection)) return [3, 2];
                          return [4, executeField(selection, rootValue, execContext)];
                        case 1:
                          fieldResult = _a2.sent();
                          resultFieldKey = utilities.resultKeyNameFromField(selection);
                          if (fieldResult !== void 0) {
                            if (result[resultFieldKey] === void 0) {
                              result[resultFieldKey] = fieldResult;
                            } else {
                              merge(result[resultFieldKey], fieldResult);
                            }
                          }
                          return [
                            2
                            /*return*/
                          ];
                        case 2:
                          if (utilities.isInlineFragment(selection)) {
                            fragment = selection;
                          } else {
                            fragment = fragmentMap[selection.name.value];
                            if (!fragment) {
                              throw new Error("No fragment named " + selection.name.value);
                            }
                          }
                          typeCondition = fragment.typeCondition.name.value;
                          if (!execContext.fragmentMatcher(rootValue, typeCondition, contextValue)) return [3, 4];
                          return [4, executeSelectionSet(fragment.selectionSet, rootValue, execContext)];
                        case 3:
                          fragmentResult = _a2.sent();
                          merge(result, fragmentResult);
                          _a2.label = 4;
                        case 4:
                          return [
                            2
                            /*return*/
                          ];
                      }
                    });
                  });
                };
                return [4, Promise.all(selectionSet.selections.map(execute))];
              case 1:
                _a.sent();
                if (execContext.resultMapper) {
                  return [2, execContext.resultMapper(result, rootValue)];
                }
                return [2, result];
            }
          });
        });
      }
      function executeField(field, rootValue, execContext) {
        return __awaiter(this, void 0, void 0, function() {
          var variables, contextValue, resolver2, fieldName, args, info, result;
          return __generator(this, function(_a) {
            switch (_a.label) {
              case 0:
                variables = execContext.variableValues, contextValue = execContext.contextValue, resolver2 = execContext.resolver;
                fieldName = field.name.value;
                args = utilities.argumentsObjectFromField(field, variables);
                info = {
                  isLeaf: !field.selectionSet,
                  resultKey: utilities.resultKeyNameFromField(field),
                  directives: getDirectiveInfoFromField(field, variables),
                  field
                };
                return [4, resolver2(fieldName, rootValue, args, contextValue, info)];
              case 1:
                result = _a.sent();
                if (!field.selectionSet) {
                  return [2, result];
                }
                if (result == null) {
                  return [2, result];
                }
                if (Array.isArray(result)) {
                  return [2, executeSubSelectedArray(field, result, execContext)];
                }
                return [2, executeSelectionSet(field.selectionSet, result, execContext)];
            }
          });
        });
      }
      function executeSubSelectedArray(field, result, execContext) {
        return Promise.all(result.map(function(item) {
          if (item === null) {
            return null;
          }
          if (Array.isArray(item)) {
            return executeSubSelectedArray(field, item, execContext);
          }
          return executeSelectionSet(field.selectionSet, item, execContext);
        }));
      }
      var __extends = /* @__PURE__ */ function() {
        var extendStatics = function(d, b) {
          extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
            d2.__proto__ = b2;
          } || function(d2, b2) {
            for (var p in b2) if (b2.hasOwnProperty(p)) d2[p] = b2[p];
          };
          return extendStatics(d, b);
        };
        return function(d, b) {
          extendStatics(d, b);
          function __() {
            this.constructor = d;
          }
          d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
      }();
      var __assign = function() {
        __assign = Object.assign || function(t) {
          for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
              t[p] = s[p];
          }
          return t;
        };
        return __assign.apply(this, arguments);
      };
      var __awaiter$1 = function(thisArg, _arguments, P, generator) {
        function adopt(value) {
          return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
          });
        }
        return new (P || (P = Promise))(function(resolve, reject) {
          function fulfilled(value) {
            try {
              step(generator.next(value));
            } catch (e) {
              reject(e);
            }
          }
          function rejected(value) {
            try {
              step(generator["throw"](value));
            } catch (e) {
              reject(e);
            }
          }
          function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
          }
          step((generator = generator.apply(thisArg, _arguments || [])).next());
        });
      };
      var __generator$1 = function(thisArg, body) {
        var _ = { label: 0, sent: function() {
          if (t[0] & 1) throw t[1];
          return t[1];
        }, trys: [], ops: [] }, f, y, t, g;
        return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
          return this;
        }), g;
        function verb(n) {
          return function(v) {
            return step([n, v]);
          };
        }
        function step(op) {
          if (f) throw new TypeError("Generator is already executing.");
          while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;
              case 4:
                _.label++;
                return { value: op[1], done: false };
              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;
              case 7:
                op = _.ops.pop();
                _.trys.pop();
                continue;
              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }
                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }
                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }
                if (t && _.label < t[2]) {
                  _.label = t[2];
                  _.ops.push(op);
                  break;
                }
                if (t[2]) _.ops.pop();
                _.trys.pop();
                continue;
            }
            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }
          if (op[0] & 5) throw op[1];
          return { value: op[0] ? op[1] : void 0, done: true };
        }
      };
      var __spreadArrays = function() {
        for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
        for (var r = Array(s), k = 0, i = 0; i < il; i++)
          for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
        return r;
      };
      var popOneSetOfArrayBracketsFromTypeName = function(typename) {
        var noSpace = typename.replace(/\s/g, "");
        var sansOneBracketPair = noSpace.replace(/\[(.*)\]/, function(str, matchStr, offset, fullStr) {
          return ((matchStr != null && matchStr.length) > 0 ? matchStr : null) || noSpace;
        });
        return sansOneBracketPair;
      };
      var addTypeNameToResult = function(result, __typename, typePatcher, typePatcherContext) {
        if (Array.isArray(result)) {
          var fixedTypename_1 = popOneSetOfArrayBracketsFromTypeName(__typename);
          return result.map(function(e) {
            return addTypeNameToResult(e, fixedTypename_1, typePatcher, typePatcherContext);
          });
        }
        if (null == result || typeof result === "number" || typeof result === "boolean" || typeof result === "string") {
          return result;
        }
        return typePatcher(result, __typename, typePatcher, typePatcherContext);
      };
      var quickFindRestDirective = function(field) {
        if (field.directives && field.directives.length) {
          return field.directives.find(function(directive) {
            return "rest" === directive.name.value;
          });
        }
        return null;
      };
      function findRestDirectivesThenInsertNullsForOmittedFields(resultKey, current, mainDefinition, fragmentMap, currentSelectionSet) {
        if (currentSelectionSet == null || null == current || typeof current === "number" || typeof current === "boolean" || typeof current === "string") {
          return current;
        }
        currentSelectionSet.selections.forEach(function(node) {
          if (utilities.isInlineFragment(node)) {
            findRestDirectivesThenInsertNullsForOmittedFields(resultKey, current, mainDefinition, fragmentMap, node.selectionSet);
          } else if (node.kind === "FragmentSpread") {
            var fragment = fragmentMap[node.name.value];
            findRestDirectivesThenInsertNullsForOmittedFields(resultKey, current, mainDefinition, fragmentMap, fragment.selectionSet);
          } else if (utilities.isField(node)) {
            var name_1 = utilities.resultKeyNameFromField(node);
            if (name_1 === resultKey && quickFindRestDirective(node) != null) {
              insertNullsForAnyOmittedFields(current, mainDefinition, fragmentMap, node.selectionSet);
            } else {
              findRestDirectivesThenInsertNullsForOmittedFields(resultKey, current, mainDefinition, fragmentMap, node.selectionSet);
            }
          } else {
            return function(node2) {
              throw new Error("Unhandled Node Type in SelectionSetNode.selections");
            }(node);
          }
        });
        return current;
      }
      function insertNullsForAnyOmittedFields(current, mainDefinition, fragmentMap, currentSelectionSet) {
        if (null == current || typeof current === "number" || typeof current === "boolean" || typeof current === "string") {
          return;
        }
        if (Array.isArray(current)) {
          current.forEach(function(c) {
            return insertNullsForAnyOmittedFields(c, mainDefinition, fragmentMap, currentSelectionSet);
          });
          return;
        }
        currentSelectionSet.selections.forEach(function(node) {
          if (utilities.isInlineFragment(node)) {
            insertNullsForAnyOmittedFields(current, mainDefinition, fragmentMap, node.selectionSet);
          } else if (node.kind === "FragmentSpread") {
            var fragment = fragmentMap[node.name.value];
            insertNullsForAnyOmittedFields(current, mainDefinition, fragmentMap, fragment.selectionSet);
          } else if (utilities.isField(node)) {
            var value = current[node.name.value];
            if (node.name.value === "__typename") ;
            else if (typeof value === "undefined") {
              current[node.name.value] = null;
            } else if (value != null && typeof value === "object" && node.selectionSet != null) {
              insertNullsForAnyOmittedFields(value, mainDefinition, fragmentMap, node.selectionSet);
            }
          } else {
            return function(node2) {
              throw new Error("Unhandled Node Type in SelectionSetNode.selections");
            }(node);
          }
        });
      }
      var getEndpointOptions = function(endpoints, endpoint) {
        var result = endpoints[endpoint || DEFAULT_ENDPOINT_KEY] || endpoints[DEFAULT_ENDPOINT_KEY];
        if (typeof result === "string") {
          return { uri: result };
        }
        return __assign({ responseTransformer: null }, result);
      };
      var replaceLegacyParam = function(endpoint, name, value) {
        if (value === void 0 || name === void 0) {
          return endpoint;
        }
        return endpoint.replace(":" + name, value);
      };
      var PathBuilder = (
        /** @class */
        function() {
          function PathBuilder2() {
          }
          PathBuilder2.replacerForPath = function(path) {
            if (path in PathBuilder2.cache) {
              return PathBuilder2.cache[path];
            }
            var queryOrigStartIndex = path.indexOf("?");
            var pathBits = path.split(PathBuilder2.argReplacement);
            var chunkActions = [];
            var hasBegunQuery = false;
            pathBits.reduce(function(processedCount, bit) {
              if (bit === "" || bit === "{}") {
                return processedCount + bit.length;
              }
              var nextIndex = processedCount + bit.length;
              if (bit[0] === "{" && bit[bit.length - 1] === "}") {
                var _keyPath_1 = bit.slice(1, bit.length - 1).split(".");
                chunkActions.push(function(props, useQSEncoder) {
                  try {
                    var value = PathBuilderLookupValue(props, _keyPath_1);
                    if (!useQSEncoder || (typeof value !== "object" || value == null)) {
                      return String(value);
                    } else {
                      return qs.stringify(value);
                    }
                  } catch (e) {
                    var key = [path, _keyPath_1.join(".")].join("|");
                    if (!(key in PathBuilder2.warnTable)) {
                      console.warn("Warning: RestLink caught an error while unpacking", key, "This tends to happen if you forgot to pass a parameter needed for creating an @rest(path, or if RestLink was configured to deeply unpack a path parameter that wasn't provided. This message will only log once per detected instance. Trouble-shooting hint: check @rest(path: and the variables provided to this query.");
                      PathBuilder2.warnTable[key] = true;
                    }
                    return "";
                  }
                });
              } else {
                chunkActions.push(bit);
                if (!hasBegunQuery && nextIndex >= queryOrigStartIndex) {
                  hasBegunQuery = true;
                  chunkActions.push(true);
                }
              }
              return nextIndex;
            }, 0);
            var result = function(props) {
              var hasEnteredQuery = false;
              var tmp = chunkActions.reduce(function(accumulator, action) {
                if (typeof action === "string") {
                  return accumulator + action;
                } else if (typeof action === "boolean") {
                  hasEnteredQuery = true;
                  return accumulator;
                } else {
                  return accumulator + action(props, hasEnteredQuery);
                }
              }, "");
              return tmp;
            };
            return PathBuilder2.cache[path] = result;
          };
          PathBuilder2.cache = {};
          PathBuilder2.warnTable = {};
          PathBuilder2.argReplacement = /({[._a-zA-Z0-9]*})/;
          return PathBuilder2;
        }()
      );
      function PathBuilderLookupValue(tmp, keyPath) {
        if (keyPath.length === 0) {
          return tmp;
        }
        var remainingKeyPath = __spreadArrays(keyPath);
        var key = remainingKeyPath.shift();
        return PathBuilderLookupValue(tmp[key], remainingKeyPath);
      }
      var noMangleKeys = ["__typename"];
      var globalScope = typeof globalThis === "object" && globalThis || global;
      var convertObjectKeys = function(object, __converter, keypath) {
        if (keypath === void 0) {
          keypath = [];
        }
        var converter = null;
        if (__converter.length != 2) {
          converter = function(name, keypath2) {
            return __converter(name);
          };
        } else {
          converter = __converter;
        }
        if (Array.isArray(object)) {
          return object.map(function(o, index) {
            return convertObjectKeys(o, converter, __spreadArrays(keypath, [String(index)]));
          });
        }
        if (object == null || typeof object !== "object" || object.constructor !== Object) {
          return object;
        }
        if (globalScope.FileList && object instanceof FileList || globalScope.File && object instanceof File) {
          return object;
        }
        return Object.keys(object).reduce(function(acc, key) {
          var value = object[key];
          if (noMangleKeys.indexOf(key) !== -1) {
            acc[key] = value;
            return acc;
          }
          var nestedKeyPath = __spreadArrays(keypath, [key]);
          acc[converter(key, nestedKeyPath)] = convertObjectKeys(value, converter, nestedKeyPath);
          return acc;
        }, {});
      };
      var noOpNameNormalizer = function(name) {
        return name;
      };
      var normalizeHeaders = function(headers) {
        if (headers instanceof Headers) {
          return headers;
        } else {
          return new Headers(headers || {});
        }
      };
      var concatHeadersMergePolicy = function() {
        var headerGroups = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          headerGroups[_i] = arguments[_i];
        }
        return headerGroups.reduce(function(accumulator, current) {
          if (!current) {
            return accumulator;
          }
          if (!current.forEach) {
            current = normalizeHeaders(current);
          }
          current.forEach(function(value, key) {
            accumulator.append(key, value);
          });
          return accumulator;
        }, new Headers());
      };
      var overrideHeadersMergePolicy = function(linkHeaders, headersToOverride, requestHeaders) {
        var result = new Headers();
        linkHeaders.forEach(function(value, key) {
          if (headersToOverride.indexOf(key) !== -1) {
            return;
          }
          result.append(key, value);
        });
        return concatHeadersMergePolicy(result, requestHeaders || new Headers());
      };
      var makeOverrideHeadersMergePolicy = function(headersToOverride) {
        return function(linkHeaders, requestHeaders) {
          return overrideHeadersMergePolicy(linkHeaders, headersToOverride, requestHeaders);
        };
      };
      var SUPPORTED_HTTP_VERBS = ["GET", "POST", "PUT", "PATCH", "DELETE"];
      var validateRequestMethodForOperationType = function(method, operationType) {
        switch (operationType) {
          case "query":
            if (SUPPORTED_HTTP_VERBS.indexOf(method.toUpperCase()) !== -1) {
              return;
            }
            throw new Error('A "query" operation can only support "GET" requests but got "' + method + '".');
          case "mutation":
            if (SUPPORTED_HTTP_VERBS.indexOf(method.toUpperCase()) !== -1) {
              return;
            }
            throw new Error('"mutation" operations do not support that HTTP-verb');
          case "subscription":
            throw new Error('A "subscription" operation is not supported yet.');
          default:
            var _exhaustiveCheck = operationType;
            return _exhaustiveCheck;
        }
      };
      var rethrowServerSideError = function(response, result, message) {
        var error = new Error(message);
        error.response = response;
        error.statusCode = response.status;
        error.result = result;
        throw error;
      };
      var addTypeToNode = function(node, typename) {
        if (node === null || node === void 0 || typeof node !== "object") {
          return node;
        }
        if (!Array.isArray(node)) {
          node["__typename"] = typename;
          return node;
        }
        return node.map(function(item) {
          return addTypeToNode(item, typename);
        });
      };
      var resolver = function(fieldName, root, args, context, info) {
        return __awaiter$1(void 0, void 0, void 0, function() {
          var directives, isLeaf, resultKey, exportVariablesByNode, exportVariables, copyExportVariables, aliasedNode, preAliasingNode, isATypeCall, isNotARestCall, credentials, endpoints, headers, customFetch, operationType, typePatcher, mainDefinition, fragmentDefinitions, linkLevelNameNormalizer, linkLevelNameDenormalizer, serializers, responseTransformer, fragmentMap, _a, path, endpoint, pathBuilder, endpointOption, neitherPathsProvided, allParams, pathWithParams, _b, method, type, bodyBuilder, bodyKey, perRequestNameDenormalizer, perRequestNameNormalizer, bodySerializer, body, overrideHeaders, maybeBody_1, serializedBody, requestParams, requestUrl, response, result, parsed, error_1, transformer, err_1;
          return __generator$1(this, function(_c) {
            switch (_c.label) {
              case 0:
                directives = info.directives, isLeaf = info.isLeaf, resultKey = info.resultKey;
                exportVariablesByNode = context.exportVariablesByNode;
                exportVariables = exportVariablesByNode.get(root) || {};
                copyExportVariables = function(result2) {
                  if (result2 instanceof Array) {
                    result2.forEach(copyExportVariables);
                  } else {
                    exportVariablesByNode.set(result2, __assign({}, exportVariables));
                  }
                  return result2;
                };
                aliasedNode = (root || {})[resultKey];
                preAliasingNode = (root || {})[fieldName];
                if (root && directives && directives.export) {
                  exportVariables[directives.export.as] = preAliasingNode;
                }
                isATypeCall = directives && directives.type;
                if (!isLeaf && isATypeCall) {
                  if (directives.rest) {
                    throw new Error("Invalid use of @type(name: ...) directive on a call that also has @rest(...)");
                  }
                  copyExportVariables(preAliasingNode);
                  return [2, addTypeToNode(preAliasingNode, directives.type.name)];
                }
                isNotARestCall = !directives || !directives.rest;
                if (isNotARestCall) {
                  return [2, copyExportVariables(aliasedNode || preAliasingNode)];
                }
                credentials = context.credentials, endpoints = context.endpoints, headers = context.headers, customFetch = context.customFetch, operationType = context.operationType, typePatcher = context.typePatcher, mainDefinition = context.mainDefinition, fragmentDefinitions = context.fragmentDefinitions, linkLevelNameNormalizer = context.fieldNameNormalizer, linkLevelNameDenormalizer = context.fieldNameDenormalizer, serializers = context.serializers, responseTransformer = context.responseTransformer;
                fragmentMap = utilities.createFragmentMap(fragmentDefinitions);
                _a = directives.rest, path = _a.path, endpoint = _a.endpoint, pathBuilder = _a.pathBuilder;
                endpointOption = getEndpointOptions(endpoints, endpoint);
                neitherPathsProvided = path == null && pathBuilder == null;
                if (neitherPathsProvided) {
                  throw new Error('One of ("path" | "pathBuilder") must be set in the @rest() directive. This request had neither, please add one');
                }
                if (!pathBuilder) {
                  if (!path.includes(":")) {
                    pathBuilder = PathBuilder.replacerForPath(path);
                  } else {
                    console.warn("Deprecated: '@rest(path:' contains a ':' colon, this format will be removed in future versions");
                    pathBuilder = function(_a2) {
                      var args2 = _a2.args, exportVariables2 = _a2.exportVariables;
                      var legacyArgs = __assign(__assign({}, args2), exportVariables2);
                      var pathWithParams2 = Object.keys(legacyArgs).reduce(function(acc, e) {
                        return replaceLegacyParam(acc, e, legacyArgs[e]);
                      }, path);
                      if (pathWithParams2.includes(":")) {
                        throw new Error('Missing parameters to run query, specify it in the query params or use an export directive. (If you need to use ":" inside a variable string make sure to encode the variables properly using `encodeURIComponent`. Alternatively see documentation about using pathBuilder.)');
                      }
                      return pathWithParams2;
                    };
                  }
                }
                allParams = {
                  args,
                  exportVariables,
                  context,
                  "@rest": directives.rest,
                  replacer: pathBuilder
                };
                pathWithParams = pathBuilder(allParams);
                _b = directives.rest, method = _b.method, type = _b.type, bodyBuilder = _b.bodyBuilder, bodyKey = _b.bodyKey, perRequestNameDenormalizer = _b.fieldNameDenormalizer, perRequestNameNormalizer = _b.fieldNameNormalizer, bodySerializer = _b.bodySerializer;
                if (!method) {
                  method = "GET";
                }
                if (!bodyKey) {
                  bodyKey = "input";
                }
                body = void 0;
                overrideHeaders = void 0;
                if (-1 === ["GET", "DELETE"].indexOf(method)) {
                  if (!bodyBuilder) {
                    maybeBody_1 = allParams.exportVariables[bodyKey] || allParams.args && allParams.args[bodyKey];
                    if (!maybeBody_1) {
                      throw new Error("[GraphQL " + method + " " + operationType + " using a REST call without a body]. No `" + bodyKey + "` was detected. Pass bodyKey, or bodyBuilder to the @rest() directive to resolve this.");
                    }
                    bodyBuilder = function(argsWithExport) {
                      return maybeBody_1;
                    };
                  }
                  body = convertObjectKeys(bodyBuilder(allParams), perRequestNameDenormalizer || linkLevelNameDenormalizer || noOpNameNormalizer);
                  serializedBody = void 0;
                  if (typeof bodySerializer === "string") {
                    if (!serializers.hasOwnProperty(bodySerializer)) {
                      throw new Error('"bodySerializer" must correspond to configured serializer. ' + ("Please make sure to specify a serializer called " + bodySerializer + ' in the "bodySerializers" property of the RestLink.'));
                    }
                    serializedBody = serializers[bodySerializer](body, headers);
                  } else {
                    serializedBody = bodySerializer ? bodySerializer(body, headers) : serializers[DEFAULT_SERIALIZER_KEY](body, headers);
                  }
                  body = serializedBody.body;
                  overrideHeaders = new Headers(serializedBody.headers);
                }
                validateRequestMethodForOperationType(method, operationType || "query");
                requestParams = __assign({ method, headers: overrideHeaders || headers, body }, credentials ? { credentials } : {});
                requestUrl = "" + endpointOption.uri + pathWithParams;
                return [4, (customFetch || fetch)(requestUrl, requestParams)];
              case 1:
                response = _c.sent();
                context.responses.push(response);
                if (!response.ok) return [3, 2];
                if (response.status === 204 || response.headers.get("Content-Length") === "0") {
                  result = {};
                } else {
                  result = response;
                }
                return [3, 9];
              case 2:
                if (!(response.status === 404)) return [3, 3];
                result = null;
                return [3, 9];
              case 3:
                parsed = void 0;
                _c.label = 4;
              case 4:
                _c.trys.push([4, 6, , 8]);
                return [4, response.clone().json()];
              case 5:
                parsed = _c.sent();
                return [3, 8];
              case 6:
                error_1 = _c.sent();
                return [4, response.clone().text()];
              case 7:
                parsed = _c.sent();
                return [3, 8];
              case 8:
                rethrowServerSideError(response, parsed, "Response not successful: Received status code " + response.status);
                _c.label = 9;
              case 9:
                transformer = endpointOption.responseTransformer || responseTransformer;
                if (!transformer) return [3, 14];
                _c.label = 10;
              case 10:
                _c.trys.push([10, 12, , 13]);
                return [4, transformer(result, type)];
              case 11:
                result = _c.sent();
                return [3, 13];
              case 12:
                err_1 = _c.sent();
                console.warn("An error occurred in a responseTransformer:");
                throw err_1;
              case 13:
                return [3, 16];
              case 14:
                if (!(result && result.json)) return [3, 16];
                return [4, result.json()];
              case 15:
                result = _c.sent();
                _c.label = 16;
              case 16:
                result = convertObjectKeys(result, perRequestNameNormalizer || linkLevelNameNormalizer || noOpNameNormalizer);
                result = findRestDirectivesThenInsertNullsForOmittedFields(resultKey, result, mainDefinition, fragmentMap, mainDefinition.selectionSet);
                result = addTypeNameToResult(result, type, typePatcher, {
                  resolverParams: { fieldName, root, args, context, info }
                });
                return [2, copyExportVariables(result)];
            }
          });
        });
      };
      var DEFAULT_ENDPOINT_KEY = "";
      var DEFAULT_SERIALIZER_KEY = "";
      var DEFAULT_JSON_SERIALIZER = function(data, headers) {
        if (!headers.has("content-type")) {
          headers.append("Content-Type", "application/json");
        }
        return {
          body: JSON.stringify(data),
          headers
        };
      };
      var CONNECTION_REMOVE_CONFIG = {
        test: function(directive) {
          return directive.name.value === "rest";
        },
        remove: true
      };
      var RestLink = (
        /** @class */
        function(_super) {
          __extends(RestLink2, _super);
          function RestLink2(_a) {
            var _b;
            var uri = _a.uri, endpoints = _a.endpoints, headers = _a.headers, fieldNameNormalizer = _a.fieldNameNormalizer, fieldNameDenormalizer = _a.fieldNameDenormalizer, typePatcher = _a.typePatcher, customFetch = _a.customFetch, credentials = _a.credentials, bodySerializers = _a.bodySerializers, defaultSerializer = _a.defaultSerializer, responseTransformer = _a.responseTransformer;
            var _this = _super.call(this) || this;
            var fallback = {};
            fallback[DEFAULT_ENDPOINT_KEY] = uri || "";
            _this.endpoints = Object.assign({}, endpoints || fallback);
            if (uri == null && endpoints == null) {
              throw new Error("A RestLink must be initialized with either 1 uri, or a map of keyed-endpoints");
            }
            if (uri != null) {
              var currentDefaultURI = (endpoints || {})[DEFAULT_ENDPOINT_KEY];
              if (currentDefaultURI != null && currentDefaultURI != uri) {
                throw new Error("RestLink was configured with a default uri that doesn't match what's passed in to the endpoints map.");
              }
              _this.endpoints[DEFAULT_ENDPOINT_KEY] = uri;
            }
            if (_this.endpoints[DEFAULT_ENDPOINT_KEY] == null) {
              console.warn("RestLink configured without a default URI. All @rest(…) directives must provide an endpoint key!");
            }
            if (typePatcher == null) {
              _this.typePatcher = function(result, __typename, _2) {
                return __assign({ __typename }, result);
              };
            } else if (!Array.isArray(typePatcher) && typeof typePatcher === "object" && Object.keys(typePatcher).map(function(key) {
              return typePatcher[key];
            }).reduce(
              // Make sure all of the values are patcher-functions
              function(current, patcher) {
                return current && typeof patcher === "function";
              },
              true
            )) {
              var table_1 = typePatcher;
              _this.typePatcher = function(data, outerType, patchDeeper, context) {
                var __typename = data.__typename || outerType;
                if (Array.isArray(data)) {
                  return data.map(function(d) {
                    return patchDeeper(d, __typename, patchDeeper, context);
                  });
                }
                var subPatcher = table_1[__typename] || function(result) {
                  return result;
                };
                return __assign({ __typename }, subPatcher(data, __typename, patchDeeper, context));
              };
            } else {
              throw new Error("RestLink was configured with a typePatcher of invalid type!");
            }
            if (bodySerializers && bodySerializers.hasOwnProperty(DEFAULT_SERIALIZER_KEY)) {
              console.warn("RestLink was configured to override the default serializer! This may result in unexpected behavior");
            }
            _this.responseTransformer = responseTransformer || null;
            _this.fieldNameNormalizer = fieldNameNormalizer || null;
            _this.fieldNameDenormalizer = fieldNameDenormalizer || null;
            _this.headers = normalizeHeaders(headers);
            _this.credentials = credentials || null;
            _this.customFetch = customFetch;
            _this.serializers = __assign((_b = {}, _b[DEFAULT_SERIALIZER_KEY] = defaultSerializer || DEFAULT_JSON_SERIALIZER, _b), bodySerializers || {});
            _this.processedDocuments = /* @__PURE__ */ new Map();
            return _this;
          }
          RestLink2.prototype.removeRestSetsFromDocument = function(query) {
            var cached = this.processedDocuments.get(query);
            if (cached)
              return cached;
            utilities.checkDocument(query);
            var docClone = utilities.removeDirectivesFromDocument([CONNECTION_REMOVE_CONFIG], query);
            this.processedDocuments.set(query, docClone);
            return docClone;
          };
          RestLink2.prototype.request = function(operation, forward) {
            var query = operation.query, variables = operation.variables, getContext = operation.getContext, setContext = operation.setContext;
            var context = getContext();
            var isRestQuery = utilities.hasDirectives(["rest"], query);
            if (!isRestQuery) {
              return forward(operation);
            }
            var nonRest = this.removeRestSetsFromDocument(query);
            var headersMergePolicy = context.headersMergePolicy;
            if (headersMergePolicy == null && Array.isArray(context.headersToOverride)) {
              headersMergePolicy = makeOverrideHeadersMergePolicy(context.headersToOverride);
            } else if (headersMergePolicy == null) {
              headersMergePolicy = concatHeadersMergePolicy;
            }
            var headers = headersMergePolicy(this.headers, context.headers);
            if (!headers.has("Accept")) {
              headers.append("Accept", "application/json");
            }
            var credentials = context.credentials || this.credentials;
            var queryWithTypename = utilities.addTypenameToDocument(query);
            var mainDefinition = utilities.getMainDefinition(query);
            var fragmentDefinitions = utilities.getFragmentDefinitions(query);
            var operationType = (mainDefinition || {}).operation || "query";
            var requestContext = {
              headers,
              endpoints: this.endpoints,
              // Provide an empty map for this request's exports to be stuffed into
              exportVariablesByNode: /* @__PURE__ */ new Map(),
              credentials,
              customFetch: this.customFetch,
              operationType,
              fieldNameNormalizer: this.fieldNameNormalizer,
              fieldNameDenormalizer: this.fieldNameDenormalizer,
              mainDefinition,
              fragmentDefinitions,
              typePatcher: this.typePatcher,
              serializers: this.serializers,
              responses: [],
              responseTransformer: this.responseTransformer
            };
            var resolverOptions = {};
            var obs;
            if (nonRest && forward) {
              operation.query = nonRest;
              obs = forward(operation);
            } else
              obs = core.Observable.of({ data: {} });
            return obs.flatMap(function(_a) {
              var data = _a.data, errors = _a.errors;
              return new core.Observable(function(observer) {
                graphql(resolver, queryWithTypename, data, requestContext, variables, resolverOptions).then(function(data2) {
                  setContext({
                    restResponses: (context.restResponses || []).concat(requestContext.responses)
                  });
                  observer.next({ data: data2, errors });
                  observer.complete();
                }).catch(function(err) {
                  if (err.name === "AbortError")
                    return;
                  if (err.result && err.result.errors) {
                    observer.next(err.result);
                  }
                  observer.error(err);
                });
              });
            });
          };
          return RestLink2;
        }(core.ApolloLink)
      );
      exports2.RestLink = RestLink;
      exports2.PathBuilder = PathBuilder;
      Object.defineProperty(exports2, "__esModule", { value: true });
    });
  }
});
export default require_bundle_umd();
//# sourceMappingURL=apollo-link-rest.js.map
