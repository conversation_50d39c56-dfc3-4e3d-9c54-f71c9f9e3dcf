import {
  $ as $2
} from "./chunk-GTLTXTHK.js";
import {
  d
} from "./chunk-VWRBV6HI.js";
import {
  u
} from "./chunk-5HYEUVPS.js";
import {
  RouterLink,
  RouterView,
  useRouter
} from "./chunk-Z6VJM5ZM.js";
import {
  F,
  a,
  je,
  s
} from "./chunk-MFP3SY2Q.js";
import {
  _
} from "./chunk-TACAB4LM.js";
import {
  $,
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  guardReactiveProps,
  i2 as i,
  mergeProps,
  normalizeClass,
  normalizeProps,
  openBlock,
  ref,
  renderList,
  toDisplayString,
  unref,
  useScroll,
  watch,
  withCtx
} from "./chunk-NPL3RUXR.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-4DUSDZ2B.js";
import "./chunk-BVF5FUF3.js";
import "./chunk-YRBF5NWE.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-IX2KTD5L.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-URP2UYTW.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-MR4E537R.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CAP4CFCM.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/components/Form/LabelInput.vue2.js
var g = ["for"];
var y = ["id", "placeholder", "value"];
var b = defineComponent({
  __name: "LabelInput",
  props: {
    inputId: {},
    placeholder: {},
    value: {},
    layout: {}
  },
  emits: ["updateValue"],
  setup(_2, { emit: i2 }) {
    const s2 = i2, { cx: t } = i(), d2 = (e2) => {
      const a2 = e2.target;
      s2("updateValue", a2.value);
    };
    return (e2, a2) => (openBlock(), createElementBlock("div", normalizeProps(guardReactiveProps(unref(t)("flex-1 flex gap-1 items-center pointer-events-none group"))), [
      e2.layout !== "modal" ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        createBaseVNode("label", mergeProps(
          unref(t)(
            "absolute w-full h-full top-0 left-0 pointer-events-auto opacity-0 cursor-text"
          ),
          { for: e2.inputId }
        ), null, 16, g),
        createBaseVNode("input", mergeProps(
          unref(t)(
            "flex-1 text-c-1 rounded pointer-events-auto relative w-full pl-1.25 -ml-0.5 md:-ml-1.25 h-8 group-hover-input has-[:focus-visible]:outline z-10"
          ),
          {
            id: e2.inputId,
            placeholder: e2.placeholder,
            value: e2.value,
            onInput: d2
          }
        ), null, 16, y)
      ], 64)) : (openBlock(), createElementBlock("span", normalizeProps(mergeProps({ key: 1 }, unref(t)("flex items-center text-c-1 h-8"))), toDisplayString(e2.value), 17))
    ], 16));
  }
});

// node_modules/@scalar/api-client/dist/components/Form/LabelInput.vue.js
var e = s(b, [["__scopeId", "data-v-fced736a"]]);

// node_modules/@scalar/api-client/dist/views/Collection/CollectionInfoForm.vue2.js
var N = ["aria-label"];
var U = { class: "ml-1.25 group relative" };
var S = defineComponent({
  __name: "CollectionInfoForm",
  setup(L) {
    const { activeCollection: e2 } = F(), { collectionMutators: l } = je(), n = computed(
      () => {
        var t;
        return ((t = e2 == null ? void 0 : e2.value) == null ? void 0 : t["x-scalar-icon"]) || "interface-content-folder";
      }
    ), x = (t) => {
      var r, o;
      (r = e2 == null ? void 0 : e2.value) != null && r.uid && l.edit((o = e2 == null ? void 0 : e2.value) == null ? void 0 : o.uid, "x-scalar-icon", t);
    }, _2 = (t) => {
      e2.value && l.edit(e2.value.uid, "info.title", t);
    }, u2 = computed(() => {
      var t, r, o, s2, i2, d2, m;
      return {
        icon: (t = e2 == null ? void 0 : e2.value) == null ? void 0 : t["x-scalar-icon"],
        title: (o = (r = e2 == null ? void 0 : e2.value) == null ? void 0 : r.info) == null ? void 0 : o.title,
        description: (i2 = (s2 = e2 == null ? void 0 : e2.value) == null ? void 0 : s2.info) == null ? void 0 : i2.description,
        version: (m = (d2 = e2 == null ? void 0 : e2.value) == null ? void 0 : d2.info) == null ? void 0 : m.version
      };
    });
    return (t, r) => (openBlock(), createElementBlock("div", {
      "aria-label": `Collection: ${u2.value.title}`,
      class: "mx-auto flex h-fit w-full flex-col gap-2 pb-3 pt-6 md:mx-auto md:max-w-[720px]"
    }, [
      createVNode($2, {
        modelValue: n.value,
        placement: "bottom-start",
        "onUpdate:modelValue": r[0] || (r[0] = (o) => x(o))
      }, {
        default: withCtx(() => [
          createVNode(unref($), {
            class: "hover:bg-b-2 aspect-square h-7 w-7 cursor-pointer rounded border border-transparent p-0 hover:border-inherit",
            variant: "ghost"
          }, {
            default: withCtx(() => [
              createVNode(unref(d), {
                class: "text-c-2 size-5",
                src: n.value,
                "stroke-width": "2"
              }, null, 8, ["src"])
            ]),
            _: 1
          })
        ]),
        _: 1
      }, 8, ["modelValue"]),
      createBaseVNode("div", U, [
        createVNode(e, {
          inputId: "collectionName",
          placeholder: "Untitled Collection",
          value: u2.value.title,
          class: "text-xl font-bold",
          onUpdateValue: _2
        }, null, 8, ["value"])
      ])
    ], 8, N));
  }
});

// node_modules/@scalar/api-client/dist/views/Collection/CollectionInfoForm.vue.js
var f = s(S, [["__scopeId", "data-v-4581ed59"]]);

// node_modules/@scalar/api-client/dist/views/Collection/CollectionNavigation.vue.js
var R = { class: "bg-b-1 sticky -top-[104px] z-10 mx-auto w-full" };
var V = {
  key: 0,
  class: "flex max-w-40 items-center"
};
var F2 = { class: "text-c-1 mr-[6.25px] hidden overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium md:block" };
var G = defineComponent({
  __name: "CollectionNavigation",
  props: {
    isSticky: { type: Boolean }
  },
  setup(A) {
    const { currentRoute: a2 } = useRouter(), { activeCollection: o } = F(), h = computed(() => {
      var e2, s2;
      return [
        {
          displayName: "Overview",
          // icon: 'Collection',
          to: {
            name: "collection.overview",
            params: {
              [a.Collection]: (e2 = o.value) == null ? void 0 : e2.uid
            }
          }
        },
        // {
        //   displayName: 'Authentication',
        //   // icon: 'Lock',
        //   to: {
        //     name: 'collection.authentication',
        //     params: {
        //       [PathId.Collection]: activeCollection.value?.uid,
        //     },
        //   },
        // },
        {
          displayName: "Servers",
          // icon: 'Server',
          to: {
            name: "collection.servers",
            params: {
              [a.Collection]: (s2 = o.value) == null ? void 0 : s2.uid
            }
          }
        },
        // {
        //   displayName: 'Environments',
        //   // icon: 'Brackets',
        //   to: {
        //     name: 'collection.environment',
        //     params: {
        //       [PathId.Collection]: activeCollection.value?.uid,
        //     },
        //   },
        // },
        // {
        //   displayName: 'Cookies',
        //   // icon: 'Cookie',
        //   to: {
        //     name: 'collection.cookies',
        //     params: {
        //       [PathId.Collection]: activeCollection.value?.uid,
        //     },
        //   },
        // },
        // {
        //   displayName: 'Scripts',
        //   // icon: 'CodeFolder',
        //   to: {
        //     name: 'collection.scripts',
        //     params: {
        //       [PathId.Collection]: activeCollection.value?.uid,
        //     },
        //   },
        // },
        // {
        //   displayName: 'Sync',
        //   // icon: 'Download',
        //   to: {
        //     name: 'collection.sync',
        //   },
        // },
        {
          displayName: "Settings",
          // icon: 'Settings',
          to: {
            name: "collection.settings"
          }
        }
      ];
    });
    return (e2, s2) => {
      var m, d2, p;
      return openBlock(), createElementBlock("div", R, [
        createVNode(f),
        createBaseVNode("div", {
          class: normalizeClass([
            "items-center text-sm font-medium",
            e2.isSticky ? "h-fit border-b md:grid md:grid-cols-[1fr_720px_1fr] md:px-4" : "flex md:mx-auto md:max-w-[720px]"
          ])
        }, [
          e2.isSticky ? (openBlock(), createElementBlock("div", V, [
            createVNode(unref(d), {
              class: "text-c-2 hidden size-3.5 md:block",
              src: ((m = unref(o)) == null ? void 0 : m["x-scalar-icon"]) || "interface-content-folder",
              "stroke-width": "2"
            }, null, 8, ["src"]),
            createBaseVNode("span", F2, toDisplayString((p = (d2 = unref(o)) == null ? void 0 : d2.info) == null ? void 0 : p.title), 1)
          ])) : createCommentVNode("", true),
          createBaseVNode("div", {
            class: normalizeClass(["flex w-full max-w-[720px] gap-2 pl-1.5 md:ml-1.5 md:pl-0", !e2.isSticky && "border-b"])
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(h.value, ({ to: n, displayName: w }, y2) => (openBlock(), createBlock(unref(RouterLink), {
              key: y2,
              class: "-ml-2 flex h-10 cursor-pointer items-center whitespace-nowrap px-2 text-center text-sm font-medium no-underline -outline-offset-1 has-[:focus-visible]:outline",
              to: n
            }, {
              default: withCtx(() => {
                var u2;
                return [
                  createBaseVNode("span", {
                    class: normalizeClass([
                      "flex-center h-full w-full border-b",
                      typeof n.name == "string" && typeof unref(a2).name == "string" && ((u2 = unref(a2).name) != null && u2.startsWith(n.name)) ? "text-c-1 border-c-1" : "text-c-2 hover:text-c-1 border-transparent"
                    ])
                  }, toDisplayString(w), 3)
                ];
              }),
              _: 2
            }, 1032, ["to"]))), 128))
          ], 2)
        ], 2)
      ]);
    };
  }
});

// node_modules/@scalar/api-client/dist/views/Collection/Collection.vue2.js
var V2 = { class: "w-full md:mx-auto md:max-w-[720px]" };
var g2 = defineComponent({
  __name: "Collection",
  setup($3) {
    const { activeCollection: i2 } = F(), m = useRouter(), o = ref(null), { y: a2 } = useScroll(o), f2 = computed(() => a2.value > 104);
    return watch(
      i2,
      (t) => {
        var e2;
        if (((e2 = t == null ? void 0 : t.info) == null ? void 0 : e2.title) === "Drafts") {
          const u2 = t.requests[0];
          m.push({
            name: "request",
            params: { [a.Request]: u2 }
          });
        }
      },
      {
        immediate: true
      }
    ), (t, e2) => (openBlock(), createBlock(u, {
      ref_key: "el",
      ref: o,
      class: "h-fit overflow-auto pb-6 xl:overflow-auto"
    }, {
      default: withCtx(() => [
        createVNode(_, { class: "xl:h-fit" }, {
          default: withCtx(() => [
            createVNode(G, { isSticky: f2.value }, null, 8, ["isSticky"]),
            createBaseVNode("div", V2, [
              createVNode(unref(RouterView))
            ])
          ]),
          _: 1
        })
      ]),
      _: 1
    }, 512));
  }
});
export {
  g2 as default
};
//# sourceMappingURL=Collection.vue-SW2DVFGN.js.map
