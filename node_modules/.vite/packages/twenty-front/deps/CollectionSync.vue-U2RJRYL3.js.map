{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/CollectionSync.vue2.js"], "sourcesContent": ["import { defineComponent as e, openBlock as o, createBlock as n, withCtx as r, createTextVNode as c } from \"vue\";\nimport _ from \"../../components/ViewLayout/ViewLayoutSection.vue.js\";\nconst a = /* @__PURE__ */ e({\n  __name: \"CollectionSync\",\n  setup(i) {\n    return (l, t) => (o(), n(_, null, {\n      title: r(() => t[0] || (t[0] = [\n        c(\"Sync\")\n      ])),\n      _: 1\n    }));\n  }\n});\nexport {\n  a as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM,GAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG,MAAM;AAAA,MAChC,OAAO,QAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,QAC7B,gBAAE,MAAM;AAAA,MACV,EAAE;AAAA,MACF,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": []}