import {
  Z,
  _
} from "./chunk-TG5SUGVT.js";
import {
  s
} from "./chunk-MFP3SY2Q.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  defineComponent,
  guardReactiveProps,
  i2 as i,
  mergeProps,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  openBlock,
  ref,
  renderSlot,
  resolveDynamicComponent,
  unref,
  w2 as w,
  withCtx
} from "./chunk-NPL3RUXR.js";

// node_modules/@scalar/api-client/dist/components/DataTable/DataTable.vue.js
var b = {
  key: 0,
  class: "sr-only"
};
var g = defineComponent({
  __name: "DataTable",
  props: {
    columns: {},
    scroll: { type: Boolean }
  },
  setup(v) {
    const { cx: a } = i();
    return (o, w2) => (openBlock(), createElementBlock("div", normalizeProps(guardReactiveProps(
      unref(a)(
        o.scroll ? "overflow-x-auto custom-scroll" : "overflow-visible",
        "scalar-data-table bg-b-1"
      )
    )), [
      createBaseVNode("table", {
        class: normalizeClass(["mb-0 grid min-h-8 auto-rows-auto", { "w-max min-w-full": o.scroll }]),
        style: normalizeStyle({
          gridTemplateColumns: o.columns.map((r) => r || "1fr").join(" ")
        })
      }, [
        o.$slots.caption ? (openBlock(), createElementBlock("caption", b, [
          renderSlot(o.$slots, "caption")
        ])) : createCommentVNode("", true),
        renderSlot(o.$slots, "default")
      ], 6)
    ], 16));
  }
});

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableRow.vue.js
var c = {};
var s2 = { class: "group contents w-fit min-w-full" };
function l(t, a) {
  return openBlock(), createElementBlock("tr", s2, [
    renderSlot(t.$slots, "default")
  ]);
}
var i2 = s(c, [["render", l]]);

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableCell.vue.js
var f = defineComponent({
  inheritAttrs: false,
  __name: "DataTableCell",
  props: {
    is: { default: "td" }
  },
  setup(b2) {
    const { cx: r } = i();
    return (e, i3) => (openBlock(), createBlock(resolveDynamicComponent(e.is), mergeProps(
      unref(r)(
        "box-content max-h-8 min-h-8 min-w-8 border-l-0 border-t border-b-0 border-r flex text-sm last:border-r-0 group-last:border-b-transparent p-0 m-0 relative"
      ),
      {
        class: "group-[.alert]:bg-b-alert group-[.error]:bg-b-danger",
        role: "cell"
      }
    ), {
      default: withCtx(() => [
        renderSlot(e.$slots, "default")
      ]),
      _: 3
    }, 16));
  }
});

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableInput.vue2.js
var S = ["for"];
var T = { class: "row-1 overflow-x-auto" };
var N = ["readOnly", "type", "value"];
var P = {
  key: 2,
  class: "scalar-input-required centered-y text-xxs text-c-3 bg-b-1 absolute right-2 pt-px opacity-100 shadow-[-8px_0_4px_var(--scalar-background-1)] transition-opacity duration-150 peer-has-[:focus-visible]:opacity-0"
};
var x = {
  key: 1,
  class: "centered-y text-orange absolute right-7 text-xs"
};
var R = defineComponent({
  inheritAttrs: false,
  __name: "DataTableInput",
  props: {
    id: {},
    type: {},
    containerClass: {},
    required: { type: Boolean, default: false },
    modelValue: {},
    canAddCustomEnumValue: { type: Boolean, default: true },
    readOnly: { type: Boolean, default: false },
    enum: {},
    min: {},
    max: {},
    environment: {},
    envVariables: {},
    workspace: {}
  },
  emits: ["update:modelValue", "inputFocus", "inputBlur", "selectVariable"],
  setup(v, { emit: y }) {
    const a = v, r = y, n = ref(true), V = ref(false), m2 = ref(null), w2 = () => {
      V.value || r("inputBlur");
    }, f2 = computed(
      () => a.type === "password" ? n.value ? "password" : "text" : a.type ?? "text"
    ), k = () => {
      var e, l2;
      !((e = a.enum) != null && e.length) && !a.readOnly && ((l2 = m2.value) == null || l2.focus());
    };
    return (e, l2) => (openBlock(), createBlock(f, {
      class: normalizeClass(["row relative", e.containerClass])
    }, {
      default: withCtx(() => [
        e.$slots.default ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: "text-c-1 flex items-center pl-3 pr-0",
          for: e.id ?? "",
          onClick: k
        }, [
          renderSlot(e.$slots, "default", {}, void 0, true),
          l2[5] || (l2[5] = createTextVNode(": "))
        ], 8, S)) : createCommentVNode("", true),
        createBaseVNode("div", T, [
          a.enum && a.enum.length ? (openBlock(), createBlock(Z, {
            key: 0,
            canAddCustomValue: a.canAddCustomEnumValue,
            modelValue: a.modelValue,
            value: a.enum,
            "onUpdate:modelValue": l2[0] || (l2[0] = (o) => r("update:modelValue", o))
          }, null, 8, ["canAddCustomValue", "modelValue", "value"])) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
            n.value && e.type === "password" ? (openBlock(), createElementBlock("input", mergeProps({ key: 0 }, e.id ? { ...e.$attrs, id: e.id } : e.$attrs, {
              autocomplete: "off",
              class: "text-c-1 disabled:text-c-2 py-1.25 peer w-full min-w-0 border-none px-2 -outline-offset-2",
              "data-1p-ignore": "",
              readOnly: e.readOnly,
              spellcheck: "false",
              type: f2.value,
              value: e.modelValue,
              onInput: l2[1] || (l2[1] = (o) => r(
                "update:modelValue",
                o.target.value ?? ""
              ))
            }), null, 16, N)) : (openBlock(), createBlock(_, mergeProps({
              key: 1,
              ref_key: "codeInput",
              ref: m2
            }, e.$attrs, {
              id: e.id,
              class: "text-c-1 disabled:text-c-2 peer w-full min-w-0 border-none",
              disableCloseBrackets: "",
              disableTabIndent: "",
              envVariables: e.envVariables,
              environment: e.environment,
              max: e.max,
              min: e.min,
              modelValue: e.modelValue ?? "",
              readOnly: e.readOnly,
              required: !!e.required,
              spellcheck: "false",
              type: f2.value,
              workspace: e.workspace,
              onBlur: w2,
              onFocus: l2[2] || (l2[2] = (o) => r("inputFocus")),
              "onUpdate:modelValue": l2[3] || (l2[3] = (o) => r("update:modelValue", o))
            }), null, 16, ["id", "envVariables", "environment", "max", "min", "modelValue", "readOnly", "required", "type", "workspace"])),
            e.required ? (openBlock(), createElementBlock("div", P, " Required ")) : createCommentVNode("", true)
          ], 64))
        ]),
        e.$slots.warning ? (openBlock(), createElementBlock("div", x, [
          renderSlot(e.$slots, "warning", {}, void 0, true)
        ])) : createCommentVNode("", true),
        renderSlot(e.$slots, "icon", {}, void 0, true),
        e.type === "password" ? (openBlock(), createBlock(unref(w), {
          key: 2,
          class: "-ml-.5 mr-0.75 h-6 w-6 self-center p-1.5",
          icon: n.value ? "Show" : "Hide",
          label: n.value ? "Show Password" : "Hide Password",
          onClick: l2[4] || (l2[4] = (o) => n.value = !n.value)
        }, null, 8, ["icon", "label"])) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["class"]));
  }
});

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableInput.vue.js
var m = s(R, [["__scopeId", "data-v-921a6c09"]]);

export {
  g,
  f,
  i2 as i,
  m
};
//# sourceMappingURL=chunk-7YYIGOII.js.map
