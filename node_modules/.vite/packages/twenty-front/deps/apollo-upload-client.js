import {
  core_exports,
  http_exports,
  init_core,
  init_http
} from "./chunk-6Q2IFXCX.js";
import "./chunk-JB6CC7ZF.js";
import "./chunk-Z7KI3FL7.js";
import "./chunk-37DL3DMY.js";
import "./chunk-R5Y5ZCXM.js";
import "./chunk-I7RZVVY3.js";
import "./chunk-LQCADWFL.js";
import "./chunk-I4HVGP7M.js";
import {
  __commonJS,
  __toCommonJS,
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/extract-files/public/ReactNativeFile.js
var require_ReactNativeFile = __commonJS({
  "node_modules/extract-files/public/ReactNativeFile.js"(exports, module) {
    "use strict";
    module.exports = class ReactNativeFile {
      constructor({ uri, name, type }) {
        this.uri = uri;
        this.name = name;
        this.type = type;
      }
    };
  }
});

// node_modules/extract-files/public/isExtractableFile.js
var require_isExtractableFile = __commonJS({
  "node_modules/extract-files/public/isExtractableFile.js"(exports, module) {
    "use strict";
    var ReactNativeFile = require_ReactNativeFile();
    module.exports = function isExtractableFile(value) {
      return typeof File !== "undefined" && value instanceof File || typeof Blob !== "undefined" && value instanceof Blob || value instanceof ReactNativeFile;
    };
  }
});

// node_modules/extract-files/public/extractFiles.js
var require_extractFiles = __commonJS({
  "node_modules/extract-files/public/extractFiles.js"(exports, module) {
    "use strict";
    var defaultIsExtractableFile = require_isExtractableFile();
    module.exports = function extractFiles(value, path = "", isExtractableFile = defaultIsExtractableFile) {
      const files = /* @__PURE__ */ new Map();
      const clones = /* @__PURE__ */ new Map();
      function recurse(value2, path2, recursed) {
        let clone = value2;
        if (isExtractableFile(value2)) {
          clone = null;
          const filePaths = files.get(value2);
          filePaths ? filePaths.push(path2) : files.set(value2, [path2]);
        } else {
          const isList = Array.isArray(value2) || typeof FileList !== "undefined" && value2 instanceof FileList;
          const isObject = value2 && value2.constructor === Object;
          if (isList || isObject) {
            const hasClone = clones.has(value2);
            if (hasClone) clone = clones.get(value2);
            else {
              clone = isList ? [] : {};
              clones.set(value2, clone);
            }
            if (!recursed.has(value2)) {
              const pathPrefix = path2 ? `${path2}.` : "";
              const recursedDeeper = new Set(recursed).add(value2);
              if (isList) {
                let index = 0;
                for (const item of value2) {
                  const itemClone = recurse(
                    item,
                    pathPrefix + index++,
                    recursedDeeper
                  );
                  if (!hasClone) clone.push(itemClone);
                }
              } else
                for (const key in value2) {
                  const propertyClone = recurse(
                    value2[key],
                    pathPrefix + key,
                    recursedDeeper
                  );
                  if (!hasClone) clone[key] = propertyClone;
                }
            }
          }
        }
        return clone;
      }
      return {
        clone: recurse(value, path, /* @__PURE__ */ new Set()),
        files
      };
    };
  }
});

// node_modules/apollo-upload-client/public/formDataAppendFile.js
var require_formDataAppendFile = __commonJS({
  "node_modules/apollo-upload-client/public/formDataAppendFile.js"(exports, module) {
    "use strict";
    module.exports = function formDataAppendFile(formData, fieldName, file) {
      formData.append(fieldName, file, file.name);
    };
  }
});

// node_modules/apollo-upload-client/public/isExtractableFile.js
var require_isExtractableFile2 = __commonJS({
  "node_modules/apollo-upload-client/public/isExtractableFile.js"(exports, module) {
    "use strict";
    module.exports = require_isExtractableFile();
  }
});

// node_modules/apollo-upload-client/public/createUploadLink.js
var require_createUploadLink = __commonJS({
  "node_modules/apollo-upload-client/public/createUploadLink.js"(exports, module) {
    "use strict";
    var { ApolloLink, Observable } = (init_core(), __toCommonJS(core_exports));
    var {
      createSignalIfSupported,
      fallbackHttpConfig,
      parseAndCheckHttpResponse,
      rewriteURIForGET,
      selectHttpOptionsAndBody,
      selectURI,
      serializeFetchParameter
    } = (init_http(), __toCommonJS(http_exports));
    var extractFiles = require_extractFiles();
    var formDataAppendFile = require_formDataAppendFile();
    var isExtractableFile = require_isExtractableFile2();
    module.exports = function createUploadLink({
      uri: fetchUri = "/graphql",
      useGETForQueries,
      isExtractableFile: customIsExtractableFile = isExtractableFile,
      FormData: CustomFormData,
      formDataAppendFile: customFormDataAppendFile = formDataAppendFile,
      fetch: customFetch,
      fetchOptions,
      credentials,
      headers,
      includeExtensions
    } = {}) {
      const linkConfig = {
        http: { includeExtensions },
        options: fetchOptions,
        credentials,
        headers
      };
      return new ApolloLink((operation) => {
        const context = operation.getContext();
        const {
          // Apollo Studio client awareness `name` and `version` can be configured
          // via `ApolloClient` constructor options:
          // https://apollographql.com/docs/studio/client-awareness/#using-apollo-server-and-apollo-client
          clientAwareness: { name, version } = {},
          headers: headers2
        } = context;
        const contextConfig = {
          http: context.http,
          options: context.fetchOptions,
          credentials: context.credentials,
          headers: {
            // Client awareness headers can be overridden by context `headers`.
            ...name && { "apollographql-client-name": name },
            ...version && { "apollographql-client-version": version },
            ...headers2
          }
        };
        const { options, body } = selectHttpOptionsAndBody(
          operation,
          fallbackHttpConfig,
          linkConfig,
          contextConfig
        );
        const { clone, files } = extractFiles(body, "", customIsExtractableFile);
        let uri = selectURI(operation, fetchUri);
        if (files.size) {
          delete options.headers["content-type"];
          const RuntimeFormData = CustomFormData || FormData;
          const form = new RuntimeFormData();
          form.append("operations", serializeFetchParameter(clone, "Payload"));
          const map = {};
          let i = 0;
          files.forEach((paths) => {
            map[++i] = paths;
          });
          form.append("map", JSON.stringify(map));
          i = 0;
          files.forEach((paths, file) => {
            customFormDataAppendFile(form, ++i, file);
          });
          options.body = form;
        } else {
          if (useGETForQueries && // If the operation contains some mutations GET shouldn’t be used.
          !operation.query.definitions.some(
            (definition) => definition.kind === "OperationDefinition" && definition.operation === "mutation"
          ))
            options.method = "GET";
          if (options.method === "GET") {
            const { newURI, parseError } = rewriteURIForGET(uri, body);
            if (parseError)
              return new Observable((observer) => {
                observer.error(parseError);
              });
            uri = newURI;
          } else options.body = serializeFetchParameter(clone, "Payload");
        }
        const { controller } = createSignalIfSupported();
        if (controller) {
          if (options.signal)
            options.signal.aborted ? (
              // Signal already aborted, so immediately abort.
              controller.abort()
            ) : (
              // Signal not already aborted, so setup a listener to abort when it
              // does.
              options.signal.addEventListener(
                "abort",
                () => {
                  controller.abort();
                },
                {
                  // Prevent a memory leak if the user configured abort controller
                  // is long lasting, or controls multiple things.
                  once: true
                }
              )
            );
          options.signal = controller.signal;
        }
        const runtimeFetch = customFetch || fetch;
        return new Observable((observer) => {
          let cleaningUp;
          runtimeFetch(uri, options).then((response) => {
            operation.setContext({ response });
            return response;
          }).then(parseAndCheckHttpResponse(operation)).then((result) => {
            observer.next(result);
            observer.complete();
          }).catch((error) => {
            if (!cleaningUp) {
              if (error.result && error.result.errors && error.result.data)
                observer.next(error.result);
              observer.error(error);
            }
          });
          return () => {
            cleaningUp = true;
            if (controller) controller.abort();
          };
        });
      });
    };
  }
});

// node_modules/apollo-upload-client/public/ReactNativeFile.js
var require_ReactNativeFile2 = __commonJS({
  "node_modules/apollo-upload-client/public/ReactNativeFile.js"(exports, module) {
    "use strict";
    module.exports = require_ReactNativeFile();
  }
});

// node_modules/apollo-upload-client/public/index.mjs
var import_createUploadLink = __toESM(require_createUploadLink(), 1);
var import_formDataAppendFile = __toESM(require_formDataAppendFile(), 1);
var import_isExtractableFile = __toESM(require_isExtractableFile2(), 1);
var import_ReactNativeFile = __toESM(require_ReactNativeFile2(), 1);
var export_ReactNativeFile = import_ReactNativeFile.default;
var export_createUploadLink = import_createUploadLink.default;
var export_formDataAppendFile = import_formDataAppendFile.default;
var export_isExtractableFile = import_isExtractableFile.default;
export {
  export_ReactNativeFile as ReactNativeFile,
  export_createUploadLink as createUploadLink,
  export_formDataAppendFile as formDataAppendFile,
  export_isExtractableFile as isExtractableFile
};
//# sourceMappingURL=apollo-upload-client.js.map
