import {
  i
} from "./chunk-KEHKEING.js";
import {
  b
} from "./chunk-4Q4UAWOO.js";
import {
  s
} from "./chunk-MFP3SY2Q.js";
import {
  createBaseVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  openBlock,
  unref
} from "./chunk-NPL3RUXR.js";

// node_modules/@scalar/api-client/dist/assets/keycap.ascii.js
var n = `          .:=+++++=================-
         .--#*                       :.
        .-:-**                        -.
      .:-::-+*                        =:
     .:-:::-=#                         ::
    .-:::----**                        ..
   .-:::::---=#                         ..
  :-::::::----**                         ..
.:-::::::::----*=                        ..
.-::::::::------+-                        ..
..::::::::-------=                         ..
 .:::::----------++                        ..
   .:::----------+**+*++*+*++*+++*++++++++++:.
   ------------+*+=-=======================.
   .----------+*+=========================:.
    :--------+*+=-========================.
    .=------+*+=-========================:.
     .=----+*+=-------=================+-.
     .----+*+=----------================.
     .:=-+*+=----------=-==============-
      .-+*+=----------------===========.
       .-+=------------------====-====:.`;

// node_modules/@scalar/api-client/dist/components/EmptyState.vue2.js
var p = { class: "flex-center flex w-full scale-75" };
var m = { class: "relative" };
var d = { class: "relative -ml-12" };
var h = defineComponent({
  __name: "EmptyState",
  setup(_) {
    return (f, r2) => (openBlock(), createElementBlock("div", p, [
      createBaseVNode("div", m, [
        createVNode(b, {
          class: "keycap-hotkey right-14 border-transparent text-xl",
          hotkey: ""
        }),
        createVNode(i, {
          art: unref(n),
          class: "text-c-3 !leading-[6px]"
        }, null, 8, ["art"])
      ]),
      createBaseVNode("div", d, [
        r2[0] || (r2[0] = createBaseVNode("div", { class: "keycap-hotkey right-16 text-xl" }, "K", -1)),
        createVNode(i, {
          art: unref(n),
          class: "keycap-n !leading-[6px]"
        }, null, 8, ["art"])
      ])
    ]));
  }
});

// node_modules/@scalar/api-client/dist/components/EmptyState.vue.js
var r = s(h, [["__scopeId", "data-v-6e1f579f"]]);

export {
  r
};
//# sourceMappingURL=chunk-LLFI6D6O.js.map
