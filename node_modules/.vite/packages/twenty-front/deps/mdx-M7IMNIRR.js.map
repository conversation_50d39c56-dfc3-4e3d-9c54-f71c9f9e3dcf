{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/mdx/mdx.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/mdx/mdx.ts\nvar conf = {\n  comments: {\n    blockComment: [\"{/*\", \"*/}\"]\n  },\n  brackets: [[\"{\", \"}\"]],\n  autoClosingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"\\u201C\", close: \"\\u201D\" },\n    { open: \"\\u2018\", close: \"\\u2019\" },\n    { open: \"`\", close: \"`\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"_\", close: \"_\" },\n    { open: \"**\", close: \"**\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: /^\\s*- .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"- \" }\n    },\n    {\n      beforeText: /^\\s*\\+ .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"+ \" }\n    },\n    {\n      beforeText: /^\\s*\\* .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"* \" }\n    },\n    {\n      beforeText: /^> /,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"> \" }\n    },\n    {\n      beforeText: /<\\w+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /\\s+>\\s*$/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /<\\/\\w+>/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Outdent }\n    },\n    ...Array.from({ length: 100 }, (_, index) => ({\n      beforeText: new RegExp(`^${index}\\\\. .+`),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: `${index + 1}. ` }\n    }))\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".mdx\",\n  control: /[!#()*+.[\\\\\\]_`{}\\-]/,\n  escapes: /\\\\@control/,\n  tokenizer: {\n    root: [\n      [/^---$/, { token: \"meta.content\", next: \"@frontmatter\", nextEmbedded: \"yaml\" }],\n      [/^\\s*import/, { token: \"keyword\", next: \"@import\", nextEmbedded: \"js\" }],\n      [/^\\s*export/, { token: \"keyword\", next: \"@export\", nextEmbedded: \"js\" }],\n      [/<\\w+/, { token: \"type.identifier\", next: \"@jsx\" }],\n      [/<\\/?\\w+>/, \"type.identifier\"],\n      [\n        /^(\\s*)(>*\\s*)(#{1,6}\\s)/,\n        [{ token: \"white\" }, { token: \"comment\" }, { token: \"keyword\", next: \"@header\" }]\n      ],\n      [/^(\\s*)(>*\\s*)([*+-])(\\s+)/, [\"white\", \"comment\", \"keyword\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(-{3,}|\\*{3,}|_{3,})$/, [\"white\", \"comment\", \"keyword\"]],\n      [/`{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_backtick\" }],\n      [/~{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_tilde\" }],\n      [\n        /`{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_backtick\", nextEmbedded: \"$1\" }\n      ],\n      [\n        /~{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_tilde\", nextEmbedded: \"$1\" }\n      ],\n      [/^(\\s*)(-{4,})$/, [\"white\", \"comment\"]],\n      [/^(\\s*)(>+)/, [\"white\", \"comment\"]],\n      { include: \"content\" }\n    ],\n    content: [\n      [\n        /(\\[)(.+)(]\\()(.+)(\\s+\".*\")(\\))/,\n        [\"\", \"string.link\", \"\", \"type.identifier\", \"string.link\", \"\"]\n      ],\n      [/(\\[)(.+)(]\\()(.+)(\\))/, [\"\", \"type.identifier\", \"\", \"string.link\", \"\"]],\n      [/(\\[)(.+)(]\\[)(.+)(])/, [\"\", \"type.identifier\", \"\", \"type.identifier\", \"\"]],\n      [/(\\[)(.+)(]:\\s+)(\\S*)/, [\"\", \"type.identifier\", \"\", \"string.link\"]],\n      [/(\\[)(.+)(])/, [\"\", \"type.identifier\", \"\"]],\n      [/`.*`/, \"variable.source\"],\n      [/_/, { token: \"emphasis\", next: \"@emphasis_underscore\" }],\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@emphasis_asterisk\" }],\n      [/\\*\\*/, { token: \"strong\", next: \"@strong\" }],\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }]\n    ],\n    import: [[/'\\s*(;|$)/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    expression: [\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\" }],\n      [/}/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    export: [[/^\\s*$/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    jsx: [\n      [/\\s+/, \"\"],\n      [/(\\w+)(=)(\"(?:[^\"\\\\]|\\\\.)*\")/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+)(=)('(?:[^'\\\\]|\\\\.)*')/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+(?=\\s|>|={|$))/, [\"attribute.name\"]],\n      [/={/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }],\n      [/>/, { token: \"type.identifier\", next: \"@pop\" }]\n    ],\n    header: [\n      [/.$/, { token: \"keyword\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"keyword\" }]\n    ],\n    strong: [\n      [/\\*\\*/, { token: \"strong\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"strong\" }]\n    ],\n    emphasis_underscore: [\n      [/_/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    emphasis_asterisk: [\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    frontmatter: [[/^---$/, { token: \"meta.content\", nextEmbedded: \"@pop\", next: \"@pop\" }]],\n    codeblock_highlight_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_highlight_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;;;;AAAA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,MAoDA;AA/EJ;AAAA;AAwBA;AAjBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,cAAc,CAAC,OAAO,KAAK;AAAA,MAC7B;AAAA,MACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,MACrB,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAU,OAAO,IAAS;AAAA,QAClC,EAAE,MAAM,KAAU,OAAO,IAAS;AAAA,QAClC,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,QAC1B,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,QACnG;AAAA,QACA;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,QACnG;AAAA,QACA;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,QACnG;AAAA,QACA;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,QACnG;AAAA,QACA;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,QACnF;AAAA,QACA;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,QACnF;AAAA,QACA;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,QAAQ;AAAA,QACpF;AAAA,QACA,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,GAAG,CAAC,GAAG,WAAW;AAAA,UAC5C,YAAY,IAAI,OAAO,IAAI,KAAK,QAAQ;AAAA,UACxC,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK;AAAA,QAC/G,EAAE;AAAA,MACJ;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,CAAC,SAAS,EAAE,OAAO,gBAAgB,MAAM,gBAAgB,cAAc,OAAO,CAAC;AAAA,UAC/E,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,WAAW,cAAc,KAAK,CAAC;AAAA,UACxE,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,WAAW,cAAc,KAAK,CAAC;AAAA,UACxE,CAAC,QAAQ,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,UACnD,CAAC,YAAY,iBAAiB;AAAA,UAC9B;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,UAClF;AAAA,UACA,CAAC,6BAA6B,CAAC,SAAS,WAAW,WAAW,OAAO,CAAC;AAAA,UACtE,CAAC,iCAAiC,CAAC,SAAS,WAAW,UAAU,OAAO,CAAC;AAAA,UACzE,CAAC,iCAAiC,CAAC,SAAS,WAAW,UAAU,OAAO,CAAC;AAAA,UACzE,CAAC,sCAAsC,CAAC,SAAS,WAAW,SAAS,CAAC;AAAA,UACtE,CAAC,iBAAiB,EAAE,OAAO,UAAU,MAAM,sBAAsB,CAAC;AAAA,UAClE,CAAC,iBAAiB,EAAE,OAAO,UAAU,MAAM,mBAAmB,CAAC;AAAA,UAC/D;AAAA,YACE;AAAA,YACA,EAAE,OAAO,UAAU,MAAM,iCAAiC,cAAc,KAAK;AAAA,UAC/E;AAAA,UACA;AAAA,YACE;AAAA,YACA,EAAE,OAAO,UAAU,MAAM,8BAA8B,cAAc,KAAK;AAAA,UAC5E;AAAA,UACA,CAAC,kBAAkB,CAAC,SAAS,SAAS,CAAC;AAAA,UACvC,CAAC,cAAc,CAAC,SAAS,SAAS,CAAC;AAAA,UACnC,EAAE,SAAS,UAAU;AAAA,QACvB;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE;AAAA,YACA,CAAC,IAAI,eAAe,IAAI,mBAAmB,eAAe,EAAE;AAAA,UAC9D;AAAA,UACA,CAAC,yBAAyB,CAAC,IAAI,mBAAmB,IAAI,eAAe,EAAE,CAAC;AAAA,UACxE,CAAC,wBAAwB,CAAC,IAAI,mBAAmB,IAAI,mBAAmB,EAAE,CAAC;AAAA,UAC3E,CAAC,wBAAwB,CAAC,IAAI,mBAAmB,IAAI,aAAa,CAAC;AAAA,UACnE,CAAC,eAAe,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,UAC3C,CAAC,QAAQ,iBAAiB;AAAA,UAC1B,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,uBAAuB,CAAC;AAAA,UACzD,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,qBAAqB,CAAC;AAAA,UAC9D,CAAC,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,UAC7C,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,eAAe,cAAc,KAAK,CAAC;AAAA,QAC/E;AAAA,QACA,QAAQ,CAAC,CAAC,aAAa,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC,CAAC;AAAA,QAC/E,YAAY;AAAA,UACV,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,cAAc,CAAC;AAAA,UACzD,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,QAC1E;AAAA,QACA,QAAQ,CAAC,CAAC,SAAS,EAAE,OAAO,qBAAqB,MAAM,QAAQ,cAAc,OAAO,CAAC,CAAC;AAAA,QACtF,KAAK;AAAA,UACH,CAAC,OAAO,EAAE;AAAA,UACV,CAAC,+BAA+B,CAAC,kBAAkB,YAAY,QAAQ,CAAC;AAAA,UACxE,CAAC,+BAA+B,CAAC,kBAAkB,YAAY,QAAQ,CAAC;AAAA,UACxE,CAAC,sBAAsB,CAAC,gBAAgB,CAAC;AAAA,UACzC,CAAC,MAAM,EAAE,OAAO,qBAAqB,MAAM,eAAe,cAAc,KAAK,CAAC;AAAA,UAC9E,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,QAClD;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,MAAM,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,UACzC,EAAE,SAAS,UAAU;AAAA,UACrB,CAAC,KAAK,EAAE,OAAO,UAAU,CAAC;AAAA,QAC5B;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UAC1C,EAAE,SAAS,UAAU;AAAA,UACrB,CAAC,KAAK,EAAE,OAAO,SAAS,CAAC;AAAA,QAC3B;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,UACzC,EAAE,SAAS,UAAU;AAAA,UACrB,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,QAC7B;AAAA,QACA,mBAAmB;AAAA,UACjB,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,UAChD,EAAE,SAAS,UAAU;AAAA,UACrB,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,QAC7B;AAAA,QACA,aAAa,CAAC,CAAC,SAAS,EAAE,OAAO,gBAAgB,cAAc,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,QACtF,8BAA8B;AAAA,UAC5B,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UACxE,CAAC,OAAO,iBAAiB;AAAA,QAC3B;AAAA,QACA,2BAA2B;AAAA,UACzB,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UACxE,CAAC,OAAO,iBAAiB;AAAA,QAC3B;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UAClD,CAAC,OAAO,iBAAiB;AAAA,QAC3B;AAAA,QACA,iBAAiB;AAAA,UACf,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UAClD,CAAC,OAAO,iBAAiB;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}