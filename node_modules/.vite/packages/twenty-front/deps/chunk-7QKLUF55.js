import {
  $ as $2,
  e,
  k,
  require_mime_type
} from "./chunk-YDXMHME2.js";
import {
  N as N2
} from "./chunk-SNDMCBA5.js";
import {
  f as f2,
  g,
  i as i4,
  m as m2
} from "./chunk-7YYIGOII.js";
import {
  _ as _2
} from "./chunk-TG5SUGVT.js";
import {
  s
} from "./chunk-OFOUQLHF.js";
import {
  useRouter
} from "./chunk-Z6VJM5ZM.js";
import {
  REGEX,
  REQUEST_METHODS,
  a,
  canMethodHaveBody,
  httpStatusCodes,
  isDefined,
  je,
  makeUrlAbsolute,
  mergeUrls,
  pkceOptions,
  requestExampleParametersSchema,
  s as s2,
  shouldUseProxy
} from "./chunk-MFP3SY2Q.js";
import {
  _
} from "./chunk-TACAB4LM.js";
import {
  $,
  E,
  E2,
  Fragment,
  I,
  <PERSON>,
  N,
  P,
  Q,
  S,
  Se,
  T,
  T2,
  V,
  b2 as b,
  be,
  c,
  c2,
  capitalize,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineComponent,
  f,
  ge,
  i,
  i2,
  i3,
  m2 as m,
  mergeModels,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onMounted,
  openBlock,
  readonly,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  toDisplayString,
  u,
  u2,
  unref,
  useId,
  useModel,
  vShow,
  w,
  w3 as w2,
  watch,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-NPL3RUXR.js";
import {
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/components/SectionFilterButton.vue.js
var l = ["aria-controls", "aria-selected", "tabindex"];
var p = defineComponent({
  __name: "SectionFilterButton",
  props: {
    selected: { type: Boolean },
    controls: {}
  },
  setup(a3) {
    return (e5, c4) => (openBlock(), createElementBlock("button", {
      "aria-controls": e5.controls ?? "",
      "aria-selected": !!e5.selected,
      class: normalizeClass(["hover:bg-b-2 flex w-fit cursor-pointer items-center whitespace-nowrap rounded p-1 px-2 text-center font-medium has-[:focus-visible]:outline", { "text-c-1 pointer-events-none": e5.selected }]),
      role: "tab",
      tabindex: e5.selected ? 0 : -1,
      type: "button"
    }, [
      renderSlot(e5.$slots, "default")
    ], 10, l));
  }
});

// node_modules/@scalar/api-client/dist/components/SectionFilter.vue2.js
var _3 = { class: "request-section-content request-section-content-filter fade-request-section-content text-c-3 pointer-events-auto relative hidden w-full justify-end gap-[1.5px] rounded py-2 text-xs xl:flex" };
var $3 = { class: "context-bar-group-hover:text-c-1 absolute -right-[30px] top-1/2 flex -translate-y-1/2 items-center" };
var F = { class: "context-bar-group-hover:hidden mr-1.5" };
var K = defineComponent({
  __name: "SectionFilter",
  props: mergeModels({
    filters: { default: () => [] },
    filterIds: {}
  }, {
    modelValue: {},
    modelModifiers: {}
  }),
  emits: ["update:modelValue"],
  setup(l2) {
    const t2 = useModel(l2, "modelValue"), r5 = ref(), c4 = (s5) => {
      const o3 = s5 === "prev" ? -1 : 1, e5 = t2.value ? l2.filters.indexOf(t2.value) : 0, n4 = l2.filters.length, d3 = (e5 + o3 + n4) % n4;
      t2.value = l2.filters[d3], nextTick(() => {
        if (r5.value) {
          const u4 = r5.value.querySelector(
            'button[aria-selected="true"]'
          );
          u4 && u4.focus();
        }
      });
    };
    return (s5, o3) => (openBlock(), createElementBlock("div", {
      ref_key: "tablist",
      ref: r5,
      class: "filter-hover context-bar-group ml-auto hidden lg:flex",
      role: "tablist",
      onKeydown: [
        o3[0] || (o3[0] = withKeys((e5) => c4("prev"), ["left"])),
        o3[1] || (o3[1] = withKeys((e5) => c4("next"), ["right"]))
      ]
    }, [
      createBaseVNode("div", _3, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(s5.filters, (e5) => {
          var n4;
          return openBlock(), createBlock(p, {
            key: e5,
            class: "filter-hover-item",
            controls: (n4 = s5.filterIds) == null ? void 0 : n4[e5],
            role: "tab",
            selected: t2.value === e5,
            onClick: (d3) => t2.value = e5
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(e5), 1)
            ]),
            _: 2
          }, 1032, ["controls", "selected", "onClick"]);
        }), 128)),
        createBaseVNode("div", $3, [
          createBaseVNode("span", F, toDisplayString(t2.value), 1),
          createVNode(unref(c), {
            icon: "FilterList",
            size: "md",
            thickness: "2"
          })
        ])
      ])
    ], 544));
  }
});

// node_modules/@scalar/api-client/dist/components/SectionFilter.vue.js
var f3 = s2(K, [["__scopeId", "data-v-55241daf"]]);

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableHeader.vue.js
var x = defineComponent({
  __name: "DataTableHeader",
  setup(i7) {
    const { cx: e5 } = i2();
    return (t2, u4) => (openBlock(), createBlock(f2, mergeProps({ is: "th" }, unref(e5)("truncate font-medium px-2 py-1.5")), {
      default: withCtx(() => [
        renderSlot(t2.$slots, "default")
      ]),
      _: 3
    }, 16));
  }
});

// node_modules/@scalar/api-client/dist/components/ViewLayout/ViewLayoutCollapse.vue.js
var v = ["aria-labelledby"];
var k2 = { class: "bg-b-2 flex items-center" };
var w3 = { class: "text-c-1 flex flex-1 items-center gap-1.5" };
var B = ["id"];
var V2 = {
  key: 0,
  class: "sr-only"
};
var $4 = {
  key: 0,
  class: "bg-b-2 text-c-2 text-3xs inline-flex h-4 w-4 items-center justify-center rounded-full border font-semibold"
};
var D = { class: "sr-only" };
var N3 = {
  key: 0,
  class: "ui-not-open:invisible flex items-center gap-2 pr-1.5"
};
var P2 = defineComponent({
  __name: "ViewLayoutCollapse",
  props: {
    defaultOpen: { type: Boolean, default: true },
    itemCount: { default: 0 },
    layout: { default: "client" }
  },
  setup(O2) {
    const c4 = useId();
    return (e5, I4) => (openBlock(), createBlock(unref(N), {
      as: "div",
      class: "group/collapse focus-within:text-c-1 text-c-2 request-item",
      defaultOpen: e5.defaultOpen,
      static: e5.layout === "reference" ? true : void 0
    }, {
      default: withCtx(({ open: o3 }) => [
        createBaseVNode("section", {
          "aria-labelledby": unref(c4),
          class: "contents"
        }, [
          createBaseVNode("div", k2, [
            createVNode(unref(Q), {
              class: normalizeClass([
                "hover:text-c-1 group box-content flex max-h-8 flex-1 items-center gap-2.5 overflow-hidden px-1 py-1.5 text-sm font-medium outline-none md:px-1.5 xl:pl-2 xl:pr-0.5",
                { "!pl-3": e5.layout === "reference" },
                { "group-last/collapse:border-b": e5.layout === "client" }
              ]),
              disabled: e5.layout === "reference"
            }, {
              default: withCtx(() => [
                e5.layout !== "reference" ? (openBlock(), createBlock(unref(c), {
                  key: 0,
                  class: normalizeClass([
                    "text-c-3 group-hover:text-c-1 ui-open:rotate-90 ui-not-open:rotate-0 rounded-px outline-offset-2 group-focus-visible:outline"
                  ]),
                  icon: "ChevronRight",
                  size: "md"
                })) : createCommentVNode("", true),
                createBaseVNode("h2", w3, [
                  createBaseVNode("span", {
                    id: unref(c4),
                    class: "contents"
                  }, [
                    renderSlot(e5.$slots, "title", { open: o3 }),
                    o3 ? createCommentVNode("", true) : (openBlock(), createElementBlock("span", V2, " (Collapsed) "))
                  ], 8, B),
                  !o3 && e5.itemCount ? (openBlock(), createElementBlock("span", $4, [
                    createTextVNode(toDisplayString(e5.itemCount) + " ", 1),
                    createBaseVNode("span", D, "Item" + toDisplayString(e5.itemCount === 1 ? "" : "s"), 1)
                  ])) : createCommentVNode("", true)
                ])
              ]),
              _: 2
            }, 1032, ["class", "disabled"]),
            e5.$slots.actions ? (openBlock(), createElementBlock("div", N3, [
              renderSlot(e5.$slots, "actions", { open: o3 })
            ])) : createCommentVNode("", true)
          ]),
          createVNode(unref(V), mergeProps(e5.$attrs, { class: "diclosure-panel h-full max-h-fit rounded-b" }), {
            default: withCtx(() => [
              renderSlot(e5.$slots, "default", { open: o3 })
            ]),
            _: 2
          }, 1040)
        ], 8, v)
      ]),
      _: 3
    }, 8, ["defaultOpen", "static"]));
  }
});

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableCheckbox.vue.js
var f4 = ["checked", "disabled"];
var y = defineComponent({
  __name: "DataTableCheckbox",
  props: {
    modelValue: { type: Boolean },
    disabled: { type: Boolean },
    align: { default: "center" }
  },
  emits: ["update:modelValue"],
  setup(h2) {
    const s5 = i({
      base: "w-8 h-8 flex items-center justify-center text-border peer-checked:text-c-1 pointer-events-none absolute",
      variants: {
        align: {
          left: "left-0",
          center: "centered"
        }
      }
    });
    return (e5, o3) => (openBlock(), createBlock(f2, { class: "group/cell relative flex min-w-8" }, {
      default: withCtx(() => [
        createBaseVNode("input", {
          checked: e5.modelValue,
          class: "peer absolute inset-0 cursor-pointer opacity-0 disabled:cursor-default",
          disabled: !!e5.disabled,
          type: "checkbox",
          onChange: o3[0] || (o3[0] = (r5) => e5.$emit("update:modelValue", r5.target.checked))
        }, null, 40, f4),
        createBaseVNode("div", {
          class: normalizeClass(unref(s5)({ align: e5.align }))
        }, [
          createBaseVNode("div", {
            class: normalizeClass([
              "absolute m-auto size-3/4 rounded border-[1px] opacity-0",
              !e5.disabled && "group-has-[:focus-visible]/cell:border-c-accent group-hover/cell:opacity-100 group-has-[:focus-visible]/cell:opacity-100"
            ])
          }, null, 2),
          createVNode(unref(c), {
            icon: "Checkmark",
            size: "xs",
            thickness: "2.5"
          })
        ], 2)
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/libs/request.js
var m3 = (e5) => !!(e5.description || e5.type || e5.default || e5.format || e5.minimum || e5.maximum);
var n = (e5) => computed(() => {
  if (e5.value === void 0 || e5.value === "") return false;
  if (e5.type) {
    if (e5.type === "integer") {
      const a3 = Number(e5.value);
      if (isNaN(a3) || !Number.isInteger(a3))
        return "Value must be a whole number (e.g., 42)";
      if (e5.minimum !== void 0 && a3 < e5.minimum)
        return `Value must be ${e5.minimum} or greater`;
      if (e5.maximum !== void 0 && a3 > e5.maximum)
        return `Value must be ${e5.maximum} or less`;
    }
    if (e5.type === "number") {
      const a3 = Number(e5.value);
      if (isNaN(a3))
        return "Value must be a number (e.g., 42.5)";
      if (e5.minimum !== void 0 && a3 < e5.minimum)
        return `Value must be ${e5.minimum} or greater`;
      if (e5.maximum !== void 0 && a3 > e5.maximum)
        return `Value must be ${e5.maximum} or less`;
    }
    if (e5.type === "string" && e5.format) {
      if (e5.format === "date" && !/^\d{4}-\d{2}-\d{2}$/.test(e5.value))
        return "Please enter a valid date in YYYY-MM-DD format (e.g., 2024-03-20)";
      if (e5.format === "date-time" && !/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(Z|[+-]\d{2}:\d{2})$/.test(e5.value))
        return "Please enter a valid date and time in RFC 3339 format (e.g., 2024-03-20T13:45:30Z)";
      if (e5.format === "email" && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e5.value))
        return "Please enter a valid email address (e.g., <EMAIL>)";
      if (e5.format === "uri" && !/^[a-zA-Z][a-zA-Z0-9+.-]*:.+$/.test(e5.value))
        return "Please enter a valid URI (e.g., https://example.com)";
    }
  }
  return false;
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestTableTooltip.vue2.js
var g2 = { class: "bg-b-1 mr-0.25 pl-1 pr-1.5 group-[.alert]:bg-transparent group-[.error]:bg-transparent" };
var b2 = { class: "w-content bg-b-1 text-xxs text-c-1 pointer-events-none grid min-w-48 gap-1.5 rounded p-2 leading-5 shadow-lg" };
var h = {
  key: 0,
  class: "text-error-1"
};
var y2 = {
  key: 1,
  class: "schema text-c-2 flex items-center"
};
var v2 = { key: 0 };
var k3 = { key: 1 };
var _4 = { key: 2 };
var w4 = { key: 3 };
var x2 = { key: 4 };
var C = {
  key: 2,
  class: "text-pretty text-sm leading-snug",
  style: { maxWidth: "16rem" }
};
var V3 = defineComponent({
  __name: "RequestTableTooltip",
  props: {
    item: {}
  },
  setup(I4) {
    return (e5, B5) => (openBlock(), createBlock(unref(T2), {
      align: "start",
      class: "w-full pr-px",
      delay: 0,
      side: "left",
      triggerClass: "before:absolute before:content-[''] before:bg-gradient-to-r before:from-transparent before:to-b-1 group-[.alert]:before:to-b-alert group-[.error]:before:to-b-danger before:min-h-[calc(100%-4px)] before:pointer-events-none before:right-[23px] before:top-0.5 before:w-3 absolute h-full right-0 -outline-offset-1"
    }, {
      trigger: withCtx(() => [
        createBaseVNode("div", g2, [
          createVNode(unref(c), {
            class: normalizeClass(
              unref(n)(e5.item).value ? "text-orange brightness-[.9]" : "text-c-3 group-hover/info:text-c-1"
            ),
            icon: unref(n)(e5.item).value ? "Alert" : "Info",
            size: "sm",
            thickness: "1.5"
          }, null, 8, ["class", "icon"])
        ])
      ]),
      content: withCtx(() => [
        createBaseVNode("div", b2, [
          unref(n)(e5.item).value ? (openBlock(), createElementBlock("div", h, toDisplayString(unref(n)(e5.item).value), 1)) : (openBlock(), createElementBlock("div", y2, [
            e5.item.type ? (openBlock(), createElementBlock("span", v2, toDisplayString(e5.item.type), 1)) : createCommentVNode("", true),
            e5.item.format ? (openBlock(), createElementBlock("span", k3, toDisplayString(e5.item.format), 1)) : createCommentVNode("", true),
            e5.item.minimum ? (openBlock(), createElementBlock("span", _4, "min: " + toDisplayString(e5.item.minimum), 1)) : createCommentVNode("", true),
            e5.item.maximum ? (openBlock(), createElementBlock("span", w4, "max: " + toDisplayString(e5.item.maximum), 1)) : createCommentVNode("", true),
            e5.item.default ? (openBlock(), createElementBlock("span", x2, "default: " + toDisplayString(e5.item.default), 1)) : createCommentVNode("", true)
          ])),
          e5.item.description && !unref(n)(e5.item).value ? (openBlock(), createElementBlock("span", C, toDisplayString(e5.item.description), 1)) : createCommentVNode("", true)
        ])
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestTableTooltip.vue.js
var r = s2(V3, [["__scopeId", "data-v-ac049e60"]]);

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestTable.vue2.js
var j = { class: "contents" };
var L = { class: "sr-only" };
var N4 = { class: "text-c-2 filemask flex w-full max-w-[100%] items-end justify-end overflow-hidden p-1" };
var O = ["onClick"];
var A = {
  key: 1,
  class: "p-0.5"
};
var ee = defineComponent({
  __name: "RequestTable",
  props: {
    items: {},
    hasCheckboxDisabled: { type: Boolean, default: false },
    showUploadButton: { type: Boolean, default: false },
    isGlobal: { type: Boolean, default: false },
    isReadOnly: { type: Boolean, default: false },
    environment: {},
    envVariables: {},
    workspace: {},
    invalidParams: {}
  },
  emits: ["updateRow", "toggleRow", "addRow", "deleteRow", "inputFocus", "inputBlur", "uploadFile", "removeFile"],
  setup(h2, { emit: R2 }) {
    const c4 = h2, n4 = R2, C4 = ["", "", "36px"], k7 = (l2, a3, b4) => {
      n4("updateRow", l2, a3, b4);
    }, F4 = (l2) => {
      n4("uploadFile", l2);
    }, U3 = (l2) => Array.isArray(l2.default) && l2.default.length === 1 ? l2.default[0] : l2.default;
    return (l2, a3) => {
      const b4 = resolveComponent("RouterLink");
      return openBlock(), createBlock(g, {
        class: "group/table flex-1",
        columns: C4
      }, {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(l2.items, (e5, i7) => (openBlock(), createBlock(i4, {
            key: i7,
            id: e5.key,
            class: normalizeClass({
              alert: unref(n)(e5).value,
              error: l2.invalidParams && l2.invalidParams.has(e5.key)
            })
          }, {
            default: withCtx(() => [
              createBaseVNode("label", j, [
                l2.isGlobal ? (openBlock(), createBlock(b4, {
                  key: 0,
                  class: "!border-r-1/2 border-t-1/2 text-c-2 flex items-center justify-center",
                  to: e5.route ?? {}
                }, {
                  default: withCtx(() => [
                    a3[5] || (a3[5] = createBaseVNode("span", { class: "sr-only" }, "Global", -1)),
                    createVNode(unref(T2), {
                      as: "div",
                      side: "top"
                    }, {
                      trigger: withCtx(() => [
                        createVNode(unref(c), {
                          class: "text-c-1",
                          icon: "Globe",
                          size: "xs"
                        })
                      ]),
                      content: withCtx(() => a3[4] || (a3[4] = [
                        createBaseVNode("div", { class: "w-content bg-b-1 text-xxs text-c-1 z-100 pointer-events-none z-10 grid max-w-[320px] gap-1.5 rounded p-2 leading-5 shadow-lg" }, [
                          createBaseVNode("div", { class: "text-c-1 flex items-center" }, [
                            createBaseVNode("span", { class: "text-pretty" }, " Global cookies are shared across the whole workspace. ")
                          ])
                        ], -1)
                      ])),
                      _: 1
                    })
                  ]),
                  _: 2
                }, 1032, ["to"])) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                  createBaseVNode("span", L, " Row " + toDisplayString(e5.enabled ? "Enabled" : "Disabled"), 1),
                  createVNode(y, {
                    class: "!border-r-1/2",
                    disabled: c4.hasCheckboxDisabled,
                    modelValue: e5.enabled,
                    "onUpdate:modelValue": (o3) => n4("toggleRow", i7, o3)
                  }, null, 8, ["disabled", "modelValue", "onUpdate:modelValue"])
                ], 64))
              ]),
              createVNode(f2, null, {
                default: withCtx(() => [
                  createVNode(_2, {
                    disableCloseBrackets: "",
                    disabled: c4.isReadOnly,
                    disableEnter: "",
                    disableTabIndent: "",
                    envVariables: l2.envVariables,
                    environment: l2.environment,
                    modelValue: e5.key,
                    placeholder: "Key",
                    required: !!e5.required,
                    workspace: l2.workspace,
                    onBlur: a3[0] || (a3[0] = (o3) => n4("inputBlur")),
                    onFocus: a3[1] || (a3[1] = (o3) => n4("inputFocus")),
                    onSelectVariable: (o3) => k7(i7, "key", o3),
                    "onUpdate:modelValue": (o3) => n4("updateRow", i7, "key", o3)
                  }, null, 8, ["disabled", "envVariables", "environment", "modelValue", "required", "workspace", "onSelectVariable", "onUpdate:modelValue"])
                ]),
                _: 2
              }, 1024),
              createVNode(f2, null, {
                default: withCtx(() => [
                  createVNode(_2, {
                    class: normalizeClass({
                      "pr-6": unref(m3)(e5)
                    }),
                    default: e5.default,
                    disableCloseBrackets: "",
                    disabled: c4.isReadOnly,
                    disableEnter: "",
                    disableTabIndent: "",
                    enum: e5.enum ?? [],
                    envVariables: l2.envVariables,
                    environment: l2.environment,
                    examples: e5.examples ?? [],
                    max: e5.maximum,
                    min: e5.minimum,
                    modelValue: e5.value,
                    nullable: !!e5.nullable,
                    placeholder: "Value",
                    type: e5.type,
                    workspace: l2.workspace,
                    onBlur: a3[2] || (a3[2] = (o3) => n4("inputBlur")),
                    onFocus: a3[3] || (a3[3] = (o3) => n4("inputFocus")),
                    onSelectVariable: (o3) => k7(i7, "value", o3),
                    "onUpdate:modelValue": (o3) => n4("updateRow", i7, "value", o3)
                  }, {
                    icon: withCtx(() => [
                      unref(m3)(e5) ? (openBlock(), createBlock(r, {
                        key: 0,
                        item: { ...e5, default: U3(e5) }
                      }, null, 8, ["item"])) : createCommentVNode("", true)
                    ]),
                    _: 2
                  }, 1032, ["class", "default", "disabled", "enum", "envVariables", "environment", "examples", "max", "min", "modelValue", "nullable", "type", "workspace", "onSelectVariable", "onUpdate:modelValue"])
                ]),
                _: 2
              }, 1024),
              l2.showUploadButton ? (openBlock(), createBlock(f2, {
                key: 0,
                class: "group/upload flex items-center justify-center whitespace-nowrap"
              }, {
                default: withCtx(() => {
                  var o3;
                  return [
                    e5.file ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                      createBaseVNode("div", N4, [
                        createBaseVNode("span", null, toDisplayString((o3 = e5.file) == null ? void 0 : o3.name), 1)
                      ]),
                      createBaseVNode("button", {
                        class: "bg-b-2 centered-x centered-y absolute hidden w-[calc(100%_-_8px)] rounded p-0.5 text-center text-xs font-medium group-hover/upload:block",
                        type: "button",
                        onClick: (S3) => n4("removeFile", i7)
                      }, " Delete ", 8, O)
                    ], 64)) : (openBlock(), createElementBlock("div", A, [
                      createVNode(unref($), {
                        class: "bg-b-2 hover:bg-b-3 text-c-2 h-fit border-0 py-px shadow-none",
                        size: "sm",
                        variant: "outlined",
                        onClick: (S3) => F4(i7)
                      }, {
                        default: withCtx(() => [
                          a3[6] || (a3[6] = createBaseVNode("span", null, "Upload File", -1)),
                          createVNode(unref(c), {
                            class: "ml-1",
                            icon: "UploadSimple",
                            size: "xs",
                            thickness: "2.5"
                          })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])
                    ]))
                  ];
                }),
                _: 2
              }, 1024)) : createCommentVNode("", true)
            ]),
            _: 2
          }, 1032, ["id", "class"]))), 128))
        ]),
        _: 1
      });
    };
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestTable.vue.js
var m4 = s2(ee, [["__scopeId", "data-v-cabdb6a1"]]);

// node_modules/@scalar/api-client/dist/hooks/useFileDialog.js
function n2({ multiple: u4, accept: c4, onChange: l2, onError: t2 } = {}) {
  const i7 = ref(null);
  let e5;
  typeof document < "u" && (e5 = document.createElement("input"), e5.type = "file", e5.onchange = (p3) => {
    const s5 = p3.target;
    i7.value = s5.files, l2 == null || l2(i7.value);
  }, e5.onerror = () => t2 == null ? void 0 : t2(), e5.multiple = u4, e5.accept = c4);
  const f6 = () => {
    if (!e5) return t2 == null ? void 0 : t2();
    e5.click();
  };
  return {
    files: readonly(i7),
    open: f6
  };
}

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestBody.vue2.js
var me = {
  key: 0,
  class: "border-t-1/2 text-c-3 flex min-h-10 w-full items-center justify-center p-2 text-sm"
};
var ce = {
  key: 1,
  class: "flex items-center justify-center overflow-hidden border-t p-1.5"
};
var fe = { class: "text-c-2 w-full max-w-full overflow-hidden whitespace-nowrap rounded border px-1.5 py-1 text-xs" };
var Ue = defineComponent({
  __name: "RequestBody",
  props: {
    example: {},
    operation: {},
    environment: {},
    envVariables: {},
    workspace: {},
    title: {}
  },
  setup(l2) {
    const { requestExampleMutators: d3 } = je(), Q4 = {
      json: "json",
      xml: "xml",
      yaml: "yaml",
      edn: "edn",
      other: "html"
    }, x3 = Object.entries({
      multipartForm: "Multipart Form",
      formUrlEncoded: "Form URL Encoded",
      binaryFile: "Binary File",
      json: "JSON",
      xml: "XML",
      yaml: "YAML",
      edn: "EDN",
      other: "Other",
      none: "None"
    }).map(([e5, a3]) => ({
      id: e5,
      label: a3
    })), R2 = computed(() => {
      var o3;
      const { activeBody: e5, formData: a3, raw: n4 } = l2.example.body;
      return e5 === "formData" ? (a3 == null ? void 0 : a3.encoding) === "urlencoded" ? "formUrlEncoded" : "multipartForm" : e5 === "binary" ? "binaryFile" : e5 === "raw" && (n4 != null && n4.encoding) ? n4.encoding === "html" ? "other" : n4.encoding : Object.keys(((o3 = l2.operation.requestBody) == null ? void 0 : o3.content) || {})[0] || "none";
    }), p3 = computed({
      get: () => x3.find(
        (e5) => e5.id === R2.value
      ) ?? x3[x3.length - 1] ?? x3[0],
      set: (e5) => {
        e5 != null && e5.id && L3(e5.id);
      }
    }), B5 = ref(null), Z3 = computed(() => {
      var a3;
      const e5 = (a3 = p3.value) == null ? void 0 : a3.id;
      return Q4[e5] ?? "plaintext";
    });
    function P6() {
      console.log("deleteRow");
    }
    const N6 = (e5, a3, n4) => {
      var o3, i7, s5, r5, f6, V4, J3;
      const t2 = y5.value;
      if (t2.length > e5) {
        const u4 = [...t2];
        u4[e5] = {
          ...u4[e5],
          value: ((o3 = u4[e5]) == null ? void 0 : o3.value) || "",
          key: ((i7 = u4[e5]) == null ? void 0 : i7.key) || "",
          enabled: ((s5 = u4[e5]) == null ? void 0 : s5.enabled) ?? false,
          [a3]: n4 || ""
        }, (((r5 = u4[e5]) == null ? void 0 : r5.key) !== "" || ((f6 = u4[e5]) == null ? void 0 : f6.value) !== "") && (u4[e5].enabled = true), ((V4 = u4[e5]) == null ? void 0 : V4.key) === "" && ((J3 = u4[e5]) == null ? void 0 : J3.value) === "" && u4.splice(e5, 1), d3.edit(
          l2.example.uid,
          "body.formData.value",
          u4
        );
      } else {
        const u4 = [requestExampleParametersSchema.parse({ [a3]: n4 })];
        d3.edit(l2.example.uid, "body.formData.value", u4), nextTick(() => {
          var $7;
          if (!B5.value) return;
          ($7 = B5.value.querySelectorAll("input")[a3 === "key" ? 0 : 1]) == null || $7.focus();
        });
      }
      e5 === t2.length - 1 && O2();
    }, y5 = computed(() => {
      var e5;
      return ((e5 = l2.example.body.formData) == null ? void 0 : e5.value) ?? [];
    }), T4 = () => {
      const e5 = y5.value[y5.value.length - 1];
      (!e5 || e5.key !== "" || e5.value !== "") && O2();
    }, O2 = () => {
      const e5 = requestExampleParametersSchema.parse({
        enabled: false
      }), a3 = [...y5.value, e5];
      l2.example.body.formData ? d3.edit(l2.example.uid, "body.formData.value", a3) : d3.edit(l2.example.uid, "body.formData", {
        value: a3,
        encoding: "form-data"
      });
    }, S3 = (e5, a3) => {
      const n4 = y5.value;
      if (n4.length > e5) {
        const t2 = [...n4];
        t2[e5] && (t2[e5].enabled = a3), d3.edit(
          l2.example.uid,
          "body.formData.value",
          t2
        );
      }
    }, q3 = (e5) => d3.edit(l2.example.uid, "body.raw.value", e5), W2 = (e5) => e5 === "multipartForm" ? {
      activeBody: "formData",
      encoding: "form-data",
      header: "multipart/form-data"
    } : e5 === "formUrlEncoded" ? {
      activeBody: "formData",
      encoding: "urlencoded",
      header: "application/x-www-form-urlencoded"
    } : e5 === "binaryFile" ? {
      activeBody: "binary",
      encoding: void 0,
      header: "application/octet-stream"
    } : e5 === "json" || e5.endsWith("+json") ? {
      activeBody: "raw",
      encoding: "json",
      header: e5.endsWith("+json") ? `application/${e5}` : "application/json"
    } : e5 === "xml" ? {
      activeBody: "raw",
      encoding: "xml",
      header: "application/xml"
    } : e5 === "yaml" ? {
      activeBody: "raw",
      encoding: "yaml",
      header: "application/yaml"
    } : e5 === "edn" ? {
      activeBody: "raw",
      encoding: "edn",
      header: "application/edn"
    } : e5 === "other" ? {
      activeBody: "raw",
      encoding: "html",
      header: "application/html"
    } : { activeBody: "raw", encoding: void 0, header: void 0 }, L3 = (e5) => {
      var s5, r5;
      const { activeBody: a3, encoding: n4, header: t2 } = W2(e5);
      if (d3.edit(l2.example.uid, "body.activeBody", a3), n4 && a3 === "raw")
        d3.edit(l2.example.uid, "body.raw", {
          encoding: n4,
          value: ((s5 = l2.example.body.raw) == null ? void 0 : s5.value) ?? ""
        });
      else if (n4 && a3 === "formData")
        d3.edit(l2.example.uid, "body.formData", {
          encoding: n4,
          value: ((r5 = l2.example.body.formData) == null ? void 0 : r5.value) ?? []
        });
      else if (!n4 && a3 !== "binary") {
        const { raw: f6, ...V4 } = l2.example.body;
        d3.edit(l2.example.uid, "body", V4);
      }
      const o3 = [...l2.example.parameters.headers], i7 = o3.findIndex(
        (f6) => f6.key.toLowerCase() === "content-type"
      );
      if (i7 >= 0)
        t2 && o3[i7] ? o3[i7].value = t2 : o3[i7] && (a3 !== "raw" || e5 === "none") && o3.splice(i7, 1);
      else if (t2) {
        const f6 = o3[o3.length - 1];
        f6 && f6.key === "" && f6.value === "" ? o3.splice(o3.length - 1, 0, {
          key: "Content-Type",
          value: t2,
          enabled: true
        }) : o3.push({
          key: "Content-Type",
          value: t2,
          enabled: true
        });
      }
      d3.edit(l2.example.uid, "parameters.headers", o3);
    }, M2 = async (e5) => {
      const { open: a3 } = n2({
        onChange: async (n4) => {
          var o3, i7;
          const t2 = n4 == null ? void 0 : n4[0];
          if (t2) {
            const r5 = [...y5.value];
            r5[e5] = {
              ...r5[e5],
              file: t2,
              value: ((o3 = r5[e5]) == null ? void 0 : o3.value) || t2.name,
              key: ((i7 = r5[e5]) == null ? void 0 : i7.key) || t2.name,
              enabled: true
            }, d3.edit(
              l2.example.uid,
              "body.formData.value",
              r5
            ), T4();
          }
        },
        multiple: false,
        accept: "*/*"
      });
      a3();
    }, I4 = () => d3.edit(l2.example.uid, "body.binary", void 0);
    function z5(e5) {
      const a3 = y5.value, n4 = [...a3], t2 = a3[e5], o3 = t2 == null ? void 0 : t2.file;
      if (a3.length > 1 && (!(t2 != null && t2.key) && !(t2 != null && t2.value) || o3 && (t2 == null ? void 0 : t2.key) === o3.name && (t2 == null ? void 0 : t2.value) === o3.name))
        n4.splice(e5, 1);
      else {
        const i7 = n4[e5];
        i7 && (i7.file = void 0);
      }
      d3.edit(l2.example.uid, "body.formData.value", n4);
    }
    function _8() {
      const { open: e5 } = n2({
        onChange: async (a3) => {
          const n4 = a3 == null ? void 0 : a3[0];
          n4 && d3.edit(l2.example.uid, "body.binary", n4);
        },
        multiple: false,
        accept: "*/*"
      });
      e5();
    }
    watch(
      p3,
      (e5) => {
        ["multipartForm", "formUrlEncoded"].includes((e5 == null ? void 0 : e5.id) || "") && T4();
      },
      { immediate: true }
    ), watch(
      () => l2.example.uid,
      () => {
        l2.operation.method && canMethodHaveBody(l2.operation.method) && L3(R2.value), ["multipartForm", "formUrlEncoded"].includes(
          R2.value
        ) && T4();
      },
      { immediate: true }
    );
    const w6 = computed(() => {
      var o3, i7, s5;
      const e5 = (o3 = p3.value) == null ? void 0 : o3.id, { header: a3 } = W2(e5), n4 = ((i7 = l2.operation.requestBody) == null ? void 0 : i7.content) || {}, t2 = a3 ? ((s5 = n4[a3]) == null ? void 0 : s5.examples) || {} : {};
      return Object.entries(t2).map(([r5, f6]) => ({
        id: r5,
        label: r5,
        value: f6
      }));
    }), U3 = computed({
      get: () => {
        var t2;
        const e5 = ((t2 = l2.example.body.raw) == null ? void 0 : t2.value) ?? "{}", a3 = JSON.parse(e5);
        return w6.value.find((o3) => {
          const i7 = o3.value;
          return JSON.stringify(i7.value) === JSON.stringify(a3);
        }) ?? w6.value[0];
      },
      set: (e5) => {
        if (e5 != null && e5.id) {
          const a3 = w6.value.find((n4) => n4.id === e5.id);
          if (a3) {
            const n4 = a3.value;
            q3(JSON.stringify(n4.value, null, 2));
          }
        }
      }
    });
    return (e5, a3) => (openBlock(), createBlock(P2, null, {
      title: withCtx(() => [
        createTextVNode(toDisplayString(e5.title), 1)
      ]),
      default: withCtx(() => [
        createVNode(g, { columns: [""] }, {
          default: withCtx(() => [
            createVNode(i4, null, {
              default: withCtx(() => [
                createVNode(x, { class: "relative col-span-full flex h-8 cursor-pointer items-center justify-between !p-0" }, {
                  default: withCtx(() => [
                    createVNode(unref(P), {
                      modelValue: p3.value,
                      "onUpdate:modelValue": a3[0] || (a3[0] = (n4) => p3.value = n4),
                      options: unref(x3),
                      teleport: ""
                    }, {
                      default: withCtx(() => [
                        createVNode(unref($), {
                          class: "text-c-2 hover:text-c-1 flex h-full w-fit gap-1.5 px-3 font-normal",
                          fullWidth: "",
                          variant: "ghost"
                        }, {
                          default: withCtx(() => {
                            var n4;
                            return [
                              createBaseVNode("span", null, toDisplayString((n4 = p3.value) == null ? void 0 : n4.label), 1),
                              createVNode(unref(c), {
                                icon: "ChevronDown",
                                size: "md"
                              })
                            ];
                          }),
                          _: 1
                        })
                      ]),
                      _: 1
                    }, 8, ["modelValue", "options"]),
                    w6.value.length > 0 ? (openBlock(), createBlock(unref(P), {
                      key: 0,
                      modelValue: U3.value,
                      "onUpdate:modelValue": a3[1] || (a3[1] = (n4) => U3.value = n4),
                      options: w6.value,
                      side: "left",
                      teleport: ""
                    }, {
                      default: withCtx(() => [
                        createVNode(unref($), {
                          class: "text-c-2 hover:text-c-1 flex h-full w-fit gap-1.5 px-2 font-normal",
                          fullWidth: "",
                          variant: "ghost"
                        }, {
                          default: withCtx(() => {
                            var n4;
                            return [
                              createBaseVNode("span", null, toDisplayString((n4 = U3.value) == null ? void 0 : n4.label), 1),
                              createVNode(unref(c), {
                                icon: "ChevronDown",
                                size: "md"
                              })
                            ];
                          }),
                          _: 1
                        })
                      ]),
                      _: 1
                    }, 8, ["modelValue", "options"])) : createCommentVNode("", true)
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }),
            createVNode(i4, null, {
              default: withCtx(() => {
                var n4, t2, o3, i7, s5, r5;
                return [
                  ((n4 = p3.value) == null ? void 0 : n4.id) === "none" ? (openBlock(), createElementBlock("div", me, a3[2] || (a3[2] = [
                    createBaseVNode("span", null, "No Body", -1)
                  ]))) : ((t2 = p3.value) == null ? void 0 : t2.id) === "binaryFile" ? (openBlock(), createElementBlock("div", ce, [
                    e5.example.body.binary ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                      createBaseVNode("span", fe, toDisplayString(e5.example.body.binary.name), 1),
                      createVNode(unref($), {
                        class: "bg-b-2 hover:bg-b-3 text-c-2 ml-1 border-0 shadow-none",
                        size: "sm",
                        variant: "outlined",
                        onClick: I4
                      }, {
                        default: withCtx(() => a3[3] || (a3[3] = [
                          createTextVNode(" Delete ")
                        ])),
                        _: 1
                      })
                    ], 64)) : (openBlock(), createBlock(unref($), {
                      key: 1,
                      class: "bg-b-2 hover:bg-b-3 text-c-2 border-0 shadow-none",
                      size: "sm",
                      variant: "outlined",
                      onClick: _8
                    }, {
                      default: withCtx(() => [
                        a3[4] || (a3[4] = createBaseVNode("span", null, "Upload File", -1)),
                        createVNode(unref(c), {
                          class: "ml-1",
                          icon: "UploadSimple",
                          size: "xs",
                          thickness: "2.5"
                        })
                      ]),
                      _: 1
                    }))
                  ])) : ((o3 = p3.value) == null ? void 0 : o3.id) == "multipartForm" ? (openBlock(), createBlock(m4, {
                    key: 2,
                    ref_key: "tableWrapperRef",
                    ref: B5,
                    class: "!m-0 rounded-t-none border-b-0 border-l-0 border-r-0 border-t-0 shadow-none",
                    columns: ["32px", "", "", "104px"],
                    envVariables: e5.envVariables,
                    environment: e5.environment,
                    items: y5.value,
                    showUploadButton: "",
                    workspace: e5.workspace,
                    onDeleteRow: P6,
                    onRemoveFile: z5,
                    onToggleRow: S3,
                    onUpdateRow: N6,
                    onUploadFile: M2
                  }, null, 8, ["envVariables", "environment", "items", "workspace"])) : ((i7 = p3.value) == null ? void 0 : i7.id) == "formUrlEncoded" ? (openBlock(), createBlock(m4, {
                    key: 3,
                    ref_key: "tableWrapperRef",
                    ref: B5,
                    class: "!m-0 rounded-t-none border-b-0 border-l-0 border-r-0 border-t-0 shadow-none",
                    columns: ["32px", "", "", "104px"],
                    envVariables: e5.envVariables,
                    environment: e5.environment,
                    items: y5.value,
                    showUploadButton: "",
                    workspace: e5.workspace,
                    onDeleteRow: P6,
                    onRemoveFile: z5,
                    onToggleRow: S3,
                    onUpdateRow: N6,
                    onUploadFile: M2
                  }, null, 8, ["envVariables", "environment", "items", "workspace"])) : (openBlock(), createBlock(_2, {
                    key: 4,
                    class: "border-t-1/2 px-1",
                    content: "",
                    envVariables: e5.envVariables,
                    environment: e5.environment,
                    language: Z3.value,
                    lineNumbers: "",
                    lint: "",
                    modelValue: ((r5 = (s5 = e5.example.body) == null ? void 0 : s5.raw) == null ? void 0 : r5.value) ?? "",
                    workspace: e5.workspace,
                    "onUpdate:modelValue": q3
                  }, null, 8, ["envVariables", "environment", "language", "modelValue", "workspace"]))
                ];
              }),
              _: 1
            }),
            createVNode(i4)
          ]),
          _: 1
        })
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestBody.vue.js
var e2 = s2(Ue, [["__scopeId", "data-v-6f3ffafd"]]);

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestParams.vue.js
var G = { class: "text-c-2 request-meta-buttons flex whitespace-nowrap opacity-0 group-hover/params:opacity-100 has-[:focus-visible]:opacity-100" };
var U = { class: "sr-only" };
var Z = defineComponent({
  __name: "RequestParams",
  props: {
    example: {},
    environment: {},
    envVariables: {},
    workspace: {},
    title: {},
    paramKey: {},
    readOnlyEntries: { default: () => [] },
    invalidParams: {}
  },
  setup(t2) {
    const { requestExampleMutators: r5 } = je(), s5 = computed(() => t2.example.parameters[t2.paramKey] ?? []);
    onMounted(() => v5());
    const o3 = () => {
      const e5 = requestExampleParametersSchema.parse({ enabled: false }), a3 = [...s5.value, e5];
      r5.edit(t2.example.uid, `parameters.${t2.paramKey}`, a3);
    }, u4 = ref(null), C4 = (e5, a3, g4) => {
      const c4 = s5.value;
      if (c4.length > e5) {
        const n4 = [...c4];
        if (!n4[e5]) return;
        n4[e5] = { ...n4[e5], [a3]: g4 }, (n4[e5].key !== "" || n4[e5].value !== "") && (n4[e5].enabled = true), n4[e5].key === "" && n4[e5].value === "" && n4.splice(e5, 1), r5.edit(
          t2.example.uid,
          `parameters.${t2.paramKey}`,
          n4
        );
      } else {
        const n4 = [requestExampleParametersSchema.parse({ [a3]: g4 })];
        r5.edit(t2.example.uid, `parameters.${t2.paramKey}`, n4), nextTick(() => {
          var y5;
          if (!u4.value) return;
          (y5 = u4.value.querySelectorAll("input")[a3 === "key" ? 0 : 1]) == null || y5.focus();
        });
      }
      e5 === c4.length - 1 && o3();
    }, q3 = (e5, a3) => r5.edit(
      t2.example.uid,
      `parameters.${t2.paramKey}.${e5}.enabled`,
      a3
    ), E6 = () => {
      const e5 = s5.value.filter((a3) => a3.required);
      r5.edit(
        t2.example.uid,
        `parameters.${t2.paramKey}`,
        e5
      ), nextTick(() => o3());
    };
    function v5() {
      if (s5.value.length === 0)
        o3();
      else if (s5.value.length >= 1) {
        const e5 = s5.value[s5.value.length - 1];
        e5 && e5.key !== "" && e5.value !== "" && o3();
      }
    }
    const K2 = computed(
      () => s5.value.filter((e5) => e5.key || e5.value).length
    ), $7 = computed(() => s5.value.length > 1);
    watch(
      () => t2.example,
      (e5, a3) => {
        e5 !== a3 && v5();
      },
      { immediate: true }
    );
    const f6 = computed(() => (t2.readOnlyEntries ?? []).length > 0);
    return (e5, a3) => (openBlock(), createBlock(P2, {
      class: "group/params",
      itemCount: K2.value
    }, {
      title: withCtx(() => [
        createTextVNode(toDisplayString(e5.title), 1)
      ]),
      actions: withCtx(() => [
        createBaseVNode("div", G, [
          $7.value ? (openBlock(), createBlock(unref(T2), {
            key: 0,
            side: "right",
            sideOffset: 12
          }, {
            trigger: withCtx(() => [
              createVNode(unref($), {
                class: "px-1 transition-none",
                size: "sm",
                variant: "ghost",
                onClick: withModifiers(E6, ["stop"])
              }, {
                default: withCtx(() => [
                  a3[0] || (a3[0] = createTextVNode(" Clear ")),
                  createBaseVNode("span", U, "All " + toDisplayString(e5.title), 1)
                ]),
                _: 1
              })
            ]),
            content: withCtx(() => a3[1] || (a3[1] = [
              createBaseVNode("div", { class: "w-content bg-b-1 text-xxs text-c-1 pointer-events-none z-10 grid min-w-48 gap-1.5 rounded p-2 leading-5 shadow-lg" }, [
                createBaseVNode("div", { class: "text-c-2 flex items-center" }, [
                  createBaseVNode("span", null, "Clear optional parameters")
                ])
              ], -1)
            ])),
            _: 1
          })) : createCommentVNode("", true)
        ])
      ]),
      default: withCtx(() => [
        createBaseVNode("div", {
          ref_key: "tableWrapperRef",
          ref: u4
        }, [
          f6.value ? (openBlock(), createBlock(m4, {
            key: 0,
            class: normalizeClass(["flex-1", {
              "bg-mix-transparent bg-mix-amount-95 bg-c-3": f6.value
            }]),
            columns: ["32px", "", ""],
            envVariables: e5.envVariables,
            environment: e5.environment,
            isGlobal: "",
            isReadOnly: "",
            items: e5.readOnlyEntries,
            workspace: e5.workspace,
            invalidParams: e5.invalidParams
          }, null, 8, ["class", "envVariables", "environment", "items", "workspace", "invalidParams"])) : createCommentVNode("", true),
          createVNode(m4, {
            class: "flex-1",
            columns: ["32px", "", ""],
            envVariables: e5.envVariables,
            environment: e5.environment,
            items: s5.value,
            workspace: e5.workspace,
            invalidParams: e5.invalidParams,
            onToggleRow: q3,
            onUpdateRow: C4
          }, null, 8, ["envVariables", "environment", "items", "workspace", "invalidParams"])
        ], 512)
      ]),
      _: 1
    }, 8, ["itemCount"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestPathParams.vue.js
var A2 = defineComponent({
  __name: "RequestPathParams",
  props: {
    example: {},
    operation: {},
    paramKey: {},
    title: {},
    environment: {},
    envVariables: {},
    workspace: {},
    invalidParams: {}
  },
  setup(a3) {
    const { requestMutators: p3, requestExampleMutators: u4 } = je(), s5 = computed(
      () => a3.example.parameters[a3.paramKey].map((e5) => ({
        ...e5,
        enum: e5.enum
      }))
    ), v5 = (e5, r5, n4) => {
      var m7, t2;
      const i7 = a3.example.parameters[a3.paramKey], o3 = (m7 = i7[e5]) == null ? void 0 : m7.key;
      if (o3) {
        if (r5 === "key") {
          if ((t2 = i7[e5]) != null && t2.required)
            return;
          if (n4) {
            const l2 = encodeURIComponent(o3), c4 = encodeURIComponent(n4), g4 = new RegExp(`(?<=/):${l2}(?=[/?#]|$)`, "g"), k7 = a3.operation.path.replace(g4, `:${c4}`);
            p3.edit(a3.operation.uid, "path", k7);
          } else {
            i7.splice(e5, 1);
            const l2 = new RegExp(`/:${encodeURIComponent(o3)}(?=[/?#]|$)`, "g"), c4 = a3.operation.path.replace(l2, "");
            p3.edit(a3.operation.uid, "path", c4);
          }
        }
        u4.edit(
          a3.example.uid,
          `parameters.${a3.paramKey}.${e5}.${r5}`,
          n4
        );
      }
    }, y5 = (e5) => {
      var m7;
      const r5 = ((m7 = e5.match(REGEX.PATH)) == null ? void 0 : m7.map((t2) => t2.slice(1, -1))) || [], n4 = a3.example.parameters[a3.paramKey], i7 = new Map(n4.map((t2) => [t2.key, t2])), o3 = r5.map(
        (t2) => i7.get(t2) || { key: t2, value: "", enabled: true }
      );
      n4.forEach((t2) => {
        !r5.includes(t2.key) && (t2.value || t2.required) && o3.push(t2);
      }), n4.splice(0, n4.length, ...o3), u4.edit(a3.example.uid, `parameters.${a3.paramKey}`, n4);
    }, P6 = (e5) => {
      e5 && y5(e5);
    };
    return watch(
      () => a3.operation.path,
      (e5) => {
        e5 && P6(e5);
      }
    ), (e5, r5) => (openBlock(), createBlock(P2, {
      itemCount: s5.value.length
    }, {
      title: withCtx(() => [
        createTextVNode(toDisplayString(e5.title), 1)
      ]),
      default: withCtx(() => [
        s5.value.length ? (openBlock(), createBlock(m4, {
          key: 0,
          class: "flex-1",
          columns: ["32px", "", ""],
          envVariables: e5.envVariables,
          environment: e5.environment,
          items: s5.value,
          workspace: e5.workspace,
          invalidParams: e5.invalidParams,
          onUpdateRow: v5
        }, null, 8, ["envVariables", "environment", "items", "workspace", "invalidParams"])) : createCommentVNode("", true)
      ]),
      _: 1
    }, 8, ["itemCount"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/DeleteRequestAuthModal.vue.js
var k4 = { class: "text-c-2 mb-4 text-sm leading-normal" };
var C2 = { class: "flex justify-between gap-2" };
var B2 = defineComponent({
  __name: "DeleteRequestAuthModal",
  props: {
    state: {},
    scheme: {}
  },
  emits: ["close", "delete"],
  setup(p3, { emit: f6 }) {
    const l2 = p3, c4 = f6, { securitySchemeMutators: h2 } = je(), x3 = () => {
      var e5;
      (e5 = l2.scheme) != null && e5.id && h2.delete(l2.scheme.id), c4("delete");
    };
    return (e5, t2) => (openBlock(), createBlock(unref(f), {
      size: "xxs",
      state: e5.state,
      title: "Delete Security Scheme"
    }, {
      default: withCtx(() => {
        var a3;
        return [
          createBaseVNode("p", k4, " This cannot be undone. You’re about to delete the " + toDisplayString((a3 = e5.scheme) == null ? void 0 : a3.label) + " security scheme from the collection. ", 1),
          createBaseVNode("div", C2, [
            createVNode(unref($), {
              class: "flex h-8 cursor-pointer items-center gap-1.5 px-3 shadow-none focus:outline-none",
              type: "button",
              variant: "outlined",
              onClick: t2[0] || (t2[0] = (o3) => c4("close"))
            }, {
              default: withCtx(() => t2[1] || (t2[1] = [
                createTextVNode(" Cancel ")
              ])),
              _: 1
            }),
            createVNode(unref($), {
              class: "flex h-8 cursor-pointer items-center gap-1.5 px-3 shadow-none focus:outline-none",
              type: "submit",
              onClick: x3
            }, {
              default: withCtx(() => {
                var o3;
                return [
                  createTextVNode(" Delete " + toDisplayString((o3 = e5.scheme) == null ? void 0 : o3.label), 1)
                ];
              }),
              _: 1
            })
          ])
        ];
      }),
      _: 1
    }, 8, ["state"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/OAuthScopesInput.vue.js
var z = { class: "flex h-fit w-full" };
var E3 = { class: "flex-1" };
var F2 = {
  class: "grid auto-rows-auto",
  style: { gridTemplateColumns: "1fr auto" }
};
var I2 = { key: 0 };
var R = { class: "font-code text-xs" };
var H = defineComponent({
  __name: "OAuthScopesInput",
  props: {
    flow: {},
    updateScheme: { type: Function }
  },
  setup(o3) {
    const g4 = computed(
      () => {
        var e5;
        return Object.entries(((e5 = o3.flow) == null ? void 0 : e5.scopes) ?? {}).map(([l2, s5]) => ({
          id: l2,
          label: l2,
          description: s5
        }));
      }
    ), u4 = computed(() => {
      var e5;
      return ((e5 = o3.flow) == null ? void 0 : e5.selectedScopes) || [];
    });
    function _8(e5, l2) {
      l2 ? o3.updateScheme(`flows.${o3.flow.type}.selectedScopes`, [
        ...u4.value,
        e5
      ]) : o3.updateScheme(
        `flows.${o3.flow.type}.selectedScopes`,
        u4.value.filter((s5) => s5 !== e5)
      );
    }
    return (e5, l2) => (openBlock(), createBlock(unref(f2), { class: "h-auto !max-h-[initial] min-h-8 items-center" }, {
      default: withCtx(() => [
        createBaseVNode("div", z, [
          l2[0] || (l2[0] = createBaseVNode("div", { class: "text-c-1 h-full items-center" }, null, -1)),
          createVNode(unref(N), {
            as: "div",
            class: "bl flex w-full flex-col"
          }, {
            default: withCtx(() => {
              var s5, h2;
              return [
                createVNode(unref(Q), {
                  class: normalizeClass([
                    "group/scopes-accordion hover:text-c-1 flex h-auto min-h-8 cursor-pointer items-center gap-1.5 pl-3 pr-2 text-left",
                    (((h2 = (s5 = e5.flow) == null ? void 0 : s5.selectedScopes) == null ? void 0 : h2.length) || 0) > 0 ? "text-c-1" : "text-c-3"
                  ])
                }, {
                  default: withCtx(({ open: c4 }) => {
                    var f6, i7, p3;
                    return [
                      createBaseVNode("div", E3, " Scopes Selected " + toDisplayString(((i7 = (f6 = e5.flow) == null ? void 0 : f6.selectedScopes) == null ? void 0 : i7.length) || 0) + " / " + toDisplayString(Object.keys(((p3 = e5.flow) == null ? void 0 : p3.scopes) ?? {}).length || 0), 1),
                      createVNode(unref(c), {
                        class: "text-c-3 group-hover/scopes-accordion:text-c-2",
                        icon: c4 ? "ChevronDown" : "ChevronRight",
                        size: "sm"
                      }, null, 8, ["icon"])
                    ];
                  }),
                  _: 1
                }, 8, ["class"]),
                createVNode(unref(V), { as: "template" }, {
                  default: withCtx(() => [
                    createBaseVNode("table", F2, [
                      (openBlock(true), createElementBlock(Fragment, null, renderList(g4.value, ({ id: c4, label: f6, description: i7 }) => (openBlock(), createBlock(unref(i4), {
                        key: c4,
                        class: "text-c-2",
                        onClick: (p3) => _8(c4, !u4.value.includes(c4))
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(f2), { class: "hover:text-c-1 !max-h-[initial] w-full cursor-pointer px-3 py-1.5" }, {
                            default: withCtx(() => [
                              createBaseVNode("span", null, [
                                i7 ? (openBlock(), createElementBlock("span", I2, [
                                  createBaseVNode("span", R, toDisplayString(f6), 1),
                                  createTextVNode(" – " + toDisplayString(i7), 1)
                                ])) : createCommentVNode("", true)
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          createVNode(unref(y), {
                            modelValue: u4.value.includes(c4),
                            "onUpdate:modelValue": () => {
                            }
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 2
                      }, 1032, ["onClick"]))), 128))
                    ])
                  ]),
                  _: 1
                })
              ];
            }),
            _: 1
          })
        ])
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuthDataTableInput.vue.js
var v3 = ["for"];
var k5 = defineComponent({
  __name: "RequestAuthDataTableInput",
  props: {
    type: {},
    containerClass: {},
    required: { type: Boolean, default: false },
    modelValue: {},
    readOnly: { type: Boolean, default: false },
    environment: {},
    envVariables: {},
    workspace: {}
  },
  emits: ["update:modelValue", "inputFocus", "inputBlur", "selectVariable"],
  setup(i7, { emit: d3 }) {
    const e5 = i7, a3 = d3, l2 = useId();
    return (o3, n4) => (openBlock(), createBlock(m2, mergeProps({
      id: unref(l2),
      canAddCustomEnumValue: !e5.readOnly,
      containerClass: e5.containerClass
    }, o3.$attrs, {
      envVariables: e5.envVariables,
      environment: e5.environment,
      modelValue: e5.modelValue,
      readOnly: e5.readOnly,
      required: e5.required,
      type: e5.type,
      workspace: e5.workspace,
      onInputBlur: n4[0] || (n4[0] = (t2) => a3("inputBlur")),
      onInputFocus: n4[1] || (n4[1] = (t2) => a3("inputFocus")),
      onSelectVariable: n4[2] || (n4[2] = (t2) => a3("selectVariable", t2)),
      "onUpdate:modelValue": n4[3] || (n4[3] = (t2) => a3("update:modelValue", t2))
    }), {
      default: withCtx(() => [
        createBaseVNode("label", { for: unref(l2) }, [
          renderSlot(o3.$slots, "default")
        ], 8, v3)
      ]),
      icon: withCtx(() => [
        renderSlot(o3.$slots, "icon")
      ]),
      _: 3
    }, 16, ["id", "canAddCustomEnumValue", "containerClass", "envVariables", "environment", "modelValue", "readOnly", "required", "type", "workspace"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/libs/oauth2.js
var y3 = () => {
  const e5 = new Uint8Array(32);
  return crypto.getRandomValues(e5), btoa(String.fromCharCode(...e5)).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
};
var P3 = async (e5, i7) => {
  if (i7 === "plain") return e5;
  const a3 = new TextEncoder().encode(e5), s5 = await crypto.subtle.digest("SHA-256", a3);
  return btoa(String.fromCharCode(...new Uint8Array(s5))).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
};
var C3 = async (e5, i7, o3) => {
  try {
    if (!e5) return [new Error("Flow not found"), null];
    const a3 = e5.selectedScopes.join(" ");
    if (e5.type === "clientCredentials" || e5.type === "password")
      return _5(e5, a3, {
        proxyUrl: o3
      });
    const s5 = (Math.random() + 1).toString(36).substring(2, 10), t2 = new URL(e5.authorizationUrl);
    let l2 = null;
    if (e5.type === "implicit")
      t2.searchParams.set("response_type", "token");
    else if (e5.type === "authorizationCode" && (t2.searchParams.set("response_type", "code"), e5["x-usePkce"] !== "no")) {
      const r5 = y3(), c4 = await P3(r5, e5["x-usePkce"]);
      l2 = {
        codeVerifier: r5,
        codeChallenge: c4,
        codeChallengeMethod: e5["x-usePkce"] === "SHA-256" ? "S256" : "plain"
      }, t2.searchParams.set("code_challenge", c4), t2.searchParams.set("code_challenge_method", l2.codeChallengeMethod);
    }
    if (e5["x-scalar-redirect-uri"].startsWith("/")) {
      const r5 = i7.url || window.location.origin + window.location.pathname, c4 = new URL(e5["x-scalar-redirect-uri"], r5).toString();
      t2.searchParams.set("redirect_uri", c4);
    } else
      t2.searchParams.set("redirect_uri", e5["x-scalar-redirect-uri"]);
    t2.searchParams.set("client_id", e5["x-scalar-client-id"]), t2.searchParams.set("state", s5), a3 && t2.searchParams.set("scope", a3);
    const n4 = window.open(t2, "openAuth2Window", "left=100,top=100,width=800,height=600");
    return n4 ? new Promise((r5) => {
      const c4 = setInterval(() => {
        var m7;
        let u4 = null, h2 = null;
        try {
          const d3 = new URL(n4.location.href).searchParams;
          u4 = d3.get("access_token"), h2 = d3.get("code");
          const g4 = new URLSearchParams(n4.location.href.split("#")[1]);
          u4 || (u4 = g4.get("access_token")), h2 || (h2 = g4.get("code"));
        } catch {
        }
        if (n4.closed || u4 || h2)
          if (clearInterval(c4), n4.close(), u4) {
            const d3 = (m7 = n4.location.href.match(/state=([^&]*)/)) == null ? void 0 : m7[1];
            r5(d3 === s5 ? [null, u4] : [new Error("State mismatch"), null]);
          } else h2 ? new URL(n4.location.href).searchParams.get("state") === s5 ? _5(e5, a3, {
            code: h2,
            pkce: l2,
            proxyUrl: o3
          }).then(r5) : r5([new Error("State mismatch"), null]) : (clearInterval(c4), r5([new Error("Window was closed without granting authorization"), null]));
      }, 200);
    }) : [new Error("Failed to open auth window"), null];
  } catch {
    return [new Error("Failed to authorize oauth2 flow"), null];
  }
};
var _5 = async (e5, i7, {
  code: o3,
  pkce: a3,
  proxyUrl: s5
} = {}) => {
  if (!e5) return [new Error("OAuth2 flow was not defined"), null];
  const t2 = new URLSearchParams();
  t2.set("client_id", e5["x-scalar-client-id"]), i7 && (e5.type === "clientCredentials" || e5.type === "password") && t2.set("scope", i7), e5.clientSecret && t2.set("client_secret", e5.clientSecret), "x-scalar-redirect-uri" in e5 && e5["x-scalar-redirect-uri"] && t2.set("redirect_uri", e5["x-scalar-redirect-uri"]), o3 ? (t2.set("code", o3), t2.set("grant_type", "authorization_code"), a3 && t2.set("code_verifier", a3.codeVerifier)) : e5.type === "password" ? (t2.set("grant_type", "password"), t2.set("username", e5.username), t2.set("password", e5.password)) : t2.set("grant_type", "client_credentials");
  try {
    const l2 = {
      "Content-Type": "application/x-www-form-urlencoded"
    };
    l2.Authorization = `Basic ${btoa(`${e5["x-scalar-client-id"]}:${e5.clientSecret}`)}`;
    const p3 = shouldUseProxy(s5, e5.tokenUrl) ? `${s5}?${new URLSearchParams([["scalar_url", e5.tokenUrl]]).toString()}` : e5.tokenUrl, n4 = await fetch(p3, {
      method: "POST",
      headers: l2,
      body: t2
    }), { access_token: r5 } = await n4.json();
    return [null, r5];
  } catch {
    return [new Error("Failed to get an access token. Please check your credentials."), null];
  }
};

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/OAuth2.vue.js
var I3 = { class: "flex h-8 items-center justify-self-end" };
var A3 = { class: "border-t-1/2 flex h-8 w-full items-center justify-end" };
var W = defineComponent({
  __name: "OAuth2",
  props: {
    collection: {},
    environment: {},
    envVariables: {},
    flow: {},
    scheme: {},
    server: {},
    workspace: {}
  },
  setup(f6) {
    const y5 = S(), { toast: g4 } = i3(), { securitySchemeMutators: S3 } = je(), n4 = (l2, e5) => S3.edit(f6.scheme.uid, l2, e5), T4 = async () => {
      var t2, v5;
      if (y5.isLoading || !((t2 = f6.collection) != null && t2.uid)) return;
      if (y5.startLoading(), !f6.server) {
        g4("No server selected", "error");
        return;
      }
      const [l2, e5] = await C3(
        f6.flow,
        f6.server,
        (v5 = f6.workspace) == null ? void 0 : v5.proxyUrl
      ).finally(() => y5.stopLoading());
      e5 ? n4(`flows.${f6.flow.type}.token`, e5) : (console.error(l2), g4((l2 == null ? void 0 : l2.message) ?? "Failed to authorize", "error"));
    }, i7 = {
      environment: f6.environment,
      envVariables: f6.envVariables,
      workspace: f6.workspace
    };
    return (l2, e5) => (openBlock(), createElementBlock(Fragment, null, [
      l2.flow.token ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        createVNode(unref(i4), null, {
          default: withCtx(() => [
            createVNode(k5, mergeProps(i7, {
              class: "border-r-transparent",
              modelValue: l2.flow.token,
              placeholder: "QUxMIFlPVVIgQkFTRSBBUkUgQkVMT05HIFRPIFVT",
              type: "password",
              "onUpdate:modelValue": e5[0] || (e5[0] = (t2) => n4(`flows.${l2.flow.type}.token`, t2))
            }), {
              default: withCtx(() => e5[10] || (e5[10] = [
                createTextVNode(" Access Token ")
              ])),
              _: 1
            }, 16, ["modelValue"])
          ]),
          _: 1
        }),
        createVNode(unref(i4), { class: "min-w-full" }, {
          default: withCtx(() => [
            createBaseVNode("div", I3, [
              createVNode(unref($), {
                class: "mr-1 p-0 px-2 py-0.5",
                loading: unref(y5),
                size: "sm",
                variant: "outlined",
                onClick: e5[1] || (e5[1] = (t2) => n4(`flows.${l2.flow.type}.token`, ""))
              }, {
                default: withCtx(() => e5[11] || (e5[11] = [
                  createTextVNode(" Clear ")
                ])),
                _: 1
              }, 8, ["loading"])
            ])
          ]),
          _: 1
        })
      ], 64)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        createVNode(unref(i4), null, {
          default: withCtx(() => [
            "authorizationUrl" in l2.flow ? (openBlock(), createBlock(k5, mergeProps({ key: 0 }, i7, {
              modelValue: l2.flow.authorizationUrl,
              placeholder: "https://galaxy.scalar.com/authorize",
              "onUpdate:modelValue": e5[2] || (e5[2] = (t2) => n4(`flows.${l2.flow.type}.authorizationUrl`, t2))
            }), {
              default: withCtx(() => e5[12] || (e5[12] = [
                createTextVNode(" Auth URL ")
              ])),
              _: 1
            }, 16, ["modelValue"])) : createCommentVNode("", true),
            "tokenUrl" in l2.flow ? (openBlock(), createBlock(k5, mergeProps({ key: 1 }, i7, {
              modelValue: l2.flow.tokenUrl,
              placeholder: "https://galaxy.scalar.com/token",
              "onUpdate:modelValue": e5[3] || (e5[3] = (t2) => n4(`flows.${l2.flow.type}.tokenUrl`, t2))
            }), {
              default: withCtx(() => e5[13] || (e5[13] = [
                createTextVNode(" Token URL ")
              ])),
              _: 1
            }, 16, ["modelValue"])) : createCommentVNode("", true)
          ]),
          _: 1
        }),
        "x-scalar-redirect-uri" in l2.flow ? (openBlock(), createBlock(unref(i4), { key: 0 }, {
          default: withCtx(() => [
            createVNode(k5, mergeProps(i7, {
              modelValue: l2.flow["x-scalar-redirect-uri"],
              placeholder: "https://galaxy.scalar.com/callback",
              "onUpdate:modelValue": e5[4] || (e5[4] = (t2) => n4(`flows.${l2.flow.type}.x-scalar-redirect-uri`, t2))
            }), {
              default: withCtx(() => e5[14] || (e5[14] = [
                createTextVNode(" Redirect URL ")
              ])),
              _: 1
            }, 16, ["modelValue"])
          ]),
          _: 1
        })) : createCommentVNode("", true),
        l2.flow.type === "password" ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
          createVNode(unref(i4), null, {
            default: withCtx(() => [
              createVNode(k5, mergeProps(i7, {
                class: "text-c-2",
                modelValue: l2.flow.username,
                placeholder: "janedoe",
                "onUpdate:modelValue": e5[5] || (e5[5] = (t2) => n4(`flows.${l2.flow.type}.username`, t2))
              }), {
                default: withCtx(() => e5[15] || (e5[15] = [
                  createTextVNode(" Username ")
                ])),
                _: 1
              }, 16, ["modelValue"])
            ]),
            _: 1
          }),
          createVNode(unref(i4), null, {
            default: withCtx(() => [
              createVNode(k5, mergeProps(i7, {
                modelValue: l2.flow.password,
                placeholder: "********",
                type: "password",
                "onUpdate:modelValue": e5[6] || (e5[6] = (t2) => n4(`flows.${l2.flow.type}.password`, t2))
              }), {
                default: withCtx(() => e5[16] || (e5[16] = [
                  createTextVNode(" Password ")
                ])),
                _: 1
              }, 16, ["modelValue"])
            ]),
            _: 1
          })
        ], 64)) : createCommentVNode("", true),
        createVNode(unref(i4), null, {
          default: withCtx(() => [
            createVNode(k5, mergeProps(i7, {
              modelValue: l2.flow["x-scalar-client-id"],
              placeholder: "12345",
              "onUpdate:modelValue": e5[7] || (e5[7] = (t2) => n4(`flows.${l2.flow.type}.x-scalar-client-id`, t2))
            }), {
              default: withCtx(() => e5[17] || (e5[17] = [
                createTextVNode(" Client ID ")
              ])),
              _: 1
            }, 16, ["modelValue"])
          ]),
          _: 1
        }),
        "clientSecret" in l2.flow ? (openBlock(), createBlock(unref(i4), { key: 2 }, {
          default: withCtx(() => [
            createVNode(k5, mergeProps(i7, {
              modelValue: l2.flow.clientSecret,
              placeholder: "XYZ123",
              type: "password",
              "onUpdate:modelValue": e5[8] || (e5[8] = (t2) => n4(`flows.${l2.flow.type}.clientSecret`, t2))
            }), {
              default: withCtx(() => e5[18] || (e5[18] = [
                createTextVNode(" Client Secret ")
              ])),
              _: 1
            }, 16, ["modelValue"])
          ]),
          _: 1
        })) : createCommentVNode("", true),
        "x-usePkce" in l2.flow ? (openBlock(), createBlock(unref(i4), { key: 3 }, {
          default: withCtx(() => [
            createVNode(k5, mergeProps(i7, {
              enum: unref(pkceOptions),
              modelValue: l2.flow["x-usePkce"],
              readOnly: "",
              "onUpdate:modelValue": e5[9] || (e5[9] = (t2) => n4(
                `flows.${l2.flow.type}.x-usePkce`,
                t2
              ))
            }), {
              default: withCtx(() => e5[19] || (e5[19] = [
                createTextVNode(" Use PKCE ")
              ])),
              _: 1
            }, 16, ["enum", "modelValue"])
          ]),
          _: 1
        })) : createCommentVNode("", true),
        Object.keys(l2.flow.scopes ?? {}).length ? (openBlock(), createBlock(unref(i4), { key: 4 }, {
          default: withCtx(() => [
            createVNode(H, {
              flow: l2.flow,
              updateScheme: n4
            }, null, 8, ["flow"])
          ]),
          _: 1
        })) : createCommentVNode("", true)
      ], 64)),
      l2.flow.token ? createCommentVNode("", true) : (openBlock(), createBlock(unref(i4), {
        key: 2,
        class: "min-w-full"
      }, {
        default: withCtx(() => [
          createBaseVNode("div", A3, [
            createVNode(unref($), {
              class: "mr-1 p-0 px-2 py-0.5",
              loading: unref(y5),
              size: "sm",
              variant: "outlined",
              onClick: T4
            }, {
              default: withCtx(() => e5[20] || (e5[20] = [
                createTextVNode(" Authorize ")
              ])),
              _: 1
            }, 8, ["loading"])
          ])
        ]),
        _: 1
      }))
    ], 64));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuthTab.vue2.js
var z2 = {
  key: 0,
  class: "flex min-h-8 border-t text-sm"
};
var M = { class: "flex h-8 max-w-full gap-2.5 overflow-x-auto px-3" };
var Q2 = ["onClick"];
var D2 = { class: "relative z-10" };
var E4 = {
  key: 4,
  class: "text-c-3 bg-b-1 flex min-h-16 items-center justify-center border-t px-4 text-sm"
};
var X = defineComponent({
  __name: "RequestAuthTab",
  props: {
    collection: {},
    environment: {},
    envVariables: {},
    layout: {},
    securitySchemeUids: {},
    server: {},
    workspace: {}
  },
  setup(w6) {
    const { securitySchemes: S3, securitySchemeMutators: B5 } = je(), g4 = computed(
      () => w6.securitySchemeUids.map((t2) => ({
        scheme: S3[t2]
      }))
    ), d3 = ref(""), I4 = (t2) => {
      if (t2.type === "apiKey")
        return `${capitalize(t2.nameKey)}: ${t2.in}`;
      if (t2.type === "oauth2") {
        const r5 = Object.values(t2.flows ?? {})[0];
        return `${capitalize(t2.nameKey)}: ${d3.value ? d3.value : (r5 == null ? void 0 : r5.type) ?? ""}`;
      }
      return t2.type === "http" ? `${capitalize(t2.nameKey)}: ${t2.scheme}` : `${capitalize(t2.nameKey)}: ${t2.type}`;
    }, V4 = (t2, r5, e5) => B5.edit(t2, r5, e5), y5 = {
      environment: w6.environment,
      envVariables: w6.envVariables,
      workspace: w6.workspace
    };
    return (t2, r5) => (openBlock(true), createElementBlock(Fragment, null, renderList(g4.value, ({ scheme: e5 }, K2) => (openBlock(), createElementBlock(Fragment, {
      key: e5 == null ? void 0 : e5.uid
    }, [
      g4.value.length > 1 ? (openBlock(), createBlock(unref(i4), {
        key: 0,
        class: normalizeClass({
          "request-example-references-header": t2.layout === "reference"
        })
      }, {
        default: withCtx(() => [
          createVNode(unref(f2), {
            class: normalizeClass([
              "text-c-3 flex items-center pl-3 font-medium",
              t2.layout === "reference" && `border-t ${K2 !== 0 ? "mt-2" : ""}`
            ])
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(I4(e5)), 1)
            ]),
            _: 2
          }, 1032, ["class"])
        ]),
        _: 2
      }, 1032, ["class"])) : createCommentVNode("", true),
      (e5 == null ? void 0 : e5.type) === "http" ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        e5.scheme === "bearer" ? (openBlock(), createBlock(unref(i4), { key: 0 }, {
          default: withCtx(() => [
            createVNode(k5, mergeProps({ ref_for: true }, y5, {
              containerClass: t2.layout === "reference" && "border-t",
              modelValue: e5.token,
              placeholder: "Token",
              type: "password",
              "onUpdate:modelValue": (a3) => V4(e5.uid, "token", a3)
            }), {
              default: withCtx(() => r5[0] || (r5[0] = [
                createTextVNode(" Bearer Token ")
              ])),
              _: 2
            }, 1040, ["containerClass", "modelValue", "onUpdate:modelValue"])
          ]),
          _: 2
        }, 1024)) : (e5 == null ? void 0 : e5.scheme) === "basic" ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
          createVNode(unref(i4), null, {
            default: withCtx(() => [
              createVNode(k5, mergeProps({ ref_for: true }, y5, {
                class: "text-c-2",
                containerClass: t2.layout === "reference" && "auth-blend-required border-t",
                modelValue: e5.username,
                placeholder: "janedoe",
                required: "",
                "onUpdate:modelValue": (a3) => V4(e5.uid, "username", a3)
              }), {
                default: withCtx(() => r5[1] || (r5[1] = [
                  createTextVNode(" Username ")
                ])),
                _: 2
              }, 1040, ["containerClass", "modelValue", "onUpdate:modelValue"])
            ]),
            _: 2
          }, 1024),
          createVNode(unref(i4), null, {
            default: withCtx(() => [
              createVNode(k5, mergeProps({ ref_for: true }, y5, {
                modelValue: e5.password,
                placeholder: "********",
                type: "password",
                "onUpdate:modelValue": (a3) => V4(e5.uid, "password", a3)
              }), {
                default: withCtx(() => r5[2] || (r5[2] = [
                  createTextVNode(" Password ")
                ])),
                _: 2
              }, 1040, ["modelValue", "onUpdate:modelValue"])
            ]),
            _: 2
          }, 1024)
        ], 64)) : createCommentVNode("", true)
      ], 64)) : (e5 == null ? void 0 : e5.type) === "apiKey" ? (openBlock(), createElementBlock(Fragment, { key: 2 }, [
        createVNode(unref(i4), null, {
          default: withCtx(() => [
            createVNode(k5, mergeProps({ ref_for: true }, y5, {
              containerClass: t2.layout === "reference" && "border-t",
              modelValue: e5.name,
              placeholder: "api-key",
              "onUpdate:modelValue": (a3) => V4(e5.uid, "name", a3)
            }), {
              default: withCtx(() => r5[3] || (r5[3] = [
                createTextVNode(" Name ")
              ])),
              _: 2
            }, 1040, ["containerClass", "modelValue", "onUpdate:modelValue"])
          ]),
          _: 2
        }, 1024),
        createVNode(unref(i4), null, {
          default: withCtx(() => [
            createVNode(k5, mergeProps({ ref_for: true }, y5, {
              modelValue: e5.value,
              placeholder: "QUxMIFlPVVIgQkFTRSBBUkUgQkVMT05HIFRPIFVT",
              "onUpdate:modelValue": (a3) => V4(e5.uid, "value", a3)
            }), {
              default: withCtx(() => r5[4] || (r5[4] = [
                createTextVNode(" Value ")
              ])),
              _: 2
            }, 1040, ["modelValue", "onUpdate:modelValue"])
          ]),
          _: 2
        }, 1024)
      ], 64)) : (e5 == null ? void 0 : e5.type) === "oauth2" ? (openBlock(), createElementBlock(Fragment, { key: 3 }, [
        createVNode(unref(i4), null, {
          default: withCtx(() => [
            Object.keys(e5.flows).length > 1 ? (openBlock(), createElementBlock("div", z2, [
              createBaseVNode("div", M, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(e5 == null ? void 0 : e5.flows, (a3, p3, c4) => (openBlock(), createElementBlock("button", {
                  key: p3,
                  class: normalizeClass(["floating-bg text-c-3 relative cursor-pointer border-b-[1px] border-transparent py-1 text-sm font-medium", {
                    "!text-c-1 !rounded-none border-b-[1px] !border-current": t2.layout !== "reference" && (d3.value === p3 || c4 === 0 && !d3.value),
                    "!text-c-1 !rounded-none border-b-[1px] !border-current opacity-100": t2.layout === "reference" && (d3.value === p3 || c4 === 0 && !d3.value)
                  }]),
                  type: "button",
                  onClick: (L3) => d3.value = p3
                }, [
                  createBaseVNode("span", D2, toDisplayString(p3), 1)
                ], 10, Q2))), 128))
              ])
            ])) : createCommentVNode("", true)
          ]),
          _: 2
        }, 1024),
        (openBlock(true), createElementBlock(Fragment, null, renderList(e5 == null ? void 0 : e5.flows, (a3, p3, c4) => (openBlock(), createElementBlock(Fragment, { key: p3 }, [
          d3.value === p3 || c4 === 0 && !d3.value ? (openBlock(), createBlock(W, mergeProps({
            key: 0,
            ref_for: true
          }, y5, {
            collection: t2.collection,
            flow: a3,
            scheme: e5,
            server: t2.server,
            workspace: t2.workspace
          }), null, 16, ["collection", "flow", "scheme", "server", "workspace"])) : createCommentVNode("", true)
        ], 64))), 128))
      ], 64)) : (e5 == null ? void 0 : e5.type) === "openIdConnect" ? (openBlock(), createElementBlock("div", E4, " Coming soon ")) : createCommentVNode("", true)
    ], 64))), 128));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuthTab.vue.js
var f5 = s2(X, [["__scopeId", "data-v-00684fd6"]]);

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuthDataTable.vue2.js
var _6 = {
  key: 0,
  class: "flex flex-wrap gap-x-2.5 overflow-hidden border-t px-3"
};
var $5 = ["onClick"];
var A4 = { class: "relative z-10 whitespace-nowrap font-medium" };
var B3 = {
  key: 0,
  class: "z-1 absolute inset-x-1 bottom-[var(--scalar-border-width)] left-1/2 h-px w-full -translate-x-1/2 bg-current"
};
var q = {
  key: 2,
  class: "text-c-3 bg-b-1 flex min-h-16 items-center justify-center border-t px-4 text-sm"
};
var F3 = defineComponent({
  __name: "RequestAuthDataTable",
  props: {
    collection: {},
    environment: {},
    envVariables: {},
    layout: { default: "client" },
    selectedSchemeOptions: { default: () => [] },
    server: {},
    workspace: {}
  },
  setup(c4) {
    const u4 = E2(), f6 = ref(
      null
    ), r5 = ref(0), m7 = computed(() => {
      const e5 = c4.selectedSchemeOptions[r5.value];
      if (!e5) return [];
      const t2 = e5 == null ? void 0 : e5.id.split(",");
      return t2.length > 1 ? t2 : [e5.id];
    });
    return watch(
      () => c4.selectedSchemeOptions,
      (e5) => {
        e5[r5.value] || (r5.value = Math.max(0, r5.value - 1));
      }
    ), (e5, t2) => (openBlock(), createElementBlock("form", {
      onSubmit: t2[1] || (t2[1] = withModifiers(() => {
      }, ["prevent"]))
    }, [
      e5.selectedSchemeOptions.length > 1 ? (openBlock(), createElementBlock("div", _6, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(e5.selectedSchemeOptions, (l2, n4) => (openBlock(), createElementBlock("div", {
          key: l2.id,
          class: normalizeClass(["z-1 relative -mb-[var(--scalar-border-width)] flex h-8 cursor-pointer", [r5.value === n4 ? "text-c-1" : "text-c-3"]])
        }, [
          createBaseVNode("button", {
            class: "floating-bg relative cursor-pointer border-b-[1px] border-transparent py-1 text-sm font-medium",
            type: "button",
            onClick: (D5) => r5.value = n4
          }, [
            createBaseVNode("span", A4, toDisplayString(l2.label), 1)
          ], 8, $5),
          t2[2] || (t2[2] = createBaseVNode("div", { class: "bg-border absolute -inset-x-96 bottom-0 z-0 h-[var(--scalar-border-width)]" }, null, -1)),
          r5.value === n4 ? (openBlock(), createElementBlock("div", B3)) : createCommentVNode("", true)
        ], 2))), 128))
      ])) : createCommentVNode("", true),
      m7.value.length ? (openBlock(), createBlock(unref(g), {
        key: 1,
        class: normalizeClass(["flex-1", e5.layout === "reference" && "border-0"]),
        columns: [""]
      }, {
        default: withCtx(() => [
          createVNode(f5, {
            collection: e5.collection,
            envVariables: e5.envVariables,
            environment: e5.environment,
            layout: e5.layout,
            securitySchemeUids: m7.value,
            server: e5.server,
            workspace: e5.workspace
          }, null, 8, ["collection", "envVariables", "environment", "layout", "securitySchemeUids", "server", "workspace"])
        ]),
        _: 1
      }, 8, ["class"])) : (openBlock(), createElementBlock("div", q, " No authentication selected ")),
      createVNode(B2, {
        scheme: f6.value,
        state: unref(u4),
        onClose: t2[0] || (t2[0] = (l2) => unref(u4).hide())
      }, null, 8, ["scheme", "state"])
    ], 32));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuthDataTable.vue.js
var m5 = s2(F3, [["__scopeId", "data-v-3569c0b2"]]);

// node_modules/@scalar/api-client/dist/views/Request/consts/new-auth-options.js
var t = {
  apiKeyCookie: {
    label: "API Key in Cookies",
    payload: {
      type: "apiKey",
      in: "cookie",
      nameKey: "apiKeyCookie"
    }
  },
  apiKeyHeader: {
    label: "API Key in Headers",
    payload: {
      type: "apiKey",
      in: "header",
      nameKey: "apiKeyHeader"
    }
  },
  apiKeyQuery: {
    label: "API Key in Query Params",
    payload: {
      type: "apiKey",
      in: "query",
      nameKey: "apiKeyQuery"
    }
  },
  httpBasic: {
    label: "HTTP Basic",
    payload: {
      type: "http",
      scheme: "basic",
      nameKey: "httpBasic"
    }
  },
  httpBearer: {
    label: "HTTP Bearer",
    payload: {
      type: "http",
      scheme: "bearer",
      nameKey: "httpBearer"
    }
  },
  oauth2Implicit: {
    label: "Oauth2 Implicit Flow",
    payload: {
      type: "oauth2",
      nameKey: "oauth2Implicit",
      flows: {
        implicit: {
          type: "implicit"
        }
      }
    }
  },
  oauth2Password: {
    label: "Oauth2 Password Flow",
    payload: {
      type: "oauth2",
      nameKey: "oauth2Password",
      flows: {
        password: {
          type: "password"
        }
      }
    }
  },
  oauth2ClientCredentials: {
    label: "Oauth2 Client Credentials",
    payload: {
      type: "oauth2",
      nameKey: "oauth2ClientCredentials",
      flows: {
        clientCredentials: {
          type: "clientCredentials"
        }
      }
    }
  },
  oauth2AuthorizationFlow: {
    label: "Oauth2 Authorization Code",
    payload: {
      type: "oauth2",
      nameKey: "oauth2AuthorizationFlow",
      flows: {
        authorizationCode: {
          type: "authorizationCode"
        }
      }
    }
  }
};
var o = Object.entries(t);
var i5 = o.map(
  ([e5, a3]) => ({
    id: e5,
    isDeletable: false,
    ...a3
  })
);

// node_modules/@scalar/api-client/dist/views/Request/libs/auth.js
var d = (t2) => ({
  id: t2.uid,
  label: t2.type === "openIdConnect" ? `${t2.nameKey} (coming soon)` : t2.nameKey
});
var b3 = (t2, e5) => d(
  t2.reduce(
    (r5, a3, i7) => {
      const m7 = e5[a3];
      return m7 && (r5.nameKey += `${i7 > 0 ? " & " : ""}${m7.nameKey}`, r5.uid = `${r5.uid}${i7 > 0 ? "," : ""}${m7.uid}`), r5;
    },
    { type: "complex", nameKey: "", uid: "" }
  )
);
var A5 = (t2, e5) => {
  var r5;
  return JSON.stringify(t2 == null ? void 0 : t2.security) === "[{}]" && ((r5 = e5 == null ? void 0 : e5.security) != null && r5.length) ? !!(e5 != null && e5.security.find((i7) => JSON.stringify(i7) === "{}")) ? e5.security : [...e5.security, {}] : (t2 == null ? void 0 : t2.security) ?? (e5 == null ? void 0 : e5.security) ?? [];
};
var D3 = (t2, e5, r5, a3 = false) => {
  {
    const i7 = e5.reduce(
      (n4, s5) => {
        const u4 = r5[s5];
        return u4 && (n4[u4.nameKey] = u4), n4;
      },
      {}
    ), m7 = t2.flatMap((n4) => {
      const s5 = Object.keys(n4);
      if (s5.length > 1) {
        const u4 = s5.map((g4) => {
          var h2;
          return (h2 = i7[g4]) == null ? void 0 : h2.uid;
        }).filter(isDefined);
        return b3(u4, r5);
      }
      if (s5[0]) {
        const u4 = i7[s5[0]];
        if (u4) return d(u4);
      }
      return [];
    }), y5 = e5.filter((n4) => !m7.some((s5) => s5.id === n4)).map((n4) => {
      const s5 = r5[n4];
      return s5 ? d(s5) : null;
    }).filter(isDefined), f6 = [
      { label: "Required authentication", options: m7 },
      { label: "Available authentication", options: y5 }
    ];
    return a3 ? m7.length ? f6 : y5 : (f6.push({
      label: "Add new authentication",
      options: i5
    }), f6);
  }
};

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuth.vue2.js
var le = ["id"];
var oe = { class: "-mx-1 flex flex-1" };
var ne = { class: "text-c-1" };
var he = defineComponent({
  __name: "RequestAuth",
  props: {
    collection: {},
    environment: {},
    envVariables: {},
    layout: {},
    operation: {},
    selectedSecuritySchemeUids: {},
    server: {},
    title: {},
    workspace: {}
  },
  setup(s5) {
    const { layout: v5 } = s(), {
      securitySchemes: S3,
      securitySchemeMutators: O2,
      requestMutators: w6,
      collectionMutators: M2
    } = je(), R2 = useId(), C4 = ref(null), y5 = E2(), b4 = ref(
      null
    ), A6 = computed(() => {
      const e5 = A5(s5.operation, s5.collection);
      return { filteredRequirements: e5.filter((o3) => Object.keys(o3).length), requirements: e5 };
    }), g4 = computed(() => {
      const { filteredRequirements: e5, requirements: t2 } = A6.value;
      if (!t2.length) return null;
      const n4 = !t2.some(
        (u4) => Object.keys(u4).length > 1
      ) && e5.length < t2.length, l2 = n4 ? "Unlock" : "Lock", c4 = n4 ? "Optional" : "Required", j5 = `${e5.length === 1 ? (() => {
        const u4 = Object.keys(e5[0] || {});
        return u4.length > 1 ? u4.join(" & ") : u4[0] || "";
      })() : ""} ${c4}`;
      return { icon: l2, text: j5 };
    }), a3 = computed(
      () => s5.selectedSecuritySchemeUids.map((e5) => {
        if (Array.isArray(e5)) return b3(e5, S3);
        const t2 = S3[e5 ?? ""];
        if (t2)
          return d(t2);
      }).filter(isDefined)
    );
    function U3(e5) {
      var n4;
      const t2 = e5.find((l2) => l2.payload), o3 = e5.filter((l2) => !l2.payload).map(({ id: l2 }) => {
        const c4 = l2.split(",");
        return c4.length > 1 ? c4 : l2;
      });
      if (t2 != null && t2.payload) {
        const l2 = O2.add(
          t2.payload,
          (n4 = s5.collection) == null ? void 0 : n4.uid
        );
        l2 && o3.push(l2.uid);
      }
      V4(o3);
    }
    const V4 = (e5) => {
      var t2;
      v5 === "modal" || s5.layout === "reference" ? M2.edit(s5.collection.uid, "selectedSecuritySchemeUids", e5) : (t2 = s5.operation) != null && t2.uid && w6.edit(s5.operation.uid, "selectedSecuritySchemeUids", e5);
    };
    function $7({ id: e5, label: t2 }) {
      b4.value = { id: e5, label: t2 }, y5.show();
    }
    const B5 = (e5) => {
      var o3;
      if (!e5) return;
      const t2 = s5.selectedSecuritySchemeUids.filter((n4) => {
        const l2 = e5.split(",");
        return l2.length > 1 && Array.isArray(n4) && l2.length === n4.length ? n4.every((c4) => !l2.includes(c4)) : n4 !== e5;
      });
      V4(t2), (o3 = C4.value) == null || o3.$el.focus(), y5.hide();
    }, T4 = computed(
      () => {
        var e5;
        return D3(
          A6.value.filteredRequirements,
          ((e5 = s5.collection) == null ? void 0 : e5.securitySchemes) ?? [],
          S3,
          v5 === "modal" || s5.layout === "reference"
        );
      }
    );
    return (e5, t2) => (openBlock(), createBlock(P2, {
      class: "group/params",
      itemCount: a3.value.length,
      layout: e5.layout
    }, {
      title: withCtx(() => [
        createBaseVNode("div", {
          id: unref(R2),
          class: "inline-flex items-center gap-1"
        }, [
          createBaseVNode("span", null, toDisplayString(e5.title), 1),
          g4.value ? (openBlock(), createElementBlock("span", {
            key: 0,
            class: normalizeClass(["text-c-3 text-xs leading-[normal]", { "text-c-1": g4.value.text === "Required" }])
          }, toDisplayString(g4.value.text), 3)) : createCommentVNode("", true)
        ], 8, le)
      ]),
      actions: withCtx(() => [
        createBaseVNode("div", oe, [
          createVNode(unref(w), {
            class: "w-72 text-xs",
            isDeletable: unref(v5) !== "modal" && e5.layout !== "reference",
            modelValue: a3.value,
            multiple: "",
            options: T4.value,
            onDelete: $7,
            "onUpdate:modelValue": U3
          }, {
            default: withCtx(() => [
              createVNode(unref($), {
                ref_key: "comboboxButtonRef",
                ref: C4,
                "aria-describedby": unref(R2),
                class: "py-0.75 hover:bg-b-3 text-c-1 hover:text-c-1 h-auto px-1.5 font-normal",
                fullWidth: "",
                variant: "ghost"
              }, {
                default: withCtx(() => {
                  var o3;
                  return [
                    createBaseVNode("div", ne, [
                      a3.value.length === 0 ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                        t2[2] || (t2[2] = createBaseVNode("span", { class: "sr-only" }, "Select", -1)),
                        t2[3] || (t2[3] = createTextVNode(" Auth Type "))
                      ], 64)) : a3.value.length === 1 ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                        t2[4] || (t2[4] = createBaseVNode("span", { class: "sr-only" }, "Selected Auth Type:", -1)),
                        createTextVNode(" " + toDisplayString((o3 = a3.value[0]) == null ? void 0 : o3.label), 1)
                      ], 64)) : (openBlock(), createElementBlock(Fragment, { key: 2 }, [
                        t2[5] || (t2[5] = createTextVNode(" Multiple ")),
                        t2[6] || (t2[6] = createBaseVNode("span", { class: "sr-only" }, "Auth Types Selected", -1))
                      ], 64))
                    ]),
                    createVNode(unref(c), {
                      class: "ml-1 shrink-0",
                      icon: "ChevronDown",
                      size: "sm"
                    })
                  ];
                }),
                _: 1
              }, 8, ["aria-describedby"])
            ]),
            _: 1
          }, 8, ["isDeletable", "modelValue", "options"])
        ])
      ]),
      default: withCtx(() => [
        createVNode(m5, {
          collection: e5.collection,
          envVariables: e5.envVariables,
          environment: e5.environment,
          layout: e5.layout,
          selectedSchemeOptions: a3.value,
          server: e5.server,
          workspace: e5.workspace
        }, null, 8, ["collection", "envVariables", "environment", "layout", "selectedSchemeOptions", "server", "workspace"]),
        createVNode(B2, {
          scheme: b4.value,
          state: unref(y5),
          onClose: t2[0] || (t2[0] = (o3) => unref(y5).hide()),
          onDelete: t2[1] || (t2[1] = (o3) => {
            var n4;
            return B5((n4 = b4.value) == null ? void 0 : n4.id);
          })
        }, null, 8, ["scheme", "state"])
      ]),
      _: 1
    }, 8, ["itemCount", "layout"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestAuth/RequestAuth.vue.js
var m6 = s2(he, [["__scopeId", "data-v-8ab24b0b"]]);

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/helpers/code-builder.js
var DEFAULT_INDENTATION_CHARACTER = "";
var DEFAULT_LINE_JOIN = "\n";
var CodeBuilder = class {
  /**
   * Helper object to format and aggragate lines of code.
   * Lines are aggregated in a `code` array, and need to be joined to obtain a proper code snippet.
   */
  constructor({ indent, join } = {}) {
    this.postProcessors = [];
    this.code = [];
    this.indentationCharacter = DEFAULT_INDENTATION_CHARACTER;
    this.lineJoin = DEFAULT_LINE_JOIN;
    this.indentLine = (line, indentationLevel = 0) => {
      const whitespace = this.indentationCharacter.repeat(indentationLevel);
      return `${whitespace}${line}`;
    };
    this.unshift = (line, indentationLevel) => {
      const newLine = this.indentLine(line, indentationLevel);
      this.code.unshift(newLine);
    };
    this.push = (line, indentationLevel) => {
      const newLine = this.indentLine(line, indentationLevel);
      this.code.push(newLine);
    };
    this.blank = () => {
      this.code.push("");
    };
    this.join = () => {
      const unreplacedCode = this.code.join(this.lineJoin);
      const replacedOutput = this.postProcessors.reduce((accumulator, replacer) => replacer(accumulator), unreplacedCode);
      return replacedOutput;
    };
    this.addPostProcessor = (postProcessor) => {
      this.postProcessors = [...this.postProcessors, postProcessor];
    };
    this.indentationCharacter = indent || DEFAULT_INDENTATION_CHARACTER;
    this.lineJoin = join !== null && join !== void 0 ? join : DEFAULT_LINE_JOIN;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/helpers/escape.js
function escapeString(rawValue, options = {}) {
  const { delimiter = '"', escapeChar = "\\", escapeNewlines = true } = options;
  const stringValue = rawValue.toString();
  return [...stringValue].map((c4) => {
    if (c4 === "\b") {
      return `${escapeChar}b`;
    }
    if (c4 === "	") {
      return `${escapeChar}t`;
    }
    if (c4 === "\n") {
      if (escapeNewlines) {
        return `${escapeChar}n`;
      }
      return c4;
    }
    if (c4 === "\f") {
      return `${escapeChar}f`;
    }
    if (c4 === "\r") {
      if (escapeNewlines) {
        return `${escapeChar}r`;
      }
      return c4;
    }
    if (c4 === escapeChar) {
      return escapeChar + escapeChar;
    }
    if (c4 === delimiter) {
      return escapeChar + delimiter;
    }
    if (c4 < " " || c4 > "~") {
      return JSON.stringify(c4).slice(1, -1);
    }
    return c4;
  }).join("");
}
var escapeForSingleQuotes = (value) => escapeString(value, { delimiter: "'" });
var escapeForDoubleQuotes = (value) => escapeString(value, { delimiter: '"' });

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/c/libcurl/client.js
var libcurl = {
  info: {
    key: "libcurl",
    title: "Libcurl",
    link: "http://curl.haxx.se/libcurl",
    description: "Simple REST and HTTP API Client for C"
  },
  convert: ({ method, fullUrl, headersObj, allHeaders, postData }) => {
    const { push, blank, join } = new CodeBuilder();
    push("CURL *hnd = curl_easy_init();");
    blank();
    push(`curl_easy_setopt(hnd, CURLOPT_CUSTOMREQUEST, "${method.toUpperCase()}");`);
    push(`curl_easy_setopt(hnd, CURLOPT_URL, "${fullUrl}");`);
    const headers = Object.keys(headersObj);
    if (headers.length) {
      blank();
      push("struct curl_slist *headers = NULL;");
      headers.forEach((header) => {
        push(`headers = curl_slist_append(headers, "${header}: ${escapeForDoubleQuotes(headersObj[header])}");`);
      });
      push("curl_easy_setopt(hnd, CURLOPT_HTTPHEADER, headers);");
    }
    if (allHeaders.cookie) {
      blank();
      push(`curl_easy_setopt(hnd, CURLOPT_COOKIE, "${allHeaders.cookie}");`);
    }
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      blank();
      push(`curl_easy_setopt(hnd, CURLOPT_POSTFIELDS, ${JSON.stringify(postData.text)});`);
    }
    blank();
    push("CURLcode ret = curl_easy_perform(hnd);");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/utils/convertWithHttpSnippetLite.js
function convertWithHttpSnippetLite(client, request) {
  var _a, _b, _c, _d;
  const urlObject = new URL((request == null ? void 0 : request.url) ?? "");
  const url = urlObject.pathname === "/" ? urlObject.origin : urlObject.toString();
  const harRequest = {
    method: (request == null ? void 0 : request.method) ?? "GET",
    url,
    httpVersion: "HTTP/1.1",
    cookies: [],
    // Cookies are handled through headers
    headers: (request == null ? void 0 : request.headers) ?? [],
    headersSize: -1,
    bodySize: -1,
    queryString: Array.from(urlObject.searchParams.entries()).map(([name, value]) => ({
      name,
      value
    })),
    postData: request == null ? void 0 : request.postData
  };
  const allHeaders = ((harRequest == null ? void 0 : harRequest.headers) ?? []).reduce((acc, header) => ({
    ...acc,
    [header.name]: header.value
  }), {});
  const queryObj = (harRequest.queryString ?? []).reduce((acc, param) => ({
    ...acc,
    [param.name]: param.value
  }), {});
  const cookiesObj = (harRequest.cookies ?? []).reduce((acc, cookie) => ({
    ...acc,
    [cookie.name]: cookie.value
  }), {});
  const parsedUrl = new URL(harRequest.url);
  const uriObj = {
    protocol: parsedUrl.protocol,
    hostname: parsedUrl.hostname,
    host: parsedUrl.hostname,
    port: parsedUrl.port,
    pathname: parsedUrl.pathname.split("/").map((segment) => encodeURIComponent(decodeURIComponent(segment))).join("/") + parsedUrl.search,
    path: parsedUrl.pathname.split("/").map((segment) => encodeURIComponent(decodeURIComponent(segment))).join("/") + parsedUrl.search,
    search: parsedUrl.search,
    hash: parsedUrl.hash,
    href: parsedUrl.href,
    origin: parsedUrl.origin,
    password: parsedUrl.password,
    searchParams: parsedUrl.searchParams,
    username: parsedUrl.username,
    toString: parsedUrl.toString,
    toJSON: () => parsedUrl.toJSON()
  };
  const convertRequest = {
    url: harRequest.url,
    uriObj,
    method: ((_a = harRequest.method) == null ? void 0 : _a.toLocaleUpperCase()) ?? "GET",
    httpVersion: harRequest.httpVersion,
    cookies: harRequest.cookies ?? [],
    headers: harRequest.headers ?? [],
    headersSize: harRequest.headersSize,
    headersObj: allHeaders ?? {},
    bodySize: harRequest.bodySize,
    queryString: harRequest.queryString ?? [],
    postData: harRequest.postData ? {
      mimeType: harRequest.postData.mimeType ?? "application/json",
      text: harRequest.postData.text ?? "",
      params: harRequest.postData.params ?? [],
      paramsObj: ((_b = harRequest.postData.params) == null ? void 0 : _b.reduce((acc, param) => {
        if (param.name && param.value !== void 0) {
          acc[param.name] = param.value;
        }
        return acc;
      }, {})) ?? {}
    } : void 0,
    allHeaders: allHeaders ?? {},
    fullUrl: harRequest.url,
    queryObj: queryObj ?? {},
    cookiesObj: cookiesObj ?? {}
  };
  if (((_c = convertRequest.postData) == null ? void 0 : _c.mimeType) === "application/json" && ((_d = convertRequest.postData) == null ? void 0 : _d.text)) {
    try {
      convertRequest.postData.jsonObj = JSON.parse(convertRequest.postData.text);
    } catch (error) {
      console.error("Error parsing JSON:", error);
    }
  }
  if (typeof client.convert === "function") {
    return client.convert(convertRequest);
  }
  return "";
}

// node_modules/@scalar/snippetz/dist/plugins/c/libcurl/libcurl.js
var cLibcurl = {
  target: "c",
  client: "libcurl",
  title: "Libcurl",
  generate(request) {
    return convertWithHttpSnippetLite(libcurl, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/java/nethttp/client.js
var nethttp = {
  info: {
    key: "nethttp",
    title: "java.net.http",
    link: "https://openjdk.java.net/groups/net/httpclient/intro.html",
    description: "Java Standardized HTTP Client API"
  },
  convert: ({ allHeaders, fullUrl, method, postData }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { push, join } = new CodeBuilder({ indent: opts.indent });
    push("HttpRequest request = HttpRequest.newBuilder()");
    push(`.uri(URI.create("${fullUrl}"))`, 2);
    Object.keys(allHeaders).forEach((key) => {
      push(`.header("${key}", "${escapeForDoubleQuotes(allHeaders[key])}")`, 2);
    });
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`.method("${method.toUpperCase()}", HttpRequest.BodyPublishers.ofString(${JSON.stringify(postData.text)}))`, 2);
    } else {
      push(`.method("${method.toUpperCase()}", HttpRequest.BodyPublishers.noBody())`, 2);
    }
    push(".build();", 2);
    push("HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());");
    push("System.out.println(response.body());");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/java/nethttp/nethttp.js
var javaNethttp = {
  target: "java",
  client: "nethttp",
  title: "java.net.http",
  generate(request) {
    return convertWithHttpSnippetLite(nethttp, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/java/okhttp/client.js
var okhttp = {
  info: {
    key: "okhttp",
    title: "OkHttp",
    link: "http://square.github.io/okhttp/",
    description: "An HTTP Request Client Library"
  },
  convert: ({ postData, method, fullUrl, allHeaders }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { push, blank, join } = new CodeBuilder({ indent: opts.indent });
    const methods = ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD"];
    const methodsWithBody = ["POST", "PUT", "DELETE", "PATCH"];
    push("OkHttpClient client = new OkHttpClient();");
    blank();
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      if (postData.boundary) {
        push(`MediaType mediaType = MediaType.parse("${postData.mimeType}; boundary=${postData.boundary}");`);
      } else {
        push(`MediaType mediaType = MediaType.parse("${postData.mimeType}");`);
      }
      push(`RequestBody body = RequestBody.create(mediaType, ${JSON.stringify(postData.text)});`);
    }
    push("Request request = new Request.Builder()");
    push(`.url("${fullUrl}")`, 1);
    if (!methods.includes(method.toUpperCase())) {
      if (postData === null || postData === void 0 ? void 0 : postData.text) {
        push(`.method("${method.toUpperCase()}", body)`, 1);
      } else {
        push(`.method("${method.toUpperCase()}", null)`, 1);
      }
    } else if (methodsWithBody.includes(method.toUpperCase())) {
      if (postData === null || postData === void 0 ? void 0 : postData.text) {
        push(`.${method.toLowerCase()}(body)`, 1);
      } else {
        push(`.${method.toLowerCase()}(null)`, 1);
      }
    } else {
      push(`.${method.toLowerCase()}()`, 1);
    }
    Object.keys(allHeaders).forEach((key) => {
      push(`.addHeader("${key}", "${escapeForDoubleQuotes(allHeaders[key])}")`, 1);
    });
    push(".build();", 1);
    blank();
    push("Response response = client.newCall(request).execute();");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/java/okhttp/okhttp.js
var javaOkhttp = {
  target: "java",
  client: "okhttp",
  title: "OkHttp",
  generate(request) {
    return convertWithHttpSnippetLite(okhttp, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/java/unirest/client.js
var unirest = {
  info: {
    key: "unirest",
    title: "Unirest",
    link: "http://unirest.io/java.html",
    description: "Lightweight HTTP Request Client Library"
  },
  convert: ({ method, allHeaders, postData, fullUrl }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { join, push } = new CodeBuilder({ indent: opts.indent });
    const methods = ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"];
    if (!methods.includes(method.toUpperCase())) {
      push(`HttpResponse<String> response = Unirest.customMethod("${method.toUpperCase()}","${fullUrl}")`);
    } else {
      push(`HttpResponse<String> response = Unirest.${method.toLowerCase()}("${fullUrl}")`);
    }
    Object.keys(allHeaders).forEach((key) => {
      push(`.header("${key}", "${escapeForDoubleQuotes(allHeaders[key])}")`, 1);
    });
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`.body(${JSON.stringify(postData.text)})`, 1);
    }
    push(".asString();", 1);
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/java/unirest/unirest.js
var javaUnirest = {
  target: "java",
  client: "unirest",
  title: "Unirest",
  generate(request) {
    return convertWithHttpSnippetLite(unirest, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/helpers/headers.js
var getHeaderName = (headers, name) => Object.keys(headers).find((header) => header.toLowerCase() === name.toLowerCase());
var getHeader = (headers, name) => {
  const headerName = getHeaderName(headers, name);
  if (!headerName) {
    return void 0;
  }
  return headers[headerName];
};
var hasHeader = (headers, name) => Boolean(getHeaderName(headers, name));

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/csharp/httpclient/client.js
var getDecompressionMethods = (allHeaders) => {
  let acceptEncodings = getHeader(allHeaders, "accept-encoding");
  if (!acceptEncodings) {
    return [];
  }
  const supportedMethods = {
    gzip: "DecompressionMethods.GZip",
    deflate: "DecompressionMethods.Deflate"
  };
  const methods = [];
  if (typeof acceptEncodings === "string") {
    acceptEncodings = [acceptEncodings];
  }
  acceptEncodings.forEach((acceptEncoding) => {
    acceptEncoding.split(",").forEach((encoding) => {
      const match = /\s*([^;\s]+)/.exec(encoding);
      if (match) {
        const method = supportedMethods[match[1]];
        if (method) {
          methods.push(method);
        }
      }
    });
  });
  return methods;
};
var httpclient = {
  info: {
    key: "httpclient",
    title: "HttpClient",
    link: "https://docs.microsoft.com/en-us/dotnet/api/system.net.http.httpclient",
    description: ".NET Standard HTTP Client"
  },
  convert: ({ allHeaders, postData, method, fullUrl }, options) => {
    let _a, _b;
    const opts = {
      indent: "    ",
      ...options
    };
    const { push, join } = new CodeBuilder({ indent: opts.indent });
    push("using System.Net.Http.Headers;");
    let clienthandler = "";
    const cookies = Boolean(allHeaders.cookie);
    const decompressionMethods = getDecompressionMethods(allHeaders);
    if (cookies || decompressionMethods.length) {
      clienthandler = "clientHandler";
      push("var clientHandler = new HttpClientHandler");
      push("{");
      if (cookies) {
        push("UseCookies = false,", 1);
      }
      if (decompressionMethods.length) {
        push(`AutomaticDecompression = ${decompressionMethods.join(" | ")},`, 1);
      }
      push("};");
    }
    push(`var client = new HttpClient(${clienthandler});`);
    push("var request = new HttpRequestMessage");
    push("{");
    const methods = [
      "GET",
      "POST",
      "PUT",
      "DELETE",
      "PATCH",
      "HEAD",
      "OPTIONS",
      "TRACE"
    ];
    method = method.toUpperCase();
    if (method && methods.includes(method)) {
      method = `HttpMethod.${method[0]}${method.substring(1).toLowerCase()}`;
    } else {
      method = `new HttpMethod("${method}")`;
    }
    push(`Method = ${method},`, 1);
    push(`RequestUri = new Uri("${fullUrl}"),`, 1);
    const headers = Object.keys(allHeaders).filter((header) => {
      switch (header.toLowerCase()) {
        case "content-type":
        case "content-length":
        case "accept-encoding":
          return false;
        default:
          return true;
      }
    });
    if (headers.length) {
      push("Headers =", 1);
      push("{", 1);
      headers.forEach((key) => {
        push(`{ "${key}", "${escapeForDoubleQuotes(allHeaders[key])}" },`, 2);
      });
      push("},", 1);
    }
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      const contentType = postData.mimeType;
      switch (contentType) {
        case "application/x-www-form-urlencoded":
          push("Content = new FormUrlEncodedContent(new Dictionary<string, string>", 1);
          push("{", 1);
          (_a = postData.params) === null || _a === void 0 ? void 0 : _a.forEach((param) => {
            push(`{ "${param.name}", "${param.value}" },`, 2);
          });
          push("}),", 1);
          break;
        case "multipart/form-data":
          push("Content = new MultipartFormDataContent", 1);
          push("{", 1);
          (_b = postData.params) === null || _b === void 0 ? void 0 : _b.forEach((param) => {
            push(`new StringContent(${JSON.stringify(param.value || "")})`, 2);
            push("{", 2);
            push("Headers =", 3);
            push("{", 3);
            if (param.contentType) {
              push(`ContentType = new MediaTypeHeaderValue("${param.contentType}"),`, 4);
            }
            push('ContentDisposition = new ContentDispositionHeaderValue("form-data")', 4);
            push("{", 4);
            push(`Name = "${param.name}",`, 5);
            if (param.fileName) {
              push(`FileName = "${param.fileName}",`, 5);
            }
            push("}", 4);
            push("}", 3);
            push("},", 2);
          });
          push("},", 1);
          break;
        default:
          push(`Content = new StringContent(${JSON.stringify((postData === null || postData === void 0 ? void 0 : postData.text) || "")})`, 1);
          push("{", 1);
          push("Headers =", 2);
          push("{", 2);
          push(`ContentType = new MediaTypeHeaderValue("${contentType}")`, 3);
          push("}", 2);
          push("}", 1);
          break;
      }
    }
    push("};");
    push("using (var response = await client.SendAsync(request))");
    push("{");
    push("response.EnsureSuccessStatusCode();", 1);
    push("var body = await response.Content.ReadAsStringAsync();", 1);
    push("Console.WriteLine(body);", 1);
    push("}");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/csharp/httpclient/httpclient.js
var csharpHttpclient = {
  target: "csharp",
  client: "httpclient",
  title: "HttpClient",
  generate(request) {
    return convertWithHttpSnippetLite(httpclient, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/csharp/restsharp/client.js
var restsharp = {
  info: {
    key: "restsharp",
    title: "RestSharp",
    link: "http://restsharp.org/",
    description: "Simple REST and HTTP API Client for .NET"
  },
  convert: ({ allHeaders, method, fullUrl, headersObj, cookies, postData }) => {
    const { push, join } = new CodeBuilder();
    const isSupportedMethod = [
      "GET",
      "POST",
      "PUT",
      "DELETE",
      "PATCH",
      "HEAD",
      "OPTIONS"
    ].includes(method.toUpperCase());
    if (!isSupportedMethod) {
      return "Method not supported";
    }
    push(`var client = new RestClient("${fullUrl}");`);
    push(`var request = new RestRequest(Method.${method.toUpperCase()});`);
    Object.keys(headersObj).forEach((key) => {
      push(`request.AddHeader("${key}", "${escapeForDoubleQuotes(headersObj[key])}");`);
    });
    cookies === null || cookies === void 0 ? void 0 : cookies.forEach(({ name, value }) => {
      push(`request.AddCookie("${name}", "${value}");`);
    });
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      const header = getHeader(allHeaders, "content-type");
      const text = JSON.stringify(postData.text);
      push(`request.AddParameter("${header}", ${text}, ParameterType.RequestBody);`);
    }
    push("IRestResponse response = client.Execute(request);");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/csharp/restsharp/restsharp.js
var csharpRestsharp = {
  target: "csharp",
  client: "restsharp",
  title: "RestSharp",
  generate(request) {
    return convertWithHttpSnippetLite(restsharp, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/clojure/clj_http/client.js
var Keyword = class {
  constructor(name) {
    this.name = "";
    this.toString = () => `:${this.name}`;
    this.name = name;
  }
};
var File = class {
  constructor(path) {
    this.path = "";
    this.toString = () => `(clojure.java.io/file "${this.path}")`;
    this.path = path;
  }
};
var jsType = (input) => {
  if (input === void 0) {
    return null;
  }
  if (input === null) {
    return "null";
  }
  return input.constructor.name.toLowerCase();
};
var objEmpty = (input) => {
  if (jsType(input) === "object") {
    return Object.keys(input).length === 0;
  }
  return false;
};
var filterEmpty = (input) => {
  Object.keys(input).filter((x3) => objEmpty(input[x3])).forEach((x3) => {
    delete input[x3];
  });
  return input;
};
var padBlock = (padSize, input) => {
  const padding = " ".repeat(padSize);
  return input.replace(/\n/g, `
${padding}`);
};
var jsToEdn = (js) => {
  switch (jsType(js)) {
    case "string":
      return `"${js.replace(/"/g, '\\"')}"`;
    case "file":
      return js.toString();
    case "keyword":
      return js.toString();
    case "null":
      return "nil";
    case "regexp":
      return `#"${js.source}"`;
    case "object": {
      const obj = Object.keys(js).reduce((accumulator, key) => {
        const val = padBlock(key.length + 2, jsToEdn(js[key]));
        return `${accumulator}:${key} ${val}
 `;
      }, "").trim();
      return `{${padBlock(1, obj)}}`;
    }
    case "array": {
      const arr = js.reduce((accumulator, value) => `${accumulator} ${jsToEdn(value)}`, "").trim();
      return `[${padBlock(1, arr)}]`;
    }
    default:
      return js.toString();
  }
};
var clj_http = {
  info: {
    key: "clj_http",
    title: "clj-http",
    link: "https://github.com/dakrone/clj-http",
    description: "An idiomatic clojure http client wrapping the apache client."
  },
  convert: ({ queryObj, method, postData, url, allHeaders }, options) => {
    const { push, join } = new CodeBuilder({
      indent: options === null || options === void 0 ? void 0 : options.indent
    });
    const methods = ["get", "post", "put", "delete", "patch", "head", "options"];
    method = method.toLowerCase();
    if (!methods.includes(method)) {
      push("Method not supported");
      return join();
    }
    const params = {
      "headers": allHeaders,
      "query-params": queryObj
    };
    if (queryObj && Object.keys(queryObj).length > 0) {
      url = url.split("?")[0];
    }
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/json":
        {
          params["content-type"] = new Keyword("json");
          params["form-params"] = postData.jsonObj;
          const header = getHeaderName(params.headers, "content-type");
          if (header) {
            delete params.headers[header];
          }
        }
        break;
      case "application/x-www-form-urlencoded":
        {
          params["form-params"] = postData.paramsObj;
          const header = getHeaderName(params.headers, "content-type");
          if (header) {
            delete params.headers[header];
          }
        }
        break;
      case "text/plain":
        {
          params.body = postData.text;
          const header = getHeaderName(params.headers, "content-type");
          if (header) {
            delete params.headers[header];
          }
        }
        break;
      case "multipart/form-data": {
        if (postData.params) {
          params.multipart = postData.params.map((param) => {
            if (param.fileName && !param.value) {
              return {
                name: param.name,
                content: new File(param.fileName)
              };
            }
            return {
              name: param.name,
              content: param.value
            };
          });
          const header = getHeaderName(params.headers, "content-type");
          if (header) {
            delete params.headers[header];
          }
        }
        break;
      }
    }
    switch (getHeader(params.headers, "accept")) {
      case "application/json":
        {
          params.accept = new Keyword("json");
          const header = getHeaderName(params.headers, "accept");
          if (header) {
            delete params.headers[header];
          }
        }
        break;
    }
    push("(require '[clj-http.client :as client])\n");
    if (objEmpty(filterEmpty(params))) {
      push(`(client/${method} "${url}")`);
    } else {
      const padding = 11 + method.length + url.length;
      const formattedParams = padBlock(padding, jsToEdn(filterEmpty(params)));
      push(`(client/${method} "${url}" ${formattedParams})`);
    }
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/clojure/clj_http/clj_http.js
var clojureCljhttp = {
  target: "clojure",
  client: "clj_http",
  title: "clj-http",
  generate(request) {
    return convertWithHttpSnippetLite(clj_http, request);
  }
};

// node_modules/@scalar/snippetz/dist/plugins/dart/http/http.js
var dartHttp = {
  target: "dart",
  client: "http",
  title: "Http",
  generate(request, options) {
    var _a, _b, _c;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    let code = `import 'package:http/http.dart' as http;

void main() async {
`;
    let cookieHeader = "";
    let cookieString = "";
    if (normalizedRequest.cookies && normalizedRequest.cookies.length > 0) {
      cookieString = normalizedRequest.cookies.map((cookie) => `${encodeURIComponent(cookie.name)}=${encodeURIComponent(cookie.value)}`).join("; ");
      cookieHeader = `  "Cookie": "${cookieString}",
`;
    }
    const headers = ((_a = normalizedRequest.headers) == null ? void 0 : _a.reduce((acc, header) => {
      if (header.value && !/[; ]/.test(header.name)) {
        acc[header.name] = header.value;
      }
      return acc;
    }, {})) || {};
    if (options == null ? void 0 : options.auth) {
      const { username, password } = options.auth;
      if (username && password) {
        const credentials = `${username}:${password}`;
        headers["Authorization"] = `'Basic ' + base64Encode(utf8.encode('${credentials}'))`;
      }
    }
    if (cookieHeader) {
      headers["Cookie"] = cookieString;
    }
    if (Object.keys(headers).length > 0) {
      code += "  final headers = <String,String>{\n";
      for (const [key, value] of Object.entries(headers)) {
        if (value.includes("utf8.encode")) {
          code += `    '${key}': ${value},
`;
        } else {
          code += `    '${key}': '${value}',
`;
        }
      }
      code += "  };\n\n";
    }
    const queryString = ((_b = normalizedRequest.queryString) == null ? void 0 : _b.length) ? "?" + normalizedRequest.queryString.map((param) => `${encodeURIComponent(param.name)}=${encodeURIComponent(param.value)}`).join("&") : "";
    const url = `${normalizedRequest.url}${queryString}`;
    let body = "";
    if (normalizedRequest.postData) {
      if (normalizedRequest.postData.mimeType === "application/json") {
        body = `  final body = r'${normalizedRequest.postData.text}';

`;
      } else if (normalizedRequest.postData.mimeType === "application/x-www-form-urlencoded") {
        body = `  final body = '${((_c = normalizedRequest.postData.params) == null ? void 0 : _c.map((param) => `${encodeURIComponent(param.name)}=${encodeURIComponent(param.value ?? "")}`).join("&")) || ""}';

`;
      } else if (normalizedRequest.postData.mimeType === "multipart/form-data") {
        body = "  final body = <String,String>{\n";
        for (const param of normalizedRequest.postData.params || []) {
          const value = param.value || "";
          const fileName = param.fileName || "";
          body += `    '${param.name}': '${fileName || value}',
`;
        }
        body += "  };\n\n";
      } else if (normalizedRequest.postData.mimeType === "application/octet-stream") {
        body = `  final body = '${normalizedRequest.postData.text}';

`;
      }
    }
    if (body) {
      code += body;
    }
    const method = normalizedRequest.method.toLowerCase();
    const headersPart = Object.keys(headers).length > 0 ? ", headers: headers" : "";
    const bodyPart = body ? ", body: body" : "";
    code += `  final response = await http.${method}(Uri.parse('${url}')${headersPart}${bodyPart});
`;
    code += "  print(response.body);\n";
    code += "}";
    return code;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/go/native/client.js
var native = {
  info: {
    key: "native",
    title: "NewRequest",
    link: "http://golang.org/pkg/net/http/#NewRequest",
    description: "Golang HTTP client request"
  },
  convert: ({ postData, method, allHeaders, fullUrl }, options = {}) => {
    const { blank, push, join } = new CodeBuilder({ indent: "	" });
    const { showBoilerplate = true, checkErrors = false, printBody = true, timeout = -1, insecureSkipVerify = false } = options;
    const errorPlaceholder = checkErrors ? "err" : "_";
    const indent = showBoilerplate ? 1 : 0;
    const errorCheck = () => {
      if (checkErrors) {
        push("if err != nil {", indent);
        push("panic(err)", indent + 1);
        push("}", indent);
      }
    };
    if (showBoilerplate) {
      push("package main");
      blank();
      push("import (");
      push('"fmt"', indent);
      if (timeout > 0) {
        push('"time"', indent);
      }
      if (insecureSkipVerify) {
        push('"crypto/tls"', indent);
      }
      if (postData === null || postData === void 0 ? void 0 : postData.text) {
        push('"strings"', indent);
      }
      push('"net/http"', indent);
      if (printBody) {
        push('"io"', indent);
      }
      push(")");
      blank();
      push("func main() {");
      blank();
    }
    if (insecureSkipVerify) {
      push("insecureTransport := http.DefaultTransport.(*http.Transport).Clone()", indent);
      push("insecureTransport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}", indent);
    }
    const hasTimeout = timeout > 0;
    const hasClient = hasTimeout || insecureSkipVerify;
    const client = hasClient ? "client" : "http.DefaultClient";
    if (hasClient) {
      push("client := http.Client{", indent);
      if (hasTimeout) {
        push(`Timeout: time.Duration(${timeout} * time.Second),`, indent + 1);
      }
      if (insecureSkipVerify) {
        push("Transport: insecureTransport,", indent + 1);
      }
      push("}", indent);
      blank();
    }
    push(`url := "${fullUrl}"`, indent);
    blank();
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`payload := strings.NewReader(${JSON.stringify(postData.text)})`, indent);
      blank();
      push(`req, ${errorPlaceholder} := http.NewRequest("${method}", url, payload)`, indent);
      blank();
    } else {
      push(`req, ${errorPlaceholder} := http.NewRequest("${method}", url, nil)`, indent);
      blank();
    }
    errorCheck();
    if (Object.keys(allHeaders).length) {
      Object.keys(allHeaders).forEach((key) => {
        push(`req.Header.Add("${key}", "${escapeForDoubleQuotes(allHeaders[key])}")`, indent);
      });
      blank();
    }
    push(`res, ${errorPlaceholder} := ${client}.Do(req)`, indent);
    errorCheck();
    if (printBody) {
      blank();
      push("defer res.Body.Close()", indent);
      push(`body, ${errorPlaceholder} := io.ReadAll(res.Body)`, indent);
      errorCheck();
    }
    blank();
    push("fmt.Println(res)", indent);
    if (printBody) {
      push("fmt.Println(string(body))", indent);
    }
    if (showBoilerplate) {
      blank();
      push("}");
    }
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/go/native/native.js
var goNative = {
  target: "go",
  client: "native",
  title: "NewRequest",
  generate(request) {
    return convertWithHttpSnippetLite(native, request);
  }
};

// node_modules/@scalar/snippetz/dist/plugins/http/http11/http11.js
var httpHttp11 = {
  target: "http",
  client: "http1.1",
  title: "HTTP/1.1",
  generate(request) {
    const normalizedRequest = {
      method: "GET",
      headers: [],
      queryString: [],
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    let url;
    let path;
    try {
      url = new URL(normalizedRequest.url || "http://");
      path = url.pathname + (url.search || "");
    } catch (_error) {
      path = normalizedRequest.url || "/";
    }
    const hostname = (url == null ? void 0 : url.hostname) || "UNKNOWN_HOSTNAME";
    let requestString = `${normalizedRequest.method} ${path} HTTP/1.1\r
`;
    if (normalizedRequest.queryString.length) {
      const queryString = normalizedRequest.queryString.map((param) => `${encodeURIComponent(param.name)}=${encodeURIComponent(param.value)}`).join("&");
      requestString = `${normalizedRequest.method} ${path}?${queryString} HTTP/1.1\r
`;
    }
    const headers = /* @__PURE__ */ new Map();
    headers.set("Host", hostname);
    normalizedRequest.headers.forEach((header) => {
      if (headers.has(header.name)) {
        headers.set(header.name, `${headers.get(header.name)}, ${header.value}`);
      } else {
        headers.set(header.name, header.value);
      }
    });
    if (normalizedRequest.queryString.length) {
      const queryString = normalizedRequest.queryString.map((param) => `${encodeURIComponent(param.name)}=${encodeURIComponent(param.value)}`).join("&");
      requestString = `${normalizedRequest.method} ${path}?${queryString} HTTP/1.1\r
`;
    }
    let body = "";
    if (normalizedRequest.postData) {
      if (normalizedRequest.postData.mimeType === "application/json" && normalizedRequest.postData.text) {
        headers.set("Content-Type", "application/json");
        body = normalizedRequest.postData.text;
      } else if (normalizedRequest.postData.mimeType === "application/octet-stream" && normalizedRequest.postData.text) {
        headers.set("Content-Type", "application/octet-stream");
        body = normalizedRequest.postData.text;
      } else if (normalizedRequest.postData.mimeType === "application/x-www-form-urlencoded" && normalizedRequest.postData.params) {
        const formData = normalizedRequest.postData.params.map((param) => `${encodeURIComponent(param.name)}=${encodeURIComponent(param.value ?? "")}`).join("&");
        headers.set("Content-Type", "application/x-www-form-urlencoded");
        body = formData;
      } else if (normalizedRequest.postData.mimeType === "multipart/form-data" && normalizedRequest.postData.params) {
        const boundary = "----WebKitFormBoundary7MA4YWxkTrZu0gW";
        headers.set("Content-Type", `multipart/form-data; boundary=${boundary}`);
        body = normalizedRequest.postData.params.map((param) => {
          if (param.fileName) {
            return `--${boundary}\r
Content-Disposition: form-data; name="${param.name}"; filename="${param.fileName}"\r
\r
`;
          }
          return `--${boundary}\r
Content-Disposition: form-data; name="${param.name}"\r
\r
${param.value}\r
`;
        }).join("") + `--${boundary}--\r
`;
      }
    }
    headers.forEach((value, name) => {
      requestString += `${name}: ${value}\r
`;
    });
    requestString += `\r
${body}`;
    return requestString;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/java/asynchttp/client.js
var asynchttp = {
  info: {
    key: "asynchttp",
    title: "AsyncHttp",
    link: "https://github.com/AsyncHttpClient/async-http-client",
    description: "Asynchronous Http and WebSocket Client library for Java"
  },
  convert: ({ method, allHeaders, postData, fullUrl }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { blank, push, join } = new CodeBuilder({ indent: opts.indent });
    push("AsyncHttpClient client = new DefaultAsyncHttpClient();");
    push(`client.prepare("${method.toUpperCase()}", "${fullUrl}")`);
    Object.keys(allHeaders).forEach((key) => {
      push(`.setHeader("${key}", "${escapeForDoubleQuotes(allHeaders[key])}")`, 1);
    });
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`.setBody(${JSON.stringify(postData.text)})`, 1);
    }
    push(".execute()", 1);
    push(".toCompletableFuture()", 1);
    push(".thenAccept(System.out::println)", 1);
    push(".join();", 1);
    blank();
    push("client.close();");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/java/asynchttp/asynchttp.js
var javaAsynchttp = {
  target: "java",
  client: "asynchttp",
  title: "AsyncHttp",
  generate(request) {
    return convertWithHttpSnippetLite(asynchttp, request);
  }
};

// node_modules/@scalar/snippetz/dist/utils/arrayToObject.js
function arrayToObject(items) {
  return items.reduce((acc, item) => {
    acc[item.name] = item.value;
    return acc;
  }, {});
}

// node_modules/@scalar/snippetz/dist/utils/needsQuotes.js
function needsQuotes(key) {
  return /\s|-/.test(key);
}

// node_modules/@scalar/snippetz/dist/utils/objectToString.js
function objectToString(obj, indent = 0) {
  const parts = [];
  const indentation = " ".repeat(indent);
  const innerIndentation = " ".repeat(indent + 2);
  for (const [key, value] of Object.entries(obj)) {
    const formattedKey = needsQuotes(key) ? `'${key}'` : key;
    if (Array.isArray(value)) {
      const arrayString = value.map((item) => {
        if (typeof item === "string") {
          return `'${item}'`;
        }
        if (item && typeof item === "object") {
          return objectToString(item, indent + 2);
        }
        return item;
      }).join(`, ${innerIndentation}`);
      parts.push(`${innerIndentation}${formattedKey}: [${arrayString}]`);
    } else if (value && typeof value === "object") {
      parts.push(`${innerIndentation}${formattedKey}: ${objectToString(value, indent + 2)}`);
    } else if (typeof value === "string") {
      let formattedValue = `${value}`;
      if (value.startsWith("JSON.stringify")) {
        const lines = value.split("\n");
        if (lines.length > 1) {
          formattedValue = lines.map((line, index) => {
            if (index === 0) {
              return line;
            }
            return `${innerIndentation}${line}`;
          }).join("\n");
        }
      } else {
        formattedValue = `'${value}'`;
      }
      parts.push(`${innerIndentation}${formattedKey}: ${formattedValue}`);
    } else {
      parts.push(`${innerIndentation}${formattedKey}: ${value}`);
    }
  }
  return `{
${parts.join(",\n")}
${indentation}}`;
}

// node_modules/@scalar/snippetz/dist/plugins/js/fetch/fetch.js
var jsFetch = {
  target: "js",
  client: "fetch",
  title: "Fetch",
  generate(request) {
    var _a, _b;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    const options = {
      method: normalizedRequest.method === "GET" ? void 0 : normalizedRequest.method
    };
    const searchParams = new URLSearchParams(normalizedRequest.queryString ? arrayToObject(normalizedRequest.queryString) : void 0);
    const queryString = searchParams.size ? `?${searchParams.toString()}` : "";
    if ((_a = normalizedRequest.headers) == null ? void 0 : _a.length) {
      options.headers = {};
      normalizedRequest.headers.forEach((header) => {
        options.headers[header.name] = header.value;
      });
    }
    if ((_b = normalizedRequest.cookies) == null ? void 0 : _b.length) {
      options.headers = options.headers || {};
      normalizedRequest.cookies.forEach((cookie) => {
        options.headers["Set-Cookie"] = options.headers["Set-Cookie"] ? `${options.headers["Set-Cookie"]}; ${cookie.name}=${cookie.value}` : `${cookie.name}=${cookie.value}`;
      });
    }
    Object.keys(options).forEach((key) => {
      if (options[key] === void 0) {
        delete options[key];
      }
    });
    if (normalizedRequest.postData) {
      options.body = normalizedRequest.postData.text;
      if (normalizedRequest.postData.mimeType === "application/json") {
        options.body = `JSON.stringify(${objectToString(JSON.parse(options.body))})`;
      }
    }
    const jsonOptions = Object.keys(options).length ? `, ${objectToString(options)}` : "";
    return `fetch('${normalizedRequest.url}${queryString}'${jsonOptions})`;
  }
};

// node_modules/@scalar/snippetz/node_modules/is-regexp/index.js
var { toString } = Object.prototype;
function isRegexp(value) {
  return toString.call(value) === "[object RegExp]";
}

// node_modules/@scalar/snippetz/node_modules/is-obj/index.js
function isObject(value) {
  const type = typeof value;
  return value !== null && (type === "object" || type === "function");
}

// node_modules/get-own-enumerable-keys/index.js
var { propertyIsEnumerable } = Object.prototype;
function getOwnEnumerableKeys(object) {
  return [
    ...Object.keys(object),
    ...Object.getOwnPropertySymbols(object).filter((key) => propertyIsEnumerable.call(object, key))
  ];
}

// node_modules/@scalar/snippetz/node_modules/stringify-object/index.js
function stringifyObject(input, options, pad) {
  const seen = [];
  return function stringify(input2, options2 = {}, pad2 = "") {
    const indent = options2.indent || "	";
    let tokens;
    if (options2.inlineCharacterLimit === void 0) {
      tokens = {
        newline: "\n",
        newlineOrSpace: "\n",
        pad: pad2,
        indent: pad2 + indent
      };
    } else {
      tokens = {
        newline: "@@__STRINGIFY_OBJECT_NEW_LINE__@@",
        newlineOrSpace: "@@__STRINGIFY_OBJECT_NEW_LINE_OR_SPACE__@@",
        pad: "@@__STRINGIFY_OBJECT_PAD__@@",
        indent: "@@__STRINGIFY_OBJECT_INDENT__@@"
      };
    }
    const expandWhiteSpace = (string) => {
      if (options2.inlineCharacterLimit === void 0) {
        return string;
      }
      const oneLined = string.replace(new RegExp(tokens.newline, "g"), "").replace(new RegExp(tokens.newlineOrSpace, "g"), " ").replace(new RegExp(tokens.pad + "|" + tokens.indent, "g"), "");
      if (oneLined.length <= options2.inlineCharacterLimit) {
        return oneLined;
      }
      return string.replace(new RegExp(tokens.newline + "|" + tokens.newlineOrSpace, "g"), "\n").replace(new RegExp(tokens.pad, "g"), pad2).replace(new RegExp(tokens.indent, "g"), pad2 + indent);
    };
    if (seen.includes(input2)) {
      return '"[Circular]"';
    }
    if (input2 === null || input2 === void 0 || typeof input2 === "number" || typeof input2 === "boolean" || typeof input2 === "function" || typeof input2 === "symbol" || isRegexp(input2)) {
      return String(input2);
    }
    if (input2 instanceof Date) {
      return `new Date('${input2.toISOString()}')`;
    }
    if (Array.isArray(input2)) {
      if (input2.length === 0) {
        return "[]";
      }
      seen.push(input2);
      const returnValue = "[" + tokens.newline + input2.map((element, i7) => {
        const eol = input2.length - 1 === i7 ? tokens.newline : "," + tokens.newlineOrSpace;
        let value = stringify(element, options2, pad2 + indent);
        if (options2.transform) {
          value = options2.transform(input2, i7, value);
        }
        return tokens.indent + value + eol;
      }).join("") + tokens.pad + "]";
      seen.pop();
      return expandWhiteSpace(returnValue);
    }
    if (isObject(input2)) {
      let objectKeys = getOwnEnumerableKeys(input2);
      if (options2.filter) {
        objectKeys = objectKeys.filter((element) => options2.filter(input2, element));
      }
      if (objectKeys.length === 0) {
        return "{}";
      }
      seen.push(input2);
      const returnValue = "{" + tokens.newline + objectKeys.map((element, index) => {
        const eol = objectKeys.length - 1 === index ? tokens.newline : "," + tokens.newlineOrSpace;
        const isSymbol = typeof element === "symbol";
        const isClassic = !isSymbol && /^[a-z$_][$\w]*$/i.test(element);
        const key = isSymbol || isClassic ? element : stringify(element, options2);
        let value = stringify(input2[element], options2, pad2 + indent);
        if (options2.transform) {
          value = options2.transform(input2, element, value);
        }
        return tokens.indent + String(key) + ": " + value + eol;
      }).join("") + tokens.pad + "}";
      seen.pop();
      return expandWhiteSpace(returnValue);
    }
    input2 = input2.replace(/\\/g, "\\\\");
    input2 = String(input2).replace(/[\r\n]/g, (x3) => x3 === "\n" ? "\\n" : "\\r");
    if (options2.singleQuotes === false) {
      input2 = input2.replace(/"/g, '\\"');
      return `"${input2}"`;
    }
    input2 = input2.replace(/'/g, "\\'");
    return `'${input2}'`;
  }(input, options, pad);
}

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/javascript/axios/client.js
var axios = {
  info: {
    key: "axios",
    title: "Axios",
    link: "https://github.com/axios/axios",
    description: "Promise based HTTP client for the browser and node.js"
  },
  convert: ({ allHeaders, method, url, queryObj, postData }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { blank, push, join, addPostProcessor } = new CodeBuilder({
      indent: opts.indent
    });
    push("import axios from 'axios';");
    blank();
    const requestOptions = {
      method,
      url
    };
    if (Object.keys(queryObj).length) {
      requestOptions.params = queryObj;
    }
    if (Object.keys(allHeaders).length) {
      requestOptions.headers = allHeaders;
    }
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/x-www-form-urlencoded":
        if (postData.params) {
          push("const encodedParams = new URLSearchParams();");
          postData.params.forEach((param) => {
            push(`encodedParams.set('${param.name}', '${param.value}');`);
          });
          blank();
          requestOptions.data = "encodedParams,";
          addPostProcessor((code) => code.replace(/'encodedParams,'/, "encodedParams,"));
        }
        break;
      case "application/json":
        if (postData.jsonObj) {
          requestOptions.data = postData.jsonObj;
        }
        break;
      case "multipart/form-data":
        if (!postData.params) {
          break;
        }
        push("const form = new FormData();");
        postData.params.forEach((param) => {
          push(`form.append('${param.name}', '${param.value || param.fileName || ""}');`);
        });
        blank();
        requestOptions.data = "[form]";
        break;
      default:
        if (postData === null || postData === void 0 ? void 0 : postData.text) {
          requestOptions.data = postData.text;
        }
    }
    const optionString = stringifyObject(requestOptions, {
      indent: "  ",
      inlineCharacterLimit: 80
    }).replace('"[form]"', "form");
    push(`const options = ${optionString};`);
    blank();
    push("try {");
    push("const { data } = await axios.request(options);", 1);
    push("console.log(data);", 1);
    push("} catch (error) {");
    push("console.error(error);", 1);
    push("}");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/js/axios/axios.js
var jsAxios = {
  target: "js",
  client: "axios",
  title: "Axios",
  generate(request) {
    return convertWithHttpSnippetLite(axios, request);
  }
};

// node_modules/@scalar/snippetz/dist/plugins/js/ofetch/ofetch.js
var jsOfetch = {
  target: "js",
  client: "ofetch",
  title: "ofetch",
  generate(request) {
    var _a, _b;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    const options = {
      method: normalizedRequest.method === "GET" ? void 0 : normalizedRequest.method
    };
    const searchParams = new URLSearchParams(normalizedRequest.queryString ? arrayToObject(normalizedRequest.queryString) : void 0);
    if (searchParams.size) {
      options.query = {};
      searchParams.forEach((value, key) => {
        options.query[key] = value;
      });
    }
    if ((_a = normalizedRequest.headers) == null ? void 0 : _a.length) {
      options.headers = {};
      normalizedRequest.headers.forEach((header) => {
        options.headers[header.name] = header.value;
      });
    }
    if ((_b = normalizedRequest.cookies) == null ? void 0 : _b.length) {
      options.headers = options.headers || {};
      normalizedRequest.cookies.forEach((cookie) => {
        options.headers["Set-Cookie"] = options.headers["Set-Cookie"] ? `${options.headers["Set-Cookie"]}; ${cookie.name}=${cookie.value}` : `${cookie.name}=${cookie.value}`;
      });
    }
    Object.keys(options).forEach((key) => {
      if (options[key] === void 0) {
        delete options[key];
      }
    });
    if (normalizedRequest.postData) {
      options.body = normalizedRequest.postData.text;
      if (normalizedRequest.postData.mimeType === "application/json") {
        options.body = JSON.parse(options.body);
      }
    }
    const jsonOptions = Object.keys(options).length ? `, ${objectToString(options)}` : "";
    return `import { ofetch } from 'ofetch'

ofetch('${normalizedRequest.url}'${jsonOptions})`;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/javascript/jquery/client.js
var jquery = {
  info: {
    key: "jquery",
    title: "jQuery",
    link: "http://api.jquery.com/jquery.ajax/",
    description: "Perform an asynchronous HTTP (Ajax) requests with jQuery"
  },
  convert: ({ fullUrl, method, allHeaders, postData }, options) => {
    let _a;
    const opts = {
      indent: "  ",
      ...options
    };
    const { blank, push, join } = new CodeBuilder({ indent: opts.indent });
    const settings = {
      async: true,
      crossDomain: true,
      url: fullUrl,
      method,
      headers: allHeaders
    };
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/x-www-form-urlencoded":
        settings.data = postData.paramsObj ? postData.paramsObj : postData.text;
        break;
      case "application/json":
        settings.processData = false;
        settings.data = postData.text;
        break;
      case "multipart/form-data":
        if (!postData.params) {
          break;
        }
        push("const form = new FormData();");
        postData.params.forEach((param) => {
          push(`form.append('${param.name}', '${param.value || param.fileName || ""}');`);
        });
        settings.processData = false;
        settings.contentType = false;
        settings.mimeType = "multipart/form-data";
        settings.data = "[form]";
        if (hasHeader(allHeaders, "content-type")) {
          if ((_a = getHeader(allHeaders, "content-type")) === null || _a === void 0 ? void 0 : _a.includes("boundary")) {
            const headerName = getHeaderName(allHeaders, "content-type");
            if (headerName) {
              delete settings.headers[headerName];
            }
          }
        }
        blank();
        break;
      default:
        if (postData === null || postData === void 0 ? void 0 : postData.text) {
          settings.data = postData.text;
        }
    }
    const stringifiedSettings = stringifyObject(settings, {
      indent: opts.indent
    }).replace("'[form]'", "form");
    push(`const settings = ${stringifiedSettings};`);
    blank();
    push("$.ajax(settings).done(function (response) {");
    push("console.log(response);", 1);
    push("});");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/js/jquery/jquery.js
var jsJquery = {
  target: "js",
  client: "jquery",
  title: "jQuery",
  generate(request) {
    return convertWithHttpSnippetLite(jquery, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/javascript/xhr/client.js
var xhr = {
  info: {
    key: "xhr",
    title: "XMLHttpRequest",
    link: "https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest",
    description: "W3C Standard API that provides scripted client functionality"
  },
  convert: ({ postData, allHeaders, method, fullUrl }, options) => {
    let _a;
    const opts = {
      indent: "  ",
      cors: true,
      ...options
    };
    const { blank, push, join } = new CodeBuilder({ indent: opts.indent });
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/json":
        push(`const data = JSON.stringify(${stringifyObject(postData.jsonObj, {
          indent: opts.indent
        })});`);
        blank();
        break;
      case "multipart/form-data":
        if (!postData.params) {
          break;
        }
        push("const data = new FormData();");
        postData.params.forEach((param) => {
          push(`data.append('${param.name}', '${param.value || param.fileName || ""}');`);
        });
        if (hasHeader(allHeaders, "content-type")) {
          if ((_a = getHeader(allHeaders, "content-type")) === null || _a === void 0 ? void 0 : _a.includes("boundary")) {
            const headerName = getHeaderName(allHeaders, "content-type");
            if (headerName) {
              delete allHeaders[headerName];
            }
          }
        }
        blank();
        break;
      default:
        push(`const data = ${(postData === null || postData === void 0 ? void 0 : postData.text) ? `'${postData.text}'` : "null"};`);
        blank();
    }
    push("const xhr = new XMLHttpRequest();");
    if (opts.cors) {
      push("xhr.withCredentials = true;");
    }
    blank();
    push("xhr.addEventListener('readystatechange', function () {");
    push("if (this.readyState === this.DONE) {", 1);
    push("console.log(this.responseText);", 2);
    push("}", 1);
    push("});");
    blank();
    push(`xhr.open('${method}', '${fullUrl}');`);
    Object.keys(allHeaders).forEach((key) => {
      push(`xhr.setRequestHeader('${key}', '${escapeForSingleQuotes(allHeaders[key])}');`);
    });
    blank();
    push("xhr.send(data);");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/js/xhr/xhr.js
var jsXhr = {
  target: "js",
  client: "xhr",
  title: "XHR",
  generate(request) {
    return convertWithHttpSnippetLite(xhr, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/kotlin/okhttp/client.js
var okhttp2 = {
  info: {
    key: "okhttp",
    title: "OkHttp",
    link: "http://square.github.io/okhttp/",
    description: "An HTTP Request Client Library"
  },
  convert: ({ postData, fullUrl, method, allHeaders }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { blank, join, push } = new CodeBuilder({ indent: opts.indent });
    const methods = ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD"];
    const methodsWithBody = ["POST", "PUT", "DELETE", "PATCH"];
    push("val client = OkHttpClient()");
    blank();
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      if (postData.boundary) {
        push(`val mediaType = MediaType.parse("${postData.mimeType}; boundary=${postData.boundary}")`);
      } else {
        push(`val mediaType = MediaType.parse("${postData.mimeType}")`);
      }
      push(`val body = RequestBody.create(mediaType, ${JSON.stringify(postData.text)})`);
    }
    push("val request = Request.Builder()");
    push(`.url("${fullUrl}")`, 1);
    if (!methods.includes(method.toUpperCase())) {
      if (postData === null || postData === void 0 ? void 0 : postData.text) {
        push(`.method("${method.toUpperCase()}", body)`, 1);
      } else {
        push(`.method("${method.toUpperCase()}", null)`, 1);
      }
    } else if (methodsWithBody.includes(method.toUpperCase())) {
      if (postData === null || postData === void 0 ? void 0 : postData.text) {
        push(`.${method.toLowerCase()}(body)`, 1);
      } else {
        push(`.${method.toLowerCase()}(null)`, 1);
      }
    } else {
      push(`.${method.toLowerCase()}()`, 1);
    }
    Object.keys(allHeaders).forEach((key) => {
      push(`.addHeader("${key}", "${escapeForDoubleQuotes(allHeaders[key])}")`, 1);
    });
    push(".build()", 1);
    blank();
    push("val response = client.newCall(request).execute()");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/kotlin/okhttp/okhttp.js
var kotlinOkhttp = {
  target: "kotlin",
  client: "okhttp",
  title: "OkHttp",
  generate(request) {
    return convertWithHttpSnippetLite(okhttp2, request);
  }
};

// node_modules/@scalar/snippetz/dist/plugins/node/fetch/fetch.js
var nodeFetch = {
  target: "node",
  client: "fetch",
  title: "Fetch",
  generate(request) {
    var _a, _b;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    const options = {
      method: normalizedRequest.method === "GET" ? void 0 : normalizedRequest.method
    };
    const searchParams = new URLSearchParams(normalizedRequest.queryString ? arrayToObject(normalizedRequest.queryString) : void 0);
    const queryString = searchParams.size ? `?${searchParams.toString()}` : "";
    if ((_a = normalizedRequest.headers) == null ? void 0 : _a.length) {
      options.headers = {};
      normalizedRequest.headers.forEach((header) => {
        options.headers[header.name] = header.value;
      });
    }
    if ((_b = normalizedRequest.cookies) == null ? void 0 : _b.length) {
      options.headers = options.headers || {};
      normalizedRequest.cookies.forEach((cookie) => {
        options.headers["Set-Cookie"] = options.headers["Set-Cookie"] ? `${options.headers["Set-Cookie"]}; ${cookie.name}=${cookie.value}` : `${cookie.name}=${cookie.value}`;
      });
    }
    Object.keys(options).forEach((key) => {
      if (options[key] === void 0) {
        delete options[key];
      }
    });
    if (normalizedRequest.postData) {
      options.body = normalizedRequest.postData.text;
      if (normalizedRequest.postData.mimeType === "application/json") {
        options.body = `JSON.stringify(${objectToString(JSON.parse(options.body))})`;
      }
    }
    const jsonOptions = Object.keys(options).length ? `, ${objectToString(options)}` : "";
    return `fetch('${normalizedRequest.url}${queryString}'${jsonOptions})`;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/node/axios/client.js
var axios2 = {
  info: {
    key: "axios",
    title: "Axios",
    link: "https://github.com/axios/axios",
    description: "Promise based HTTP client for the browser and node.js"
  },
  convert: ({ method, url, queryObj, allHeaders, postData }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const { blank, join, push, addPostProcessor } = new CodeBuilder({
      indent: opts.indent
    });
    push("const axios = require('axios').default;");
    const reqOpts = {
      method,
      url
    };
    if (Object.keys(queryObj).length) {
      reqOpts.params = queryObj;
    }
    if (Object.keys(allHeaders).length) {
      reqOpts.headers = allHeaders;
    }
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/x-www-form-urlencoded":
        if (postData.params) {
          push("const { URLSearchParams } = require('url');");
          blank();
          push("const encodedParams = new URLSearchParams();");
          postData.params.forEach((param) => {
            push(`encodedParams.set('${param.name}', '${param.value}');`);
          });
          blank();
          reqOpts.data = "encodedParams,";
          addPostProcessor((code) => code.replace(/'encodedParams,'/, "encodedParams,"));
        }
        break;
      case "application/json":
        blank();
        if (postData.jsonObj) {
          reqOpts.data = postData.jsonObj;
        }
        break;
      default:
        blank();
        if (postData === null || postData === void 0 ? void 0 : postData.text) {
          reqOpts.data = postData.text;
        }
    }
    const stringifiedOptions = stringifyObject(reqOpts, {
      indent: "  ",
      inlineCharacterLimit: 80
    });
    push(`const options = ${stringifiedOptions};`);
    blank();
    push("try {");
    push("const { data } = await axios.request(options);", 1);
    push("console.log(data);", 1);
    push("} catch (error) {");
    push("console.error(error);", 1);
    push("}");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/node/axios/axios.js
var nodeAxios = {
  target: "node",
  client: "axios",
  title: "Axios",
  generate(request) {
    return convertWithHttpSnippetLite(axios2, request);
  }
};

// node_modules/@scalar/snippetz/dist/plugins/node/ofetch/ofetch.js
var nodeOfetch = {
  target: "node",
  client: "ofetch",
  title: "ofetch",
  generate(request) {
    var _a, _b;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    const options = {
      method: normalizedRequest.method === "GET" ? void 0 : normalizedRequest.method
    };
    const searchParams = new URLSearchParams(normalizedRequest.queryString ? arrayToObject(normalizedRequest.queryString) : void 0);
    if (searchParams.size) {
      options.query = {};
      searchParams.forEach((value, key) => {
        options.query[key] = value;
      });
    }
    if ((_a = normalizedRequest.headers) == null ? void 0 : _a.length) {
      options.headers = {};
      normalizedRequest.headers.forEach((header) => {
        options.headers[header.name] = header.value;
      });
    }
    if ((_b = normalizedRequest.cookies) == null ? void 0 : _b.length) {
      options.headers = options.headers || {};
      normalizedRequest.cookies.forEach((cookie) => {
        options.headers["Set-Cookie"] = options.headers["Set-Cookie"] ? `${options.headers["Set-Cookie"]}; ${cookie.name}=${cookie.value}` : `${cookie.name}=${cookie.value}`;
      });
    }
    Object.keys(options).forEach((key) => {
      if (options[key] === void 0) {
        delete options[key];
      }
    });
    if (normalizedRequest.postData) {
      options.body = normalizedRequest.postData.text;
      if (normalizedRequest.postData.mimeType === "application/json") {
        options.body = JSON.parse(options.body);
      }
    }
    const jsonOptions = Object.keys(options).length ? `, ${objectToString(options)}` : "";
    return `import { ofetch } from 'ofetch'

ofetch('${normalizedRequest.url}'${jsonOptions})`;
  }
};

// node_modules/@scalar/snippetz/dist/plugins/node/undici/undici.js
var nodeUndici = {
  target: "node",
  client: "undici",
  title: "undici",
  generate(request) {
    var _a, _b;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    const options = {
      method: normalizedRequest.method === "GET" ? void 0 : normalizedRequest.method
    };
    const searchParams = new URLSearchParams(normalizedRequest.queryString ? arrayToObject(normalizedRequest.queryString) : void 0);
    const queryString = searchParams.size ? `?${searchParams.toString()}` : "";
    if ((_a = normalizedRequest.headers) == null ? void 0 : _a.length) {
      options.headers = {};
      normalizedRequest.headers.forEach((header) => {
        options.headers[header.name] = header.value;
      });
    }
    if ((_b = normalizedRequest.cookies) == null ? void 0 : _b.length) {
      options.headers = options.headers || {};
      normalizedRequest.cookies.forEach((cookie) => {
        options.headers["Set-Cookie"] = options.headers["Set-Cookie"] ? `${options.headers["Set-Cookie"]}; ${cookie.name}=${cookie.value}` : `${cookie.name}=${cookie.value}`;
      });
    }
    Object.keys(options).forEach((key) => {
      if (options[key] === void 0) {
        delete options[key];
      }
    });
    if (normalizedRequest.postData) {
      options.body = normalizedRequest.postData.text;
      if (normalizedRequest.postData.mimeType === "application/json") {
        options.body = `JSON.stringify(${objectToString(JSON.parse(options.body))})`;
      }
    }
    const jsonOptions = Object.keys(options).length ? `, ${objectToString(options)}` : "";
    return `import { request } from 'undici'

const { statusCode, body } = await request('${normalizedRequest.url}${queryString}'${jsonOptions})`;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/objc/helpers.js
var nsDeclaration = (nsClass, name, parameters, indent) => {
  const opening = `${nsClass} *${name} = `;
  const literal = literalRepresentation(parameters, indent ? opening.length : void 0);
  return `${opening}${literal};`;
};
var literalRepresentation = (value, indentation) => {
  const join = indentation === void 0 ? ", " : `,
   ${" ".repeat(indentation)}`;
  switch (Object.prototype.toString.call(value)) {
    case "[object Number]":
      return `@${value}`;
    case "[object Array]": {
      const valuesRepresentation = value.map((value2) => literalRepresentation(value2));
      return `@[ ${valuesRepresentation.join(join)} ]`;
    }
    case "[object Object]": {
      const keyValuePairs = [];
      for (const key in value) {
        keyValuePairs.push(`@"${key}": ${literalRepresentation(value[key])}`);
      }
      return `@{ ${keyValuePairs.join(join)} }`;
    }
    case "[object Boolean]":
      return value ? "@YES" : "@NO";
    default:
      if (value === null || value === void 0) {
        return "";
      }
      return `@"${value.toString().replace(/"/g, '\\"')}"`;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/objc/nsurlsession/client.js
var nsurlsession = {
  info: {
    key: "nsurlsession",
    title: "NSURLSession",
    link: "https://developer.apple.com/library/mac/documentation/Foundation/Reference/NSURLSession_class/index.html",
    description: "Foundation's NSURLSession request"
  },
  convert: ({ allHeaders, postData, method, fullUrl }, options) => {
    let _a;
    const opts = {
      indent: "    ",
      pretty: true,
      timeout: 10,
      ...options
    };
    const { push, join, blank } = new CodeBuilder({ indent: opts.indent });
    const req = {
      hasHeaders: false,
      hasBody: false
    };
    push("#import <Foundation/Foundation.h>");
    if (Object.keys(allHeaders).length) {
      req.hasHeaders = true;
      blank();
      push(nsDeclaration("NSDictionary", "headers", allHeaders, opts.pretty));
    }
    if (postData && (postData.text || postData.jsonObj || postData.params)) {
      req.hasBody = true;
      switch (postData.mimeType) {
        case "application/x-www-form-urlencoded":
          if ((_a = postData.params) === null || _a === void 0 ? void 0 : _a.length) {
            blank();
            const [head, ...tail] = postData.params;
            push(`NSMutableData *postData = [[NSMutableData alloc] initWithData:[@"${head.name}=${head.value}" dataUsingEncoding:NSUTF8StringEncoding]];`);
            tail.forEach(({ name, value }) => {
              push(`[postData appendData:[@"&${name}=${value}" dataUsingEncoding:NSUTF8StringEncoding]];`);
            });
          } else {
            req.hasBody = false;
          }
          break;
        case "application/json":
          if (postData.jsonObj) {
            push(nsDeclaration("NSDictionary", "parameters", postData.jsonObj, opts.pretty));
            blank();
            push("NSData *postData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];");
          }
          break;
        case "multipart/form-data":
          push(nsDeclaration("NSArray", "parameters", postData.params || [], opts.pretty));
          push(`NSString *boundary = @"${postData.boundary}";`);
          blank();
          push("NSError *error;");
          push("NSMutableString *body = [NSMutableString string];");
          push("for (NSDictionary *param in parameters) {");
          push('[body appendFormat:@"--%@\\r\\n", boundary];', 1);
          push('if (param[@"fileName"]) {', 1);
          push('[body appendFormat:@"Content-Disposition:form-data; name=\\"%@\\"; filename=\\"%@\\"\\r\\n", param[@"name"], param[@"fileName"]];', 2);
          push('[body appendFormat:@"Content-Type: %@\\r\\n\\r\\n", param[@"contentType"]];', 2);
          push('[body appendFormat:@"%@", [NSString stringWithContentsOfFile:param[@"fileName"] encoding:NSUTF8StringEncoding error:&error]];', 2);
          push("if (error) {", 2);
          push('NSLog(@"%@", error);', 3);
          push("}", 2);
          push("} else {", 1);
          push('[body appendFormat:@"Content-Disposition:form-data; name=\\"%@\\"\\r\\n\\r\\n", param[@"name"]];', 2);
          push('[body appendFormat:@"%@", param[@"value"]];', 2);
          push("}", 1);
          push("}");
          push('[body appendFormat:@"\\r\\n--%@--\\r\\n", boundary];');
          push("NSData *postData = [body dataUsingEncoding:NSUTF8StringEncoding];");
          break;
        default:
          blank();
          push(`NSData *postData = [[NSData alloc] initWithData:[@"${postData.text}" dataUsingEncoding:NSUTF8StringEncoding]];`);
      }
    }
    blank();
    push(`NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@"${fullUrl}"]`);
    push("                                                       cachePolicy:NSURLRequestUseProtocolCachePolicy");
    push(`                                                   timeoutInterval:${opts.timeout.toFixed(1)}];`);
    push(`[request setHTTPMethod:@"${method}"];`);
    if (req.hasHeaders) {
      push("[request setAllHTTPHeaderFields:headers];");
    }
    if (req.hasBody) {
      push("[request setHTTPBody:postData];");
    }
    blank();
    push("NSURLSession *session = [NSURLSession sharedSession];");
    push("NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request");
    push("                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {");
    push("                                            if (error) {", 1);
    push('                                            NSLog(@"%@", error);', 2);
    push("                                            } else {", 1);
    push("                                            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *) response;", 2);
    push('                                            NSLog(@"%@", httpResponse);', 2);
    push("                                            }", 1);
    push("                                            }];");
    push("[dataTask resume];");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/objc/nsurlsession/nsurlsession.js
var objcNsurlsession = {
  target: "objc",
  client: "nsurlsession",
  title: "NSURLSession",
  generate(request) {
    return convertWithHttpSnippetLite(nsurlsession, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/ocaml/cohttp/client.js
var cohttp = {
  info: {
    key: "cohttp",
    title: "CoHTTP",
    link: "https://github.com/mirage/ocaml-cohttp",
    description: "Cohttp is a very lightweight HTTP server using Lwt or Async for OCaml"
  },
  convert: ({ fullUrl, allHeaders, postData, method }, options) => {
    const opts = {
      indent: "  ",
      ...options
    };
    const methods = ["get", "post", "head", "delete", "patch", "put", "options"];
    const { push, blank, join } = new CodeBuilder({ indent: opts.indent });
    push("open Cohttp_lwt_unix");
    push("open Cohttp");
    push("open Lwt");
    blank();
    push(`let uri = Uri.of_string "${fullUrl}" in`);
    const headers = Object.keys(allHeaders);
    if (headers.length === 1) {
      push(`let headers = Header.add (Header.init ()) "${headers[0]}" "${escapeForDoubleQuotes(allHeaders[headers[0]])}" in`);
    } else if (headers.length > 1) {
      push("let headers = Header.add_list (Header.init ()) [");
      headers.forEach((key) => {
        push(`("${key}", "${escapeForDoubleQuotes(allHeaders[key])}");`, 1);
      });
      push("] in");
    }
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`let body = Cohttp_lwt_body.of_string ${JSON.stringify(postData.text)} in`);
    }
    blank();
    const h2 = headers.length ? "~headers " : "";
    const b4 = (postData === null || postData === void 0 ? void 0 : postData.text) ? "~body " : "";
    const m7 = methods.includes(method.toLowerCase()) ? `\`${method.toUpperCase()}` : `(Code.method_of_string "${method}")`;
    push(`Client.call ${h2}${b4}${m7} uri`);
    push(">>= fun (res, body_stream) ->");
    push("(* Do stuff with the result *)", 1);
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/ocaml/cohttp/cohttp.js
var ocamlCohttp = {
  target: "ocaml",
  client: "cohttp",
  title: "Cohttp",
  generate(request) {
    return convertWithHttpSnippetLite(cohttp, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/php/helpers.js
var convertType = (obj, indent, lastIndent) => {
  lastIndent = lastIndent || "";
  indent = indent || "";
  switch (Object.prototype.toString.call(obj)) {
    case "[object Null]":
      return "null";
    case "[object Undefined]":
      return "null";
    case "[object String]":
      return `'${escapeString(obj, { delimiter: "'", escapeNewlines: false })}'`;
    case "[object Number]":
      return obj.toString();
    case "[object Array]": {
      const contents = obj.map((item) => convertType(item, `${indent}${indent}`, indent)).join(`,
${indent}`);
      return `[
${indent}${contents}
${lastIndent}]`;
    }
    case "[object Object]": {
      const result = [];
      for (const i7 in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, i7)) {
          result.push(`${convertType(i7, indent)} => ${convertType(obj[i7], `${indent}${indent}`, indent)}`);
        }
      }
      return `[
${indent}${result.join(`,
${indent}`)}
${lastIndent}]`;
    }
    default:
      return "null";
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/php/curl/client.js
var curl = {
  info: {
    key: "curl",
    title: "cURL",
    link: "http://php.net/manual/en/book.curl.php",
    description: "PHP with ext-curl"
  },
  convert: ({ uriObj, postData, fullUrl, method, httpVersion, cookies, headersObj }, options = {}) => {
    const { closingTag = false, indent = "  ", maxRedirects = 10, namedErrors = false, noTags = false, shortTags = false, timeout = 30 } = options;
    const { push, blank, join } = new CodeBuilder({ indent });
    if (!noTags) {
      push(shortTags ? "<?" : "<?php");
      blank();
    }
    push("$curl = curl_init();");
    blank();
    const curlOptions = [
      {
        escape: true,
        name: "CURLOPT_PORT",
        value: uriObj.port === "" ? null : uriObj.port
      },
      {
        escape: true,
        name: "CURLOPT_URL",
        value: fullUrl
      },
      {
        escape: false,
        name: "CURLOPT_RETURNTRANSFER",
        value: "true"
      },
      {
        escape: true,
        name: "CURLOPT_ENCODING",
        value: ""
      },
      {
        escape: false,
        name: "CURLOPT_MAXREDIRS",
        value: maxRedirects
      },
      {
        escape: false,
        name: "CURLOPT_TIMEOUT",
        value: timeout
      },
      {
        escape: false,
        name: "CURLOPT_HTTP_VERSION",
        value: httpVersion === "HTTP/1.0" ? "CURL_HTTP_VERSION_1_0" : "CURL_HTTP_VERSION_1_1"
      },
      {
        escape: true,
        name: "CURLOPT_CUSTOMREQUEST",
        value: method
      },
      {
        escape: !(postData === null || postData === void 0 ? void 0 : postData.jsonObj),
        name: "CURLOPT_POSTFIELDS",
        value: postData ? postData.jsonObj ? `json_encode(${convertType(postData.jsonObj, indent.repeat(2), indent)})` : postData.text : void 0
      }
    ];
    push("curl_setopt_array($curl, [");
    const curlopts = new CodeBuilder({ indent, join: `
${indent}` });
    curlOptions.forEach(({ value, name, escape: escape2 }) => {
      if (value !== null && value !== void 0) {
        curlopts.push(`${name} => ${escape2 ? JSON.stringify(value) : value},`);
      }
    });
    const curlCookies = cookies.map((cookie) => `${encodeURIComponent(cookie.name)}=${encodeURIComponent(cookie.value)}`);
    if (curlCookies.length) {
      curlopts.push(`CURLOPT_COOKIE => "${curlCookies.join("; ")}",`);
    }
    const headers = Object.keys(headersObj).sort().map((key) => `"${key}: ${escapeForDoubleQuotes(headersObj[key])}"`);
    if (headers.length) {
      curlopts.push("CURLOPT_HTTPHEADER => [");
      curlopts.push(headers.join(`,
${indent}${indent}`), 1);
      curlopts.push("],");
    }
    push(curlopts.join(), 1);
    push("]);");
    blank();
    push("$response = curl_exec($curl);");
    push("$err = curl_error($curl);");
    blank();
    push("curl_close($curl);");
    blank();
    push("if ($err) {");
    if (namedErrors) {
      push('echo array_flip(get_defined_constants(true)["curl"])[$err];', 1);
    } else {
      push('echo "cURL Error #:" . $err;', 1);
    }
    push("} else {");
    push("echo $response;", 1);
    push("}");
    if (!noTags && closingTag) {
      blank();
      push("?>");
    }
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/php/curl/curl.js
var phpCurl = {
  target: "php",
  client: "curl",
  title: "cURL",
  generate(request) {
    return convertWithHttpSnippetLite(curl, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/php/guzzle/client.js
var guzzle = {
  info: {
    key: "guzzle",
    title: "Guzzle",
    link: "http://docs.guzzlephp.org/en/stable/",
    description: "PHP with Guzzle"
  },
  convert: ({ postData, fullUrl, method, cookies, headersObj }, options) => {
    let _a;
    const opts = {
      closingTag: false,
      indent: "  ",
      noTags: false,
      shortTags: false,
      ...options
    };
    const { push, blank, join } = new CodeBuilder({ indent: opts.indent });
    const { code: requestCode, push: requestPush, join: requestJoin } = new CodeBuilder({ indent: opts.indent });
    if (!opts.noTags) {
      push(opts.shortTags ? "<?" : "<?php");
      blank();
    }
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/x-www-form-urlencoded":
        requestPush(`'form_params' => ${convertType(postData.paramsObj, opts.indent + opts.indent, opts.indent)},`, 1);
        break;
      case "multipart/form-data": {
        const fields = [];
        if (postData.params) {
          postData.params.forEach((param) => {
            if (param.fileName) {
              const field = {
                name: param.name,
                filename: param.fileName,
                contents: param.value
              };
              if (param.contentType) {
                field.headers = { "Content-Type": param.contentType };
              }
              fields.push(field);
            } else if (param.value) {
              fields.push({
                name: param.name,
                contents: param.value
              });
            }
          });
        }
        if (fields.length) {
          requestPush(`'multipart' => ${convertType(fields, opts.indent + opts.indent, opts.indent)}`, 1);
          if (hasHeader(headersObj, "content-type")) {
            if ((_a = getHeader(headersObj, "content-type")) === null || _a === void 0 ? void 0 : _a.indexOf("boundary")) {
              const headerName = getHeaderName(headersObj, "content-type");
              if (headerName) {
                delete headersObj[headerName];
              }
            }
          }
        }
        break;
      }
      default:
        if (postData === null || postData === void 0 ? void 0 : postData.text) {
          requestPush(`'body' => ${convertType(postData.text)},`, 1);
        }
    }
    const headers = Object.keys(headersObj).sort().map((key) => `${opts.indent}${opts.indent}'${key}' => '${escapeForSingleQuotes(headersObj[key])}',`);
    const cookieString = cookies.map((cookie) => `${encodeURIComponent(cookie.name)}=${encodeURIComponent(cookie.value)}`).join("; ");
    if (cookieString.length) {
      headers.push(`${opts.indent}${opts.indent}'cookie' => '${escapeForSingleQuotes(cookieString)}',`);
    }
    if (headers.length) {
      requestPush("'headers' => [", 1);
      requestPush(headers.join("\n"));
      requestPush("],", 1);
    }
    push("$client = new \\GuzzleHttp\\Client();");
    blank();
    if (requestCode.length) {
      push(`$response = $client->request('${method}', '${fullUrl}', [`);
      push(requestJoin());
      push("]);");
    } else {
      push(`$response = $client->request('${method}', '${fullUrl}');`);
    }
    blank();
    push("echo $response->getBody();");
    if (!opts.noTags && opts.closingTag) {
      blank();
      push("?>");
    }
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/php/guzzle/guzzle.js
var phpGuzzle = {
  target: "php",
  client: "guzzle",
  title: "Guzzle",
  generate(request) {
    return convertWithHttpSnippetLite(guzzle, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/powershell/common.js
var generatePowershellConvert = (command) => {
  const convert = ({ method, headersObj, cookies, uriObj, fullUrl, postData, allHeaders }) => {
    const { push, join } = new CodeBuilder();
    const methods = ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"];
    if (!methods.includes(method.toUpperCase())) {
      return "Method not supported";
    }
    const commandOptions = [];
    const headers = Object.keys(headersObj);
    if (headers.length) {
      push("$headers=@{}");
      headers.forEach((key) => {
        if (key !== "connection") {
          push(`$headers.Add("${key}", "${escapeString(headersObj[key], { escapeChar: "`" })}")`);
        }
      });
      commandOptions.push("-Headers $headers");
    }
    if (cookies.length) {
      push("$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession");
      cookies.forEach((cookie) => {
        push("$cookie = New-Object System.Net.Cookie");
        push(`$cookie.Name = '${cookie.name}'`);
        push(`$cookie.Value = '${cookie.value}'`);
        push(`$cookie.Domain = '${uriObj.host}'`);
        push("$session.Cookies.Add($cookie)");
      });
      commandOptions.push("-WebSession $session");
    }
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      commandOptions.push(`-ContentType '${escapeString(getHeader(allHeaders, "content-type"), {
        delimiter: "'",
        escapeChar: "`"
      })}'`);
      commandOptions.push(`-Body '${postData.text}'`);
    }
    push(`$response = ${command} -Uri '${fullUrl}' -Method ${method} ${commandOptions.join(" ")}`);
    return join();
  };
  return convert;
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/powershell/webrequest/client.js
var webrequest = {
  info: {
    key: "webrequest",
    title: "Invoke-WebRequest",
    link: "https://docs.microsoft.com/en-us/powershell/module/Microsoft.PowerShell.Utility/Invoke-WebRequest",
    description: "Powershell Invoke-WebRequest client"
  },
  convert: generatePowershellConvert("Invoke-WebRequest")
};

// node_modules/@scalar/snippetz/dist/plugins/powershell/webrequest/webrequest.js
var powershellWebrequest = {
  target: "powershell",
  client: "webrequest",
  title: "Invoke-WebRequest",
  generate(request) {
    return convertWithHttpSnippetLite(webrequest, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/powershell/restmethod/client.js
var restmethod = {
  info: {
    key: "restmethod",
    title: "Invoke-RestMethod",
    link: "https://docs.microsoft.com/en-us/powershell/module/Microsoft.PowerShell.Utility/Invoke-RestMethod",
    description: "Powershell Invoke-RestMethod client"
  },
  convert: generatePowershellConvert("Invoke-RestMethod")
};

// node_modules/@scalar/snippetz/dist/plugins/powershell/restmethod/restmethod.js
var powershellRestmethod = {
  target: "powershell",
  client: "restmethod",
  title: "Invoke-RestMethod",
  generate(request) {
    return convertWithHttpSnippetLite(restmethod, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/python/python3/client.js
var python3 = {
  info: {
    key: "python3",
    title: "http.client",
    link: "https://docs.python.org/3/library/http.client.html",
    description: "Python3 HTTP Client"
  },
  convert: ({ uriObj: { path, protocol, host }, postData, allHeaders, method }, options = {}) => {
    const { insecureSkipVerify = false } = options;
    const { push, blank, join } = new CodeBuilder();
    push("import http.client");
    if (insecureSkipVerify) {
      push("import ssl");
    }
    blank();
    if (protocol === "https:") {
      const sslContext = insecureSkipVerify ? ", context = ssl._create_unverified_context()" : "";
      push(`conn = http.client.HTTPSConnection("${host}"${sslContext})`);
      blank();
    } else {
      push(`conn = http.client.HTTPConnection("${host}")`);
      blank();
    }
    const payload = JSON.stringify(postData === null || postData === void 0 ? void 0 : postData.text);
    if (payload) {
      push(`payload = ${payload}`);
      blank();
    }
    const headers = allHeaders;
    const headerCount = Object.keys(headers).length;
    if (headerCount === 1) {
      for (const header in headers) {
        push(`headers = { '${header}': "${escapeForDoubleQuotes(headers[header])}" }`);
        blank();
      }
    } else if (headerCount > 1) {
      let count = 1;
      push("headers = {");
      for (const header in headers) {
        if (count++ !== headerCount) {
          push(`    '${header}': "${escapeForDoubleQuotes(headers[header])}",`);
        } else {
          push(`    '${header}': "${escapeForDoubleQuotes(headers[header])}"`);
        }
      }
      push("}");
      blank();
    }
    if (payload && headerCount) {
      push(`conn.request("${method}", "${path}", payload, headers)`);
    } else if (payload && !headerCount) {
      push(`conn.request("${method}", "${path}", payload)`);
    } else if (!payload && headerCount) {
      push(`conn.request("${method}", "${path}", headers=headers)`);
    } else {
      push(`conn.request("${method}", "${path}")`);
    }
    blank();
    push("res = conn.getresponse()");
    push("data = res.read()");
    blank();
    push('print(data.decode("utf-8"))');
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/python/python3/python3.js
var pythonPython3 = {
  target: "python",
  client: "python3",
  title: "http.client",
  generate(request) {
    return convertWithHttpSnippetLite(python3, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/python/helpers.js
function concatValues(concatType, values, pretty, indentation, indentLevel) {
  const currentIndent = indentation.repeat(indentLevel);
  const closingBraceIndent = indentation.repeat(indentLevel - 1);
  const join = pretty ? `,
${currentIndent}` : ", ";
  const openingBrace = concatType === "object" ? "{" : "[";
  const closingBrace = concatType === "object" ? "}" : "]";
  if (pretty) {
    return `${openingBrace}
${currentIndent}${values.join(join)}
${closingBraceIndent}${closingBrace}`;
  }
  if (concatType === "object" && values.length > 0) {
    return `${openingBrace} ${values.join(join)} ${closingBrace}`;
  }
  return `${openingBrace}${values.join(join)}${closingBrace}`;
}
var literalRepresentation2 = (value, opts, indentLevel) => {
  indentLevel = indentLevel === void 0 ? 1 : indentLevel + 1;
  switch (Object.prototype.toString.call(value)) {
    case "[object Number]":
      return value;
    case "[object Array]": {
      let pretty = false;
      const valuesRepresentation = value.map((v5) => {
        if (Object.prototype.toString.call(v5) === "[object Object]") {
          pretty = Object.keys(v5).length > 1;
        }
        return literalRepresentation2(v5, opts, indentLevel);
      });
      return concatValues("array", valuesRepresentation, pretty, opts.indent, indentLevel);
    }
    case "[object Object]": {
      const keyValuePairs = [];
      for (const key in value) {
        keyValuePairs.push(`"${key}": ${literalRepresentation2(value[key], opts, indentLevel)}`);
      }
      return concatValues("object", keyValuePairs, opts.pretty && keyValuePairs.length > 1, opts.indent, indentLevel);
    }
    case "[object Null]":
      return "None";
    case "[object Boolean]":
      return value ? "True" : "False";
    default:
      if (value === null || value === void 0) {
        return "";
      }
      return `"${value.toString().replace(/"/g, '\\"')}"`;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/python/requests/client.js
var builtInMethods = [
  "HEAD",
  "GET",
  "POST",
  "PUT",
  "PATCH",
  "DELETE",
  "OPTIONS"
];
var requests = {
  info: {
    key: "requests",
    title: "Requests",
    link: "http://docs.python-requests.org/en/latest/api/#requests.request",
    description: "Requests HTTP library"
  },
  convert: ({ queryObj, url, postData, allHeaders, method }, options) => {
    const opts = {
      indent: "    ",
      pretty: true,
      ...options
    };
    const { push, blank, join } = new CodeBuilder({ indent: opts.indent });
    push("import requests");
    blank();
    push(`url = "${url}"`);
    blank();
    let qs;
    if (Object.keys(queryObj).length) {
      qs = `querystring = ${JSON.stringify(queryObj)}`;
      push(qs);
      blank();
    }
    const headers = allHeaders;
    let payload = {};
    const files = {};
    let hasFiles = false;
    let hasPayload = false;
    let jsonPayload = false;
    switch (postData === null || postData === void 0 ? void 0 : postData.mimeType) {
      case "application/json":
        if (postData.jsonObj) {
          push(`payload = ${literalRepresentation2(postData.jsonObj, opts)}`);
          jsonPayload = true;
          hasPayload = true;
        }
        break;
      case "multipart/form-data":
        if (!postData.params) {
          break;
        }
        payload = {};
        postData.params.forEach((p3) => {
          if (p3.fileName) {
            files[p3.name] = `open('${p3.fileName}', 'rb')`;
            hasFiles = true;
          } else {
            payload[p3.name] = p3.value;
            hasPayload = true;
          }
        });
        if (hasFiles) {
          push(`files = ${literalRepresentation2(files, opts)}`);
          if (hasPayload) {
            push(`payload = ${literalRepresentation2(payload, opts)}`);
          }
          const headerName = getHeaderName(headers, "content-type");
          if (headerName) {
            delete headers[headerName];
          }
        } else {
          const nonFilePayload = JSON.stringify(postData.text);
          if (nonFilePayload) {
            push(`payload = ${nonFilePayload}`);
            hasPayload = true;
          }
        }
        break;
      default: {
        if (!postData) {
          break;
        }
        if (postData.mimeType === "application/x-www-form-urlencoded" && postData.paramsObj) {
          push(`payload = ${literalRepresentation2(postData.paramsObj, opts)}`);
          hasPayload = true;
          break;
        }
        const payload2 = JSON.stringify(postData.text);
        if (payload2) {
          push(`payload = ${payload2}`);
          hasPayload = true;
        }
      }
    }
    const headerCount = Object.keys(headers).length;
    if (headerCount === 0 && (hasPayload || hasFiles)) {
      blank();
    } else if (headerCount === 1) {
      for (const header in headers) {
        push(`headers = {"${header}": "${escapeForDoubleQuotes(headers[header])}"}`);
        blank();
      }
    } else if (headerCount > 1) {
      let count = 1;
      push("headers = {");
      for (const header in headers) {
        if (count !== headerCount) {
          push(`"${header}": "${escapeForDoubleQuotes(headers[header])}",`, 1);
        } else {
          push(`"${header}": "${escapeForDoubleQuotes(headers[header])}"`, 1);
        }
        count += 1;
      }
      push("}");
      blank();
    }
    let request = builtInMethods.includes(method) ? `response = requests.${method.toLowerCase()}(url` : `response = requests.request("${method}", url`;
    if (hasPayload) {
      if (jsonPayload) {
        request += ", json=payload";
      } else {
        request += ", data=payload";
      }
    }
    if (hasFiles) {
      request += ", files=files";
    }
    if (headerCount > 0) {
      request += ", headers=headers";
    }
    if (qs) {
      request += ", params=querystring";
    }
    request += ")";
    push(request);
    blank();
    push("print(response.json())");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/python/requests/requests.js
var pythonRequests = {
  target: "python",
  client: "requests",
  title: "Requests",
  generate(request) {
    return convertWithHttpSnippetLite(requests, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/r/httr/client.js
var httr = {
  info: {
    key: "httr",
    title: "httr",
    link: "https://cran.r-project.org/web/packages/httr/vignettes/quickstart.html",
    description: "httr: Tools for Working with URLs and HTTP"
  },
  convert: ({ url, queryObj, queryString, postData, allHeaders, method }, options = {}) => {
    let _a, _b;
    const { push, blank, join } = new CodeBuilder({
      indent: (_a = options.indent) !== null && _a !== void 0 ? _a : "  "
    });
    push("library(httr)");
    blank();
    push(`url <- "${url}"`);
    blank();
    const qs = queryObj;
    delete queryObj.key;
    const entries = Object.entries(qs);
    const entriesCount = entries.length;
    if (entriesCount === 1) {
      const entry = entries[0];
      push(`queryString <- list(${entry[0]} = "${entry[1]}")`);
      blank();
    } else if (entriesCount > 1) {
      push("queryString <- list(");
      entries.forEach(([key, value], i7) => {
        const isLastItem = i7 !== entriesCount - 1;
        const maybeComma = isLastItem ? "," : "";
        push(`${key} = "${value}"${maybeComma}`, 1);
      });
      push(")");
      blank();
    }
    const payload = JSON.stringify(postData === null || postData === void 0 ? void 0 : postData.text);
    if (payload) {
      push(`payload <- ${payload}`);
      blank();
    }
    if (postData && (postData.text || postData.jsonObj || postData.params)) {
      switch (postData.mimeType) {
        case "application/x-www-form-urlencoded":
          push('encode <- "form"');
          blank();
          break;
        case "application/json":
          push('encode <- "json"');
          blank();
          break;
        case "multipart/form-data":
          push('encode <- "multipart"');
          blank();
          break;
        default:
          push('encode <- "raw"');
          blank();
          break;
      }
    }
    const cookieHeader = getHeader(allHeaders, "cookie");
    const acceptHeader = getHeader(allHeaders, "accept");
    const setCookies = cookieHeader ? `set_cookies(\`${String(cookieHeader).replace(/;/g, '", `').replace(/` /g, "`").replace(/[=]/g, '` = "')}")` : void 0;
    const setAccept = acceptHeader ? `accept("${escapeForDoubleQuotes(acceptHeader)}")` : void 0;
    const setContentType = `content_type("${escapeForDoubleQuotes((_b = postData === null || postData === void 0 ? void 0 : postData.mimeType) !== null && _b !== void 0 ? _b : "application/octet-stream")}")`;
    const otherHeaders = Object.entries(allHeaders).filter(([key]) => !["cookie", "accept", "content-type"].includes(key.toLowerCase())).map(([key, value]) => `'${key}' = '${escapeForSingleQuotes(value)}'`).join(", ");
    const setHeaders = otherHeaders ? `add_headers(${otherHeaders})` : void 0;
    let request = `response <- VERB("${method}", url`;
    if (payload) {
      request += ", body = payload";
    }
    if (queryString.length) {
      request += ", query = queryString";
    }
    const headerAdditions = [setHeaders, setContentType, setAccept, setCookies].filter((x3) => !!x3).join(", ");
    if (headerAdditions) {
      request += `, ${headerAdditions}`;
    }
    if (postData && (postData.text || postData.jsonObj || postData.params)) {
      request += ", encode = encode";
    }
    request += ")";
    push(request);
    blank();
    push('content(response, "text")');
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/r/httr/httr.js
var rHttr = {
  target: "r",
  client: "httr",
  title: "httr",
  generate(request) {
    return convertWithHttpSnippetLite(httr, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/ruby/native/client.js
var native2 = {
  info: {
    key: "native",
    title: "net::http",
    link: "http://ruby-doc.org/stdlib-2.2.1/libdoc/net/http/rdoc/Net/HTTP.html",
    description: "Ruby HTTP client"
  },
  convert: ({ uriObj, method: rawMethod, fullUrl, postData, allHeaders }, options = {}) => {
    const { insecureSkipVerify = false } = options;
    const { push, blank, join } = new CodeBuilder();
    push("require 'uri'");
    push("require 'net/http'");
    blank();
    const method = rawMethod.toUpperCase();
    const methods = [
      "GET",
      "POST",
      "HEAD",
      "DELETE",
      "PATCH",
      "PUT",
      "OPTIONS",
      "COPY",
      "LOCK",
      "UNLOCK",
      "MOVE",
      "TRACE"
    ];
    const capMethod = method.charAt(0) + method.substring(1).toLowerCase();
    if (!methods.includes(method)) {
      push(`class Net::HTTP::${capMethod} < Net::HTTPRequest`);
      push(`  METHOD = '${method.toUpperCase()}'`);
      push(`  REQUEST_HAS_BODY = '${(postData === null || postData === void 0 ? void 0 : postData.text) ? "true" : "false"}'`);
      push("  RESPONSE_HAS_BODY = true");
      push("end");
      blank();
    }
    push(`url = URI("${fullUrl}")`);
    blank();
    push("http = Net::HTTP.new(url.host, url.port)");
    if (uriObj.protocol === "https:") {
      push("http.use_ssl = true");
      if (insecureSkipVerify) {
        push("http.verify_mode = OpenSSL::SSL::VERIFY_NONE");
      }
    }
    blank();
    push(`request = Net::HTTP::${capMethod}.new(url)`);
    const headers = Object.keys(allHeaders);
    if (headers.length) {
      headers.forEach((key) => {
        push(`request["${key}"] = '${escapeForSingleQuotes(allHeaders[key])}'`);
      });
    }
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`request.body = ${JSON.stringify(postData.text)}`);
    }
    blank();
    push("response = http.request(request)");
    push("puts response.read_body");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/ruby/native/native.js
var rubyNative = {
  target: "ruby",
  client: "native",
  title: "net::http",
  generate(request) {
    return convertWithHttpSnippetLite(native2, request);
  }
};

// node_modules/@scalar/snippetz/dist/plugins/shell/curl/curl.js
var shellCurl = {
  target: "shell",
  client: "curl",
  title: "Curl",
  generate(request, configuration) {
    var _a, _b, _c, _d, _e;
    const normalizedRequest = {
      method: "GET",
      ...request
    };
    normalizedRequest.method = normalizedRequest.method.toUpperCase();
    const parts = ["curl"];
    const queryString = ((_a = normalizedRequest.queryString) == null ? void 0 : _a.length) ? "?" + normalizedRequest.queryString.map((param) => {
      const encodedName = encodeURIComponent(param.name);
      const encodedValue = encodeURIComponent(param.value);
      return `${encodedName}=${encodedValue}`;
    }).join("&") : "";
    const url = `${normalizedRequest.url}${queryString}`;
    const hasSpecialChars = /[\s<>[\]{}|\\^%]/.test(url);
    const urlPart = queryString || hasSpecialChars ? `'${url}'` : url;
    parts[0] = `curl ${urlPart}`;
    if (normalizedRequest.method !== "GET") {
      parts.push(`--request ${normalizedRequest.method}`);
    }
    if (((_b = configuration == null ? void 0 : configuration.auth) == null ? void 0 : _b.username) && ((_c = configuration == null ? void 0 : configuration.auth) == null ? void 0 : _c.password)) {
      parts.push(`--user '${configuration.auth.username}:${configuration.auth.password}'`);
    }
    if ((_d = normalizedRequest.headers) == null ? void 0 : _d.length) {
      normalizedRequest.headers.forEach((header) => {
        parts.push(`--header '${header.name}: ${header.value}'`);
      });
      const acceptEncoding = normalizedRequest.headers.find((header) => header.name.toLowerCase() === "accept-encoding");
      if (acceptEncoding && /gzip|deflate/.test(acceptEncoding.value)) {
        parts.push("--compressed");
      }
    }
    if ((_e = normalizedRequest.cookies) == null ? void 0 : _e.length) {
      const cookieString = normalizedRequest.cookies.map((cookie) => {
        const encodedName = encodeURIComponent(cookie.name);
        const encodedValue = encodeURIComponent(cookie.value);
        return `${encodedName}=${encodedValue}`;
      }).join("; ");
      parts.push(`--cookie '${cookieString}'`);
    }
    if (normalizedRequest.postData) {
      if (normalizedRequest.postData.mimeType === "application/json") {
        if (normalizedRequest.postData.text) {
          const jsonData = JSON.parse(normalizedRequest.postData.text);
          const prettyJson = JSON.stringify(jsonData, null, 2);
          parts.push(`--data '${prettyJson}'`);
        }
      } else if (normalizedRequest.postData.mimeType === "application/octet-stream") {
        parts.push(`--data-binary '${normalizedRequest.postData.text}'`);
      } else if (normalizedRequest.postData.mimeType === "application/x-www-form-urlencoded" && normalizedRequest.postData.params) {
        normalizedRequest.postData.params.forEach((param) => {
          parts.push(`--data-urlencode '${encodeURIComponent(param.name)}=${param.value}'`);
        });
      } else if (normalizedRequest.postData.mimeType === "multipart/form-data" && normalizedRequest.postData.params) {
        normalizedRequest.postData.params.forEach((param) => {
          if (param.fileName !== void 0) {
            parts.push(`--form '${param.name}=@${param.fileName}'`);
          } else {
            parts.push(`--form '${param.name}=${param.value}'`);
          }
        });
      } else {
        parts.push(`--data "${normalizedRequest.postData.text}"`);
      }
    }
    return parts.join(" \\\n  ");
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/helpers/shell.js
var quote = (value = "") => {
  const safe = /^[a-z0-9-_/.@%^=:]+$/i;
  const isShellSafe = safe.test(value);
  if (isShellSafe) {
    return value;
  }
  return `'${value.replace(/'/g, "'\\''")}'`;
};
var escape = (value) => value.replace(/\r/g, "\\r").replace(/\n/g, "\\n");

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/shell/wget/client.js
var wget = {
  info: {
    key: "wget",
    title: "Wget",
    link: "https://www.gnu.org/software/wget/",
    description: "a free software package for retrieving files using HTTP, HTTPS"
  },
  convert: ({ method, postData, allHeaders, fullUrl }, options) => {
    const opts = {
      indent: "  ",
      short: false,
      verbose: false,
      ...options
    };
    const { push, join } = new CodeBuilder({
      indent: opts.indent,
      // @ts-expect-error SEEMS LEGIT
      join: opts.indent !== false ? ` \\
${opts.indent}` : " "
    });
    if (opts.verbose) {
      push(`wget ${opts.short ? "-v" : "--verbose"}`);
    } else {
      push(`wget ${opts.short ? "-q" : "--quiet"}`);
    }
    push(`--method ${quote(method)}`);
    Object.keys(allHeaders).forEach((key) => {
      const header = `${key}: ${allHeaders[key]}`;
      push(`--header ${quote(header)}`);
    });
    if (postData === null || postData === void 0 ? void 0 : postData.text) {
      push(`--body-data ${escape(quote(postData.text))}`);
    }
    push(opts.short ? "-O" : "--output-document");
    push(`- ${quote(fullUrl)}`);
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/shell/wget/wget.js
var shellWget = {
  target: "shell",
  client: "wget",
  title: "Wget",
  generate(request) {
    return convertWithHttpSnippetLite(wget, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/shell/httpie/client.js
var httpie = {
  info: {
    key: "httpie",
    title: "HTTPie",
    link: "http://httpie.org/",
    description: "a CLI, cURL-like tool for humans"
  },
  convert: ({ allHeaders, postData, queryObj, fullUrl, method, url }, options) => {
    var _a;
    const opts = {
      body: false,
      cert: false,
      headers: false,
      indent: "  ",
      pretty: false,
      print: false,
      queryParams: false,
      short: false,
      style: false,
      timeout: false,
      verbose: false,
      verify: false,
      ...options
    };
    const { push, join, unshift } = new CodeBuilder({
      indent: opts.indent,
      // @ts-expect-error SEEMS LEGIT
      join: opts.indent !== false ? ` \\
${opts.indent}` : " "
    });
    let raw = false;
    const flags = [];
    if (opts.headers) {
      flags.push(opts.short ? "-h" : "--headers");
    }
    if (opts.body) {
      flags.push(opts.short ? "-b" : "--body");
    }
    if (opts.verbose) {
      flags.push(opts.short ? "-v" : "--verbose");
    }
    if (opts.print) {
      flags.push(`${opts.short ? "-p" : "--print"}=${opts.print}`);
    }
    if (opts.verify) {
      flags.push(`--verify=${opts.verify}`);
    }
    if (opts.cert) {
      flags.push(`--cert=${opts.cert}`);
    }
    if (opts.pretty) {
      flags.push(`--pretty=${opts.pretty}`);
    }
    if (opts.style) {
      flags.push(`--style=${opts.style}`);
    }
    if (opts.timeout) {
      flags.push(`--timeout=${opts.timeout}`);
    }
    if (opts.queryParams) {
      Object.keys(queryObj).forEach((name) => {
        const value = queryObj[name];
        if (Array.isArray(value)) {
          value.forEach((val) => {
            push(`${name}==${quote(val)}`);
          });
        } else {
          push(`${name}==${quote(value)}`);
        }
      });
    }
    Object.keys(allHeaders).sort().forEach((key) => {
      push(`${key}:${quote(allHeaders[key])}`);
    });
    if ((postData === null || postData === void 0 ? void 0 : postData.mimeType) === "application/x-www-form-urlencoded") {
      if ((_a = postData.params) == null ? void 0 : _a.length) {
        flags.push(opts.short ? "-f" : "--form");
        postData.params.forEach((param) => {
          push(`${param.name}=${quote(param.value)}`);
        });
      }
    } else {
      raw = true;
    }
    const cliFlags = flags.length ? `${flags.join(" ")} ` : "";
    url = quote(opts.queryParams ? url : fullUrl);
    unshift(`http ${cliFlags}${method} ${url}`);
    if (raw && (postData === null || postData === void 0 ? void 0 : postData.text)) {
      const postDataText = quote(postData.text);
      unshift(`echo ${postDataText} | `);
    }
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/shell/httpie/httpie.js
var shellHttpie = {
  target: "shell",
  client: "httpie",
  title: "HTTPie",
  generate(request) {
    return convertWithHttpSnippetLite(httpie, request);
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/swift/helpers.js
var buildString = (length, str) => str.repeat(length);
var concatArray = (arr, pretty, indentation, indentLevel) => {
  const currentIndent = buildString(indentLevel, indentation);
  const closingBraceIndent = buildString(indentLevel - 1, indentation);
  const join = pretty ? `,
${currentIndent}` : ", ";
  if (pretty) {
    return `[
${currentIndent}${arr.join(join)}
${closingBraceIndent}]`;
  }
  return `[${arr.join(join)}]`;
};
var literalDeclaration = (name, parameters, opts) => `let ${name} = ${literalRepresentation3(parameters, opts)}`;
var literalRepresentation3 = (value, opts, indentLevel) => {
  indentLevel = indentLevel === void 0 ? 1 : indentLevel + 1;
  switch (Object.prototype.toString.call(value)) {
    case "[object Number]":
      return value;
    case "[object Array]": {
      let pretty = false;
      const valuesRepresentation = value.map((v5) => {
        if (Object.prototype.toString.call(v5) === "[object Object]") {
          pretty = Object.keys(v5).length > 1;
        }
        return literalRepresentation3(v5, opts, indentLevel);
      });
      return concatArray(valuesRepresentation, pretty, opts.indent, indentLevel);
    }
    case "[object Object]": {
      const keyValuePairs = [];
      for (const key in value) {
        keyValuePairs.push(`"${key}": ${literalRepresentation3(value[key], opts, indentLevel)}`);
      }
      return concatArray(
        keyValuePairs,
        // @ts-expect-error needs better types
        opts.pretty && keyValuePairs.length > 1,
        // @ts-expect-error needs better types
        opts.indent,
        indentLevel
      );
    }
    case "[object Boolean]":
      return value.toString();
    default:
      if (value === null || value === void 0) {
        return "";
      }
      return `"${value.toString().replace(/"/g, '\\"')}"`;
  }
};

// node_modules/@scalar/snippetz/dist/httpsnippet-lite/esm/targets/swift/nsurlsession/client.js
var nsurlsession2 = {
  info: {
    key: "nsurlsession",
    title: "NSURLSession",
    link: "https://developer.apple.com/library/mac/documentation/Foundation/Reference/NSURLSession_class/index.html",
    description: "Foundation's NSURLSession request"
  },
  convert: ({ allHeaders, postData, fullUrl, method }, options) => {
    let _a;
    const opts = {
      indent: "  ",
      pretty: true,
      timeout: "10",
      ...options
    };
    const { push, blank, join } = new CodeBuilder({ indent: opts.indent });
    const req = {
      hasHeaders: false,
      hasBody: false
    };
    push("import Foundation");
    if (Object.keys(allHeaders).length) {
      req.hasHeaders = true;
      blank();
      push(literalDeclaration("headers", allHeaders, opts));
    }
    if (postData && (postData.text || postData.jsonObj || postData.params)) {
      req.hasBody = true;
      switch (postData.mimeType) {
        case "application/x-www-form-urlencoded":
          blank();
          if ((_a = postData.params) === null || _a === void 0 ? void 0 : _a.length) {
            const [head, ...tail] = postData.params;
            push(`let postData = NSMutableData(data: "${head.name}=${head.value}".data(using: String.Encoding.utf8)!)`);
            tail.forEach(({ name, value }) => {
              push(`postData.append("&${name}=${value}".data(using: String.Encoding.utf8)!)`);
            });
          } else {
            req.hasBody = false;
          }
          break;
        case "application/json":
          if (postData.jsonObj) {
            push(`${literalDeclaration("parameters", postData.jsonObj, opts)} as [String : Any]`);
            blank();
            push("let postData = JSONSerialization.data(withJSONObject: parameters, options: [])");
          }
          break;
        case "multipart/form-data":
          push(literalDeclaration("parameters", postData.params, opts));
          blank();
          push(`let boundary = "${postData.boundary}"`);
          blank();
          push('var body = ""');
          push("var error: NSError? = nil");
          push("for param in parameters {");
          push('let paramName = param["name"]!', 1);
          push('body += "--\\(boundary)\\r\\n"', 1);
          push('body += "Content-Disposition:form-data; name=\\"\\(paramName)\\""', 1);
          push('if let filename = param["fileName"] {', 1);
          push('let contentType = param["content-type"]!', 2);
          push("let fileContent = String(contentsOfFile: filename, encoding: String.Encoding.utf8)", 2);
          push("if (error != nil) {", 2);
          push("print(error as Any)", 3);
          push("}", 2);
          push('body += "; filename=\\"\\(filename)\\"\\r\\n"', 2);
          push('body += "Content-Type: \\(contentType)\\r\\n\\r\\n"', 2);
          push("body += fileContent", 2);
          push('} else if let paramValue = param["value"] {', 1);
          push('body += "\\r\\n\\r\\n\\(paramValue)"', 2);
          push("}", 1);
          push("}");
          break;
        default:
          blank();
          push(`let postData = NSData(data: "${postData.text}".data(using: String.Encoding.utf8)!)`);
      }
    }
    blank();
    push(`let request = NSMutableURLRequest(url: NSURL(string: "${fullUrl}")! as URL,`);
    push("                                        cachePolicy: .useProtocolCachePolicy,");
    push(
      // @ts-expect-error needs better types
      `                                    timeoutInterval: ${Number.parseInt(opts.timeout, 10).toFixed(1)})`
    );
    push(`request.httpMethod = "${method}"`);
    if (req.hasHeaders) {
      push("request.allHTTPHeaderFields = headers");
    }
    if (req.hasBody) {
      push("request.httpBody = postData as Data");
    }
    blank();
    push("let session = URLSession.shared");
    push("let dataTask = session.dataTask(with: request as URLRequest, completionHandler: { (data, response, error) -> Void in");
    push("if (error != nil) {", 1);
    push("print(error as Any)", 2);
    push("} else {", 1);
    push("let httpResponse = response as? HTTPURLResponse", 2);
    push("print(httpResponse)", 2);
    push("}", 1);
    push("})");
    blank();
    push("dataTask.resume()");
    return join();
  }
};

// node_modules/@scalar/snippetz/dist/plugins/swift/nsurlsession/nsurlsession.js
var swiftNsurlsession = {
  target: "swift",
  client: "nsurlsession",
  title: "NSURLSession",
  generate(request) {
    return convertWithHttpSnippetLite(nsurlsession2, request);
  }
};

// node_modules/@scalar/snippetz/dist/clients.js
var clients = [
  {
    key: "c",
    title: "C",
    default: "libcurl",
    clients: [cLibcurl]
  },
  {
    key: "csharp",
    title: "C#",
    default: "restsharp",
    clients: [csharpHttpclient, csharpRestsharp]
  },
  {
    key: "clojure",
    title: "Clojure",
    default: "clj_http",
    clients: [clojureCljhttp]
  },
  {
    key: "dart",
    title: "Dart",
    default: "http",
    clients: [dartHttp]
  },
  {
    key: "go",
    title: "Go",
    default: "native",
    clients: [goNative]
  },
  {
    key: "http",
    title: "HTTP",
    default: "http1.1",
    clients: [httpHttp11]
  },
  {
    key: "java",
    title: "Java",
    default: "unirest",
    clients: [javaAsynchttp, javaNethttp, javaOkhttp, javaUnirest]
  },
  {
    key: "js",
    title: "JavaScript",
    default: "fetch",
    clients: [jsFetch, jsAxios, jsOfetch, jsJquery, jsXhr]
  },
  {
    key: "kotlin",
    title: "Kotlin",
    default: "okhttp",
    clients: [kotlinOkhttp]
  },
  {
    key: "node",
    title: "Node.js",
    default: "fetch",
    clients: [nodeFetch, nodeAxios, nodeOfetch, nodeUndici]
  },
  {
    key: "objc",
    title: "Objective-C",
    default: "nsurlsession",
    clients: [objcNsurlsession]
  },
  {
    key: "ocaml",
    title: "OCaml",
    default: "cohttp",
    clients: [ocamlCohttp]
  },
  {
    key: "php",
    title: "PHP",
    default: "curl",
    clients: [phpCurl, phpGuzzle]
  },
  {
    key: "powershell",
    title: "Powershell",
    default: "webrequest",
    clients: [powershellWebrequest, powershellRestmethod]
  },
  {
    key: "python",
    title: "Python",
    default: "python3",
    clients: [pythonPython3, pythonRequests]
  },
  {
    key: "r",
    title: "R",
    default: "httr",
    clients: [rHttr]
  },
  {
    key: "ruby",
    title: "Ruby",
    default: "native",
    clients: [rubyNative]
  },
  {
    key: "shell",
    title: "Shell",
    default: "curl",
    clients: [shellCurl, shellWget, shellHttpie]
  },
  {
    key: "swift",
    title: "Swift",
    default: "nsurlsession",
    clients: [swiftNsurlsession]
  }
];

// node_modules/@scalar/snippetz/dist/snippetz.js
function snippetz() {
  function findPlugin(target, client) {
    var _a;
    return (_a = clients.find(({ key }) => key === target)) == null ? void 0 : _a.clients.find((plugin) => plugin.client === client);
  }
  return {
    print(target, client, request) {
      var _a;
      return (_a = findPlugin(target, client)) == null ? void 0 : _a.generate(request);
    },
    clients() {
      return clients;
    },
    plugins() {
      return clients.flatMap(({ key, clients: clients2 }) => clients2.map((plugin) => ({
        target: key,
        client: plugin.client
      })));
    },
    findPlugin,
    hasPlugin(target, client) {
      return Boolean(findPlugin(target, client));
    }
  };
}

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/helpers/filter-security-requirements.js
var c3 = (s5, m7 = [], n4 = {}) => {
  const a3 = new Set(
    s5.map((e5) => Object.keys(e5).sort().join(","))
  );
  return m7.reduce((e5, r5) => {
    const o3 = Array.isArray(r5) ? r5 : [r5], p3 = o3.map((t2) => {
      var i7;
      return (i7 = n4[t2]) == null ? void 0 : i7.nameKey;
    }).sort().join(",");
    return a3.has(p3) && e5.push(...o3.map((t2) => n4[t2]).filter(isDefined)), e5;
  }, []);
};

// node_modules/@scalar/api-client/dist/views/Components/CodeSnippet/helpers/convert-to-har-request.js
var w5 = ({
  baseUrl: u4 = "",
  method: g4,
  body: r5,
  path: h2,
  cookies: s5,
  headers: o3,
  query: m7
}) => {
  var p3, c4, l2;
  const D5 = mergeUrls(u4, h2, void 0, true), i7 = {
    method: g4.toUpperCase(),
    url: D5.toString(),
    httpVersion: "HTTP/1.1",
    headers: [],
    queryString: [],
    cookies: [],
    headersSize: -1,
    bodySize: -1
  };
  if (s5.length && (i7.cookies = s5.filter((e5) => e5.enabled).map(({ key: e5, value: t2 }) => ({
    name: e5,
    value: t2
  }))), o3.length && (i7.headers = o3.filter((e5) => e5.enabled && !(e5.key.toLowerCase() === "accept" && e5.value === "*/*")).map(({ key: e5, value: t2 }) => ({
    name: e5.replace(/\b\w/g, (a3) => a3.toUpperCase()),
    value: t2
  }))), m7.length && (i7.queryString = m7.filter((e5) => e5.enabled).map(({ key: e5, value: t2 }) => ({
    name: e5,
    value: t2
  }))), r5)
    try {
      const e5 = ((p3 = o3.find((t2) => t2.key.toLowerCase() === "content-type")) == null ? void 0 : p3.value) || "application/json";
      if (r5.activeBody === "formData" && r5.formData) {
        const t2 = {};
        r5.formData.value.forEach(({ key: a3, value: f6, file: n4, enabled: S3 }) => {
          S3 && (n4 ? t2[a3] = {
            type: "file",
            text: "BINARY",
            name: a3 || "blob",
            size: n4.size,
            fileName: n4.name,
            mimeType: n4.type || "application/octet-stream"
          } : t2[a3] ? (Array.isArray(t2[a3]) || (t2[a3] = [t2[a3]]), t2[a3].push(f6)) : t2[a3] = f6);
        }), ((c4 = r5.formData) == null ? void 0 : c4.encoding) === "urlencoded" ? i7.postData = {
          mimeType: e5,
          text: new URLSearchParams(t2).toString()
        } : i7.postData = {
          mimeType: e5,
          text: JSON.stringify(t2)
        };
      } else r5.activeBody === "raw" && r5.raw && (i7.postData = {
        mimeType: e5,
        text: ((l2 = r5.raw) == null ? void 0 : l2.value) ?? ""
      });
    } catch {
    }
  return i7;
};

// node_modules/@scalar/api-client/dist/views/Components/CodeSnippet/helpers/get-har-request.js
var q2 = "YOUR_SECRET_TOKEN";
var o2 = ({
  operation: s5,
  example: t2,
  server: u4,
  securitySchemes: b4 = []
}) => {
  const c4 = $2(b4, {}, q2), y5 = [
    ...(t2 == null ? void 0 : t2.parameters.headers) ?? [],
    ...Object.entries(c4.headers).map(([r5, d3]) => ({
      key: r5,
      value: d3,
      enabled: true
    }))
  ], h2 = [
    ...(t2 == null ? void 0 : t2.parameters.cookies) ?? [],
    ...c4.cookies.map((r5) => ({
      key: r5.name,
      value: r5.value,
      enabled: true
    }))
  ], E6 = [
    ...(t2 == null ? void 0 : t2.parameters.query) ?? [],
    ...Array.from(c4.urlParams.entries()).map(([r5, d3]) => ({
      key: r5,
      value: d3,
      enabled: true
    }))
  ];
  return w5({
    baseUrl: u4 == null ? void 0 : u4.url,
    method: (s5 == null ? void 0 : s5.method) ?? "get",
    path: (s5 == null ? void 0 : s5.path) ?? "/",
    body: t2 == null ? void 0 : t2.body,
    cookies: h2,
    headers: y5,
    query: E6
  });
};

// node_modules/@scalar/api-client/dist/views/Components/CodeSnippet/helpers/get-snippet.js
var s3 = "ws://replace.me";
var S2 = (i7, t2, {
  operation: l2,
  example: u4,
  server: c4,
  securitySchemes: g4 = []
}) => {
  var n4;
  try {
    const r5 = o2({
      operation: l2,
      example: u4,
      server: c4,
      securitySchemes: g4
    });
    if (!r5.url)
      return [new Error("Please enter a URL to see a code snippet"), null];
    const o3 = r5.url.startsWith("/") ? "" : "/";
    try {
      new URL(r5.url);
    } catch (e5) {
      console.error("[getSnippet] Invalid URL", e5), r5.url = `${s3}${o3}${r5.url}`;
    }
    if (((n4 = r5.postData) == null ? void 0 : n4.mimeType) === "application/json")
      try {
        JSON.parse(r5.postData.text || "{}");
      } catch (e5) {
        return console.error("[getSnippet] Invalid JSON body", e5), [new Error("Invalid JSON body"), null];
      }
    const p3 = i7.replace("javascript", "js");
    if (snippetz().hasPlugin(p3, t2)) {
      const e5 = snippetz().print(p3, t2, r5);
      return e5 ? [null, e5.replace(`${s3}${o3}`, "")] : [new Error("Error generating snippet"), null];
    }
  } catch (r5) {
    return console.error("[getSnippet] Error generating snippet", r5), [new Error("Error generating snippet"), null];
  }
  return [new Error("No snippet found"), null];
};

// node_modules/@scalar/api-client/dist/views/Components/CodeSnippet/CodeSnippet.vue.js
var g3 = {
  key: 0,
  class: "text-c-3 flex min-h-16 items-center justify-center px-4 text-sm"
};
var B4 = defineComponent({
  __name: "CodeSnippet",
  props: {
    target: {},
    client: {},
    operation: {},
    server: {},
    example: {},
    securitySchemes: { default: () => [] }
  },
  setup(t2) {
    const i7 = computed(
      () => t2.securitySchemes.flatMap((e5) => e5.type === "apiKey" ? e5.value : (e5 == null ? void 0 : e5.type) === "http" ? [
        e5.token,
        e5.password,
        btoa(`${e5.username}:${e5.password}`)
      ] : e5.type === "oauth2" ? Object.values(e5.flows).map((r5) => r5 == null ? void 0 : r5.token).filter(isDefined) : [])
    ), a3 = computed(() => {
      const [e5, r5] = S2(t2.target, t2.client, {
        operation: t2.operation,
        example: t2.example,
        server: t2.server,
        securitySchemes: t2.securitySchemes
      });
      return { error: e5, payload: r5 };
    }), o3 = computed(() => t2.target === "shell" && t2.client === "curl" ? "curl" : t2.target ?? "plaintext");
    return (e5, r5) => a3.value.error ? (openBlock(), createElementBlock("div", g3, toDisplayString(a3.value.error.message), 1)) : a3.value.payload ? (openBlock(), createBlock(unref(T), {
      key: 1,
      class: "w-full",
      content: a3.value.payload,
      hideCredentials: i7.value,
      lang: o3.value,
      lineNumbers: ""
    }, null, 8, ["content", "hideCredentials", "lang"])) : createCommentVNode("", true);
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestCodeExample.vue2.js
var D4 = { class: "w-full" };
var E5 = { class: "-mx-1 flex flex-1" };
var N5 = { class: "bg-b-1 flex items-center justify-center overflow-hidden border-t" };
var P4 = defineComponent({
  __name: "RequestCodeExample",
  props: {
    collection: {},
    example: {},
    operation: {},
    server: {},
    workspace: {}
  },
  setup(o3) {
    const { securitySchemes: u4, workspaceMutators: p3 } = je(), f6 = computed(
      () => c3(
        o3.operation.security || o3.collection.security || [],
        o3.operation.selectedSecuritySchemeUids || o3.collection.selectedSecuritySchemeUids,
        u4
      )
    ), a3 = computed(() => {
      const e5 = {};
      return {
        options: snippetz().clients().map((t2) => ({
          label: t2.title,
          options: t2.clients.map((s5) => (e5[`${t2.key},${s5.client}`] = s5.title, {
            id: `${t2.key},${s5.client}`,
            label: s5.title
          }))
        })),
        dict: e5
      };
    }), d3 = computed(() => {
      var t2;
      const e5 = o3.workspace.selectedHttpClient;
      if (!e5)
        return ((t2 = a3.value.options[0]) == null ? void 0 : t2.options[0]) ?? {
          id: "js,fetch",
          label: "Fetch"
        };
      const l2 = `${e5.targetKey},${e5.clientKey}`;
      return {
        id: l2,
        label: a3.value.dict[l2] ?? "Unknown"
      };
    }), y5 = computed(
      () => {
        var e5;
        return ((e5 = o3.workspace.selectedHttpClient) == null ? void 0 : e5.targetKey) ?? "js";
      }
    ), v5 = computed(
      () => {
        var e5;
        return ((e5 = o3.workspace.selectedHttpClient) == null ? void 0 : e5.clientKey) ?? "fetch";
      }
    ), h2 = ({ id: e5 }) => {
      const [l2, t2] = e5.split(",");
      !l2 || !t2 || p3.edit(o3.workspace.uid, "selectedHttpClient", {
        targetKey: l2,
        clientKey: t2
      });
    };
    return (e5, l2) => (openBlock(), createElementBlock("div", D4, [
      createVNode(P2, {
        class: "group/preview w-full border-b-0",
        defaultOpen: false
      }, {
        title: withCtx(() => l2[0] || (l2[0] = [
          createTextVNode("Code Snippet")
        ])),
        actions: withCtx(() => [
          createBaseVNode("div", E5, [
            createVNode(unref(c2), {
              modelValue: d3.value,
              options: a3.value.options,
              placement: "bottom-end",
              "onUpdate:modelValue": h2
            }, {
              default: withCtx(() => [
                createVNode(unref($), {
                  class: "text-c-1 hover:bg-b-3 py-0.75 flex h-full w-fit gap-1.5 px-1.5 font-normal",
                  fullWidth: "",
                  variant: "ghost"
                }, {
                  default: withCtx(() => {
                    var t2;
                    return [
                      createBaseVNode("span", null, toDisplayString((t2 = d3.value) == null ? void 0 : t2.label), 1),
                      createVNode(unref(c), {
                        icon: "ChevronDown",
                        size: "md"
                      })
                    ];
                  }),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["modelValue", "options"])
          ])
        ]),
        default: withCtx(() => [
          createVNode(g, { columns: [""] }, {
            default: withCtx(() => [
              createVNode(i4, null, {
                default: withCtx(() => [
                  createBaseVNode("div", N5, [
                    createVNode(unref(B4), {
                      class: "px-3 py-1.5",
                      client: v5.value,
                      example: e5.example,
                      operation: e5.operation,
                      securitySchemes: f6.value,
                      server: e5.server,
                      target: y5.value
                    }, null, 8, ["client", "example", "operation", "securitySchemes", "server", "target"])
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ]),
        _: 1
      })
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestCodeExample.vue.js
var r2 = s2(P4, [["__scopeId", "data-v-cf7ecb78"]]);

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestSection.vue2.js
var z3 = { class: "group pointer-events-none flex flex-1 items-center gap-1 lg:pr-24" };
var G2 = {
  key: 0,
  class: "pointer-events-auto absolute left-0 top-0 h-full w-full cursor-text opacity-0",
  for: "requestname"
};
var X2 = ["placeholder", "value"];
var J = {
  key: 2,
  class: "text-c-1 flex h-8 items-center"
};
var Y = ["id", "role"];
var ue = defineComponent({
  __name: "RequestSection",
  props: {
    collection: {},
    environment: {},
    envVariables: {},
    example: {},
    invalidParams: {},
    operation: {},
    selectedSecuritySchemeUids: {},
    server: {},
    workspace: {}
  },
  setup(l2) {
    const A6 = [
      "Auth",
      "Variables",
      "Cookies",
      "Headers",
      "Query",
      "Body"
    ], { requestMutators: P6, cookies: S3, securitySchemes: q3 } = je(), { layout: u4 } = s(), k7 = computed(() => {
      const e5 = /* @__PURE__ */ new Set(["All", ...A6]);
      return l2.example.parameters.path.length || e5.delete("Variables"), canMethodHaveBody(l2.operation.method ?? "get") || e5.delete("Body"), h2.value && e5.delete("Auth"), [...e5];
    }), r5 = computed(
      () => Object.fromEntries(
        k7.value.map((e5) => [e5, useId()])
      )
    ), h2 = computed(
      () => {
        var e5;
        return u4 === "modal" && ((e5 = l2.operation.security) == null ? void 0 : e5.length) === 0;
      }
    ), a3 = ref("All");
    watch(
      () => l2.operation,
      (e5) => {
        a3.value === "Body" && e5 && !canMethodHaveBody(e5.method) && (a3.value = "All");
      }
    );
    const g4 = (e5) => {
      const o3 = e5.target;
      P6.edit(l2.operation.uid, "summary", o3.value);
    }, B5 = computed(
      () => (l2.workspace.cookies ?? []).map((e5) => S3[e5]).filter(isDefined).filter((e5) => e5.name).filter(
        (e5) => {
          var o3;
          return k(((o3 = l2.server) == null ? void 0 : o3.url) || l2.operation.path, e5.domain);
        }
      ).map((e5) => ({
        key: e5.name,
        value: e5.value,
        route: {
          name: "cookies",
          params: {
            cookies: e5.uid
          }
        },
        enabled: true
      }))
    ), C4 = () => l2.operation.summary ? l2.operation.summary : l2.operation.path.replace(REGEX.PROTOCOL, "") ? l2.operation.path.replace(REGEX.PROTOCOL, "") : "Request Name";
    return (e5, o3) => (openBlock(), createBlock(_, {
      "aria-label": `Request: ${e5.operation.summary}`
    }, {
      title: withCtx(() => [
        createBaseVNode("div", z3, [
          unref(u4) !== "modal" ? (openBlock(), createElementBlock("label", G2)) : createCommentVNode("", true),
          unref(u4) !== "modal" ? (openBlock(), createElementBlock("input", {
            key: 1,
            id: "requestname",
            class: "text-c-1 group-hover-input pl-1.25 md:-ml-1.25 pointer-events-auto relative z-10 -ml-0.5 h-8 w-full rounded has-[:focus-visible]:outline",
            placeholder: C4(),
            value: e5.operation.summary,
            onInput: g4
          }, null, 40, X2)) : (openBlock(), createElementBlock("span", J, toDisplayString(e5.operation.summary), 1))
        ]),
        createVNode(f3, {
          modelValue: a3.value,
          "onUpdate:modelValue": o3[0] || (o3[0] = (R2) => a3.value = R2),
          filterIds: r5.value,
          filters: k7.value
        }, null, 8, ["modelValue", "filterIds", "filters"])
      ]),
      default: withCtx(() => [
        createBaseVNode("div", {
          id: r5.value.All,
          class: "request-section-content custom-scroll relative flex flex-1 flex-col divide-y",
          role: a3.value === "All" ? "tabpanel" : "none"
        }, [
          e5.collection && e5.workspace && (unref(u4) !== "modal" || Object.keys(unref(q3) ?? {}).length) ? withDirectives((openBlock(), createBlock(m6, {
            key: 0,
            id: r5.value.Auth,
            collection: e5.collection,
            envVariables: e5.envVariables,
            environment: e5.environment,
            layout: "client",
            operation: e5.operation,
            role: a3.value === "All" ? "none" : "tabpanel",
            selectedSecuritySchemeUids: e5.selectedSecuritySchemeUids,
            server: e5.server,
            title: "Authentication",
            workspace: e5.workspace
          }, null, 8, ["id", "collection", "envVariables", "environment", "operation", "role", "selectedSecuritySchemeUids", "server", "workspace"])), [
            [
              vShow,
              !h2.value && (a3.value === "All" || a3.value === "Auth")
            ]
          ]) : createCommentVNode("", true),
          withDirectives(createVNode(A2, {
            id: r5.value.Variables,
            envVariables: e5.envVariables,
            environment: e5.environment,
            example: e5.example,
            invalidParams: e5.invalidParams,
            operation: e5.operation,
            paramKey: "path",
            role: a3.value === "All" ? "none" : "tabpanel",
            title: "Variables",
            workspace: e5.workspace
          }, null, 8, ["id", "envVariables", "environment", "example", "invalidParams", "operation", "role", "workspace"]), [
            [
              vShow,
              (a3.value === "All" || a3.value === "Variables") && e5.example.parameters.path.length
            ]
          ]),
          withDirectives(createVNode(Z, {
            id: r5.value.Cookies,
            envVariables: e5.envVariables,
            environment: e5.environment,
            example: e5.example,
            invalidParams: e5.invalidParams,
            operation: e5.operation,
            paramKey: "cookies",
            readOnlyEntries: B5.value,
            role: a3.value === "All" ? "none" : "tabpanel",
            title: "Cookies",
            workspace: e5.workspace,
            workspaceParamKey: "cookies"
          }, null, 8, ["id", "envVariables", "environment", "example", "invalidParams", "operation", "readOnlyEntries", "role", "workspace"]), [
            [vShow, a3.value === "All" || a3.value === "Cookies"]
          ]),
          withDirectives(createVNode(Z, {
            id: r5.value.Headers,
            envVariables: e5.envVariables,
            environment: e5.environment,
            example: e5.example,
            invalidParams: e5.invalidParams,
            operation: e5.operation,
            paramKey: "headers",
            role: a3.value === "All" ? "none" : "tabpanel",
            title: "Headers",
            workspace: e5.workspace
          }, null, 8, ["id", "envVariables", "environment", "example", "invalidParams", "operation", "role", "workspace"]), [
            [vShow, a3.value === "All" || a3.value === "Headers"]
          ]),
          withDirectives(createVNode(Z, {
            id: r5.value.Query,
            envVariables: e5.envVariables,
            environment: e5.environment,
            example: e5.example,
            invalidParams: e5.invalidParams,
            operation: e5.operation,
            paramKey: "query",
            role: a3.value === "All" ? "none" : "tabpanel",
            title: "Query Parameters",
            workspace: e5.workspace
          }, null, 8, ["id", "envVariables", "environment", "example", "invalidParams", "operation", "role", "workspace"]), [
            [vShow, a3.value === "All" || a3.value === "Query"]
          ]),
          withDirectives(createVNode(e2, {
            id: r5.value.Body,
            envVariables: e5.envVariables,
            environment: e5.environment,
            example: e5.example,
            operation: e5.operation,
            role: a3.value === "All" ? "none" : "tabpanel",
            title: "Body",
            workspace: e5.workspace
          }, null, 8, ["id", "envVariables", "environment", "example", "operation", "role", "workspace"]), [
            [
              vShow,
              e5.operation.method && (a3.value === "All" || a3.value === "Body") && unref(canMethodHaveBody)(e5.operation.method)
            ]
          ]),
          o3[1] || (o3[1] = createBaseVNode("div", { class: "-my-0.25 flex flex-grow" }, null, -1)),
          createVNode(unref(E), null, {
            default: withCtx(() => [
              createVNode(r2, {
                collection: e5.collection,
                example: e5.example,
                operation: e5.operation,
                server: e5.server,
                workspace: e5.workspace
              }, null, 8, ["collection", "example", "operation", "server", "workspace"])
            ]),
            _: 1
          })
        ], 8, Y)
      ]),
      _: 1
    }, 8, ["aria-label"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/RequestSection.vue.js
var d2 = s2(ue, [["__scopeId", "data-v-dd5754ad"]]);

// node_modules/@scalar/api-client/dist/libs/formatters.js
var s4 = (t2, o3 = 2) => t2 > 1e3 ? (t2 / 1e3).toFixed(o3) + "s" : t2 + "ms";

// node_modules/@scalar/api-client/dist/components/AddressBar/httpStatusCodeColors.js
var e3 = {
  100: {
    color: "text-yellow"
  },
  200: {
    color: "text-green"
  },
  202: {
    color: "text-green"
  },
  300: {
    color: "text-blue"
  },
  304: {
    color: "text-blue"
  },
  400: {
    color: "text-red"
  },
  401: {
    color: "text-orange"
  },
  422: {
    color: "text-yellow"
  },
  423: {
    color: "text-purple"
  },
  505: {
    color: "text-orange"
  }
};
var r3 = (o3) => e3[o3] || {
  /** default color */
  color: "text-grey"
};

// node_modules/@scalar/api-client/dist/components/AddressBar/AddressBarHistory.vue2.js
var $6 = { class: "min-w-0" };
var j2 = { class: "text-c-1 min-w-0 truncate" };
var te = defineComponent({
  __name: "AddressBarHistory",
  props: {
    operation: {},
    target: {}
  },
  setup(m7) {
    const { requestHistory: k7, requestExampleMutators: v5 } = je(), p3 = useRouter(), f6 = computed(
      () => k7.filter((s5) => s5.request.requestUid === m7.operation.uid).slice().reverse()
    );
    function x3(s5) {
      const n4 = p3.currentRoute.value.params.workspace;
      m7.operation.uid !== s5.request.requestUid && p3.push({
        name: "request",
        params: {
          [a.Workspace]: n4,
          [a.Request]: s5.request.requestUid
        }
      }), v5.set({ ...s5.request });
    }
    return (s5, n4) => (openBlock(), createBlock(unref(ge), { as: "div" }, {
      default: withCtx(({ open: q3 }) => [
        createVNode(unref(I), {
          offset: 0,
          resize: "",
          target: s5.target
        }, createSlots({
          default: withCtx(() => {
            var u4;
            return [
              (u4 = f6.value) != null && u4.length ? (openBlock(), createBlock(unref(Se), {
                key: 0,
                class: "address-bar-history-button z-context-plus text-c-3 focus:text-c-1 relative mr-1 rounded-lg p-1.5"
              }, {
                default: withCtx(() => [
                  createVNode(unref(c), {
                    icon: "History",
                    size: "sm",
                    thickness: "2.25"
                  }),
                  n4[0] || (n4[0] = createBaseVNode("span", { class: "sr-only" }, "Request History", -1))
                ]),
                _: 1
              })) : createCommentVNode("", true)
            ];
          }),
          _: 2
        }, [
          q3 ? {
            name: "floating",
            fn: withCtx(({ width: u4 }) => [
              createVNode(unref(Me), {
                class: "custom-scroll p-0.75 grid max-h-[inherit] grid-cols-[44px,1fr,repeat(3,auto)] items-center border-t",
                static: "",
                style: normalizeStyle({ width: u4 })
              }, {
                default: withCtx(() => [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(f6.value, (t2, C4) => (openBlock(), createBlock(unref(be), {
                    key: t2.timestamp,
                    as: "button",
                    class: "font-code *:ui-active:bg-b-2 text-c-2 contents text-sm font-medium *:flex *:h-8 *:cursor-pointer *:items-center *:rounded-none *:px-1.5 first:*:rounded-l last:*:rounded-r",
                    value: C4,
                    onClick: (d3) => x3(t2)
                  }, {
                    default: withCtx(() => {
                      var d3;
                      return [
                        t2.response.method ? (openBlock(), createBlock(e, {
                          key: 0,
                          class: "text-[11px]",
                          method: t2.response.method
                        }, null, 8, ["method"])) : createCommentVNode("", true),
                        createBaseVNode("div", $6, [
                          createBaseVNode("div", j2, toDisplayString(t2.response.path), 1)
                        ]),
                        createBaseVNode("div", null, toDisplayString(unref(s4)(t2.response.duration)), 1),
                        createBaseVNode("div", {
                          class: normalizeClass([unref(r3)(t2.response.status).color])
                        }, toDisplayString(t2.response.status), 3),
                        createBaseVNode("div", null, toDisplayString((d3 = unref(httpStatusCodes)[t2.response.status]) == null ? void 0 : d3.name), 1)
                      ];
                    }),
                    _: 2
                  }, 1032, ["value", "onClick"]))), 128))
                ]),
                _: 2
              }, 1032, ["style"]),
              createVNode(unref(m), { class: "-top-[--scalar-address-bar-height] rounded-lg" })
            ]),
            key: "0"
          } : void 0
        ]), 1032, ["target"])
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/components/AddressBar/AddressBarHistory.vue.js
var a2 = s2(te, [["__scopeId", "data-v-50597ffd"]]);

// node_modules/@scalar/api-client/dist/components/Server/ServerDropdownItem.vue2.js
var P5 = ["aria-expanded"];
var j3 = { class: "overflow-hidden text-ellipsis whitespace-nowrap" };
var z4 = ["id"];
var L2 = { key: 0 };
var U2 = { class: "description text-c-3 px-3 py-1.5" };
var G3 = defineComponent({
  __name: "ServerDropdownItem",
  props: {
    collection: {},
    operation: {},
    server: {},
    serverOption: {},
    type: {}
  },
  emits: ["update:variable"],
  setup(h2, { emit: S3 }) {
    const t2 = h2, g4 = S3, u4 = useId(), { collectionMutators: k7, requestMutators: v5, servers: x3 } = je(), y5 = (e5, r5) => {
      var o3, i7;
      m7(e5) && (r5 == null || r5.stopPropagation()), t2.type === "collection" && t2.collection ? ((i7 = (o3 = t2.operation) == null ? void 0 : o3.servers) != null && i7.length && v5.edit(t2.operation.uid, "selectedServerUid", null), k7.edit(
        t2.collection.uid,
        "selectedServerUid",
        e5
      )) : t2.type === "request" && t2.operation && v5.edit(t2.operation.uid, "selectedServerUid", e5);
    }, s5 = computed(
      () => {
        var e5;
        return ((e5 = t2.server) == null ? void 0 : e5.uid) === t2.serverOption.id;
      }
    ), m7 = (e5) => {
      if (!e5) return false;
      const r5 = x3[e5];
      return Object.keys((r5 == null ? void 0 : r5.variables) ?? {}).length > 0;
    }, n4 = computed(
      () => {
        var e5;
        return s5.value && m7(((e5 = t2.server) == null ? void 0 : e5.uid) ?? "");
      }
    ), _8 = (e5, r5) => {
      g4("update:variable", e5, r5);
    };
    return (e5, r5) => {
      var o3, i7;
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["group/item flex min-h-fit flex-col rounded border", { "border-transparent": !s5.value }])
      }, [
        createBaseVNode("button", mergeProps(n4.value ? { "aria-controls": unref(u4) } : {}, {
          "aria-expanded": n4.value,
          class: ["flex min-h-8 cursor-pointer items-center gap-1.5 rounded px-1.5", s5.value ? "text-c-1 bg-b-2" : "hover:bg-b-2"],
          type: "button",
          onClick: r5[0] || (r5[0] = (w6) => y5(e5.serverOption.id, w6))
        }), [
          createVNode(unref(u), { selected: s5.value }, null, 8, ["selected"]),
          createBaseVNode("span", j3, toDisplayString(e5.serverOption.label), 1)
        ], 16, P5),
        n4.value ? (openBlock(), createElementBlock("div", {
          key: 0,
          id: unref(u4),
          class: "bg-b-2 divide divide-y rounded-b border-t *:pl-4",
          onClick: r5[1] || (r5[1] = withModifiers(() => {
          }, ["stop"]))
        }, [
          createVNode(N2, {
            variables: (o3 = e5.server) == null ? void 0 : o3.variables,
            "onUpdate:variable": _8
          }, null, 8, ["variables"]),
          (i7 = e5.server) != null && i7.description ? (openBlock(), createElementBlock("div", L2, [
            createBaseVNode("div", U2, [
              createVNode(unref(w2), {
                value: e5.server.description
              }, null, 8, ["value"])
            ])
          ])) : createCommentVNode("", true)
        ], 8, z4)) : createCommentVNode("", true)
      ], 2);
    };
  }
});

// node_modules/@scalar/api-client/dist/components/Server/ServerDropdownItem.vue.js
var p2 = s2(G3, [["__scopeId", "data-v-2ba27de2"]]);

// node_modules/@scalar/api-client/dist/components/Server/ServerDropdown.vue.js
var T3 = ["onClick"];
var j4 = { class: "flex h-4 w-4 items-center justify-center" };
var Q3 = defineComponent({
  __name: "ServerDropdown",
  props: {
    collection: {},
    operation: {},
    server: {},
    target: {}
  },
  setup(l2) {
    const { layout: y5 } = s(), { servers: p3, collectionMutators: w6, events: C4, serverMutators: D5 } = je(), f6 = computed(
      () => {
        var e5, r5;
        return (r5 = (e5 = l2.operation) == null ? void 0 : e5.servers) == null ? void 0 : r5.map((t2) => {
          var o3;
          return {
            id: t2,
            label: ((o3 = p3[t2]) == null ? void 0 : o3.url) ?? "Unknown server"
          };
        });
      }
    ), b4 = computed(
      () => {
        var e5, r5;
        return (
          // Filters out servers already present in the request
          (r5 = (e5 = l2.collection) == null ? void 0 : e5.servers) == null ? void 0 : r5.filter((t2) => {
            var o3, S3;
            return !((S3 = (o3 = l2.operation) == null ? void 0 : o3.servers) != null && S3.includes(t2));
          }).map((t2) => {
            var o3;
            return {
              id: t2,
              label: ((o3 = p3[t2]) == null ? void 0 : o3.url) ?? "Unknown server"
            };
          })
        );
      }
    ), U3 = computed(
      () => {
        var e5, r5;
        return ((e5 = f6.value) == null ? void 0 : e5.length) && ((r5 = b4.value) == null ? void 0 : r5.length);
      }
    );
    watch([() => l2.collection, () => l2.operation], ([e5, r5]) => {
      var o3;
      if (!e5 || e5.selectedServerUid || r5 != null && r5.selectedServerUid)
        return;
      const t2 = (o3 = l2.collection.servers) == null ? void 0 : o3[0];
      t2 && w6.edit(l2.collection.uid, "selectedServerUid", t2);
    });
    const B5 = () => C4.commandPalette.emit({
      commandName: "Add Server"
    }), N6 = computed(() => {
      var e5, r5, t2;
      return (r5 = (e5 = l2.server) == null ? void 0 : e5.url) != null && r5.endsWith("/") ? l2.server.url.slice(0, -1) : ((t2 = l2.server) == null ? void 0 : t2.url) || "";
    }), g4 = (e5, r5) => {
      if (!l2.server) return;
      const t2 = l2.server.variables || {};
      t2[e5] = { ...t2[e5], default: r5 }, D5.edit(l2.server.uid, "variables", t2);
    };
    return (e5, r5) => (openBlock(), createBlock(unref(b), {
      class: "max-h-[inherit] p-0 text-sm",
      focus: "",
      offset: 0,
      placement: "bottom-start",
      resize: "",
      target: e5.target,
      teleport: `#${e5.target}`
    }, {
      popover: withCtx(({ close: t2 }) => [
        createBaseVNode("div", {
          class: "custom-scroll flex max-h-[inherit] flex-col gap-1 border-t p-1",
          onClick: t2
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(f6.value, (o3) => (openBlock(), createBlock(p2, {
            key: o3.id,
            collection: e5.collection,
            operation: e5.operation,
            server: e5.server,
            serverOption: o3,
            type: "request",
            "onUpdate:variable": g4
          }, null, 8, ["collection", "operation", "server", "serverOption"]))), 128)),
          U3.value ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
            createVNode(unref(u2)),
            r5[1] || (r5[1] = createBaseVNode("div", { class: "text-xxs text-c-2 px-2.5 py-1" }, "Collection", -1))
          ], 64)) : createCommentVNode("", true),
          (openBlock(true), createElementBlock(Fragment, null, renderList(b4.value, (o3) => (openBlock(), createBlock(p2, {
            key: o3.id,
            collection: e5.collection,
            operation: e5.operation,
            server: e5.server,
            serverOption: o3,
            type: "collection",
            "onUpdate:variable": g4
          }, null, 8, ["collection", "operation", "server", "serverOption"]))), 128)),
          unref(y5) !== "modal" ? (openBlock(), createElementBlock("button", {
            key: 1,
            class: "text-xxs p-1.75 hover:bg-b-2 flex cursor-pointer items-center gap-1.5 rounded",
            type: "button",
            onClick: B5
          }, [
            createBaseVNode("div", j4, [
              createVNode(unref(c), {
                icon: "Add",
                size: "sm"
              })
            ]),
            r5[2] || (r5[2] = createBaseVNode("span", null, "Add Server", -1))
          ])) : createCommentVNode("", true)
        ], 8, T3)
      ]),
      backdrop: withCtx(() => [
        createVNode(unref(m), { class: "-top-[--scalar-address-bar-height] rounded-lg" })
      ]),
      default: withCtx(() => [
        createVNode(unref($), {
          class: "gap-0.75 z-context-plus h-6.5 hover:bg-b-2 font-code ml-0.75 text-c-2 whitespace-nowrap rounded border px-1.5 text-xs lg:text-sm",
          variant: "ghost"
        }, {
          default: withCtx(() => [
            r5[0] || (r5[0] = createBaseVNode("span", { class: "sr-only" }, "Server:", -1)),
            createTextVNode(" " + toDisplayString(N6.value), 1)
          ]),
          _: 1
        })
      ]),
      _: 1
    }, 8, ["target", "teleport"]));
  }
});

// node_modules/@scalar/api-client/dist/components/AddressBar/AddressBar.vue2.js
var G4 = ["id"];
var J2 = { class: "m-auto flex flex-row items-center" };
var X3 = { class: "address-bar-bg-states text-xxs p-0.75 group relative order-last flex w-full max-w-[calc(100dvw-24px)] flex-1 flex-row items-stretch rounded-lg lg:order-none lg:min-w-[580px] lg:max-w-[580px] xl:min-w-[720px] xl:max-w-[720px]" };
var Y2 = { class: "pointer-events-none absolute left-0 top-0 block h-full w-full overflow-hidden rounded-lg border" };
var Z2 = { class: "z-context-plus flex gap-1" };
var _7 = { class: "scroll-timeline-x scroll-timeline-x-hidden z-context-plus relative flex w-full bg-blend-normal" };
var ee2 = {
  "aria-hidden": "true",
  class: "inline-flex items-center gap-1"
};
var oe2 = { class: "sr-only" };
var ce2 = defineComponent({
  __name: "AddressBar",
  props: {
    collection: {},
    operation: {},
    server: {},
    environment: {},
    envVariables: {},
    workspace: {}
  },
  emits: ["importCurl"],
  setup(n4) {
    const p3 = useId(), { requestMutators: v5, events: c4 } = je(), { layout: h2 } = s(), f6 = ref(null), g4 = ref(null), B5 = (e5) => {
      n4.operation.path !== e5 && v5.edit(n4.operation.uid, "path", e5);
    };
    watch(
      () => n4.operation.path,
      (e5) => {
        e5 && B5(e5);
      }
    );
    const t2 = ref(100), w6 = ref(0), a3 = ref(false), s5 = ref();
    function C4() {
      a3.value ? t2.value -= (t2.value - 15) / 60 : t2.value -= w6.value / 20, t2.value <= 0 && (clearInterval(s5.value), s5.value = void 0, t2.value = 100, a3.value = false);
    }
    function y5() {
      s5.value || (a3.value = true, s5.value = setInterval(C4, 20));
    }
    function R2() {
      w6.value = t2.value, a3.value = false;
    }
    function q3() {
      clearInterval(s5.value), s5.value = void 0, t2.value = 100, a3.value = false;
    }
    c4.requestStatus.on((e5) => {
      e5 === "start" && y5(), e5 === "stop" && R2(), e5 === "abort" && q3();
    }), c4.focusAddressBar.on(() => {
      var e5, o3, d3;
      console.log("focusAddressBar", g4.value, f6.value), h2 === "modal" ? (o3 = (e5 = g4.value) == null ? void 0 : e5.$el) == null || o3.focus() : (d3 = f6.value) == null || d3.focus();
    });
    function S3(e5) {
      v5.edit(n4.operation.uid, "method", e5);
    }
    function E6() {
      const { method: e5 } = n4.operation;
      return REQUEST_METHODS[e5].backgroundColor;
    }
    function b4() {
      a3.value || (a3.value = true, c4.executeRequest.emit({ requestUid: n4.operation.uid }));
    }
    c4.hotKeys.on((e5) => {
      var o3;
      e5 != null && e5.focusAddressBar && ((o3 = f6.value) == null || o3.focus()), e5 != null && e5.executeRequest && b4();
    });
    function V4(e5) {
      v5.edit(n4.operation.uid, "path", e5);
    }
    return (e5, o3) => {
      var d3;
      return openBlock(), createElementBlock("div", {
        id: unref(p3),
        class: "scalar-address-bar order-last h-[--scalar-address-bar-height] w-full [--scalar-address-bar-height:32px] lg:order-none lg:w-auto"
      }, [
        createBaseVNode("div", J2, [
          createBaseVNode("div", X3, [
            createBaseVNode("div", Y2, [
              createBaseVNode("div", {
                class: normalizeClass(["bg-mix-transparent bg-mix-amount-90 absolute left-0 top-0 z-[1002] h-full w-full", E6()]),
                style: normalizeStyle({ transform: `translate3d(-${t2.value}%,0,0)` })
              }, null, 6)
            ]),
            createBaseVNode("div", Z2, [
              createVNode(e, {
                isEditable: unref(h2) !== "modal",
                isSquare: "",
                method: e5.operation.method,
                teleport: "",
                onChange: S3
              }, null, 8, ["isEditable", "method"])
            ]),
            createBaseVNode("div", _7, [
              e5.collection.servers.length ? (openBlock(), createBlock(unref(Q3), {
                key: 0,
                collection: e5.collection,
                layout: "client",
                operation: e5.operation,
                server: e5.server,
                target: unref(p3)
              }, null, 8, ["collection", "operation", "server", "target"])) : createCommentVNode("", true),
              o3[1] || (o3[1] = createBaseVNode("div", { class: "fade-left" }, null, -1)),
              createVNode(_2, {
                ref_key: "addressBarRef",
                ref: f6,
                "aria-label": "Path",
                class: "min-w-fit outline-none",
                disableCloseBrackets: "",
                disabled: unref(h2) === "modal",
                disableEnter: "",
                disableTabIndent: "",
                emitOnBlur: false,
                envVariables: e5.envVariables,
                environment: e5.environment,
                importCurl: "",
                modelValue: e5.operation.path,
                placeholder: (d3 = e5.server) != null && d3.uid && e5.collection.servers.includes(e5.server.uid) ? "" : "Enter a URL or cURL command",
                server: "",
                workspace: e5.workspace,
                onCurl: o3[0] || (o3[0] = (m7) => e5.$emit("importCurl", m7)),
                onSubmit: b4,
                "onUpdate:modelValue": V4
              }, null, 8, ["disabled", "envVariables", "environment", "modelValue", "placeholder", "workspace"]),
              o3[2] || (o3[2] = createBaseVNode("div", { class: "fade-right" }, null, -1))
            ]),
            createVNode(a2, {
              operation: e5.operation,
              target: unref(p3)
            }, null, 8, ["operation", "target"]),
            createVNode(unref($), {
              ref_key: "sendButtonRef",
              ref: g4,
              class: "z-context-plus relative h-auto shrink-0 overflow-hidden py-1 pl-2 pr-2.5 font-bold",
              disabled: a3.value,
              onClick: b4
            }, {
              default: withCtx(() => {
                var m7;
                return [
                  createBaseVNode("span", ee2, [
                    createVNode(unref(c), {
                      class: "relative shrink-0 fill-current",
                      icon: "Play",
                      size: "xs"
                    }),
                    o3[3] || (o3[3] = createBaseVNode("span", { class: "text-xxs hidden lg:flex" }, "Send", -1))
                  ]),
                  createBaseVNode("span", oe2, " Send " + toDisplayString(e5.operation.method) + " request to " + toDisplayString(((m7 = e5.server) == null ? void 0 : m7.url) ?? "") + toDisplayString(e5.operation.path), 1)
                ];
              }),
              _: 1
            }, 8, ["disabled"])
          ])
        ])
      ], 8, G4);
    };
  }
});

// node_modules/@scalar/api-client/dist/components/AddressBar/AddressBar.vue.js
var e4 = s2(ce2, [["__scopeId", "data-v-5e5a3c5f"]]);

// node_modules/@scalar/api-client/dist/components/OpenApiClientButton.vue2.js
var k6 = ["href"];
var v4 = defineComponent({
  __name: "OpenApiClientButton",
  props: {
    buttonSource: {},
    source: { default: "api-reference" },
    isDevelopment: { type: Boolean },
    integration: {},
    url: {}
  },
  setup(o3) {
    const a3 = computed(() => {
      const c4 = o3.url ?? (typeof window < "u" ? window.location.href : void 0), t2 = makeUrlAbsolute(c4);
      if (!(t2 != null && t2.length))
        return;
      const e5 = new URL(
        o3.isDevelopment ? "http://localhost:5065" : "https://client.scalar.com"
      );
      if (e5.searchParams.set("url", t2), o3.integration !== null && e5.searchParams.set("integration", o3.integration ?? "vue"), e5.searchParams.set("utm_source", "api-reference"), e5.searchParams.set("utm_medium", "button"), e5.searchParams.set("utm_campaign", o3.buttonSource), o3.source === "gitbook") {
        e5.searchParams.set("utm_source", "gitbook");
        const n4 = document.querySelector("img.dark\\:block[alt='Logo']"), r5 = document.querySelector("img.dark\\:hidden[alt='Logo']");
        n4 && n4 instanceof HTMLImageElement && e5.searchParams.set("dark_logo", encodeURIComponent(n4.src)), r5 && r5 instanceof HTMLImageElement && e5.searchParams.set("light_logo", encodeURIComponent(r5.src));
      }
      return e5.toString();
    });
    return (c4, t2) => a3.value ? (openBlock(), createElementBlock("a", {
      key: 0,
      class: "open-api-client-button",
      href: a3.value,
      target: "_blank"
    }, [
      createVNode(unref(c), {
        icon: "ExternalLink",
        size: "xs",
        thickness: "2.5"
      }),
      t2[0] || (t2[0] = createTextVNode(" Open API Client "))
    ], 8, k6)) : createCommentVNode("", true);
  }
});

// node_modules/@scalar/api-client/dist/components/OpenApiClientButton.vue.js
var i6 = s2(v4, [["__scopeId", "data-v-01d14731"]]);

// node_modules/@scalar/api-client/dist/libs/extractAttachmentFilename.js
var n3 = (e5) => {
  try {
    return decodeURIComponent(e5);
  } catch {
    return e5;
  }
};
function r4(e5) {
  var t2, m7;
  let a3 = "";
  if (e5) {
    const c4 = ((t2 = e5.match(/filename\*=UTF-8''([^;]+)/)) == null ? void 0 : t2[1]) ?? ((m7 = e5.match(/filename\s*=\s*"?([^";]+)"?/)) == null ? void 0 : m7[1]);
    c4 && (a3 = n3(c4.trim()));
  }
  return a3;
}

// node_modules/@scalar/api-client/dist/hooks/useResponseBody.js
var import_whatwg_mimetype = __toESM(require_mime_type(), 1);
function y4(e5) {
  const i7 = (t2) => t2 instanceof Blob, a3 = computed(() => {
    var n4;
    const t2 = ((n4 = e5.headers.find((r5) => r5.name.toLowerCase() === "content-type")) == null ? void 0 : n4.value) ?? "";
    return new import_whatwg_mimetype.default(t2);
  }), c4 = computed(() => {
    var n4;
    const t2 = ((n4 = e5.headers.find((r5) => r5.name.toLowerCase() === "content-disposition")) == null ? void 0 : n4.value) ?? "";
    return r4(t2);
  }), d3 = computed(() => i7(e5.data) ? URL.createObjectURL(e5.data) : typeof e5.data == "string" ? URL.createObjectURL(new Blob([e5.data], { type: a3.value.toString() })) : e5.data instanceof Object && Object.keys(e5.data).length ? URL.createObjectURL(
    new Blob([JSON.stringify(e5.data)], {
      type: a3.value.toString()
    })
  ) : "");
  return { mimeType: a3, attachmentFilename: c4, dataUrl: d3 };
}

export {
  y4 as y,
  f3 as f,
  P2 as P,
  m6 as m,
  snippetz,
  c3 as c,
  S2 as S,
  d2 as d,
  e4 as e,
  i6 as i
};
//# sourceMappingURL=chunk-7QKLUF55.js.map
