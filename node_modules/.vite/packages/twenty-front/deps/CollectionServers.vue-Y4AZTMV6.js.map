{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/CollectionServerForm.vue.js", "../../../../@scalar/api-client/dist/views/Collection/CollectionServers.vue2.js"], "sourcesContent": ["import { defineComponent as _, computed as u, watch as h, openBlock as o, createElementBlock as c, Fragment as k, createVNode as x, createBlock as y, createCommentVNode as n } from \"vue\";\nimport { REGEX as U } from \"@scalar/oas-utils/helpers\";\nimport E from \"../../components/Form/Form.vue.js\";\nimport S from \"../../components/Server/ServerVariablesForm.vue.js\";\nimport { useActiveEntities as g } from \"../../store/active-entities.js\";\nimport { useWorkspace as B } from \"../../store/store.js\";\nconst C = { class: \"bg-b-1 divide-0.5 flex w-full flex-col divide-y text-sm\" }, W = /* @__PURE__ */ _({\n  __name: \"CollectionServerForm\",\n  props: {\n    collectionId: { default: \"\" },\n    serverUid: { default: \"\" }\n  },\n  setup(v) {\n    const l = v, { activeWorkspaceCollections: s } = g(), { servers: d, serverMutators: i } = B(), p = [\n      {\n        label: \"URL\",\n        key: \"url\",\n        placeholder: \"https://void.scalar.com\",\n        type: \"text\"\n      },\n      {\n        label: \"Description\",\n        key: \"description\",\n        placeholder: \"Production\",\n        type: \"text\"\n      }\n    ], t = u(() => {\n      const e = s.value.find(\n        (r) => r.uid === l.collectionId\n      );\n      return d[e && typeof l.serverUid == \"string\" && l.serverUid === \"default\" ? e.servers[0] ?? \"\" : (e == null ? void 0 : e.servers.find((r) => r === l.serverUid)) ?? \"\"];\n    }), f = u(() => {\n      var e, r;\n      return (e = t.value) != null && e.url ? ((r = t.value.url.match(U.PATH)) == null ? void 0 : r.map((a) => a.slice(1, -1))) ?? [] : [];\n    });\n    h(\n      f,\n      (e) => {\n        if (!t.value) return;\n        const r = t.value.variables ? { ...t.value.variables } : {};\n        Object.keys(r).forEach((a) => {\n          e.includes(a) || delete r[a];\n        }), e.forEach((a) => {\n          r[a] || (r[a] = { default: \"\" });\n        }), i.edit(t.value.uid, \"variables\", r);\n      },\n      { immediate: !0 }\n    );\n    const m = (e, r) => {\n      !s.value || !t.value || i.edit(t.value.uid, e, r);\n    }, b = (e, r) => {\n      if (!t.value) return;\n      const a = t.value.variables || {};\n      a[e] = { ...a[e], default: r }, i.edit(t.value.uid, \"variables\", a);\n    };\n    return (e, r) => (o(), c(\"div\", C, [\n      t.value ? (o(), c(k, { key: 0 }, [\n        x(E, {\n          data: t.value,\n          onUpdate: m,\n          options: p\n        }, null, 8, [\"data\"]),\n        t.value.variables ? (o(), y(S, {\n          key: 0,\n          variables: t.value.variables,\n          \"onUpdate:variable\": b\n        }, null, 8, [\"variables\"])) : n(\"\", !0)\n      ], 64)) : n(\"\", !0)\n    ]));\n  }\n});\nexport {\n  W as default\n};\n", "import { defineComponent as w, ref as C, computed as D, openBlock as n, createElementBlock as v, createElementVNode as i, createStaticVNode as A, Fragment as M, renderList as N, createBlock as p, unref as e, toDisplayString as z, createVNode as l, withCtx as a, createCommentVNode as I } from \"vue\";\nimport { useModal as $, ScalarMarkdown as j, ScalarDropdown as B, ScalarDropdownItem as E, ScalarIcon as m, ScalarButton as x, ScalarModal as U } from \"@scalar/components\";\nimport V from \"../../components/Sidebar/Actions/DeleteSidebarListElement.vue.js\";\nimport { useActiveEntities as L } from \"../../store/active-entities.js\";\nimport P from \"./CollectionServerForm.vue.js\";\nimport { useWorkspace as F } from \"../../store/store.js\";\nconst O = { class: \"flex h-full w-full flex-col gap-12 px-1.5 pt-8\" }, R = { class: \"flex flex-col gap-4\" }, T = { class: \"bg-b-2 overflow-hidden rounded-lg border\" }, W = { class: \"flex items-center justify-between py-1 pl-3 pr-1 text-sm\" }, Y = { key: 1 }, q = { class: \"text-c-3 flex h-full items-center justify-center rounded-lg border p-4\" }, te = /* @__PURE__ */ w({\n  __name: \"CollectionServers\",\n  setup(G) {\n    const { activeCollection: o } = L(), { servers: f, events: h, serverMutators: _ } = F(), u = $(), d = C(null), S = D(() => {\n      var s;\n      return !f || !((s = o.value) != null && s.servers) ? [] : Object.values(f).filter(\n        (t) => {\n          var c;\n          return (c = o.value) == null ? void 0 : c.servers.includes(t.uid);\n        }\n      );\n    }), b = () => h.commandPalette.emit({\n      commandName: \"Add Server\"\n    }), g = () => {\n      var s;\n      !((s = o.value) != null && s.uid) || !d.value || (_.delete(d.value, o.value.uid), u.hide());\n    }, k = (s) => {\n      d.value = s, u.show();\n    };\n    return (s, t) => {\n      var c;\n      return n(), v(\"div\", O, [\n        i(\"div\", R, [\n          t[3] || (t[3] = A('<div class=\"flex items-start justify-between gap-2\"><div class=\"flex flex-col\"><div class=\"flex h-8 items-center\"><h3 class=\"font-bold\">Servers</h3></div><p class=\"text-sm\"> Add different base URLs for your API. You can use <code class=\"font-code text-c-2\">{variables}</code> for dynamic parts. </p></div></div>', 1)),\n          (n(!0), v(M, null, N(S.value, (r, y) => (n(), v(\"div\", {\n            key: r.uid\n          }, [\n            i(\"div\", T, [\n              i(\"div\", W, [\n                r.description ? (n(), p(e(j), {\n                  key: 0,\n                  value: r.description\n                }, null, 8, [\"value\"])) : (n(), v(\"span\", Y, \"Server \" + z(y + 1), 1)),\n                l(e(B), { placement: \"bottom-end\" }, {\n                  items: a(() => [\n                    l(e(E), {\n                      class: \"flex gap-2 font-normal\",\n                      onClick: (H) => k(r.uid)\n                    }, {\n                      default: a(() => [\n                        l(e(m), {\n                          class: \"inline-flex\",\n                          icon: \"Delete\",\n                          size: \"sm\",\n                          thickness: \"1.5\"\n                        }),\n                        t[1] || (t[1] = i(\"span\", null, \"Delete\", -1))\n                      ]),\n                      _: 2\n                    }, 1032, [\"onClick\"])\n                  ]),\n                  default: a(() => [\n                    l(e(x), {\n                      class: \"hover:bg-b-3 h-full max-h-8 gap-1 p-1 text-xs\",\n                      variant: \"ghost\"\n                    }, {\n                      default: a(() => [\n                        l(e(m), {\n                          class: \"text-c-3\",\n                          icon: \"Ellipses\",\n                          size: \"md\"\n                        })\n                      ]),\n                      _: 1\n                    })\n                  ]),\n                  _: 2\n                }, 1024)\n              ]),\n              e(o) ? (n(), p(P, {\n                key: 0,\n                collectionId: e(o).uid,\n                serverUid: r.uid\n              }, null, 8, [\"collectionId\", \"serverUid\"])) : I(\"\", !0)\n            ])\n          ]))), 128)),\n          i(\"div\", q, [\n            l(e(x), {\n              class: \"hover:bg-b-2 hover:text-c-1 flex items-center gap-2\",\n              size: \"sm\",\n              variant: \"ghost\",\n              onClick: b\n            }, {\n              default: a(() => [\n                l(e(m), {\n                  class: \"inline-flex\",\n                  icon: \"Add\",\n                  size: \"sm\",\n                  thickness: \"1.5\"\n                }),\n                t[2] || (t[2] = i(\"span\", null, \"Add Server\", -1))\n              ]),\n              _: 1\n            })\n          ])\n        ]),\n        l(e(U), {\n          size: \"xxs\",\n          state: e(u),\n          title: `Delete ${d.value ? (c = e(f)[d.value]) == null ? void 0 : c.url : \"Server\"}`\n        }, {\n          default: a(() => [\n            l(V, {\n              variableName: \"Server\",\n              warningMessage: \"Are you sure you want to delete this server? This action cannot be undone.\",\n              onClose: t[0] || (t[0] = (r) => e(u).hide()),\n              onDelete: g\n            })\n          ]),\n          _: 1\n        }, 8, [\"state\", \"title\"])\n      ]);\n    };\n  }\n});\nexport {\n  te as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,IAAI,EAAE,OAAO,0DAA0D;AAA7E,IAAgF,IAAoB,gBAAE;AAAA,EACpG,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,cAAc,EAAE,SAAS,GAAG;AAAA,IAC5B,WAAW,EAAE,SAAS,GAAG;AAAA,EAC3B;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,EAAE,4BAA4B,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,GAAG,gBAAgB,EAAE,IAAI,GAAE,GAAG,IAAI;AAAA,MACjG;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,IACF,GAAG,IAAI,SAAE,MAAM;AACb,YAAM,IAAI,EAAE,MAAM;AAAA,QAChB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAAA,MACrB;AACA,aAAO,EAAE,KAAK,OAAO,EAAE,aAAa,YAAY,EAAE,cAAc,YAAY,EAAE,QAAQ,CAAC,KAAK,MAAM,KAAK,OAAO,SAAS,EAAE,QAAQ,KAAK,CAAC,MAAM,MAAM,EAAE,SAAS,MAAM,EAAE;AAAA,IACxK,CAAC,GAAGA,KAAI,SAAE,MAAM;AACd,UAAI,GAAG;AACP,cAAQ,IAAI,EAAE,UAAU,QAAQ,EAAE,QAAQ,IAAI,EAAE,MAAM,IAAI,MAAM,MAAE,IAAI,MAAM,OAAO,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;AAAA,IACrI,CAAC;AACD;AAAA,MACEA;AAAA,MACA,CAAC,MAAM;AACL,YAAI,CAAC,EAAE,MAAO;AACd,cAAM,IAAI,EAAE,MAAM,YAAY,EAAE,GAAG,EAAE,MAAM,UAAU,IAAI,CAAC;AAC1D,eAAO,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC5B,YAAE,SAAS,CAAC,KAAK,OAAO,EAAE,CAAC;AAAA,QAC7B,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM;AACnB,YAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,SAAS,GAAG;AAAA,QAChC,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,KAAK,aAAa,CAAC;AAAA,MACxC;AAAA,MACA,EAAE,WAAW,KAAG;AAAA,IAClB;AACA,UAAMC,KAAI,CAAC,GAAG,MAAM;AAClB,OAAC,EAAE,SAAS,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,KAAK,GAAG,CAAC;AAAA,IAClD,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,UAAI,CAAC,EAAE,MAAO;AACd,YAAM,IAAI,EAAE,MAAM,aAAa,CAAC;AAChC,QAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,KAAK,aAAa,CAAC;AAAA,IACpE;AACA,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,MACjC,EAAE,SAAS,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,QAC/B,YAAE,GAAG;AAAA,UACH,MAAM,EAAE;AAAA,UACR,UAAUA;AAAA,UACV,SAAS;AAAA,QACX,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;AAAA,QACpB,EAAE,MAAM,aAAa,UAAE,GAAG,YAAE,GAAG;AAAA,UAC7B,KAAK;AAAA,UACL,WAAW,EAAE,MAAM;AAAA,UACnB,qBAAqB;AAAA,QACvB,GAAG,MAAM,GAAG,CAAC,WAAW,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACxC,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,IACpB,CAAC;AAAA,EACH;AACF,CAAC;;;AChED,IAAM,IAAI,EAAE,OAAO,iDAAiD;AAApE,IAAuE,IAAI,EAAE,OAAO,sBAAsB;AAA1G,IAA6G,IAAI,EAAE,OAAO,2CAA2C;AAArK,IAAwKC,KAAI,EAAE,OAAO,2DAA2D;AAAhP,IAAmP,IAAI,EAAE,KAAK,EAAE;AAAhQ,IAAmQC,KAAI,EAAE,OAAO,yEAAyE;AAAzV,IAA4V,KAAqB,gBAAE;AAAA,EACjX,QAAQ;AAAA,EACR,MAAM,GAAG;AACP,UAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,GAAG,EAAE,SAASC,IAAG,QAAQ,GAAG,gBAAgB,EAAE,IAAI,GAAE,GAAG,IAAI,EAAE,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,SAAE,MAAM;AACzH,UAAI;AACJ,aAAO,CAACA,MAAK,GAAG,IAAI,EAAE,UAAU,QAAQ,EAAE,WAAW,CAAC,IAAI,OAAO,OAAOA,EAAC,EAAE;AAAA,QACzE,CAAC,MAAM;AACL,cAAIC;AACJ,kBAAQA,KAAI,EAAE,UAAU,OAAO,SAASA,GAAE,QAAQ,SAAS,EAAE,GAAG;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC,GAAG,IAAI,MAAM,EAAE,eAAe,KAAK;AAAA,MAClC,aAAa;AAAA,IACf,CAAC,GAAG,IAAI,MAAM;AACZ,UAAI;AACJ,SAAG,IAAI,EAAE,UAAU,QAAQ,EAAE,QAAQ,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,GAAG,GAAG,EAAE,KAAK;AAAA,IAC3F,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,QAAQ,GAAG,EAAE,KAAK;AAAA,IACtB;AACA,WAAO,CAAC,GAAG,MAAM;AACf,UAAIA;AACJ,aAAO,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,QACtB,gBAAE,OAAO,GAAG;AAAA,UACV,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,kBAAE,2TAA2T,CAAC;AAAA,WAC7U,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAE,EAAE,OAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,YACrD,KAAK,EAAE;AAAA,UACT,GAAG;AAAA,YACD,gBAAE,OAAO,GAAG;AAAA,cACV,gBAAE,OAAOH,IAAG;AAAA,gBACV,EAAE,eAAe,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,kBAC5B,KAAK;AAAA,kBACL,OAAO,EAAE;AAAA,gBACX,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,UAAE,GAAG,mBAAE,QAAQ,GAAG,YAAY,gBAAE,IAAI,CAAC,GAAG,CAAC;AAAA,gBACpE,YAAE,MAAE,CAAC,GAAG,EAAE,WAAW,aAAa,GAAG;AAAA,kBACnC,OAAO,QAAE,MAAM;AAAA,oBACb,YAAE,MAAE,CAAC,GAAG;AAAA,sBACN,OAAO;AAAA,sBACP,SAAS,CAAC,MAAM,EAAE,EAAE,GAAG;AAAA,oBACzB,GAAG;AAAA,sBACD,SAAS,QAAE,MAAM;AAAA,wBACf,YAAE,MAAE,CAAC,GAAG;AAAA,0BACN,OAAO;AAAA,0BACP,MAAM;AAAA,0BACN,MAAM;AAAA,0BACN,WAAW;AAAA,wBACb,CAAC;AAAA,wBACD,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,QAAQ,MAAM,UAAU,EAAE;AAAA,sBAC9C,CAAC;AAAA,sBACD,GAAG;AAAA,oBACL,GAAG,MAAM,CAAC,SAAS,CAAC;AAAA,kBACtB,CAAC;AAAA,kBACD,SAAS,QAAE,MAAM;AAAA,oBACf,YAAE,MAAE,CAAC,GAAG;AAAA,sBACN,OAAO;AAAA,sBACP,SAAS;AAAA,oBACX,GAAG;AAAA,sBACD,SAAS,QAAE,MAAM;AAAA,wBACf,YAAE,MAAE,CAAC,GAAG;AAAA,0BACN,OAAO;AAAA,0BACP,MAAM;AAAA,0BACN,MAAM;AAAA,wBACR,CAAC;AAAA,sBACH,CAAC;AAAA,sBACD,GAAG;AAAA,oBACL,CAAC;AAAA,kBACH,CAAC;AAAA,kBACD,GAAG;AAAA,gBACL,GAAG,IAAI;AAAA,cACT,CAAC;AAAA,cACD,MAAE,CAAC,KAAK,UAAE,GAAG,YAAE,GAAG;AAAA,gBAChB,KAAK;AAAA,gBACL,cAAc,MAAE,CAAC,EAAE;AAAA,gBACnB,WAAW,EAAE;AAAA,cACf,GAAG,MAAM,GAAG,CAAC,gBAAgB,WAAW,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACxD,CAAC;AAAA,UACH,CAAC,EAAE,GAAG,GAAG;AAAA,UACT,gBAAE,OAAOC,IAAG;AAAA,YACV,YAAE,MAAE,CAAC,GAAG;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,YACX,GAAG;AAAA,cACD,SAAS,QAAE,MAAM;AAAA,gBACf,YAAE,MAAE,CAAC,GAAG;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,WAAW;AAAA,gBACb,CAAC;AAAA,gBACD,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,QAAQ,MAAM,cAAc,EAAE;AAAA,cAClD,CAAC;AAAA,cACD,GAAG;AAAA,YACL,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,QACD,YAAE,MAAE,CAAC,GAAG;AAAA,UACN,MAAM;AAAA,UACN,OAAO,MAAE,CAAC;AAAA,UACV,OAAO,UAAU,EAAE,SAASE,KAAI,MAAED,EAAC,EAAE,EAAE,KAAK,MAAM,OAAO,SAASC,GAAE,MAAM,QAAQ;AAAA,QACpF,GAAG;AAAA,UACD,SAAS,QAAE,MAAM;AAAA,YACf,YAAEC,IAAG;AAAA,cACH,cAAc;AAAA,cACd,gBAAgB;AAAA,cAChB,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,MAAE,CAAC,EAAE,KAAK;AAAA,cAC1C,UAAU;AAAA,YACZ,CAAC;AAAA,UACH,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,GAAG,CAAC,SAAS,OAAO,CAAC;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;", "names": ["f", "m", "W", "q", "f", "c", "w"]}