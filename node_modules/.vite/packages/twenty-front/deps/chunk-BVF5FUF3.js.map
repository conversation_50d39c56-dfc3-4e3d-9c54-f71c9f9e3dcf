{"version": 3, "sources": ["../../../../remark-stringify/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownOptions\n * @typedef {import('unified').Compiler<Root, string>} Compiler\n * @typedef {import('unified').Processor<undefined, undefined, undefined, Root, string>} Processor\n */\n\n/**\n * @typedef {Omit<ToMarkdownOptions, 'extensions'>} Options\n */\n\nimport {toMarkdown} from 'mdast-util-to-markdown'\n\n/**\n * Add support for serializing to markdown.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkStringify(options) {\n  /** @type {Processor} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n\n  self.compiler = compiler\n\n  /**\n   * @type {Compiler}\n   */\n  function compiler(tree) {\n    return toMarkdown(tree, {\n      ...self.data('settings'),\n      ...options,\n      // Note: this option is not in the readme.\n      // The goal is for it to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: self.data('toMarkdownExtensions') || []\n    })\n  }\n}\n"], "mappings": ";;;;;AAqBe,SAAR,gBAAiC,SAAS;AAG/C,QAAM,OAAO;AAEb,OAAK,WAAW;AAKhB,WAAS,SAAS,MAAM;AACtB,WAAO,WAAW,MAAM;AAAA,MACtB,GAAG,KAAK,KAAK,UAAU;AAAA,MACvB,GAAG;AAAA;AAAA;AAAA;AAAA,MAIH,YAAY,KAAK,KAAK,sBAAsB,KAAK,CAAC;AAAA,IACpD,CAAC;AAAA,EACH;AACF;", "names": []}