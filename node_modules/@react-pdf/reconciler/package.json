{"name": "@react-pdf/reconciler", "version": "1.1.4", "license": "MIT", "description": "Define uninitialized elements", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/diegomura/react-pdf#readme", "type": "module", "main": "./lib/index.js", "types": "./lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/reconciler"}, "scripts": {"build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w", "size": "size-limit", "lint": "eslint src", "test": "vitest"}, "files": ["lib"], "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "dependencies": {"object-assign": "^4.1.1", "scheduler": "0.25.0-rc-603e6108-20241029"}, "devDependencies": {"ast-types": "^0.14.2", "react-reconciler-23": "npm:react-reconciler@0.23.0", "react-reconciler-31": "npm:react-reconciler@0.31.0-rc-603e6108-20241029", "recast": "^0.23.9"}}