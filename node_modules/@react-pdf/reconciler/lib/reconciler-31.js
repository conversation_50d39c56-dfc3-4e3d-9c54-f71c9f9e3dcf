import e from"react";import*as n from"scheduler";function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e){if(e.__esModule)return e;var n=e.default;if("function"==typeof n){var t=function e(){return this instanceof e?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};t.prototype=n.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach((function(n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})})),t}var l,a={exports:{}},o={exports:{}},u=r(n);var i,s,c={exports:{}};
/**
 * @license React
 * react-reconciler.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */"production"===process.env.NODE_ENV?a.exports=(l||(l=1,(s=o).exports=function(n){function t(e,n,t,r){return new $r(e,n,t,r)}function r(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=Cl&&e[Cl]||e["@@iterator"])?e:null}function a(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===El?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case dl:return"Fragment";case fl:return"Portal";case ml:return"Profiler";case pl:return"StrictMode";case vl:return"Suspense";case Sl:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case yl:return(e.displayName||"Context")+".Provider";case gl:return(e._context.displayName||"Context")+".Consumer";case bl:var n=e.render;return(e=e.displayName)||(e=""!==(e=n.displayName||n.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case kl:return null!==(n=e.displayName||null)?n:a(e.type)||"Memo";case wl:n=e._payload,e=e._init;try{return a(e(n))}catch(e){}}return null}function o(e){if(void 0===rl)try{throw Error()}catch(e){var n=e.stack.trim().match(/\n( *(at )?)/);rl=n&&n[1]||"",ll=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+rl+e+ll}function i(e,n){if(!e||_l)return"";_l=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(n){var t=function(){throw Error()};if(Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}}else{try{throw Error()}catch(e){r=e}(t=e())&&"function"==typeof t.catch&&t.catch((function(){}))}}catch(e){if(e&&r&&"string"==typeof e.stack)return[e.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),u=a[0],i=a[1];if(u&&i){var s=u.split("\n"),c=i.split("\n");for(l=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;l<c.length&&!c[l].includes("DetermineComponentFrameRoot");)l++;if(r===s.length||l===c.length)for(r=s.length-1,l=c.length-1;1<=r&&0<=l&&s[r]!==c[l];)l--;for(;1<=r&&0<=l;r--,l--)if(s[r]!==c[l]){if(1!==r||1!==l)do{if(r--,0>--l||s[r]!==c[l]){var f="\n"+s[r].replace(" at new "," at ");return e.displayName&&f.includes("<anonymous>")&&(f=f.replace("<anonymous>",e.displayName)),f}}while(1<=r&&0<=l);break}}}finally{_l=!1,Error.prepareStackTrace=t}return(t=e?e.displayName||e.name:"")?o(t):""}function s(e){switch(e.tag){case 26:case 27:case 5:return o(e.type);case 16:return o("Lazy");case 13:return o("Suspense");case 19:return o("SuspenseList");case 0:case 15:return i(e.type,!1);case 11:return i(e.type.render,!1);case 1:return i(e.type,!0);default:return""}}function c(e){try{var n="";do{n+=s(e),e=e.return}while(e);return n}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function f(e){return{current:e}}function d(e){0>Na||(e.current=Ta[Na],Ta[Na]=null,Na--)}function p(e,n){Na++,Ta[Na]=e.current,e.current=n}function m(e){var n=42&e;if(0!==n)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function h(e,n){var t=e.pendingLanes;if(0===t)return 0;var r=0,l=e.suspendedLanes;e=e.pingedLanes;var a=134217727&t;return 0!==a?0!=(t=a&~l)?r=m(t):0!=(e&=a)&&(r=m(e)):0!=(t&=~l)?r=m(t):0!==e&&(r=m(e)),0===r?0:0!==n&&n!==r&&0==(n&l)&&((l=r&-r)>=(e=n&-n)||32===l&&0!=(4194176&e))?n:r}function g(e,n){switch(e){case 1:case 2:case 4:case 8:return n+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;default:return-1}}function y(){var e=Fa;return 0==(4194176&(Fa<<=1))&&(Fa=128),e}function b(){var e=Ma;return 0==(62914560&(Ma<<=1))&&(Ma=4194304),e}function v(e){for(var n=[],t=0;31>t;t++)n.push(e);return n}function S(e,n){e.pendingLanes|=n,268435456!==n&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function k(e,n,t){e.pendingLanes|=n,e.suspendedLanes&=~n;var r=31-Ua(n);e.entangledLanes|=n,e.entanglements[r]=1073741824|e.entanglements[r]|4194218&t}function w(e,n){var t=e.entangledLanes|=n;for(e=e.entanglements;t;){var r=31-Ua(t),l=1<<r;l&n|e[r]&n&&(e[r]|=n),t&=~l}}function x(e){return 2<(e&=-e)?8<e?0!=(134217727&e)?32:268435456:8:2}function z(e){"function"==typeof qa&&Ya(e)}function C(e,n){if("object"==typeof e&&null!==e){var t=Ka.get(e);return void 0!==t?t:(n={value:e,source:n,stack:c(n)},Ka.set(e,n),n)}return{value:e,source:n,stack:c(n)}}function E(e){for(;e===eo;)eo=Xa[--Za],Xa[Za]=null,Xa[--Za],Xa[Za]=null;for(;e===ro;)ro=no[--to],no[to]=null,no[--to],no[to]=null,no[--to],no[to]=null}function P(e,n){p(oo,n),p(ao,e),p(lo,null),e=Nl(n),d(lo),p(lo,e)}function _(){d(lo),d(ao),d(oo)}function R(e){null!==e.memoizedState&&p(uo,e);var n=lo.current,t=Ll(n,e.type);n!==t&&(p(ao,e),p(lo,t))}function T(e){ao.current===e&&(d(lo),d(ao)),uo.current===e&&(d(uo),ra._currentValue2=ta)}function N(){for(var e=fo,n=po=fo=0;n<e;){var t=co[n];co[n++]=null;var r=co[n];co[n++]=null;var l=co[n];co[n++]=null;var a=co[n];if(co[n++]=null,null!==r&&null!==l){var o=r.pending;null===o?l.next=l:(l.next=o.next,o.next=l),r.pending=l}0!==a&&I(t,l,a)}}function L(e,n,t,r){co[fo++]=e,co[fo++]=n,co[fo++]=t,co[fo++]=r,po|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function U(e,n,t,r){return L(e,n,t,r),F(e)}function D(e,n){return L(e,null,null,n),F(e)}function I(e,n,t){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t);for(var l=!1,a=e.return;null!==a;)a.childLanes|=t,null!==(r=a.alternate)&&(r.childLanes|=t),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(l=!0)),e=a,a=a.return;l&&null!==n&&3===e.tag&&(a=e.stateNode,l=31-Ua(t),null===(e=(a=a.hiddenUpdates)[l])?a[l]=[n]:e.push(n),n.lane=536870912|t)}function F(e){if(50<ti)throw ti=0,ri=null,Error(r(185));for(var n=e.return;null!==n;)n=(e=n).return;return 3===e.tag?e.stateNode:null}function M(e){e!==ho&&null===e.next&&(null===ho?mo=ho=e:ho=ho.next=e),yo=!0,go||(go=!0,Wa(Oa,H))}function W(e,n){if(!bo&&yo){bo=!0;do{for(var t=!1,r=mo;null!==r;){if(0!==e){var l=r.pendingLanes;if(0===l)var a=0;else{var o=r.suspendedLanes,u=r.pingedLanes;a=(1<<31-Ua(42|e)+1)-1,a=201326677&(a&=l&~(o&~u))?201326677&a|1:a?2|a:0}0!==a&&(t=!0,Q(r,a))}else a=Lu,0!=(3&(a=h(r,r===Tu?a:0)))&&(t=!0,Q(r,a));r=r.next}}while(t);bo=!1}}function H(){yo=go=!1;var e=0;0!==vo&&(Gl()&&(e=vo),vo=0);for(var n=Qa(),t=null,r=mo;null!==r;){var l=r.next,a=j(r,n);0===a?(r.next=null,null===t?mo=l:t.next=l,null===l&&(ho=t)):(t=r,(0!==e||0!=(3&a))&&(yo=!0)),r=l}W(e)}function j(e,n){for(var t=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var o=31-Ua(a),u=1<<o,i=l[o];-1===i?0!=(u&t)&&0==(u&r)||(l[o]=g(u,n)):i<=n&&(e.expiredLanes|=u),a&=~u}if(t=Lu,t=h(e,e===(n=Tu)?t:0),r=e.callbackNode,0===t||e===n&&2===Uu||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Ha(r),e.callbackNode=null,e.callbackPriority=0;if(0!=(3&t))return null!==r&&null!==r&&Ha(r),e.callbackPriority=2,e.callbackNode=null,2;if((n=t&-t)===e.callbackPriority)return n;switch(null!==r&&Ha(r),x(t)){case 2:t=Oa;break;case 8:t=Ba;break;case 32:default:t=Va;break;case 268435456:t=$a}return r=A.bind(null,e),t=Wa(t,r),e.callbackPriority=n,e.callbackNode=t,n}function A(e,n){var t=e.callbackNode;if(Wr()&&e.callbackNode!==t)return null;var r=Lu;return 0===(r=h(e,e===Tu?r:0))?null:(gr(e,r,n),j(e,Qa()),e.callbackNode===t?A.bind(null,e):null)}function Q(e,n){if(Wr())return null;gr(e,n,!0)}function O(){return 0===vo&&(vo=y()),vo}function B(){if(0==--ko&&null!==So){null!==xo&&(xo.status="fulfilled");var e=So;So=null,wo=0,xo=null;for(var n=0;n<e.length;n++)(0,e[n])()}}function V(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function $(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function q(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Y(e,n,t){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!=(2&Ru)){var l=r.pending;return null===l?n.next=n:(n.next=l.next,l.next=n),r.pending=n,n=F(e),I(e,null,t),n}return L(e,r,n,t),F(e)}function G(e,n,t){if(null!==(n=n.updateQueue)&&(n=n.shared,0!=(4194176&t))){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,w(e,t)}}function J(e,n){var t=e.updateQueue,r=e.alternate;if(null!==r&&t===(r=r.updateQueue)){var l=null,a=null;if(null!==(t=t.firstBaseUpdate)){do{var o={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};null===a?l=a=o:a=a.next=o,t=t.next}while(null!==t);null===a?l=a=n:a=a.next=n}else l=a=n;return t={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=t)}null===(e=t.lastBaseUpdate)?t.firstBaseUpdate=n:e.next=n,t.lastBaseUpdate=n}function K(){if(Co&&null!==xo)throw xo}function X(e,n,t,r){Co=!1;var l=e.updateQueue;zo=!1;var a=l.firstBaseUpdate,o=l.lastBaseUpdate,u=l.shared.pending;if(null!==u){l.shared.pending=null;var i=u,s=i.next;i.next=null,null===o?a=s:o.next=s,o=i;var c=e.alternate;null!==c&&(u=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===u?c.firstBaseUpdate=s:u.next=s,c.lastBaseUpdate=i)}if(null!==a){var f=l.baseState;for(o=0,c=s=i=null,u=a;;){var d=-536870913&u.lane,p=d!==u.lane;if(p?(Lu&d)===d:(r&d)===d){0!==d&&d===wo&&(Co=!0),null!==c&&(c=c.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});e:{var m=e,h=u;d=n;var g=t;switch(h.tag){case 1:if("function"==typeof(m=h.payload)){f=m.call(g,f,d);break e}f=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(d="function"==typeof(m=h.payload)?m.call(g,f,d):m))break e;f=il({},f,d);break e;case 2:zo=!0}}null!==(d=u.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=l.callbacks)?l.callbacks=[d]:p.push(d))}else p={lane:d,tag:u.tag,payload:u.payload,callback:u.callback,next:null},null===c?(s=c=p,i=f):c=c.next=p,o|=d;if(null===(u=u.next)){if(null===(u=l.shared.pending))break;u=(p=u).next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}null===c&&(i=f),l.baseState=i,l.firstBaseUpdate=s,l.lastBaseUpdate=c,null===a&&(l.shared.lanes=0),Hu|=o,e.lanes=o,e.memoizedState=f}}function Z(e,n){if("function"!=typeof e)throw Error(r(191,e));e.call(n)}function ee(e,n){var t=e.callbacks;if(null!==t)for(e.callbacks=null,e=0;e<t.length;e++)Z(t[e],n)}function ne(e,n){if(Ja(e,n))return!0;if("object"!=typeof e||null===e||"object"!=typeof n||null===n)return!1;var t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(r=0;r<t.length;r++){var l=t[r];if(!Eo.call(n,l)||!Ja(e[l],n[l]))return!1}return!0}function te(e){return"fulfilled"===(e=e.status)||"rejected"===e}function re(){}function le(e,n,t){switch(void 0===(t=e[t])?e.push(n):t!==n&&(n.then(re,re),n=t),n.status){case"fulfilled":return n.value;case"rejected":if((e=n.reason)===Po)throw Error(r(483));throw e;default:if("string"==typeof n.status)n.then(re,re);else{if(null!==(e=Tu)&&100<e.shellSuspendCounter)throw Error(r(482));(e=n).status="pending",e.then((function(e){if("pending"===n.status){var t=n;t.status="fulfilled",t.value=e}}),(function(e){if("pending"===n.status){var t=n;t.status="rejected",t.reason=e}}))}switch(n.status){case"fulfilled":return n.value;case"rejected":if((e=n.reason)===Po)throw Error(r(483));throw e}throw To=n,Po}}function ae(){if(null===To)throw Error(r(459));var e=To;return To=null,e}function oe(e){var n=Lo;return Lo+=1,null===No&&(No=[]),le(No,e,n)}function ue(e,n,t,r){e=r.props.ref,t.ref=void 0!==e?e:null}function ie(e,n){if(n.$$typeof===sl)throw Error(r(525));throw e=Object.prototype.toString.call(n),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function se(e){return(0,e._init)(e._payload)}function ce(e){function n(n,t){if(e){var r=n.deletions;null===r?(n.deletions=[t],n.flags|=16):r.push(t)}}function a(t,r){if(!e)return null;for(;null!==r;)n(t,r),r=r.sibling;return null}function o(e){for(var n=new Map;null!==e;)null!==e.key?n.set(e.key,e):n.set(e.index,e),e=e.sibling;return n}function u(e,n){return(e=Yr(e,n)).index=0,e.sibling=null,e}function i(n,t,r){return n.index=r,e?null!==(r=n.alternate)?(r=r.index)<t?(n.flags|=33554434,t):r:(n.flags|=33554434,t):(n.flags|=1048576,t)}function s(n){return e&&null===n.alternate&&(n.flags|=33554434),n}function c(e,n,t,r){return null===n||6!==n.tag?((n=Zr(t,e.mode,r)).return=e,n):((n=u(n,t)).return=e,n)}function f(e,n,t,r){var l=t.type;return l===dl?p(e,n,t.props.children,r,t.key):null!==n&&(n.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===wl&&se(l)===n.type)?(ue(e,0,r=u(n,t.props),t),r.return=e,r):(ue(e,0,r=Jr(t.type,t.key,t.props,null,e.mode,r),t),r.return=e,r)}function d(e,n,t,r){return null===n||4!==n.tag||n.stateNode.containerInfo!==t.containerInfo||n.stateNode.implementation!==t.implementation?((n=el(t,e.mode,r)).return=e,n):((n=u(n,t.children||[])).return=e,n)}function p(e,n,t,r,l){return null===n||7!==n.tag?((n=Kr(t,e.mode,r,l)).return=e,n):((n=u(n,t)).return=e,n)}function m(e,n,t){if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return(n=Zr(""+n,e.mode,t)).return=e,n;if("object"==typeof n&&null!==n){switch(n.$$typeof){case cl:return ue(e,0,t=Jr(n.type,n.key,n.props,null,e.mode,t),n),t.return=e,t;case fl:return(n=el(n,e.mode,t)).return=e,n;case wl:return m(e,n=(0,n._init)(n._payload),t)}if(Rl(n)||l(n))return(n=Kr(n,e.mode,t,null)).return=e,n;if("function"==typeof n.then)return m(e,oe(n),t);if(n.$$typeof===yl)return m(e,yt(e,n),t);ie(e,n)}return null}function h(e,n,t,r){var a=null!==n?n.key:null;if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return null!==a?null:c(e,n,""+t,r);if("object"==typeof t&&null!==t){switch(t.$$typeof){case cl:return t.key===a?f(e,n,t,r):null;case fl:return t.key===a?d(e,n,t,r):null;case wl:return h(e,n,t=(a=t._init)(t._payload),r)}if(Rl(t)||l(t))return null!==a?null:p(e,n,t,r,null);if("function"==typeof t.then)return h(e,n,oe(t),r);if(t.$$typeof===yl)return h(e,n,yt(e,t),r);ie(e,t)}return null}function g(e,n,t,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return c(n,e=e.get(t)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case cl:return f(n,e=e.get(null===r.key?t:r.key)||null,r,a);case fl:return d(n,e=e.get(null===r.key?t:r.key)||null,r,a);case wl:return g(e,n,t,r=(0,r._init)(r._payload),a)}if(Rl(r)||l(r))return p(n,e=e.get(t)||null,r,a,null);if("function"==typeof r.then)return g(e,n,t,oe(r),a);if(r.$$typeof===yl)return g(e,n,t,yt(n,r),a);ie(n,r)}return null}function y(t,c,f,d){if("object"==typeof f&&null!==f&&f.type===dl&&null===f.key&&(f=f.props.children),"object"==typeof f&&null!==f){switch(f.$$typeof){case cl:e:{for(var p=f.key,b=c;null!==b;){if(b.key===p){if((p=f.type)===dl){if(7===b.tag){a(t,b.sibling),(c=u(b,f.props.children)).return=t,t=c;break e}}else if(b.elementType===p||"object"==typeof p&&null!==p&&p.$$typeof===wl&&se(p)===b.type){a(t,b.sibling),ue(t,0,c=u(b,f.props),f),c.return=t,t=c;break e}a(t,b);break}n(t,b),b=b.sibling}f.type===dl?((c=Kr(f.props.children,t.mode,d,f.key)).return=t,t=c):(ue(t,0,d=Jr(f.type,f.key,f.props,null,t.mode,d),f),d.return=t,t=d)}return s(t);case fl:e:{for(b=f.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===f.containerInfo&&c.stateNode.implementation===f.implementation){a(t,c.sibling),(c=u(c,f.children||[])).return=t,t=c;break e}a(t,c);break}n(t,c),c=c.sibling}(c=el(f,t.mode,d)).return=t,t=c}return s(t);case wl:return y(t,c,f=(b=f._init)(f._payload),d)}if(Rl(f))return function(t,r,l,u){for(var s=null,c=null,f=r,d=r=0,p=null;null!==f&&d<l.length;d++){f.index>d?(p=f,f=null):p=f.sibling;var y=h(t,f,l[d],u);if(null===y){null===f&&(f=p);break}e&&f&&null===y.alternate&&n(t,f),r=i(y,r,d),null===c?s=y:c.sibling=y,c=y,f=p}if(d===l.length)return a(t,f),s;if(null===f){for(;d<l.length;d++)null!==(f=m(t,l[d],u))&&(r=i(f,r,d),null===c?s=f:c.sibling=f,c=f);return s}for(f=o(f);d<l.length;d++)null!==(p=g(f,t,d,l[d],u))&&(e&&null!==p.alternate&&f.delete(null===p.key?d:p.key),r=i(p,r,d),null===c?s=p:c.sibling=p,c=p);return e&&f.forEach((function(e){return n(t,e)})),s}(t,c,f,d);if(l(f)){if("function"!=typeof(b=l(f)))throw Error(r(150));return function(t,l,u,s){if(null==u)throw Error(r(151));for(var c=null,f=null,d=l,p=l=0,y=null,b=u.next();null!==d&&!b.done;p++,b=u.next()){d.index>p?(y=d,d=null):y=d.sibling;var v=h(t,d,b.value,s);if(null===v){null===d&&(d=y);break}e&&d&&null===v.alternate&&n(t,d),l=i(v,l,p),null===f?c=v:f.sibling=v,f=v,d=y}if(b.done)return a(t,d),c;if(null===d){for(;!b.done;p++,b=u.next())null!==(b=m(t,b.value,s))&&(l=i(b,l,p),null===f?c=b:f.sibling=b,f=b);return c}for(d=o(d);!b.done;p++,b=u.next())null!==(b=g(d,t,p,b.value,s))&&(e&&null!==b.alternate&&d.delete(null===b.key?p:b.key),l=i(b,l,p),null===f?c=b:f.sibling=b,f=b);return e&&d.forEach((function(e){return n(t,e)})),c}(t,c,f=b.call(f),d)}if("function"==typeof f.then)return y(t,c,oe(f),d);if(f.$$typeof===yl)return y(t,c,yt(t,f),d);ie(t,f)}return"string"==typeof f&&""!==f||"number"==typeof f||"bigint"==typeof f?(f=""+f,null!==c&&6===c.tag?(a(t,c.sibling),(c=u(c,f)).return=t,t=c):(a(t,c),(c=Zr(f,t.mode,d)).return=t,t=c),s(t)):a(t,c)}return function(e,n,r,l){try{Lo=0;var a=y(e,n,r,l);return No=null,a}catch(n){if(n===Po)throw n;var o=t(29,n,null,e.mode);return o.lanes=l,o.return=e,o}}}function fe(e,n){p(Fo,e=Mu),p(Io,n),Mu=e|n.baseLanes}function de(){p(Fo,Mu),p(Io,Io.current)}function pe(){Mu=Fo.current,d(Io),d(Fo)}function me(e){var n=e.alternate;p(Ho,1&Ho.current),p(Mo,e),null===Wo&&(null===n||null!==Io.current||null!==n.memoizedState)&&(Wo=e)}function he(e){if(22===e.tag){if(p(Ho,Ho.current),p(Mo,e),null===Wo){var n=e.alternate;null!==n&&null!==n.memoizedState&&(Wo=e)}}else ge()}function ge(){p(Ho,Ho.current),p(Mo,Mo.current)}function ye(e){d(Mo),Wo===e&&(Wo=null),d(Ho)}function be(e){for(var n=e;null!==n;){if(13===n.tag){var t=n.memoizedState;if(null!==t&&(null===(t=t.dehydrated)||Sa(t)||ka(t)))return n}else if(19===n.tag&&void 0!==n.memoizedProps.revealOrder){if(0!=(128&n.flags))return n}else if(null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function ve(){throw Error(r(321))}function Se(e,n){if(null===n)return!1;for(var t=0;t<n.length&&t<e.length;t++)if(!Ja(e[t],n[t]))return!1;return!0}function ke(e,n,t,r,l,a){return jo=a,Ao=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,Pl.H=null===e||null===e.memoizedState?Zo:eu,$o=!1,a=t(r,l),$o=!1,Vo&&(a=xe(n,t,r,l)),we(e),a}function we(e){Pl.H=Xo;var n=null!==Qo&&null!==Qo.next;if(jo=0,Oo=Qo=Ao=null,Bo=!1,Yo=0,Go=null,n)throw Error(r(300));null===e||lu||null!==(e=e.dependencies)&&mt(e)&&(lu=!0)}function xe(e,n,t,l){Ao=e;var a=0;do{if(Vo&&(Go=null),Yo=0,Vo=!1,25<=a)throw Error(r(301));if(a+=1,Oo=Qo=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}Pl.H=nu,o=n(t,l)}while(Vo);return o}function ze(){var e=Pl.H,n=e.useState()[0];return n="function"==typeof n.then?Te(n):n,e=e.useState()[0],(null!==Qo?Qo.memoizedState:null)!==e&&(Ao.flags|=1024),n}function Ce(){var e=0!==qo;return qo=0,e}function Ee(e,n,t){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~t}function Pe(e){if(Bo){for(e=e.memoizedState;null!==e;){var n=e.queue;null!==n&&(n.pending=null),e=e.next}Bo=!1}jo=0,Oo=Qo=Ao=null,Vo=!1,Yo=qo=0,Go=null}function _e(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Oo?Ao.memoizedState=Oo=e:Oo=Oo.next=e,Oo}function Re(){if(null===Qo){var e=Ao.alternate;e=null!==e?e.memoizedState:null}else e=Qo.next;var n=null===Oo?Ao.memoizedState:Oo.next;if(null!==n)Oo=n,Qo=e;else{if(null===e){if(null===Ao.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(Qo=e).memoizedState,baseState:Qo.baseState,baseQueue:Qo.baseQueue,queue:Qo.queue,next:null},null===Oo?Ao.memoizedState=Oo=e:Oo=Oo.next=e}return Oo}function Te(e){var n=Yo;return Yo+=1,null===Go&&(Go=[]),e=le(Go,e,n),n=Ao,null===(null===Oo?n.memoizedState:Oo.next)&&(n=n.alternate,Pl.H=null===n||null===n.memoizedState?Zo:eu),e}function Ne(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Te(e);if(e.$$typeof===yl)return gt(e)}throw Error(r(438,String(e)))}function Le(e){var n=null,t=Ao.updateQueue;if(null!==t&&(n=t.memoCache),null==n){var r=Ao.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(n={data:r.data.map((function(e){return e.slice()})),index:0})}if(null==n&&(n={data:[],index:0}),null===t&&(t=Ko(),Ao.updateQueue=t),t.memoCache=n,void 0===(t=n.data[n.index]))for(t=n.data[n.index]=Array(e),r=0;r<e;r++)t[r]=zl;return n.index++,t}function Ue(e,n){return"function"==typeof n?n(e):n}function De(e){return Ie(Re(),Qo,e)}function Ie(e,n,t){var l=e.queue;if(null===l)throw Error(r(311));l.lastRenderedReducer=t;var a=e.baseQueue,o=l.pending;if(null!==o){if(null!==a){var u=a.next;a.next=o.next,o.next=u}n.baseQueue=a=o,l.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var i=u=null,s=null,c=n=a.next,f=!1;do{var d=-536870913&c.lane;if(d!==c.lane?(Lu&d)===d:(jo&d)===d){var p=c.revertLane;if(0===p)null!==s&&(s=s.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),d===wo&&(f=!0);else{if((jo&p)===p){c=c.next,p===wo&&(f=!0);continue}d={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===s?(i=s=d,u=o):s=s.next=d,Ao.lanes|=p,Hu|=p}d=c.action,$o&&t(o,d),o=c.hasEagerState?c.eagerState:t(o,d)}else p={lane:d,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===s?(i=s=p,u=o):s=s.next=p,Ao.lanes|=d,Hu|=d;c=c.next}while(null!==c&&c!==n);if(null===s?u=o:s.next=i,!Ja(o,e.memoizedState)&&(lu=!0,f&&null!==(t=xo)))throw t;e.memoizedState=o,e.baseState=u,e.baseQueue=s,l.lastRenderedState=o}return null===a&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Fe(e){var n=Re(),t=n.queue;if(null===t)throw Error(r(311));t.lastRenderedReducer=e;var l=t.dispatch,a=t.pending,o=n.memoizedState;if(null!==a){t.pending=null;var u=a=a.next;do{o=e(o,u.action),u=u.next}while(u!==a);Ja(o,n.memoizedState)||(lu=!0),n.memoizedState=o,null===n.baseQueue&&(n.baseState=o),t.lastRenderedState=o}return[o,l]}function Me(e,n,t){var l=Ao,a=Re();t=n();var o=!Ja((Qo||a).memoizedState,t);if(o&&(a.memoizedState=t,lu=!0),a=a.queue,sn(je.bind(null,l,a,e),[e]),a.getSnapshot!==n||o||null!==Oo&&1&Oo.memoizedState.tag){if(l.flags|=2048,rn(9,He.bind(null,l,a,t,n),{destroy:void 0},null),null===Tu)throw Error(r(349));0!=(60&jo)||We(l,n,t)}return t}function We(e,n,t){e.flags|=16384,e={getSnapshot:n,value:t},null===(n=Ao.updateQueue)?(n=Ko(),Ao.updateQueue=n,n.stores=[e]):null===(t=n.stores)?n.stores=[e]:t.push(e)}function He(e,n,t,r){n.value=t,n.getSnapshot=r,Ae(n)&&Qe(e)}function je(e,n,t){return t((function(){Ae(n)&&Qe(e)}))}function Ae(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!Ja(e,t)}catch(e){return!0}}function Qe(e){var n=D(e,2);null!==n&&hr(n,0,2)}function Oe(e){var n=_e();if("function"==typeof e){var t=e;if(e=t(),$o){z(!0);try{t()}finally{z(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ue,lastRenderedState:e},n}function Be(e,n,t,r){return e.baseState=t,Ie(e,Qo,"function"==typeof r?r:Ue)}function Ve(e,n,t,l,a){if(_n(e))throw Error(r(485));if(null!==(e=n.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==Pl.T?t(!0):o.isTransition=!1,l(o),null===(t=n.pending)?(o.next=n.pending=o,$e(n,o)):(o.next=t.next,n.pending=t.next=o)}}function $e(e,n){var t=n.action,r=n.payload,l=e.state;if(n.isTransition){var a=Pl.T,o={};Pl.T=o;try{var u=t(l,r),i=Pl.S;null!==i&&i(o,u),qe(e,n,u)}catch(t){Ge(e,n,t)}finally{Pl.T=a}}else try{qe(e,n,a=t(l,r))}catch(t){Ge(e,n,t)}}function qe(e,n,t){null!==t&&"object"==typeof t&&"function"==typeof t.then?t.then((function(t){Ye(e,n,t)}),(function(t){return Ge(e,n,t)})):Ye(e,n,t)}function Ye(e,n,t){n.status="fulfilled",n.value=t,Je(n),e.state=t,null!==(n=e.pending)&&((t=n.next)===n?e.pending=null:(t=t.next,n.next=t,$e(e,t)))}function Ge(e,n,t){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{n.status="rejected",n.reason=t,Je(n),n=n.next}while(n!==r)}e.action=null}function Je(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function Ke(e,n){return n}function Xe(e,n){var t,r,l;(t=_e()).memoizedState=t.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ke,lastRenderedState:n},t.queue=r,t=Cn.bind(null,Ao,r),r.dispatch=t,r=Oe(!1);var a=Pn.bind(null,Ao,!1,r.queue);return l={state:n,dispatch:null,action:e,pending:null},(r=_e()).queue=l,t=Ve.bind(null,Ao,l,a,t),l.dispatch=t,r.memoizedState=e,[n,t,!1]}function Ze(e){return en(Re(),Qo,e)}function en(e,n,t){n=Ie(e,n,Ke)[0],e=De(Ue)[0],n="object"==typeof n&&null!==n&&"function"==typeof n.then?Te(n):n;var r=Re(),l=r.queue,a=l.dispatch;return t!==r.memoizedState&&(Ao.flags|=2048,rn(9,nn.bind(null,l,t),{destroy:void 0},null)),[n,a,e]}function nn(e,n){e.action=n}function tn(e){var n=Re(),t=Qo;if(null!==t)return en(n,t,e);Re(),n=n.memoizedState;var r=(t=Re()).queue.dispatch;return t.memoizedState=e,[n,r,!1]}function rn(e,n,t,r){return e={tag:e,create:n,inst:t,deps:r,next:null},null===(n=Ao.updateQueue)&&(n=Ko(),Ao.updateQueue=n),null===(t=n.lastEffect)?n.lastEffect=e.next=e:(r=t.next,t.next=e,e.next=r,n.lastEffect=e),e}function ln(){return Re().memoizedState}function an(e,n,t,r){var l=_e();Ao.flags|=e,l.memoizedState=rn(1|n,t,{destroy:void 0},void 0===r?null:r)}function on(e,n,t,r){var l=Re();r=void 0===r?null:r;var a=l.memoizedState.inst;null!==Qo&&null!==r&&Se(r,Qo.memoizedState.deps)?l.memoizedState=rn(n,t,a,r):(Ao.flags|=e,l.memoizedState=rn(1|n,t,a,r))}function un(e,n){an(8390656,8,e,n)}function sn(e,n){on(2048,8,e,n)}function cn(e,n){return on(4,2,e,n)}function fn(e,n){return on(4,4,e,n)}function dn(e,n){if("function"==typeof n){e=e();var t=n(e);return function(){"function"==typeof t?t():n(null)}}if(null!=n)return e=e(),n.current=e,function(){n.current=null}}function pn(e,n,t){t=null!=t?t.concat([e]):null,on(4,4,dn.bind(null,n,e),t)}function mn(){}function hn(e,n){var t=Re();n=void 0===n?null:n;var r=t.memoizedState;return null!==n&&Se(n,r[1])?r[0]:(t.memoizedState=[e,n],e)}function gn(e,n){var t=Re();n=void 0===n?null:n;var r=t.memoizedState;if(null!==n&&Se(n,r[1]))return r[0];if(r=e(),$o){z(!0);try{e()}finally{z(!1)}}return t.memoizedState=[r,n],r}function yn(e,n,t){return void 0===t||0!=(1073741824&jo)?e.memoizedState=n:(e.memoizedState=t,e=mr(),Ao.lanes|=e,Hu|=e,t)}function bn(e,n,t,r){return Ja(t,n)?t:null!==Io.current?(e=yn(e,t,r),Ja(e,n)||(lu=!0),e):0==(42&jo)?(lu=!0,e.memoizedState=t):(e=mr(),Ao.lanes|=e,Hu|=e,n)}function vn(e,n,t,r,l){var a=ql();$l(0!==a&&8>a?a:8);var o,u,i,s=Pl.T,c={};Pl.T=c,Pn(e,!1,n,t);try{var f=l(),d=Pl.S;null!==d&&d(c,f),null!==f&&"object"==typeof f&&"function"==typeof f.then?En(e,n,(o=r,u=[],i={status:"pending",value:null,reason:null,then:function(e){u.push(e)}},f.then((function(){i.status="fulfilled",i.value=o;for(var e=0;e<u.length;e++)(0,u[e])(o)}),(function(e){for(i.status="rejected",i.reason=e,e=0;e<u.length;e++)(0,u[e])(void 0)})),i),pr()):En(e,n,r,pr())}catch(t){En(e,n,{then:function(){},status:"rejected",reason:t},pr())}finally{$l(a),Pl.T=s}}function Sn(){return gt(ra)}function kn(){return Re().memoizedState}function wn(){return Re().memoizedState}function xn(e){for(var n=e.return;null!==n;){switch(n.tag){case 24:case 3:var t=pr(),r=Y(n,e=q(t),t);return null!==r&&(hr(r,0,t),G(r,n,t)),n={cache:vt()},void(e.payload=n)}n=n.return}}function zn(e,n,t){var r=pr();t={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},_n(e)?Rn(n,t):null!==(t=U(e,n,t,r))&&(hr(t,0,r),Tn(t,n,r))}function Cn(e,n,t){En(e,n,t,pr())}function En(e,n,t,r){var l={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(_n(e))Rn(n,l);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=n.lastRenderedReducer))try{var o=n.lastRenderedState,u=a(o,t);if(l.hasEagerState=!0,l.eagerState=u,Ja(u,o))return L(e,n,l,0),null===Tu&&N(),!1}catch(e){}if(null!==(t=U(e,n,l,r)))return hr(t,0,r),Tn(t,n,r),!0}return!1}function Pn(e,n,t,l){if(l={lane:2,revertLane:O(),action:l,hasEagerState:!1,eagerState:null,next:null},_n(e)){if(n)throw Error(r(479))}else null!==(n=U(e,t,l,2))&&hr(n,0,2)}function _n(e){var n=e.alternate;return e===Ao||null!==n&&n===Ao}function Rn(e,n){Vo=Bo=!0;var t=e.pending;null===t?n.next=n:(n.next=t.next,t.next=n),e.pending=n}function Tn(e,n,t){if(0!=(4194176&t)){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,w(e,t)}}function Nn(e,n,t,r){t=null==(t=t(r,n=e.memoizedState))?n:il({},n,t),e.memoizedState=t,0===e.lanes&&(e.updateQueue.baseState=t)}function Ln(e,n,t,r,l,a,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!(n.prototype&&n.prototype.isPureReactComponent&&ne(t,r)&&ne(l,a))}function Un(e,n,t,r){e=n.state,"function"==typeof n.componentWillReceiveProps&&n.componentWillReceiveProps(t,r),"function"==typeof n.UNSAFE_componentWillReceiveProps&&n.UNSAFE_componentWillReceiveProps(t,r),n.state!==e&&tu.enqueueReplaceState(n,n.state,null)}function Dn(e,n){var t=n;if("ref"in n)for(var r in t={},n)"ref"!==r&&(t[r]=n[r]);if(e=e.defaultProps)for(var l in t===n&&(t=il({},t)),e)void 0===t[l]&&(t[l]=e[l]);return t}function In(e,n){try{(0,e.onUncaughtError)(n.value,{componentStack:n.stack})}catch(e){setTimeout((function(){throw e}))}}function Fn(e,n,t){try{(0,e.onCaughtError)(t.value,{componentStack:t.stack,errorBoundary:1===n.tag?n.stateNode:null})}catch(e){setTimeout((function(){throw e}))}}function Mn(e,n,t){return(t=q(t)).tag=3,t.payload={element:null},t.callback=function(){In(e,n)},t}function Wn(e){return(e=q(e)).tag=3,e}function Hn(e,n,t,r){var l=t.type.getDerivedStateFromError;if("function"==typeof l){var a=r.value;e.payload=function(){return l(a)},e.callback=function(){Fn(n,t,r)}}var o=t.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(e.callback=function(){Fn(n,t,r),"function"!=typeof l&&(null===Ju?Ju=new Set([this]):Ju.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}function jn(e,n,t,r){n.child=null===e?Do(n,null,t,r):Uo(n,e.child,t,r)}function An(e,n,t,r,l){t=t.render;var a=n.ref;if("ref"in r){var o={};for(var u in r)"ref"!==u&&(o[u]=r[u])}else o=r;return ht(n),r=ke(e,n,t,o,a,l),u=Ce(),null===e||lu?(n.flags|=1,jn(e,n,r,l),n.child):(Ee(e,n,l),at(e,n,l))}function Qn(e,n,t,r,l){if(null===e){var a=t.type;return"function"!=typeof a||qr(a)||void 0!==a.defaultProps||null!==t.compare?((e=Jr(t.type,null,r,n,n.mode,l)).ref=n.ref,e.return=n,n.child=e):(n.tag=15,n.type=a,On(e,n,a,r,l))}if(a=e.child,!ot(e,l)){var o=a.memoizedProps;if((t=null!==(t=t.compare)?t:ne)(o,r)&&e.ref===n.ref)return at(e,n,l)}return n.flags|=1,(e=Yr(a,r)).ref=n.ref,e.return=n,n.child=e}function On(e,n,t,r,l){if(null!==e){var a=e.memoizedProps;if(ne(a,r)&&e.ref===n.ref){if(lu=!1,n.pendingProps=r=a,!ot(e,l))return n.lanes=e.lanes,at(e,n,l);0!=(131072&e.flags)&&(lu=!0)}}return qn(e,n,t,r,l)}function Bn(e,n,t){var r=n.pendingProps,l=r.children,a=0!=(2&n.stateNode._pendingVisibility),o=null!==e?e.memoizedState:null;if($n(e,n),"hidden"===r.mode||a){if(0!=(128&n.flags)){if(r=null!==o?o.baseLanes|t:t,null!==e){for(l=n.child=e.child,a=0;null!==l;)a=a|l.lanes|l.childLanes,l=l.sibling;n.childLanes=a&~r}else n.childLanes=0,n.child=null;return Vn(e,n,r,t)}if(0==(536870912&t))return n.lanes=n.childLanes=536870912,Vn(e,n,null!==o?o.baseLanes|t:t,t);n.memoizedState={baseLanes:0,cachePool:null},null!==e&&wt(0,null!==o?o.cachePool:null),null!==o?fe(n,o):de(),he(n)}else null!==o?(wt(0,o.cachePool),fe(n,o),ge(),n.memoizedState=null):(null!==e&&wt(0,null),de(),ge());return jn(e,n,l,t),n.child}function Vn(e,n,t,r){var l=kt();return l=null===l?null:{parent:pu._currentValue2,pool:l},n.memoizedState={baseLanes:t,cachePool:l},null!==e&&wt(0,null),de(),he(n),null!==e&&pt(e,n,r,!0),null}function $n(e,n){var t=n.ref;if(null===t)null!==e&&null!==e.ref&&(n.flags|=2097664);else{if("function"!=typeof t&&"object"!=typeof t)throw Error(r(284));null!==e&&e.ref===t||(n.flags|=2097664)}}function qn(e,n,t,r,l){return ht(n),t=ke(e,n,t,r,void 0,l),r=Ce(),null===e||lu?(n.flags|=1,jn(e,n,t,l),n.child):(Ee(e,n,l),at(e,n,l))}function Yn(e,n,t,r,l,a){return ht(n),n.updateQueue=null,t=xe(n,r,t,l),we(e),r=Ce(),null===e||lu?(n.flags|=1,jn(e,n,t,a),n.child):(Ee(e,n,a),at(e,n,a))}function Gn(e,n,t,r,l){if(ht(n),null===n.stateNode){var a=La,o=t.contextType;"object"==typeof o&&null!==o&&(a=gt(o)),a=new t(r,a),n.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=tu,n.stateNode=a,a._reactInternals=n,(a=n.stateNode).props=r,a.state=n.memoizedState,a.refs={},V(n),o=t.contextType,a.context="object"==typeof o&&null!==o?gt(o):La,a.state=n.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(Nn(n,t,o,r),a.state=n.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(o=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),o!==a.state&&tu.enqueueReplaceState(a,a.state,null),X(n,r,a,l),K(),a.state=n.memoizedState),"function"==typeof a.componentDidMount&&(n.flags|=4194308),r=!0}else if(null===e){a=n.stateNode;var u=n.memoizedProps,i=Dn(t,u);a.props=i;var s=a.context,c=t.contextType;o=La,"object"==typeof c&&null!==c&&(o=gt(c));var f=t.getDerivedStateFromProps;c="function"==typeof f||"function"==typeof a.getSnapshotBeforeUpdate,u=n.pendingProps!==u,c||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(u||s!==o)&&Un(n,a,r,o),zo=!1;var d=n.memoizedState;a.state=d,X(n,r,a,l),K(),s=n.memoizedState,u||d!==s||zo?("function"==typeof f&&(Nn(n,t,f,r),s=n.memoizedState),(i=zo||Ln(n,t,i,r,d,s,o))?(c||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(n.flags|=4194308)):("function"==typeof a.componentDidMount&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=s),a.props=r,a.state=s,a.context=o,r=i):("function"==typeof a.componentDidMount&&(n.flags|=4194308),r=!1)}else{a=n.stateNode,$(e,n),c=Dn(t,o=n.memoizedProps),a.props=c,f=n.pendingProps,d=a.context,s=t.contextType,i=La,"object"==typeof s&&null!==s&&(i=gt(s)),(s="function"==typeof(u=t.getDerivedStateFromProps)||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(o!==f||d!==i)&&Un(n,a,r,i),zo=!1,d=n.memoizedState,a.state=d,X(n,r,a,l),K();var p=n.memoizedState;o!==f||d!==p||zo||null!==e&&null!==e.dependencies&&mt(e.dependencies)?("function"==typeof u&&(Nn(n,t,u,r),p=n.memoizedState),(c=zo||Ln(n,t,c,r,d,p,i)||null!==e&&null!==e.dependencies&&mt(e.dependencies))?(s||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,i),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,i)),"function"==typeof a.componentDidUpdate&&(n.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(n.flags|=1024)):("function"!=typeof a.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(n.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=p),a.props=r,a.state=p,a.context=i,r=c):("function"!=typeof a.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(n.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(n.flags|=1024),r=!1)}return a=r,$n(e,n),r=0!=(128&n.flags),a||r?(a=n.stateNode,t=r&&"function"!=typeof t.getDerivedStateFromError?null:a.render(),n.flags|=1,null!==e&&r?(n.child=Uo(n,e.child,null,l),n.child=Uo(n,null,t,l)):jn(e,n,t,l),n.memoizedState=a.state,e=n.child):e=at(e,n,l),e}function Jn(e){return{baseLanes:e,cachePool:xt()}}function Kn(e,n,t){return e=null!==e?e.childLanes&~t:0,n&&(e|=Qu),e}function Xn(e,n,t){var l,a,o,u,i=n.pendingProps,s=!1,c=0!=(128&n.flags);if((l=c)||(l=(null===e||null!==e.memoizedState)&&0!=(2&Ho.current)),l&&(s=!0,n.flags&=-129),l=0!=(32&n.flags),n.flags&=-33,null===e)return a=i.children,i=i.fallback,s?(ge(),a=et({mode:"hidden",children:a},s=n.mode),i=Kr(i,s,t,null),a.return=n,i.return=n,a.sibling=i,n.child=a,(s=n.child).memoizedState=Jn(t),s.childLanes=Kn(e,l,t),n.memoizedState=au,i):(me(n),Zn(n,a));if(null!==(o=e.memoizedState)&&null!==(a=o.dehydrated)){if(c)256&n.flags?(me(n),n.flags&=-257,n=nt(e,n,t)):null!==n.memoizedState?(ge(),n.child=e.child,n.flags|=128,n=null):(ge(),s=i.fallback,a=n.mode,i=et({mode:"visible",children:i.children},a),(s=Kr(s,a,t,null)).flags|=2,i.return=n,s.return=n,i.sibling=s,n.child=i,Uo(n,e.child,null,t),(i=n.child).memoizedState=Jn(t),i.childLanes=Kn(e,l,t),n.memoizedState=au,n=s);else if(me(n),ka(a))l=wa(a).digest,(i=Error(r(419))).stack="",i.digest=l,u={value:i,source:null,stack:null},null===so?so=[u]:so.push(u),n=nt(e,n,t);else if(lu||pt(e,n,t,!1),l=0!=(t&e.childLanes),lu||l){if(null!==(l=Tu)){if(0!=(42&(i=t&-t)))i=1;else switch(i){case 2:i=1;break;case 8:i=4;break;case 32:i=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:i=64;break;case 268435456:i=134217728;break;default:i=0}if(0!==(i=0!=(i&(l.suspendedLanes|t))?0:i)&&i!==o.retryLane)throw o.retryLane=i,D(e,i),hr(l,0,i),ru}Sa(a)||Pr(),n=nt(e,n,t)}else Sa(a)?(n.flags|=128,n.child=e.child,n=Br.bind(null,e),xa(a,n),n=null):(e=o.treeContext,(n=Zn(n,i.children)).flags|=4096);return n}return s?(ge(),s=i.fallback,a=n.mode,c=(o=e.child).sibling,(i=Yr(o,{mode:"hidden",children:i.children})).subtreeFlags=31457280&o.subtreeFlags,null!==c?s=Yr(c,s):(s=Kr(s,a,t,null)).flags|=2,s.return=n,i.return=n,i.sibling=s,n.child=i,i=s,s=n.child,null===(a=e.child.memoizedState)?a=Jn(t):(null!==(o=a.cachePool)?(c=pu._currentValue2,o=o.parent!==c?{parent:c,pool:c}:o):o=xt(),a={baseLanes:a.baseLanes|t,cachePool:o}),s.memoizedState=a,s.childLanes=Kn(e,l,t),n.memoizedState=au,i):(me(n),e=(t=e.child).sibling,(t=Yr(t,{mode:"visible",children:i.children})).return=n,t.sibling=null,null!==e&&(null===(l=n.deletions)?(n.deletions=[e],n.flags|=16):l.push(e)),n.child=t,n.memoizedState=null,t)}function Zn(e,n){return(n=et({mode:"visible",children:n},e.mode)).return=e,e.child=n}function et(e,n){return Xr(e,n,0,null)}function nt(e,n,t){return Uo(n,e.child,null,t),(e=Zn(n,n.pendingProps.children)).flags|=2,n.memoizedState=null,e}function tt(e,n,t){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n),ft(e.return,n,t)}function rt(e,n,t,r,l){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:t,tailMode:l}:(a.isBackwards=n,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=t,a.tailMode=l)}function lt(e,n,t){var r=n.pendingProps,l=r.revealOrder,a=r.tail;if(jn(e,n,r.children,t),0!=(2&(r=Ho.current)))r=1&r|2,n.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=n.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&tt(e,t,n);else if(19===e.tag)tt(e,t,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(p(Ho,r),l){case"forwards":for(t=n.child,l=null;null!==t;)null!==(e=t.alternate)&&null===be(e)&&(l=t),t=t.sibling;null===(t=l)?(l=n.child,n.child=null):(l=t.sibling,t.sibling=null),rt(n,!1,l,t,a);break;case"backwards":for(t=null,l=n.child,n.child=null;null!==l;){if(null!==(e=l.alternate)&&null===be(e)){n.child=l;break}e=l.sibling,l.sibling=t,t=l,l=e}rt(n,!0,t,null,a);break;case"together":rt(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function at(e,n,t){if(null!==e&&(n.dependencies=e.dependencies),Hu|=n.lanes,0==(t&n.childLanes)){if(null===e)return null;if(pt(e,n,t,!1),0==(t&n.childLanes))return null}if(null!==e&&n.child!==e.child)throw Error(r(153));if(null!==n.child){for(t=Yr(e=n.child,e.pendingProps),n.child=t,t.return=n;null!==e.sibling;)e=e.sibling,(t=t.sibling=Yr(e,e.pendingProps)).return=n;t.sibling=null}return n.child}function ot(e,n){return 0!=(e.lanes&n)||!(null===(e=e.dependencies)||!mt(e))}function ut(e,n,t){if(null!==e)if(e.memoizedProps!==n.pendingProps)lu=!0;else{if(!ot(e,t)&&0==(128&n.flags))return lu=!1,function(e,n,t){switch(n.tag){case 3:P(n,n.stateNode.containerInfo),st(0,pu,e.memoizedState.cache);break;case 27:case 5:R(n);break;case 4:P(n,n.stateNode.containerInfo);break;case 10:st(0,n.type,n.memoizedProps.value);break;case 13:var r=n.memoizedState;if(null!==r)return null!==r.dehydrated?(me(n),n.flags|=128,null):0!=(t&n.child.childLanes)?Xn(e,n,t):(me(n),null!==(e=at(e,n,t))?e.sibling:null);me(n);break;case 19:var l=0!=(128&e.flags);if((r=0!=(t&n.childLanes))||(pt(e,n,t,!1),r=0!=(t&n.childLanes)),l){if(r)return lt(e,n,t);n.flags|=128}if(null!==(l=n.memoizedState)&&(l.rendering=null,l.tail=null,l.lastEffect=null),p(Ho,Ho.current),r)break;return null;case 22:case 23:return n.lanes=0,Bn(e,n,t);case 24:st(0,pu,e.memoizedState.cache)}return at(e,n,t)}(e,n,t);lu=0!=(131072&e.flags)}else lu=!1;switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var l=n.elementType,o=l._init;if(l=o(l._payload),n.type=l,"function"!=typeof l){if(null!=l){if((o=l.$$typeof)===bl){n.tag=11,n=An(null,n,l,e,t);break e}if(o===kl){n.tag=14,n=Qn(null,n,l,e,t);break e}}throw n=a(l)||l,Error(r(306,n,""))}qr(l)?(e=Dn(l,e),n.tag=1,n=Gn(null,n,l,e,t)):(n.tag=0,n=qn(null,n,l,e,t))}return n;case 0:return qn(e,n,n.type,n.pendingProps,t);case 1:return Gn(e,n,l=n.type,o=Dn(l,n.pendingProps),t);case 3:if(P(n,n.stateNode.containerInfo),null===e)throw Error(r(387));var u=n.pendingProps;l=(o=n.memoizedState).element,$(e,n),X(n,u,null,t);var i=n.memoizedState;return u=i.cache,st(0,pu,u),u!==o.cache&&dt(n,[pu],t,!0),K(),(u=i.element)!==l?(jn(e,n,u,t),n=n.child):n=at(e,n,t),n;case 26:case 27:case 5:return R(n),o=n.type,u=n.pendingProps,i=null!==e?e.memoizedProps:null,l=u.children,Wl(o,u)?l=null:null!==i&&Wl(o,i)&&(n.flags|=32),null!==n.memoizedState&&(o=ke(e,n,ze,null,null,t),ra._currentValue2=o),$n(e,n),jn(e,n,l,t),n.child;case 6:return null;case 13:return Xn(e,n,t);case 4:return P(n,n.stateNode.containerInfo),l=n.pendingProps,null===e?n.child=Uo(n,null,l,t):jn(e,n,l,t),n.child;case 11:return An(e,n,n.type,n.pendingProps,t);case 7:return jn(e,n,n.pendingProps,t),n.child;case 8:case 12:return jn(e,n,n.pendingProps.children,t),n.child;case 10:return l=n.pendingProps,st(0,n.type,l.value),jn(e,n,l.children,t),n.child;case 9:return o=n.type._context,l=n.pendingProps.children,ht(n),l=l(o=gt(o)),n.flags|=1,jn(e,n,l,t),n.child;case 14:return Qn(e,n,n.type,n.pendingProps,t);case 15:return On(e,n,n.type,n.pendingProps,t);case 19:return lt(e,n,t);case 22:return Bn(e,n,t);case 24:return ht(n),l=gt(pu),null===e?(null===(o=kt())&&(o=Tu,u=vt(),o.pooledCache=u,u.refCount++,null!==u&&(o.pooledCacheLanes|=t),o=u),n.memoizedState={parent:l,cache:o},V(n),st(0,pu,o)):(0!=(e.lanes&t)&&($(e,n),X(n,null,null,t),K()),o=e.memoizedState,u=n.memoizedState,o.parent!==l?(o={parent:l,cache:l},n.memoizedState=o,0===n.lanes&&(n.memoizedState=n.updateQueue.baseState=o),st(0,pu,l)):(l=u.cache,st(0,pu,l),l!==o.cache&&dt(n,[pu],t,!0))),jn(e,n,n.pendingProps.children,t),n.child;case 29:throw n.pendingProps}throw Error(r(156,n.tag))}function it(){su=iu=uu=null}function st(e,n,t){p(ou,n._currentValue2),n._currentValue2=t}function ct(e){var n=ou.current;e._currentValue2=n,d(ou)}function ft(e,n,t){for(;null!==e;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,null!==r&&(r.childLanes|=n)):null!==r&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===t)break;e=e.return}}function dt(e,n,t,l){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var u=a.child;o=o.firstContext;e:for(;null!==o;){var i=o;o=a;for(var s=0;s<n.length;s++)if(i.context===n[s]){o.lanes|=t,null!==(i=o.alternate)&&(i.lanes|=t),ft(o.return,t,e),l||(u=null);break e}o=i.next}}else if(18===a.tag){if(null===(u=a.return))throw Error(r(341));u.lanes|=t,null!==(o=u.alternate)&&(o.lanes|=t),ft(u,t,e),u=null}else u=a.child;if(null!==u)u.return=a;else for(u=a;null!==u;){if(u===e){u=null;break}if(null!==(a=u.sibling)){a.return=u.return,u=a;break}u=u.return}a=u}}function pt(e,n,t,l){e=null;for(var a=n,o=!1;null!==a;){if(!o)if(0!=(524288&a.flags))o=!0;else if(0!=(262144&a.flags))break;if(10===a.tag){var u=a.alternate;if(null===u)throw Error(r(387));if(null!==(u=u.memoizedProps)){var i=a.type;Ja(a.pendingProps.value,u.value)||(null!==e?e.push(i):e=[i])}}else if(a===uo.current){if(null===(u=a.alternate))throw Error(r(387));u.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(ra):e=[ra])}a=a.return}null!==e&&dt(n,e,t,l),n.flags|=262144}function mt(e){for(e=e.firstContext;null!==e;){var n=e.context;if(!Ja(n._currentValue2,e.memoizedValue))return!0;e=e.next}return!1}function ht(e){uu=e,su=iu=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function gt(e){return bt(uu,e)}function yt(e,n){return null===uu&&ht(e),bt(e,n)}function bt(e,n){var t=n._currentValue2;if(su!==n)if(n={context:n,memoizedValue:t,next:null},null===iu){if(null===e)throw Error(r(308));iu=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else iu=iu.next=n;return t}function vt(){return{controller:new cu,data:new Map,refCount:0}}function St(e){e.refCount--,0===e.refCount&&fu(du,(function(){e.controller.abort()}))}function kt(){var e=hu.current;return null!==e?e:Tu.pooledCache}function wt(e,n){p(hu,null===n?hu.current:n.pool)}function xt(){var e=kt();return null===e?null:{parent:pu._currentValue2,pool:e}}function zt(e){e.flags|=4}function Ct(e,n){null!==n&&(e.flags|=4),16384&e.flags&&(n=22!==e.tag?b():536870912,e.lanes|=n)}function Et(e,n){switch(e.tailMode){case"hidden":n=e.tail;for(var t=null;null!==n;)null!==n.alternate&&(t=n),n=n.sibling;null===t?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?n||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Pt(e){var n=null!==e.alternate&&e.alternate.child===e.child,t=0,r=0;if(n)for(var l=e.child;null!==l;)t|=l.lanes|l.childLanes,r|=31457280&l.subtreeFlags,r|=31457280&l.flags,l.return=e,l=l.sibling;else for(l=e.child;null!==l;)t|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=t,n}function _t(e,n,t){var l=n.pendingProps;switch(E(n),n.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return Pt(n),null;case 3:return t=n.stateNode,l=null,null!==e&&(l=e.memoizedState.cache),n.memoizedState.cache!==l&&(n.flags|=2048),ct(pu),_(),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),null!==e&&null!==e.child||null===e||e.memoizedState.isDehydrated&&0==(256&n.flags)||(n.flags|=1024,null!==so&&(yr(so),so=null)),Pt(n),null;case 26:var a;case 27:case 5:if(T(n),t=n.type,null!==e&&null!=n.stateNode)!function(e,n,t,r){e.memoizedProps!==r&&zt(n)}(e,n,0,l);else{if(!l){if(null===n.stateNode)throw Error(r(166));return Pt(n),null}e=lo.current,function(e,n,t,r){for(t=n.child;null!==t;){if(5===t.tag||6===t.tag)Fl(e,t.stateNode);else if(4!==t.tag&&!Ra&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===n)break;for(;null===t.sibling;){if(null===t.return||t.return===n)return;t=t.return}t.sibling.return=t.return,t=t.sibling}}(a=Il(t,l,oo.current,e,n),n,!1),n.stateNode=a,Ml(a,t,l,e)&&zt(n)}return Pt(n),function(e,n,t){if(Kl(n,t)){if(e.flags|=16777216,!Xl(n,t)){if(!zr())throw To=Ro,_o;e.flags|=8192}}else e.flags&=-16777217}(n,n.type,n.pendingProps),null;case 6:if(e&&null!=n.stateNode)(t=e.memoizedProps)!==l&&zt(n);else{if("string"!=typeof l&&null===n.stateNode)throw Error(r(166));e=oo.current,t=lo.current,n.stateNode=Hl(l,e,t,n)}return Pt(n),null;case 13:if(l=n.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=!1,null!==l&&null!==l.dehydrated){if(null===e){if(!a)throw Error(r(318));throw Error(r(344))}0==(128&n.flags)&&(n.memoizedState=null),n.flags|=4,Pt(n),a=!1}else null!==so&&(yr(so),so=null),a=!0;if(!a)return 256&n.flags?(ye(n),n):(ye(n),null)}if(ye(n),0!=(128&n.flags))return n.lanes=t,n;if(t=null!==l,e=null!==e&&null!==e.memoizedState,t){a=null,null!==(l=n.child).alternate&&null!==l.alternate.memoizedState&&null!==l.alternate.memoizedState.cachePool&&(a=l.alternate.memoizedState.cachePool.pool);var o=null;null!==l.memoizedState&&null!==l.memoizedState.cachePool&&(o=l.memoizedState.cachePool.pool),o!==a&&(l.flags|=2048)}return t!==e&&t&&(n.child.flags|=8192),Ct(n,n.updateQueue),Pt(n),null;case 4:return _(),null===e&&Vl(n.stateNode.containerInfo),Pt(n),null;case 10:return ct(n.type),Pt(n),null;case 19:if(d(Ho),null===(a=n.memoizedState))return Pt(n),null;if(l=0!=(128&n.flags),null===(o=a.rendering))if(l)Et(a,!1);else{if(0!==Wu||null!==e&&0!=(128&e.flags))for(e=n.child;null!==e;){if(null!==(o=be(e))){for(n.flags|=128,Et(a,!1),e=o.updateQueue,n.updateQueue=e,Ct(n,e),n.subtreeFlags=0,e=t,t=n.child;null!==t;)Gr(t,e),t=t.sibling;return p(Ho,1&Ho.current|2),n.child}e=e.sibling}null!==a.tail&&Qa()>Yu&&(n.flags|=128,l=!0,Et(a,!1),n.lanes=4194304)}else{if(!l)if(null!==(e=be(o))){if(n.flags|=128,l=!0,e=e.updateQueue,n.updateQueue=e,Ct(n,e),Et(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate)return Pt(n),null}else 2*Qa()-a.renderingStartTime>Yu&&536870912!==t&&(n.flags|=128,l=!0,Et(a,!1),n.lanes=4194304);a.isBackwards?(o.sibling=n.child,n.child=o):(null!==(e=a.last)?e.sibling=o:n.child=o,a.last=o)}return null!==a.tail?(n=a.tail,a.rendering=n,a.tail=n.sibling,a.renderingStartTime=Qa(),n.sibling=null,e=Ho.current,p(Ho,l?1&e|2:1&e),n):(Pt(n),null);case 22:case 23:return ye(n),pe(),l=null!==n.memoizedState,null!==e?null!==e.memoizedState!==l&&(n.flags|=8192):l&&(n.flags|=8192),l?0!=(536870912&t)&&0==(128&n.flags)&&(Pt(n),6&n.subtreeFlags&&(n.flags|=8192)):Pt(n),null!==(t=n.updateQueue)&&Ct(n,t.retryQueue),t=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),l=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(l=n.memoizedState.cachePool.pool),l!==t&&(n.flags|=2048),null!==e&&d(hu),null;case 24:return t=null,null!==e&&(t=e.memoizedState.cache),n.memoizedState.cache!==t&&(n.flags|=2048),ct(pu),Pt(n),null;case 25:return null}throw Error(r(156,n.tag))}function Rt(e,n){switch(E(n),n.tag){case 1:return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 3:return ct(pu),_(),0!=(65536&(e=n.flags))&&0==(128&e)?(n.flags=-65537&e|128,n):null;case 26:case 27:case 5:return T(n),null;case 13:if(ye(n),null!==(e=n.memoizedState)&&null!==e.dehydrated&&null===n.alternate)throw Error(r(340));return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 19:return d(Ho),null;case 4:return _(),null;case 10:return ct(n.type),null;case 22:case 23:return ye(n),pe(),null!==e&&d(hu),65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 24:return ct(pu),null;default:return null}}function Tt(e,n){switch(E(n),n.tag){case 3:ct(pu),_();break;case 26:case 27:case 5:T(n);break;case 4:_();break;case 13:ye(n);break;case 19:d(Ho);break;case 10:ct(n.type);break;case 22:case 23:ye(n),pe(),null!==e&&d(hu);break;case 24:ct(pu)}}function Nt(e,n){try{var t=n.updateQueue,r=null!==t?t.lastEffect:null;if(null!==r){var l=r.next;t=l;do{if((t.tag&e)===e){r=void 0;var a=t.create,o=t.inst;r=a(),o.destroy=r}t=t.next}while(t!==l)}}catch(e){jr(n,n.return,e)}}function Lt(e,n,t){try{var r=n.updateQueue,l=null!==r?r.lastEffect:null;if(null!==l){var a=l.next;r=a;do{if((r.tag&e)===e){var o=r.inst,u=o.destroy;if(void 0!==u){o.destroy=void 0,l=n;var i=t;try{u()}catch(e){jr(l,i,e)}}}r=r.next}while(r!==a)}}catch(e){jr(n,n.return,e)}}function Ut(e){var n=e.updateQueue;if(null!==n){var t=e.stateNode;try{ee(n,t)}catch(n){jr(e,e.return,n)}}}function Dt(e,n,t){t.props=Dn(e.type,e.memoizedProps),t.state=e.memoizedState;try{t.componentWillUnmount()}catch(t){jr(e,n,t)}}function It(e,n){try{var t=e.ref;if(null!==t){var r=e.stateNode;switch(e.tag){case 26:case 27:case 5:var l=Tl(r);break;default:l=r}"function"==typeof t?e.refCleanup=t(l):t.current=l}}catch(t){jr(e,n,t)}}function Ft(e,n){var t=e.ref,r=e.refCleanup;if(null!==t)if("function"==typeof r)try{r()}catch(t){jr(e,n,t)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof t)try{t(null)}catch(t){jr(e,n,t)}else t.current=null}function Mt(e){var n=e.type,t=e.memoizedProps,r=e.stateNode;try{ia(r,n,t,e)}catch(n){jr(e,e.return,n)}}function Wt(e){return 5===e.tag||3===e.tag||4===e.tag}function Ht(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||Wt(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function jt(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?fa(t,e,n):oa(t,e);else if(4!==r&&!Ra&&null!==(e=e.child))for(jt(e,n,t),e=e.sibling;null!==e;)jt(e,n,t),e=e.sibling}function At(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?ca(t,e,n):aa(t,e);else if(4!==r&&!Ra&&null!==(e=e.child))for(At(e,n,t),e=e.sibling;null!==e;)At(e,n,t),e=e.sibling}function Qt(e,n,t){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Kt(e,t),4&r&&Nt(5,t);break;case 1:if(Kt(e,t),4&r)if(e=t.stateNode,null===n)try{e.componentDidMount()}catch(e){jr(t,t.return,e)}else{var l=Dn(t.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(l,n,e.__reactInternalSnapshotBeforeUpdate)}catch(e){jr(t,t.return,e)}}64&r&&Ut(t),512&r&&It(t,t.return);break;case 3:if(Kt(e,t),64&r&&null!==(r=t.updateQueue)){if(e=null,null!==t.child)switch(t.child.tag){case 27:case 5:e=Tl(t.child.stateNode);break;case 1:e=t.child.stateNode}try{ee(r,e)}catch(e){jr(t,t.return,e)}}break;case 26:case 27:case 5:Kt(e,t),null===n&&4&r&&Mt(t),512&r&&It(t,t.return);break;case 12:case 13:default:Kt(e,t);break;case 22:if(!(l=null!==t.memoizedState||gu)){n=null!==n&&null!==n.memoizedState||yu;var a=gu,o=yu;gu=l,(yu=n)&&!o?Zt(e,t,0!=(8772&t.subtreeFlags)):Kt(e,t),gu=a,yu=o}512&r&&("manual"===t.memoizedProps.mode?It(t,t.return):Ft(t,t.return))}}function Ot(e){var n=e.alternate;null!==n&&(e.alternate=null,Ot(n)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(n=e.stateNode)&&Jl(n),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Bt(e,n,t){for(t=t.child;null!==t;)Vt(e,n,t),t=t.sibling}function Vt(e,n,t){switch(t.tag){case 26:case 27:var r,l;case 5:yu||Ft(t,n);case 6:if(r=wu,l=xu,wu=null,Bt(e,n,t),xu=l,null!==(wu=r))if(xu)try{pa(wu,t.stateNode)}catch(e){jr(t,n,e)}else try{da(wu,t.stateNode)}catch(e){jr(t,n,e)}break;case 18:null!==wu&&(xu?Ca(wu,t.stateNode):za(wu,t.stateNode));break;case 4:r=wu,l=xu,wu=t.stateNode.containerInfo,xu=!0,Bt(e,n,t),wu=r,xu=l;break;case 0:case 11:case 14:case 15:yu||Lt(2,t,n),yu||Lt(4,t,n),Bt(e,n,t);break;case 1:yu||(Ft(t,n),"function"==typeof(r=t.stateNode).componentWillUnmount&&Dt(t,n,r)),Bt(e,n,t);break;case 21:Bt(e,n,t);break;case 22:Ft(t,n),yu=(r=yu)||null!==t.memoizedState,Bt(e,n,t),yu=r;break;default:Bt(e,n,t)}}function $t(e,n){var t=function(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return null===n&&(n=e.stateNode=new vu),n;case 22:return null===(n=(e=e.stateNode)._retryCache)&&(n=e._retryCache=new vu),n;default:throw Error(r(435,e.tag))}}(e);n.forEach((function(n){var r=Vr.bind(null,e,n);t.has(n)||(t.add(n),n.then(r,r))}))}function qt(e,n){var t=n.deletions;if(null!==t)for(var l=0;l<t.length;l++){var a=t[l],o=e,u=n,i=u;e:for(;null!==i;){switch(i.tag){case 27:case 5:wu=i.stateNode,xu=!1;break e;case 3:case 4:wu=i.stateNode.containerInfo,xu=!0;break e}i=i.return}if(null===wu)throw Error(r(160));Vt(o,u,a),wu=null,xu=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&n.subtreeFlags)for(n=n.child;null!==n;)Yt(n,e),n=n.sibling}function Yt(e,n){var t=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:qt(n,e),Gt(e),4&l&&(Lt(3,e,e.return),Nt(3,e),Lt(5,e,e.return));break;case 1:qt(n,e),Gt(e),512&l&&null!==t&&Ft(t,t.return),64&l&&gu&&null!==(e=e.updateQueue)&&null!==(l=e.callbacks)&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===t?l:t.concat(l));break;case 26:var a;case 27:var o;case 5:if(qt(n,e),Gt(e),512&l&&null!==t&&Ft(t,t.return),32&e.flags){n=e.stateNode;try{ma(n)}catch(n){jr(e,e.return,n)}}4&l&&null!=e.stateNode&&function(e,n,t){try{sa(e.stateNode,e.type,t,n,e)}catch(n){jr(e,e.return,n)}}(e,n=e.memoizedProps,null!==t?t.memoizedProps:n),1024&l&&(bu=!0);break;case 6:if(qt(n,e),Gt(e),4&l&&Ol){if(null===e.stateNode)throw Error(r(162));l=e.memoizedProps,t=null!==t?t.memoizedProps:l,n=e.stateNode;try{ua(n,t,l)}catch(n){jr(e,e.return,n)}}break;case 3:qt(n,e),Gt(e),bu&&(bu=!1,Jt(e));break;case 4:case 12:qt(n,e),Gt(e);break;case 13:qt(n,e),Gt(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==t&&null!==t.memoizedState)&&(qu=Qa()),4&l&&null!==(l=e.updateQueue)&&(e.updateQueue=null,$t(e,l));break;case 22:512&l&&null!==t&&Ft(t,t.return),a=null!==e.memoizedState;var u=null!==t&&null!==t.memoizedState,i=gu,s=yu;if(gu=i||a,yu=s||u,qt(n,e),yu=s,gu=i,Gt(e),(n=e.stateNode)._current=e,n._visibility&=-3,n._visibility|=2&n._pendingVisibility,8192&l&&(n._visibility=a?-2&n._visibility:1|n._visibility,a&&(n=gu||yu,null===t||u||n||Xt(e)),null===e.memoizedProps||"manual"!==e.memoizedProps.mode))e:if(t=null,Ol)for(n=e;;){if(5===n.tag||Ea||Ra){if(null===t){u=t=n;try{o=u.stateNode,a?ha(o):ya(u.stateNode,u.memoizedProps)}catch(e){jr(u,u.return,e)}}}else if(6===n.tag){if(null===t){u=n;try{var c=u.stateNode;a?ga(c):ba(c,u.memoizedProps)}catch(e){jr(u,u.return,e)}}}else if((22!==n.tag&&23!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;null===n.sibling;){if(null===n.return||n.return===e)break e;t===n&&(t=null),n=n.return}t===n&&(t=null),n.sibling.return=n.return,n=n.sibling}4&l&&null!==(l=e.updateQueue)&&null!==(t=l.retryQueue)&&(l.retryQueue=null,$t(e,t));break;case 19:qt(n,e),Gt(e),4&l&&null!==(l=e.updateQueue)&&(e.updateQueue=null,$t(e,l));break;case 21:break;default:qt(n,e),Gt(e)}}function Gt(e){var n=e.flags;if(2&n){try{if(Ol&&(!Ra||27!==e.tag)){e:{for(var t=e.return;null!==t;){if(Wt(t)){var l=t;break e}t=t.return}throw Error(r(160))}switch(l.tag){case 27:case 5:var a=l.stateNode;32&l.flags&&(ma(a),l.flags&=-33),At(e,Ht(e),a);break;case 3:case 4:var o=l.stateNode.containerInfo;jt(e,Ht(e),o);break;default:throw Error(r(161))}}}catch(n){jr(e,e.return,n)}e.flags&=-3}4096&n&&(e.flags&=-4097)}function Jt(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var n=e;Jt(n),5===n.tag&&1024&n.flags&&la(n.stateNode),e=e.sibling}}function Kt(e,n){if(8772&n.subtreeFlags)for(n=n.child;null!==n;)Qt(e,n.alternate,n),n=n.sibling}function Xt(e){for(e=e.child;null!==e;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:Lt(4,n,n.return),Xt(n);break;case 1:Ft(n,n.return);var t=n.stateNode;"function"==typeof t.componentWillUnmount&&Dt(n,n.return,t),Xt(n);break;case 26:case 27:case 5:Ft(n,n.return),Xt(n);break;case 22:Ft(n,n.return),null===n.memoizedState&&Xt(n);break;default:Xt(n)}e=e.sibling}}function Zt(e,n,t){for(t=t&&0!=(8772&n.subtreeFlags),n=n.child;null!==n;){var r=n.alternate,l=e,a=n,o=a.flags;switch(a.tag){case 0:case 11:case 15:Zt(l,a,t),Nt(4,a);break;case 1:if(Zt(l,a,t),"function"==typeof(l=(r=a).stateNode).componentDidMount)try{l.componentDidMount()}catch(e){jr(r,r.return,e)}if(null!==(l=(r=a).updateQueue)){var u=r.stateNode;try{var i=l.shared.hiddenCallbacks;if(null!==i)for(l.shared.hiddenCallbacks=null,l=0;l<i.length;l++)Z(i[l],u)}catch(e){jr(r,r.return,e)}}t&&64&o&&Ut(a),It(a,a.return);break;case 26:case 27:case 5:Zt(l,a,t),t&&null===r&&4&o&&Mt(a),It(a,a.return);break;case 12:case 13:default:Zt(l,a,t);break;case 22:null===a.memoizedState&&Zt(l,a,t),It(a,a.return)}n=n.sibling}}function er(e,n){var t=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),e=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(e=n.memoizedState.cachePool.pool),e!==t&&(null!=e&&e.refCount++,null!=t&&St(t))}function nr(e,n){e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&St(e))}function tr(e,n,t,r){if(10256&n.subtreeFlags)for(n=n.child;null!==n;)rr(e,n,t,r),n=n.sibling}function rr(e,n,t,r){var l=n.flags;switch(n.tag){case 0:case 11:case 15:tr(e,n,t,r),2048&l&&Nt(9,n);break;case 3:tr(e,n,t,r),2048&l&&(e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&St(e)));break;case 12:if(2048&l){tr(e,n,t,r),e=n.stateNode;try{var a=n.memoizedProps,o=a.id,u=a.onPostCommit;"function"==typeof u&&u(o,null===n.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(e){jr(n,n.return,e)}}else tr(e,n,t,r);break;case 23:break;case 22:a=n.stateNode,null!==n.memoizedState?4&a._visibility?tr(e,n,t,r):ar(e,n):4&a._visibility?tr(e,n,t,r):(a._visibility|=4,lr(e,n,t,r,0!=(10256&n.subtreeFlags))),2048&l&&er(n.alternate,n);break;case 24:tr(e,n,t,r),2048&l&&nr(n.alternate,n);break;default:tr(e,n,t,r)}}function lr(e,n,t,r,l){for(l=l&&0!=(10256&n.subtreeFlags),n=n.child;null!==n;){var a=e,o=n,u=t,i=r,s=o.flags;switch(o.tag){case 0:case 11:case 15:lr(a,o,u,i,l),Nt(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?4&c._visibility?lr(a,o,u,i,l):ar(a,o):(c._visibility|=4,lr(a,o,u,i,l)),l&&2048&s&&er(o.alternate,o);break;case 24:lr(a,o,u,i,l),l&&2048&s&&nr(o.alternate,o);break;default:lr(a,o,u,i,l)}n=n.sibling}}function ar(e,n){if(10256&n.subtreeFlags)for(n=n.child;null!==n;){var t=e,r=n,l=r.flags;switch(r.tag){case 22:ar(t,r),2048&l&&er(r.alternate,r);break;case 24:ar(t,r),2048&l&&nr(r.alternate,r);break;default:ar(t,r)}n=n.sibling}}function or(e){if(e.subtreeFlags&Cu)for(e=e.child;null!==e;)ur(e),e=e.sibling}function ur(e){switch(e.tag){case 26:or(e),e.flags&Cu&&(null!==e.memoizedState?_a(zu,e.memoizedState,e.memoizedProps):ea(e.type,e.memoizedProps));break;case 5:or(e),e.flags&Cu&&ea(e.type,e.memoizedProps);break;case 3:case 4:var n;or(e);break;case 22:null===e.memoizedState&&(null!==(n=e.alternate)&&null!==n.memoizedState?(n=Cu,Cu=16777216,or(e),Cu=n):or(e));break;default:or(e)}}function ir(e){var n=e.alternate;if(null!==n&&null!==(e=n.child)){n.child=null;do{n=e.sibling,e.sibling=null,e=n}while(null!==e)}}function sr(e){var n=e.deletions;if(0!=(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Su=r,dr(r,e)}ir(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)cr(e),e=e.sibling}function cr(e){switch(e.tag){case 0:case 11:case 15:sr(e),2048&e.flags&&Lt(9,e,e.return);break;case 3:case 12:default:sr(e);break;case 22:var n=e.stateNode;null!==e.memoizedState&&4&n._visibility&&(null===e.return||13!==e.return.tag)?(n._visibility&=-5,fr(e)):sr(e)}}function fr(e){var n=e.deletions;if(0!=(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Su=r,dr(r,e)}ir(e)}for(e=e.child;null!==e;){switch((n=e).tag){case 0:case 11:case 15:Lt(8,n,n.return),fr(n);break;case 22:4&(t=n.stateNode)._visibility&&(t._visibility&=-5,fr(n));break;default:fr(n)}e=e.sibling}}function dr(e,n){for(;null!==Su;){var t=Su;switch(t.tag){case 0:case 11:case 15:Lt(8,t,n);break;case 23:case 22:if(null!==t.memoizedState&&null!==t.memoizedState.cachePool){var r=t.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:St(t.memoizedState.cache)}if(null!==(r=t.child))r.return=t,Su=r;else e:for(t=e;null!==Su;){var l=(r=Su).sibling,a=r.return;if(Ot(r),r===t){Su=null;break e}if(null!==l){l.return=a,Su=l;break e}Su=a}}}function pr(){return 0!=(2&Ru)&&0!==Lu?Lu&-Lu:null!==Pl.T?0!==wo?wo:O():Yl()}function mr(){0===Qu&&(Qu=0==(536870912&Lu)||io?y():536870912);var e=Mo.current;return null!==e&&(e.flags|=32),Qu}function hr(e,n,t){(e===Tu&&2===Uu||null!==e.cancelPendingCommit)&&(wr(e,0),Sr(e,Lu,Qu)),S(e,t),0!=(2&Ru)&&e===Tu||(e===Tu&&(0==(2&Ru)&&(ju|=t),4===Wu&&Sr(e,Lu,Qu)),M(e))}function gr(e,n,t){if(0!=(6&Ru))throw Error(r(327));var l=(t=!t&&0==(60&n)&&0==(n&e.expiredLanes))?function(e,n){var t=Ru;Ru|=2;var l=Cr(),a=Er();Tu===e&&Lu===n||(Gu=null,Yu=Qa()+500,wr(e,n));e:for(;;)try{if(0!==Uu&&null!==Nu){n=Nu;var o=Du;n:switch(Uu){case 1:case 6:Uu=0,Du=null,Ur(e,n,o);break;case 2:if(te(o)){Uu=0,Du=null,Lr(n);break}n=function(){2===Uu&&Tu===e&&(Uu=7),M(e)},o.then(n,n);break e;case 3:Uu=7;break e;case 4:Uu=5;break e;case 7:te(o)?(Uu=0,Du=null,Lr(n)):(Uu=0,Du=null,Ur(e,n,o));break;case 5:var u=null;switch(Nu.tag){case 26:u=Nu.memoizedState;case 5:case 27:var i=Nu,s=i.type,c=i.pendingProps;if(u?Pa(u):Xl(s,c)){Uu=0,Du=null;var f=i.sibling;if(null!==f)Nu=f;else{var d=i.return;null!==d?(Nu=d,Dr(d)):Nu=null}break n}}Uu=0,Du=null,Ur(e,n,o);break;case 8:kr(),Wu=6;break e;default:throw Error(r(462))}}Tr();break}catch(n){xr(e,n)}return it(),Pl.H=l,Pl.A=a,Ru=t,null!==Nu?0:(Tu=null,Lu=0,N(),Wu)}(e,n):_r(e,n);if(0!==l)for(var a=t;;){if(6===l)Sr(e,n,0);else{if(t=e.current.alternate,a&&!vr(t)){l=_r(e,n),a=!1;continue}if(2===l){if(a=n,e.errorRecoveryDisabledLanes&a)var o=0;else o=0!=(o=-536870913&e.pendingLanes)?o:536870912&o?536870912:0;if(0!==o){n=o;e:{var u=e;l=Bu;var i=Bl;if(i&&(wr(u,o).flags|=256),2!==(o=_r(u,o))){if(Fu&&!i){u.errorRecoveryDisabledLanes|=a,ju|=a,l=4;break e}a=Vu,Vu=l,null!==a&&yr(a)}l=o}if(a=!1,2!==l)continue}}if(1===l){wr(e,0),Sr(e,n,0);break}e:{switch(a=e,l){case 0:case 1:throw Error(r(345));case 4:if((4194176&n)===n){Sr(a,n,Qu);break e}break;case 2:Vu=null;break;case 3:case 5:break;default:throw Error(r(329))}if(a.finishedWork=t,a.finishedLanes=n,(62914560&n)===n&&10<(l=qu+300-Qa())){if(Sr(a,n,Qu),0!==h(a,0))break e;a.timeoutHandle=jl(br.bind(null,a,t,Vu,Gu,$u,n,Qu,ju,Ou,Iu,2,-0,0),l)}else br(a,t,Vu,Gu,$u,n,Qu,ju,Ou,Iu,0,-0,0)}}break}M(e)}function yr(e){null===Vu?Vu=e:Vu.push.apply(Vu,e)}function br(e,n,t,r,l,a,o,u,i,s,c,f,d){if((8192&(s=n.subtreeFlags)||16785408==(16785408&s))&&(Zl(),ur(n),null!==(n=na())))return e.cancelPendingCommit=n(Fr.bind(null,e,t,r,l,o,u,i,1,f,d)),void Sr(e,a,o);Fr(e,t,r,l,o)}function vr(e){for(var n=e;;){var t=n.tag;if((0===t||11===t||15===t)&&16384&n.flags&&null!==(t=n.updateQueue)&&null!==(t=t.stores))for(var r=0;r<t.length;r++){var l=t[r],a=l.getSnapshot;l=l.value;try{if(!Ja(a(),l))return!1}catch(e){return!1}}if(t=n.child,16384&n.subtreeFlags&&null!==t)t.return=n,n=t;else{if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Sr(e,n,t){n&=~Au,n&=~ju,e.suspendedLanes|=n,e.pingedLanes&=~n;for(var r=e.expirationTimes,l=n;0<l;){var a=31-Ua(l),o=1<<a;r[a]=-1,l&=~o}0!==t&&k(e,t,n)}function kr(){if(null!==Nu){if(0===Uu)var e=Nu.return;else e=Nu,it(),Pe(e),No=null,Lo=0,e=Nu;for(;null!==e;)Tt(e.alternate,e),e=e.return;Nu=null}}function wr(e,n){e.finishedWork=null,e.finishedLanes=0;var t=e.timeoutHandle;t!==Ql&&(e.timeoutHandle=Ql,Al(t)),null!==(t=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,t()),kr(),Tu=e,Nu=t=Yr(e.current,null),Lu=n,Uu=0,Du=null,Fu=Iu=!1,Ou=Qu=Au=ju=Hu=Wu=0,Vu=Bu=null,$u=!1,0!=(8&n)&&(n|=32&n);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=n;0<r;){var l=31-Ua(r),a=1<<l;n|=e[l],r&=~a}return Mu=n,N(),t}function xr(e,n){Ao=null,Pl.H=Xo,n===Po?(n=ae(),Uu=zr()&&0==(134217727&Hu)&&0==(134217727&ju)?2:3):n===_o?(n=ae(),Uu=4):Uu=n===ru?8:null!==n&&"object"==typeof n&&"function"==typeof n.then?6:1,Du=n,null===Nu&&(Wu=1,In(e,C(n,e.current)))}function zr(){var e=Mo.current;return null===e||((4194176&Lu)===Lu?null===Wo:((62914560&Lu)===Lu||0!=(536870912&Lu))&&e===Wo)}function Cr(){var e=Pl.H;return Pl.H=Xo,null===e?Xo:e}function Er(){var e=Pl.A;return Pl.A=Eu,e}function Pr(){Wu=4,0==(134217727&Hu)&&0==(134217727&ju)||null===Tu||Sr(Tu,Lu,Qu)}function _r(e,n){var t=Ru;Ru|=2;var l=Cr(),a=Er();Tu===e&&Lu===n||(Gu=null,wr(e,n)),n=!1;e:for(;;)try{if(0!==Uu&&null!==Nu){var o=Nu,u=Du;switch(Uu){case 8:kr(),Wu=6;break e;case 3:case 2:n||null!==Mo.current||(n=!0);default:Uu=0,Du=null,Ur(e,o,u)}}Rr();break}catch(n){xr(e,n)}if(n&&e.shellSuspendCounter++,it(),Ru=t,Pl.H=l,Pl.A=a,null!==Nu)throw Error(r(261));return Tu=null,Lu=0,N(),Wu}function Rr(){for(;null!==Nu;)Nr(Nu)}function Tr(){for(;null!==Nu&&!ja();)Nr(Nu)}function Nr(e){var n=ut(e.alternate,e,Mu);e.memoizedProps=e.pendingProps,null===n?Dr(e):Nu=n}function Lr(e){var n=e,t=n.alternate;switch(n.tag){case 15:case 0:n=Yn(t,n,n.pendingProps,n.type,void 0,Lu);break;case 11:n=Yn(t,n,n.pendingProps,n.type.render,n.ref,Lu);break;case 5:Pe(n);default:Tt(t,n),n=ut(t,n=Nu=Gr(n,Mu),Mu)}e.memoizedProps=e.pendingProps,null===n?Dr(e):Nu=n}function Ur(e,n,t){it(),Pe(n),No=null,Lo=0;var l=n.return;try{if(function(e,n,t,l,a){if(t.flags|=32768,null!==l&&"object"==typeof l&&"function"==typeof l.then){if(null!==(n=t.alternate)&&pt(n,t,a,!0),null!==(t=Mo.current)){switch(t.tag){case 13:return null===Wo?Pr():null===t.alternate&&0===Wu&&(Wu=3),t.flags&=-257,t.flags|=65536,t.lanes=a,l===Ro?t.flags|=16384:(null===(n=t.updateQueue)?t.updateQueue=new Set([l]):n.add(l),Ar(e,l,a)),!1;case 22:return t.flags|=65536,l===Ro?t.flags|=16384:(null===(n=t.updateQueue)?(n={transitions:null,markerInstances:null,retryQueue:new Set([l])},t.updateQueue=n):null===(t=n.retryQueue)?n.retryQueue=new Set([l]):t.add(l),Ar(e,l,a)),!1}throw Error(r(435,t.tag))}return Ar(e,l,a),Pr(),!1}var o=Error(r(520),{cause:l});if(o=C(o,t),null===Bu?Bu=[o]:Bu.push(o),4!==Wu&&(Wu=2),null===n)return!0;l=C(l,t),t=n;do{switch(t.tag){case 3:return t.flags|=65536,e=a&-a,t.lanes|=e,J(t,e=Mn(t.stateNode,l,e)),!1;case 1:if(n=t.type,o=t.stateNode,0==(128&t.flags)&&("function"==typeof n.getDerivedStateFromError||null!==o&&"function"==typeof o.componentDidCatch&&(null===Ju||!Ju.has(o))))return t.flags|=65536,a&=-a,t.lanes|=a,Hn(a=Wn(a),e,t,l),J(t,a),!1}t=t.return}while(null!==t);return!1}(e,l,n,t,Lu))return Wu=1,In(e,C(t,e.current)),void(Nu=null)}catch(n){if(null!==l)throw Nu=l,n;return Wu=1,In(e,C(t,e.current)),void(Nu=null)}32768&n.flags?Ir(n,!0):Dr(n)}function Dr(e){var n=e;do{if(0!=(32768&n.flags))return void Ir(n,Iu);e=n.return;var t=_t(n.alternate,n,Mu);if(null!==t)return void(Nu=t);if(null!==(n=n.sibling))return void(Nu=n);Nu=n=e}while(null!==n);0===Wu&&(Wu=5)}function Ir(e,n){do{var t=Rt(e.alternate,e);if(null!==t)return t.flags&=32767,void(Nu=t);if(null!==(t=e.return)&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!n&&null!==(e=e.sibling))return void(Nu=e);Nu=e=t}while(null!==e);Wu=6,Nu=null}function Fr(e,n,t,l,a,o,u,i,s,c){var f=Pl.T,d=ql();try{$l(2),Pl.T=null,function(e,n,t,l,a,o){do{Wr()}while(null!==Xu);if(0!=(6&Ru))throw Error(r(327));var u=e.finishedWork;if(l=e.finishedLanes,null===u)return null;if(e.finishedWork=null,e.finishedLanes=0,u===e.current)throw Error(r(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var i=u.lanes|u.childLanes;if(function(e,n,t,r){var l=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0,n=e.entanglements;var a=e.expirationTimes,o=e.hiddenUpdates;for(t=l&~t;0<t;){var u=31-Ua(t);l=1<<u,n[u]=0,a[u]=-1;var i=o[u];if(null!==i)for(o[u]=null,u=0;u<i.length;u++){var s=i[u];null!==s&&(s.lane&=-536870913)}t&=~l}0!==r&&k(e,r,0)}(e,l,i|=po,o),e===Tu&&(Nu=Tu=null,Lu=0),0==(10256&u.subtreeFlags)&&0==(10256&u.flags)||Ku||(Ku=!0,ei=i,ni=t,Wa(Va,(function(){return Wr(),null}))),t=0!=(15990&u.flags),0!=(15990&u.subtreeFlags)||t){t=Pl.T,Pl.T=null,o=ql(),$l(2);var s=Ru;Ru|=4,function(e,n){for(Ul(e.containerInfo),Su=n;null!==Su;)if(n=(e=Su).child,0!=(1028&e.subtreeFlags)&&null!==n)n.return=e,Su=n;else for(;null!==Su;){var t=(e=Su).alternate;switch(n=e.flags,e.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!=(1024&n)&&null!==t){n=void 0;var l=e,a=t.memoizedProps;t=t.memoizedState;var o=l.stateNode;try{var u=Dn(l.type,a,(l.elementType,l.type));n=o.getSnapshotBeforeUpdate(u,t),o.__reactInternalSnapshotBeforeUpdate=n}catch(e){jr(l,l.return,e)}}break;case 3:0!=(1024&n)&&Ol&&va(e.stateNode.containerInfo);break;default:if(0!=(1024&n))throw Error(r(163))}if(null!==(n=e.sibling)){n.return=e.return,Su=n;break}Su=e.return}u=ku,ku=!1}(e,u),Yt(u,e),Dl(e.containerInfo),e.current=u,Qt(e,u.alternate,u),Aa(),Ru=s,$l(o),Pl.T=t}else e.current=u;if(Ku?(Ku=!1,Xu=e,Zu=l):Mr(e,i),0===(i=e.pendingLanes)&&(Ju=null),u.stateNode,M(e),null!==n)for(a=e.onRecoverableError,u=0;u<n.length;u++)a((i=n[u]).value,{componentStack:i.stack});0!=(3&Zu)&&Wr(),i=e.pendingLanes,0!=(4194218&l)&&0!=(42&i)?e===ri?ti++:(ti=0,ri=e):ti=0,W(0)}(e,n,t,l,d,a)}finally{Pl.T=f,$l(d)}}function Mr(e,n){0==(e.pooledCacheLanes&=n)&&null!=(n=e.pooledCache)&&(e.pooledCache=null,St(n))}function Wr(){if(null!==Xu){var e=Xu,n=ei;ei=0;var t=x(Zu),l=32>t?32:t;t=Pl.T;var a=ql();try{if($l(l),Pl.T=null,null===Xu)var o=!1;else{l=ni,ni=null;var u=Xu,i=Zu;if(Xu=null,Zu=0,0!=(6&Ru))throw Error(r(331));var s=Ru;Ru|=4,cr(u.current),rr(u,u.current,i,l),Ru=s,W(0),Ga&&Ga.onPostCommitFiberRoot,o=!0}return o}finally{$l(a),Pl.T=t,Mr(e,n)}}return!1}function Hr(e,n,t){n=C(t,n),null!==(e=Y(e,n=Mn(e.stateNode,n,2),2))&&(S(e,2),M(e))}function jr(e,n,t){if(3===e.tag)Hr(e,e,t);else for(;null!==n;){if(3===n.tag){Hr(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Ju||!Ju.has(r))){e=C(t,e),null!==(r=Y(n,t=Wn(2),2))&&(Hn(t,r,n,e),S(r,2),M(r));break}}n=n.return}}function Ar(e,n,t){var r=e.pingCache;if(null===r){r=e.pingCache=new _u;var l=new Set;r.set(n,l)}else void 0===(l=r.get(n))&&(l=new Set,r.set(n,l));l.has(t)||(Fu=!0,l.add(t),e=Qr.bind(null,e,n,t),n.then(e,e))}function Qr(e,n,t){var r=e.pingCache;null!==r&&r.delete(n),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,Tu===e&&(Lu&t)===t&&(4===Wu||3===Wu&&(62914560&Lu)===Lu&&300>Qa()-qu?0==(2&Ru)&&wr(e,0):Au|=t,Ou===Lu&&(Ou=0)),M(e)}function Or(e,n){0===n&&(n=b()),null!==(e=D(e,n))&&(S(e,n),M(e))}function Br(e){var n=e.memoizedState,t=0;null!==n&&(t=n.retryLane),Or(e,t)}function Vr(e,n){var t=0;switch(e.tag){case 13:var l=e.stateNode,a=e.memoizedState;null!==a&&(t=a.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==l&&l.delete(n),Or(e,t)}function $r(e,n,t,r){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function qr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Yr(e,n){var r=e.alternate;return null===r?((r=t(e.tag,n,e.key,e.mode)).elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=n,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=31457280&e.flags,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,n=e.dependencies,r.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r.refCleanup=e.refCleanup,r}function Gr(e,n){e.flags&=31457282;var t=e.alternate;return null===t?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,n=t.dependencies,e.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Jr(e,n,l,a,o,u){var i=0;if(a=e,"function"==typeof e)qr(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case dl:return Kr(l.children,o,u,n);case pl:i=8,o|=24;break;case ml:return(e=t(12,l,n,2|o)).elementType=ml,e.lanes=u,e;case vl:return(e=t(13,l,n,o)).elementType=vl,e.lanes=u,e;case Sl:return(e=t(19,l,n,o)).elementType=Sl,e.lanes=u,e;case xl:return Xr(l,o,u,n);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case hl:case yl:i=10;break e;case gl:i=9;break e;case bl:i=11;break e;case kl:i=14;break e;case wl:i=16,a=null;break e}i=29,l=Error(r(130,null===e?"null":typeof e,"")),a=null}return(n=t(i,l,n,o)).elementType=e,n.type=a,n.lanes=u,n}function Kr(e,n,r,l){return(e=t(7,e,l,n)).lanes=r,e}function Xr(e,n,l,a){(e=t(22,e,a,n)).elementType=xl,e.lanes=l;var o={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var e=o._current;if(null===e)throw Error(r(456));if(0==(2&o._pendingVisibility)){var n=D(e,2);null!==n&&(o._pendingVisibility|=2,hr(n,0,2))}},attach:function(){var e=o._current;if(null===e)throw Error(r(456));if(0!=(2&o._pendingVisibility)){var n=D(e,2);null!==n&&(o._pendingVisibility&=-3,hr(n,0,2))}}};return e.stateNode=o,e}function Zr(e,n,r){return(e=t(6,e,null,n)).lanes=r,e}function el(e,n,r){return(n=t(4,null!==e.children?e.children:[],e.key,n)).lanes=r,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function nl(e,n,t,r,l,a,o,u){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=Ql,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=v(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=v(0),this.hiddenUpdates=v(null),this.identifierPrefix=r,this.onUncaughtError=l,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function tl(e,n,t,r,l,a){l=function(e){return e?e=La:La}(l),null===r.context?r.context=l:r.pendingContext=l,(r=q(n)).payload={element:t},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(t=Y(e,r,n))&&(hr(t,0,n),G(t,e,n))}var rl,ll,al={},ol=e,ul=u,il=Object.assign,sl=Symbol.for("react.element"),cl=Symbol.for("react.transitional.element"),fl=Symbol.for("react.portal"),dl=Symbol.for("react.fragment"),pl=Symbol.for("react.strict_mode"),ml=Symbol.for("react.profiler"),hl=Symbol.for("react.provider"),gl=Symbol.for("react.consumer"),yl=Symbol.for("react.context"),bl=Symbol.for("react.forward_ref"),vl=Symbol.for("react.suspense"),Sl=Symbol.for("react.suspense_list"),kl=Symbol.for("react.memo"),wl=Symbol.for("react.lazy"),xl=Symbol.for("react.offscreen"),zl=Symbol.for("react.memo_cache_sentinel"),Cl=Symbol.iterator,El=Symbol.for("react.client.reference"),Pl=ol.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,_l=!1,Rl=Array.isArray,Tl=n.getPublicInstance,Nl=n.getRootHostContext,Ll=n.getChildHostContext,Ul=n.prepareForCommit,Dl=n.resetAfterCommit,Il=n.createInstance,Fl=n.appendInitialChild,Ml=n.finalizeInitialChildren,Wl=n.shouldSetTextContent,Hl=n.createTextInstance,jl=null,Al=null,Ql=n.noTimeout,Ol=!0,Bl=null,Vl=null,$l=n.setCurrentUpdatePriority,ql=n.getCurrentUpdatePriority,Yl=n.resolveUpdatePriority,Gl=n.shouldAttemptEagerTransition,Jl=null;n.requestPostPaintCallback;var Kl=n.maySuspendCommit,Xl=null,Zl=null,ea=null,na=null,ta=null,ra=null,la=null,aa=n.appendChild,oa=n.appendChildToContainer,ua=n.commitTextUpdate,ia=null,sa=n.commitUpdate,ca=n.insertBefore,fa=null,da=n.removeChild,pa=n.removeChildFromContainer,ma=n.resetTextContent,ha=null,ga=null,ya=null,ba=null,va=n.clearContainer,Sa=null,ka=null,wa=null,xa=null,za=null,Ca=null,Ea=null,Pa=null,_a=null,Ra=null,Ta=[],Na=-1,La={},Ua=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(Da(e)/Ia|0)|0},Da=Math.log,Ia=Math.LN2,Fa=128,Ma=4194304,Wa=ul.unstable_scheduleCallback,Ha=ul.unstable_cancelCallback,ja=ul.unstable_shouldYield,Aa=ul.unstable_requestPaint,Qa=ul.unstable_now,Oa=ul.unstable_ImmediatePriority,Ba=ul.unstable_UserBlockingPriority,Va=ul.unstable_NormalPriority,$a=ul.unstable_IdlePriority,qa=ul.log,Ya=ul.unstable_setDisableYieldValue,Ga=null,Ja="function"==typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e==1/n)||e!=e&&n!=n},Ka=new WeakMap,Xa=[],Za=0,eo=null,no=[],to=0,ro=null,lo=f(null),ao=f(null),oo=f(null),uo=f(null),io=!1,so=null;Error(r(519));var co=[],fo=0,po=0,mo=null,ho=null,go=!1,yo=!1,bo=!1,vo=0,So=null,ko=0,wo=0,xo=null,zo=!1,Co=!1,Eo=Object.prototype.hasOwnProperty,Po=Error(r(460)),_o=Error(r(474)),Ro={then:function(){}},To=null,No=null,Lo=0,Uo=ce(!0),Do=ce(!1),Io=f(null),Fo=f(0),Mo=f(null),Wo=null,Ho=f(0),jo=0,Ao=null,Qo=null,Oo=null,Bo=!1,Vo=!1,$o=!1,qo=0,Yo=0,Go=null,Jo=0,Ko=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}},Xo={readContext:gt,use:Ne,useCallback:ve,useContext:ve,useEffect:ve,useImperativeHandle:ve,useLayoutEffect:ve,useInsertionEffect:ve,useMemo:ve,useReducer:ve,useRef:ve,useState:ve,useDebugValue:ve,useDeferredValue:ve,useTransition:ve,useSyncExternalStore:ve,useId:ve};Xo.useCacheRefresh=ve,Xo.useMemoCache=ve,Xo.useHostTransitionStatus=ve,Xo.useFormState=ve,Xo.useActionState=ve,Xo.useOptimistic=ve;var Zo={readContext:gt,use:Ne,useCallback:function(e,n){return _e().memoizedState=[e,void 0===n?null:n],e},useContext:gt,useEffect:un,useImperativeHandle:function(e,n,t){t=null!=t?t.concat([e]):null,an(4194308,4,dn.bind(null,n,e),t)},useLayoutEffect:function(e,n){return an(4194308,4,e,n)},useInsertionEffect:function(e,n){an(4,2,e,n)},useMemo:function(e,n){var t=_e();n=void 0===n?null:n;var r=e();if($o){z(!0);try{e()}finally{z(!1)}}return t.memoizedState=[r,n],r},useReducer:function(e,n,t){var r=_e();if(void 0!==t){var l=t(n);if($o){z(!0);try{t(n)}finally{z(!1)}}}else l=n;return r.memoizedState=r.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},r.queue=e,e=e.dispatch=zn.bind(null,Ao,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},_e().memoizedState=e},useState:function(e){var n=(e=Oe(e)).queue,t=Cn.bind(null,Ao,n);return n.dispatch=t,[e.memoizedState,t]},useDebugValue:mn,useDeferredValue:function(e,n){return yn(_e(),e,n)},useTransition:function(){var e=Oe(!1);return e=vn.bind(null,Ao,e.queue,!0,!1),_e().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,t){var l=Ao,a=_e();if(t=n(),null===Tu)throw Error(r(349));0!=(60&Lu)||We(l,n,t),a.memoizedState=t;var o={value:t,getSnapshot:n};return a.queue=o,un(je.bind(null,l,o,e),[e]),l.flags|=2048,rn(9,He.bind(null,l,o,t,n),{destroy:void 0},null),t},useId:function(){var e=_e(),n=Tu.identifierPrefix;return n=":"+n+"r"+(Jo++).toString(32)+":",e.memoizedState=n},useCacheRefresh:function(){return _e().memoizedState=xn.bind(null,Ao)}};Zo.useMemoCache=Le,Zo.useHostTransitionStatus=Sn,Zo.useFormState=Xe,Zo.useActionState=Xe,Zo.useOptimistic=function(e){var n=_e();n.memoizedState=n.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=t,n=Pn.bind(null,Ao,!0,t),t.dispatch=n,[e,n]};var eu={readContext:gt,use:Ne,useCallback:hn,useContext:gt,useEffect:sn,useImperativeHandle:pn,useInsertionEffect:cn,useLayoutEffect:fn,useMemo:gn,useReducer:De,useRef:ln,useState:function(){return De(Ue)},useDebugValue:mn,useDeferredValue:function(e,n){return bn(Re(),Qo.memoizedState,e,n)},useTransition:function(){var e=De(Ue)[0],n=Re().memoizedState;return["boolean"==typeof e?e:Te(e),n]},useSyncExternalStore:Me,useId:kn};eu.useCacheRefresh=wn,eu.useMemoCache=Le,eu.useHostTransitionStatus=Sn,eu.useFormState=Ze,eu.useActionState=Ze,eu.useOptimistic=function(e,n){return Be(Re(),0,e,n)};var nu={readContext:gt,use:Ne,useCallback:hn,useContext:gt,useEffect:sn,useImperativeHandle:pn,useInsertionEffect:cn,useLayoutEffect:fn,useMemo:gn,useReducer:Fe,useRef:ln,useState:function(){return Fe(Ue)},useDebugValue:mn,useDeferredValue:function(e,n){var t=Re();return null===Qo?yn(t,e,n):bn(t,Qo.memoizedState,e,n)},useTransition:function(){var e=Fe(Ue)[0],n=Re().memoizedState;return["boolean"==typeof e?e:Te(e),n]},useSyncExternalStore:Me,useId:kn};nu.useCacheRefresh=wn,nu.useMemoCache=Le,nu.useHostTransitionStatus=Sn,nu.useFormState=tn,nu.useActionState=tn,nu.useOptimistic=function(e,n){var t=Re();return null!==Qo?Be(t,0,e,n):(t.baseState=e,[e,t.queue.dispatch])};var tu={isMounted:function(e){return!!(e=e._reactInternals)&&function(e){var n=e,t=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do{0!=(4098&(n=e).flags)&&(t=n.return),e=n.return}while(e)}return 3===n.tag?t:null}(e)===e},enqueueSetState:function(e,n,t){e=e._reactInternals;var r=pr(),l=q(r);l.payload=n,null!=t&&(l.callback=t),null!==(n=Y(e,l,r))&&(hr(n,0,r),G(n,e,r))},enqueueReplaceState:function(e,n,t){e=e._reactInternals;var r=pr(),l=q(r);l.tag=1,l.payload=n,null!=t&&(l.callback=t),null!==(n=Y(e,l,r))&&(hr(n,0,r),G(n,e,r))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var t=pr(),r=q(t);r.tag=2,null!=n&&(r.callback=n),null!==(n=Y(e,r,t))&&(hr(n,0,t),G(n,e,t))}};"function"==typeof reportError&&reportError;var ru=Error(r(461)),lu=!1,au={dehydrated:null,treeContext:null,retryLane:0},ou=f(null),uu=null,iu=null,su=null,cu="undefined"!=typeof AbortController?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(n,t){e.push(t)}};this.abort=function(){n.aborted=!0,e.forEach((function(e){return e()}))}},fu=ul.unstable_scheduleCallback,du=ul.unstable_NormalPriority,pu={$$typeof:yl,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0},mu=Pl.S;Pl.S=function(e,n){"object"==typeof n&&null!==n&&"function"==typeof n.then&&function(e,n){if(null===So){var t=So=[];ko=0,wo=O(),xo={status:"pending",value:void 0,then:function(e){t.push(e)}}}ko++,n.then(B,B)}(0,n),null!==mu&&mu(e,n)};var hu=f(null),gu=!1,yu=!1,bu=!1,vu="function"==typeof WeakSet?WeakSet:Set,Su=null,ku=!1,wu=null,xu=!1,zu=null,Cu=8192,Eu={getCacheForType:function(e){var n=gt(pu),t=n.data.get(e);return void 0===t&&(t=e(),n.data.set(e,t)),t}};if("function"==typeof Symbol&&Symbol.for){var Pu=Symbol.for;Pu("selector.component"),Pu("selector.has_pseudo_class"),Pu("selector.role"),Pu("selector.test_id"),Pu("selector.text")}var _u="function"==typeof WeakMap?WeakMap:Map,Ru=0,Tu=null,Nu=null,Lu=0,Uu=0,Du=null,Iu=!1,Fu=!1,Mu=0,Wu=0,Hu=0,ju=0,Au=0,Qu=0,Ou=0,Bu=null,Vu=null,$u=!1,qu=0,Yu=1/0,Gu=null,Ju=null,Ku=!1,Xu=null,Zu=0,ei=0,ni=null,ti=0,ri=null;return al.createContainer=function(e,n,r,l,a,o,u,i,s,c){return function(e,n,r,l,a,o,u,i,s,c,f,d){return e=new nl(e,n,r,u,i,s,c,null),n=1,!0===o&&(n|=24),o=t(3,null,null,n),e.current=o,o.stateNode=e,(n=vt()).refCount++,e.pooledCache=n,n.refCount++,o.memoizedState={element:l,isDehydrated:r,cache:n},V(o),e}(e,n,!1,null,0,l,o,u,i,s)},al.flushSyncWork=function(){return 0!=(6&Ru)||(W(0),!1)},al.updateContainer=function(e,n,t,r){var l=n.current,a=pr();return tl(l,a,e,n,t,r),a},al.updateContainerSync=function(e,n,t,r){return 0===n.tag&&Wr(),tl(n.current,2,e,n,t,r),2},al},s.exports.default=s.exports,Object.defineProperty(s.exports,"__esModule",{value:!0})),o.exports):a.exports=function(){return i||(i=1,n=c,"production"!==process.env.NODE_ENV&&(n.exports=function(n){function t(e,n,t,r){return new sa(e,n,t,r)}function r(){console.error("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://react.dev/link/rules-of-hooks")}function l(){console.error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")}function a(){}function o(e){var n=[];return e.forEach((function(e){n.push(e)})),n.sort().join(", ")}function i(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=Ya&&e[Ya]||e["@@iterator"])?e:null}function s(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===Ga?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case Ia:return"Fragment";case Da:return"Portal";case Ma:return"Profiler";case Fa:return"StrictMode";case Qa:return"Suspense";case Oa:return"SuspenseList"}if("object"==typeof e)switch("number"==typeof e.tag&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case ja:return(e.displayName||"Context")+".Provider";case Ha:return(e._context.displayName||"Context")+".Consumer";case Aa:var n=e.render;return(e=e.displayName)||(e=""!==(e=n.displayName||n.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case Ba:return null!==(n=e.displayName||null)?n:s(e.type)||"Memo";case Va:n=e._payload,e=e._init;try{return s(e(n))}catch(e){}}return null}function c(e){var n=e.type;switch(e.tag){case 24:return"Cache";case 9:return(n._context.displayName||"Context")+".Consumer";case 10:return(n.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=n.render).displayName||e.name||"",n.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 26:case 27:case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return s(n);case 8:return n===Fa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 14:case 15:if("function"==typeof n)return n.displayName||n.name||null;if("string"==typeof n)return n;break;case 29:if(null!=(n=e._debugInfo))for(var t=n.length-1;0<=t;t--)if("string"==typeof n[t].name)return n[t].name;if(null!==e.return)return c(e.return)}return null}function f(){}function d(e){if(void 0===Xa)try{throw Error()}catch(e){var n=e.stack.trim().match(/\n( *(at )?)/);Xa=n&&n[1]||"",Za=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xa+e+Za}function p(e,n){if(!e||eo)return"";var t,r=no.get(e);if(void 0!==r)return r;eo=!0,r=Error.prepareStackTrace,Error.prepareStackTrace=void 0,t=Ja.H,Ja.H=null,function(){if(0===Ka){ka=console.log,wa=console.info,xa=console.warn,za=console.error,Ca=console.group,Ea=console.groupCollapsed,Pa=console.groupEnd;var e={configurable:!0,enumerable:!0,value:f,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}Ka++}();try{var l={DetermineComponentFrameRoot:function(){try{if(n){var t=function(){throw Error()};if(Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}}else{try{throw Error()}catch(e){r=e}(t=e())&&"function"==typeof t.catch&&t.catch((function(){}))}}catch(e){if(e&&r&&"string"==typeof e.stack)return[e.stack,r.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=l.DetermineComponentFrameRoot(),u=o[0],i=o[1];if(u&&i){var s=u.split("\n"),c=i.split("\n");for(o=a=0;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;for(;o<c.length&&!c[o].includes("DetermineComponentFrameRoot");)o++;if(a===s.length||o===c.length)for(a=s.length-1,o=c.length-1;1<=a&&0<=o&&s[a]!==c[o];)o--;for(;1<=a&&0<=o;a--,o--)if(s[a]!==c[o]){if(1!==a||1!==o)do{if(a--,0>--o||s[a]!==c[o]){var p="\n"+s[a].replace(" at new "," at ");return e.displayName&&p.includes("<anonymous>")&&(p=p.replace("<anonymous>",e.displayName)),"function"==typeof e&&no.set(e,p),p}}while(1<=a&&0<=o);break}}}finally{eo=!1,Ja.H=t,function(){if(0==--Ka){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Na({},e,{value:ka}),info:Na({},e,{value:wa}),warn:Na({},e,{value:xa}),error:Na({},e,{value:za}),group:Na({},e,{value:Ca}),groupCollapsed:Na({},e,{value:Ea}),groupEnd:Na({},e,{value:Pa})})}0>Ka&&console.error("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=r}return s=(s=e?e.displayName||e.name:"")?d(s):"","function"==typeof e&&no.set(e,s),s}function m(e){switch(e.tag){case 26:case 27:case 5:return d(e.type);case 16:return d("Lazy");case 13:return d("Suspense");case 19:return d("SuspenseList");case 0:case 15:return p(e.type,!1);case 11:return p(e.type.render,!1);case 1:return p(e.type,!0);default:return""}}function h(e){try{var n="";do{n+=m(e);var t=e._debugInfo;if(t)for(var r=t.length-1;0<=r;r--){var l=t[r];if("string"==typeof l.name){var a=n,o=l.env;n=a+d(l.name+(o?" ["+o+"]":""))}}e=e.return}while(e);return n}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function g(){return null===to?"":h(to)}function y(e,n,t,r,l,a,o){var u=to;Ja.getCurrentStack=null===e?null:g,ro=!1,to=e;try{return n(t,r,l,a,o)}finally{to=u}throw Error("runWithFiberInDEV should never be called in production. This is a bug in React.")}function b(e){return{current:e}}function v(e,n){0>iu?console.error("Unexpected pop."):(n!==uu[iu]&&console.error("Unexpected Fiber popped."),e.current=ou[iu],ou[iu]=null,uu[iu]=null,iu--)}function S(e,n,t){iu++,ou[iu]=e.current,uu[iu]=t,e.current=n}function k(e){var n=42&e;if(0!==n)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return console.error("Should have found matching lanes. This is a bug in React."),e}}function w(e,n){var t=e.pendingLanes;if(0===t)return 0;var r=0,l=e.suspendedLanes;e=e.pingedLanes;var a=134217727&t;return 0!==a?0!=(t=a&~l)?r=k(t):0!=(e&=a)&&(r=k(e)):0!=(t&=~l)?r=k(t):0!==e&&(r=k(e)),0===r?0:0!==n&&n!==r&&0==(n&l)&&((l=r&-r)>=(e=n&-n)||32===l&&0!=(4194176&e))?n:r}function x(e,n){switch(e){case 1:case 2:case 4:case 8:return n+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return console.error("Should have found matching lanes. This is a bug in React."),-1}}function z(){var e=pu;return 0==(4194176&(pu<<=1))&&(pu=128),e}function C(){var e=mu;return 0==(62914560&(mu<<=1))&&(mu=4194304),e}function E(e){for(var n=[],t=0;31>t;t++)n.push(e);return n}function P(e,n){e.pendingLanes|=n,268435456!==n&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function _(e,n,t){e.pendingLanes|=n,e.suspendedLanes&=~n;var r=31-cu(n);e.entangledLanes|=n,e.entanglements[r]=1073741824|e.entanglements[r]|4194218&t}function R(e,n){var t=e.entangledLanes|=n;for(e=e.entanglements;t;){var r=31-cu(t),l=1<<r;l&n|e[r]&n&&(e[r]|=n),t&=~l}}function T(e,n,t){if(_u)for(e=e.pendingUpdatersLaneMap;0<t;){var r=31-cu(t),l=1<<r;e[r].add(n),t&=~l}}function N(e,n){if(_u)for(var t=e.pendingUpdatersLaneMap,r=e.memoizedUpdaters;0<n;){var l=31-cu(n);e=1<<l,0<(l=t[l]).size&&(l.forEach((function(e){var n=e.alternate;null!==n&&r.has(n)||r.add(e)})),l.clear()),n&=~e}}function L(e){return 2<(e&=-e)?8<e?0!=(134217727&e)?32:268435456:8:2}function U(e){"function"==typeof zu&&Cu(e)}function D(e,n){if("object"==typeof e&&null!==e){var t=Tu.get(e);return void 0!==t?t:(n={value:e,source:n,stack:h(n)},Tu.set(e,n),n)}return{value:e,source:n,stack:h(n)}}function I(e){for(;e===Uu;)Uu=Nu[--Lu],Nu[Lu]=null,Du=Nu[--Lu],Nu[Lu]=null;for(;e===Mu;)Mu=Iu[--Fu],Iu[Fu]=null,Hu=Iu[--Fu],Iu[Fu]=null,Wu=Iu[--Fu],Iu[Fu]=null}function F(){console.error("Expected to be hydrating. This is a bug in React. Please file an issue.")}function M(e){return null===e&&console.error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue."),e}function W(e,n){S(Qu,n,e),S(Au,e,e),S(ju,null,e),n=oo(n),v(ju,e),S(ju,n,e)}function H(e){v(ju,e),v(Au,e),v(Qu,e)}function j(){return M(ju.current)}function A(e){null!==e.memoizedState&&S(Ou,e,e);var n=M(ju.current),t=uo(n,e.type);n!==t&&(S(Au,e,e),S(ju,t,e))}function Q(e){Au.current===e&&(v(ju,e),v(Au,e)),Ou.current===e&&(v(Ou,e),Uo._currentValue2=Lo)}function O(){for(var e=Yu,n=Gu=Yu=0;n<e;){var t=qu[n];qu[n++]=null;var r=qu[n];qu[n++]=null;var l=qu[n];qu[n++]=null;var a=qu[n];if(qu[n++]=null,null!==r&&null!==l){var o=r.pending;null===o?l.next=l:(l.next=o.next,o.next=l),r.pending=l}0!==a&&q(t,l,a)}}function B(e,n,t,r){qu[Yu++]=e,qu[Yu++]=n,qu[Yu++]=t,qu[Yu++]=r,Gu|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function V(e,n,t,r){return B(e,n,t,r),Y(e)}function $(e,n){return B(e,null,null,n),Y(e)}function q(e,n,t){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t);for(var l=!1,a=e.return;null!==a;)a.childLanes|=t,null!==(r=a.alternate)&&(r.childLanes|=t),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(l=!0)),e=a,a=a.return;l&&null!==n&&3===e.tag&&(a=e.stateNode,l=31-cu(t),null===(e=(a=a.hiddenUpdates)[l])?a[l]=[n]:e.push(n),n.lane=536870912|t)}function Y(e){if(Yf>qf)throw Zf=Yf=0,ed=Gf=null,Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");Zf>Xf&&(Zf=0,ed=null,console.error("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.")),null===e.alternate&&0!=(4098&e.flags)&&ua(e);for(var n=e,t=n.return;null!==t;)null===n.alternate&&0!=(4098&n.flags)&&ua(e),t=(n=t).return;return 3===n.tag?n.stateNode:null}function G(){var e=Zu;return Zu=0,e}function J(e){var n=Zu;return Zu=e,n}function K(e){var n=Zu;return Zu+=e,n}function X(e){Xu=Ju(),0>e.actualStartTime&&(e.actualStartTime=Xu)}function Z(e){if(0<=Xu){var n=Ju()-Xu;e.actualDuration+=n,e.selfBaseDuration=n,Xu=-1}}function ee(e){if(0<=Xu){var n=Ju()-Xu;e.actualDuration+=n,Xu=-1}}function ne(){if(0<=Xu){var e=Ju()-Xu;Xu=-1,Zu+=e}}function te(){Xu=Ju()}function re(e){for(var n=e.child;n;)e.actualDuration+=n.actualDuration,n=n.sibling}function le(e){e!==ri&&null===e.next&&(null===ri?ti=ri=e:ri=ri.next=e),oi=!0,null!==Ja.actQueue?ai||(ai=!0,fe(oe)):li||(li=!0,fe(oe))}function ae(e,n){if(!ui&&oi){ui=!0;do{for(var t=!1,r=ti;null!==r;){if(0!==e){var l=r.pendingLanes;if(0===l)var a=0;else{var o=r.suspendedLanes,u=r.pingedLanes;a=(1<<31-cu(42|e)+1)-1,a=201326677&(a&=l&~(o&~u))?201326677&a|1:a?2|a:0}0!==a&&(t=!0,se(r,a))}else a=df,0!=(3&(a=w(r,r===cf?a:0)))&&(t=!0,se(r,a));r=r.next}}while(t);ui=!1}}function oe(){oi=ai=li=!1;var e=0;0!==ii&&(Co()&&(e=ii),ii=0);for(var n=vu(),t=null,r=ti;null!==r;){var l=r.next,a=ue(r,n);0===a?(r.next=null,null===t?ti=l:t.next=l,null===l&&(ri=t)):(t=r,(0!==e||0!=(3&a))&&(oi=!0)),r=l}ae(e)}function ue(e,n){for(var t=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var o=31-cu(a),u=1<<o,i=l[o];-1===i?0!=(u&t)&&0==(u&r)||(l[o]=x(u,n)):i<=n&&(e.expiredLanes|=u),a&=~u}if(t=df,t=w(e,e===(n=cf)?t:0),r=e.callbackNode,0===t||e===n&&wf===hf||null!==e.cancelPendingCommit)return null!==r&&ce(r),e.callbackNode=null,e.callbackPriority=0;if(0!=(3&t))return null!==r&&ce(r),e.callbackPriority=2,e.callbackNode=null,2;if((n=t&-t)===e.callbackPriority&&(null===Ja.actQueue||r===si))return n;switch(ce(r),L(t)){case 2:t=Su;break;case 8:t=ku;break;case 32:default:t=wu;break;case 268435456:t=xu}return r=ie.bind(null,e),null!==Ja.actQueue?(Ja.actQueue.push(r),t=si):t=hu(t,r),e.callbackPriority=n,e.callbackNode=t,n}function ie(e,n){ni=ei=!1;var t=e.callbackNode;if(Jl()&&e.callbackNode!==t)return null;var r=df;return 0===(r=w(e,e===cf?r:0))?null:(El(e,r,n),ue(e,vu()),e.callbackNode===t?ie.bind(null,e):null)}function se(e,n){if(Jl())return null;ei=ni,ni=!1,El(e,n,!0)}function ce(e){e!==si&&null!==e&&gu(e)}function fe(e){null!==Ja.actQueue&&Ja.actQueue.push((function(){return e(),null})),hu(Su,e)}function de(){return 0===ii&&(ii=z()),ii}function pe(){if(0==--fi&&null!==ci){null!==pi&&(pi.status="fulfilled");var e=ci;ci=null,di=0,pi=null;for(var n=0;n<e.length;n++)(0,e[n])()}}function me(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function he(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ge(e){return{lane:e,tag:mi,payload:null,callback:null,next:null}}function ye(e,n,t){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,Si===r&&!vi){var l=c(e);console.error("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback.\n\nPlease update the following component: %s",l),vi=!0}return(sf&Zc)!==Xc?(null===(l=r.pending)?n.next=n:(n.next=l.next,l.next=n),r.pending=n,n=Y(e),q(e,null,t),n):(B(e,r,n,t),Y(e))}function be(e,n,t){if(null!==(n=n.updateQueue)&&(n=n.shared,0!=(4194176&t))){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,R(e,t)}}function ve(e,n){var t=e.updateQueue,r=e.alternate;if(null!==r&&t===(r=r.updateQueue)){var l=null,a=null;if(null!==(t=t.firstBaseUpdate)){do{var o={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};null===a?l=a=o:a=a.next=o,t=t.next}while(null!==t);null===a?l=a=n:a=a.next=n}else l=a=n;return t={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=t)}null===(e=t.lastBaseUpdate)?t.firstBaseUpdate=n:e.next=n,t.lastBaseUpdate=n}function Se(){if(ki&&null!==pi)throw pi}function ke(e,n,t,r){ki=!1;var l=e.updateQueue;bi=!1,Si=l.shared;var a=l.firstBaseUpdate,o=l.lastBaseUpdate,u=l.shared.pending;if(null!==u){l.shared.pending=null;var i=u,s=i.next;i.next=null,null===o?a=s:o.next=s,o=i;var c=e.alternate;null!==c&&(u=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===u?c.firstBaseUpdate=s:u.next=s,c.lastBaseUpdate=i)}if(null!==a){var f=l.baseState;for(o=0,c=s=i=null,u=a;;){var d=-536870913&u.lane,p=d!==u.lane;if(p?(df&d)===d:(r&d)===d){0!==d&&d===di&&(ki=!0),null!==c&&(c=c.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});e:{d=e;var m=u,h=n,g=t;switch(m.tag){case hi:if("function"==typeof(m=m.payload)){Rc=!0;var y=m.call(g,f,h);if(8&d.mode){U(!0);try{m.call(g,f,h)}finally{U(!1)}}Rc=!1,f=y;break e}f=m;break e;case yi:d.flags=-65537&d.flags|128;case mi:if("function"==typeof(y=m.payload)){if(Rc=!0,m=y.call(g,f,h),8&d.mode){U(!0);try{y.call(g,f,h)}finally{U(!1)}}Rc=!1}else m=y;if(null==m)break e;f=Na({},f,m);break e;case gi:bi=!0}}null!==(d=u.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=l.callbacks)?l.callbacks=[d]:p.push(d))}else p={lane:d,tag:u.tag,payload:u.payload,callback:u.callback,next:null},null===c?(s=c=p,i=f):c=c.next=p,o|=d;if(null===(u=u.next)){if(null===(u=l.shared.pending))break;u=(p=u).next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}null===c&&(i=f),l.baseState=i,l.firstBaseUpdate=s,l.lastBaseUpdate=c,null===a&&(l.shared.lanes=0),_f|=o,e.lanes=o,e.memoizedState=f}Si=null}function we(e,n){if("function"!=typeof e)throw Error("Invalid argument passed as callback. Expected a function. Instead received: "+e);e.call(n)}function xe(e,n){var t=e.shared.hiddenCallbacks;if(null!==t)for(e.shared.hiddenCallbacks=null,e=0;e<t.length;e++)we(t[e],n)}function ze(e,n){var t=e.callbacks;if(null!==t)for(e.callbacks=null,e=0;e<t.length;e++)we(t[e],n)}function Ce(e,n){if(Ru(e,n))return!0;if("object"!=typeof e||null===e||"object"!=typeof n||null===n)return!1;var t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(r=0;r<t.length;r++){var l=t[r];if(!wi.call(n,l)||!Ru(e[l],n[l]))return!1}return!0}function Ee(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Pe(){}function _e(e,n,t){null!==Ja.actQueue&&(Ja.didUsePromise=!0);var r=e.thenables;switch(void 0===(t=r[t])?r.push(n):t!==n&&(e.didWarnAboutUncachedPromise||(e.didWarnAboutUncachedPromise=!0,console.error("A component was suspended by an uncached promise. Creating promises inside a Client Component or hook is not yet supported, except via a Suspense-compatible library or framework.")),n.then(Pe,Pe),n=t),n.status){case"fulfilled":return n.value;case"rejected":throw Te(e=n.reason),e;default:if("string"==typeof n.status)n.then(Pe,Pe);else{if(null!==(e=cf)&&100<e.shellSuspendCounter)throw Error("async/await is not yet supported in Client Components, only Server Components. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.");(e=n).status="pending",e.then((function(e){if("pending"===n.status){var t=n;t.status="fulfilled",t.value=e}}),(function(e){if("pending"===n.status){var t=n;t.status="rejected",t.reason=e}}))}switch(n.status){case"fulfilled":return n.value;case"rejected":throw Te(e=n.reason),e}throw Mi=n,Wi=!0,Di}}function Re(){if(null===Mi)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var e=Mi;return Mi=null,Wi=!1,e}function Te(e){if(e===Di)throw Error("Hooks are not supported inside an async component. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.")}function Ne(e){var n=as;return null!=e&&(as=null===n?e:n.concat(e)),n}function Le(e,n,t){for(var r=Object.keys(e.props),l=0;l<r.length;l++){var a=r[l];if("children"!==a&&"key"!==a){null===n&&((n=ma(e,t.mode,0))._debugInfo=as,n.return=t),y(n,(function(e){console.error("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",e)}),a);break}}}function Ue(e){var n=ls;return ls+=1,null===rs&&(rs={didWarnAboutUncachedPromise:!1,thenables:[]}),_e(rs,e,n)}function De(e,n,t,r){e=r.props.ref,t.ref=void 0!==e?e:null}function Ie(e,n){if(n.$$typeof===La)throw Error('A React Element from an older version of React was rendered. This is not supported. It can happen if:\n- Multiple copies of the "react" package is used.\n- A library pre-bundled an old copy of "react" or "react/jsx-runtime".\n- A compiler tries to "inline" JSX instead of using the runtime.');throw e=Object.prototype.toString.call(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}function Fe(e,n){var t=c(e)||"Component";is[t]||(is[t]=!0,n=n.displayName||n.name||"Component",3===e.tag?console.error("Functions are not valid as a React child. This may happen if you return %s instead of <%s /> from render. Or maybe you meant to call this function rather than return it.\n  root.render(%s)",n,n,n):console.error("Functions are not valid as a React child. This may happen if you return %s instead of <%s /> from render. Or maybe you meant to call this function rather than return it.\n  <%s>{%s}</%s>",n,n,t,n,t))}function Me(e,n){var t=c(e)||"Component";ss[t]||(ss[t]=!0,n=String(n),3===e.tag?console.error("Symbols are not valid as a React child.\n  root.render(%s)",n):console.error("Symbols are not valid as a React child.\n  <%s>%s</%s>",t,n,t))}function We(e){function n(n,t){if(e){var r=n.deletions;null===r?(n.deletions=[t],n.flags|=16):r.push(t)}}function r(t,r){if(!e)return null;for(;null!==r;)n(t,r),r=r.sibling;return null}function l(e){for(var n=new Map;null!==e;)null!==e.key?n.set(e.key,e):n.set(e.index,e),e=e.sibling;return n}function o(e,n){return(e=fa(e,n)).index=0,e.sibling=null,e}function u(n,t,r){return n.index=r,e?null!==(r=n.alternate)?(r=r.index)<t?(n.flags|=33554434,t):r:(n.flags|=33554434,t):(n.flags|=1048576,t)}function s(n){return e&&null===n.alternate&&(n.flags|=33554434),n}function c(e,n,t,r){return null===n||6!==n.tag?((n=ya(t,e.mode,r)).return=e,n._debugOwner=e,n._debugInfo=as,n):((n=o(n,t)).return=e,n._debugInfo=as,n)}function f(e,n,t,r){var l=t.type;return l===Ia?(Le(t,n=p(e,n,t.props.children,r,t.key),e),n):null!==n&&(n.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===Va&&ts(l)===n.type)?(De(e,0,r=o(n,t.props),t),r.return=e,r._debugOwner=t._owner,r._debugInfo=as,r):(De(e,0,r=ma(t,e.mode,r),t),r.return=e,r._debugInfo=as,r)}function d(e,n,t,r){return null===n||4!==n.tag||n.stateNode.containerInfo!==t.containerInfo||n.stateNode.implementation!==t.implementation?((n=ba(t,e.mode,r)).return=e,n._debugInfo=as,n):((n=o(n,t.children||[])).return=e,n._debugInfo=as,n)}function p(e,n,t,r,l){return null===n||7!==n.tag?((n=ha(t,e.mode,r,l)).return=e,n._debugOwner=e,n._debugInfo=as,n):((n=o(n,t)).return=e,n._debugInfo=as,n)}function m(e,n,t){if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return(n=ya(""+n,e.mode,t)).return=e,n._debugOwner=e,n._debugInfo=as,n;if("object"==typeof n&&null!==n){switch(n.$$typeof){case Ua:return De(e,0,t=ma(n,e.mode,t),n),t.return=e,e=Ne(n._debugInfo),t._debugInfo=as,as=e,t;case Da:return(n=ba(n,e.mode,t)).return=e,n._debugInfo=as,n;case Va:var r=Ne(n._debugInfo);return e=m(e,n=ts(n),t),as=r,e}if(lo(n)||i(n))return(t=ha(n,e.mode,t,null)).return=e,t._debugOwner=e,e=Ne(n._debugInfo),t._debugInfo=as,as=e,t;if("function"==typeof n.then)return r=Ne(n._debugInfo),e=m(e,Ue(n),t),as=r,e;if(n.$$typeof===ja)return m(e,cr(e,n),t);Ie(e,n)}return"function"==typeof n&&Fe(e,n),"symbol"==typeof n&&Me(e,n),null}function h(e,n,t,r){var l=null!==n?n.key:null;if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return null!==l?null:c(e,n,""+t,r);if("object"==typeof t&&null!==t){switch(t.$$typeof){case Ua:return t.key===l?(l=Ne(t._debugInfo),e=f(e,n,t,r),as=l,e):null;case Da:return t.key===l?d(e,n,t,r):null;case Va:return l=Ne(t._debugInfo),e=h(e,n,t=ts(t),r),as=l,e}if(lo(t)||i(t))return null!==l?null:(l=Ne(t._debugInfo),e=p(e,n,t,r,null),as=l,e);if("function"==typeof t.then)return l=Ne(t._debugInfo),e=h(e,n,Ue(t),r),as=l,e;if(t.$$typeof===ja)return h(e,n,cr(e,t),r);Ie(e,t)}return"function"==typeof t&&Fe(e,t),"symbol"==typeof t&&Me(e,t),null}function g(e,n,t,r,l){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return c(n,e=e.get(t)||null,""+r,l);if("object"==typeof r&&null!==r){switch(r.$$typeof){case Ua:return t=e.get(null===r.key?t:r.key)||null,e=Ne(r._debugInfo),n=f(n,t,r,l),as=e,n;case Da:return d(n,e=e.get(null===r.key?t:r.key)||null,r,l);case Va:var a=Ne(r._debugInfo);return n=g(e,n,t,r=ts(r),l),as=a,n}if(lo(r)||i(r))return t=e.get(t)||null,e=Ne(r._debugInfo),n=p(n,t,r,l,null),as=e,n;if("function"==typeof r.then)return a=Ne(r._debugInfo),n=g(e,n,t,Ue(r),l),as=a,n;if(r.$$typeof===ja)return g(e,n,t,cr(n,r),l);Ie(n,r)}return"function"==typeof r&&Fe(n,r),"symbol"==typeof r&&Me(n,r),null}function b(e,n,t,r){if("object"!=typeof t||null===t)return r;switch(t.$$typeof){case Ua:case Da:a(e,n,t);var l=t.key;if("string"!=typeof l)break;if(null===r){(r=new Set).add(l);break}if(!r.has(l)){r.add(l);break}y(n,(function(){console.error("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version.",l)}));break;case Va:b(e,n,t=ts(t),r)}return r}function v(t,a,c,f){if("object"==typeof c&&null!==c&&c.type===Ia&&null===c.key&&(Le(c,null,t),c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case Ua:var d=Ne(c._debugInfo);e:{for(var p=c.key,y=a;null!==y;){if(y.key===p){if((p=c.type)===Ia){if(7===y.tag){r(t,y.sibling),(a=o(y,c.props.children)).return=t,a._debugOwner=c._owner,a._debugInfo=as,Le(c,a,t),t=a;break e}}else if(y.elementType===p||"object"==typeof p&&null!==p&&p.$$typeof===Va&&ts(p)===y.type){r(t,y.sibling),De(t,0,a=o(y,c.props),c),a.return=t,a._debugOwner=c._owner,a._debugInfo=as,t=a;break e}r(t,y);break}n(t,y),y=y.sibling}c.type===Ia?((a=ha(c.props.children,t.mode,f,c.key)).return=t,a._debugOwner=t,a._debugInfo=as,Le(c,a,t),t=a):(De(t,0,f=ma(c,t.mode,f),c),f.return=t,f._debugInfo=as,t=f)}return t=s(t),as=d,t;case Da:e:{for(c=(d=c).key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===d.containerInfo&&a.stateNode.implementation===d.implementation){r(t,a.sibling),(a=o(a,d.children||[])).return=t,t=a;break e}r(t,a);break}n(t,a),a=a.sibling}(a=ba(d,t.mode,f)).return=t,t=a}return s(t);case Va:return d=Ne(c._debugInfo),t=v(t,a,c=ts(c),f),as=d,t}if(lo(c))return d=Ne(c._debugInfo),t=function(t,a,o,i){for(var s=null,c=null,f=null,d=a,p=a=0,y=null;null!==d&&p<o.length;p++){d.index>p?(y=d,d=null):y=d.sibling;var v=h(t,d,o[p],i);if(null===v){null===d&&(d=y);break}s=b(t,v,o[p],s),e&&d&&null===v.alternate&&n(t,d),a=u(v,a,p),null===f?c=v:f.sibling=v,f=v,d=y}if(p===o.length)return r(t,d),c;if(null===d){for(;p<o.length;p++)null!==(d=m(t,o[p],i))&&(s=b(t,d,o[p],s),a=u(d,a,p),null===f?c=d:f.sibling=d,f=d);return c}for(d=l(d);p<o.length;p++)null!==(y=g(d,t,p,o[p],i))&&(s=b(t,y,o[p],s),e&&null!==y.alternate&&d.delete(null===y.key?p:y.key),a=u(y,a,p),null===f?c=y:f.sibling=y,f=y);return e&&d.forEach((function(e){return n(t,e)})),c}(t,a,c,f),as=d,t;if(i(c)){if(d=Ne(c._debugInfo),"function"!=typeof(y=i(c)))throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");return(p=y.call(c))===c?0===t.tag&&"[object GeneratorFunction]"===Object.prototype.toString.call(t.type)&&"[object Generator]"===Object.prototype.toString.call(p)||(os||console.error("Using Iterators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. You can also use an Iterable that can iterate multiple times over the same items."),os=!0):c.entries!==y||Ui||(console.error("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Ui=!0),t=function(t,a,o,i){if(null==o)throw Error("An iterable object provided no iterator.");for(var s=null,c=null,f=a,d=a=0,p=null,y=null,v=o.next();null!==f&&!v.done;d++,v=o.next()){f.index>d?(p=f,f=null):p=f.sibling;var S=h(t,f,v.value,i);if(null===S){null===f&&(f=p);break}y=b(t,S,v.value,y),e&&f&&null===S.alternate&&n(t,f),a=u(S,a,d),null===c?s=S:c.sibling=S,c=S,f=p}if(v.done)return r(t,f),s;if(null===f){for(;!v.done;d++,v=o.next())null!==(f=m(t,v.value,i))&&(y=b(t,f,v.value,y),a=u(f,a,d),null===c?s=f:c.sibling=f,c=f);return s}for(f=l(f);!v.done;d++,v=o.next())null!==(p=g(f,t,d,v.value,i))&&(y=b(t,p,v.value,y),e&&null!==p.alternate&&f.delete(null===p.key?d:p.key),a=u(p,a,d),null===c?s=p:c.sibling=p,c=p);return e&&f.forEach((function(e){return n(t,e)})),s}(t,a,p,f),as=d,t}if("function"==typeof c.then)return d=Ne(c._debugInfo),t=v(t,a,Ue(c),f),as=d,t;if(c.$$typeof===ja)return v(t,a,cr(t,c),f);Ie(t,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(d=""+c,null!==a&&6===a.tag?(r(t,a.sibling),(a=o(a,d)).return=t,t=a):(r(t,a),(a=ya(d,t.mode,f)).return=t,a._debugOwner=t,a._debugInfo=as,t=a),s(t)):("function"==typeof c&&Fe(t,c),"symbol"==typeof c&&Me(t,c),r(t,a))}return function(e,n,r,l){var a=as;as=null;try{ls=0;var o=v(e,n,r,l);return rs=null,o}catch(n){if(n===Di)throw n;var u=t(29,n,null,e.mode);u.lanes=l,u.return=e;var i=u._debugInfo=as;if(u._debugOwner=e._debugOwner,null!=i)for(var s=i.length-1;0<=s;s--)if("string"==typeof i[s].stack){u._debugOwner=i[s];break}return u}finally{as=a}}}function He(e,n){var t=Ef;S(ms,t,e),S(ps,n,e),Ef=t|n.baseLanes}function je(e){S(ms,Ef,e),S(ps,ps.current,e)}function Ae(e){Ef=ms.current,v(ps,e),v(ms,e)}function Qe(e){var n=e.alternate;S(vs,vs.current&ys,e),S(hs,e,e),null===gs&&(null===n||null!==ps.current||null!==n.memoizedState)&&(gs=e)}function Oe(e){if(22===e.tag){if(S(vs,vs.current,e),S(hs,e,e),null===gs){var n=e.alternate;null!==n&&null!==n.memoizedState&&(gs=e)}}else Be(e)}function Be(e){S(vs,vs.current,e),S(hs,hs.current,e)}function Ve(e){v(hs,e),gs===e&&(gs=null),v(vs,e)}function $e(e){for(var n=e;null!==n;){if(13===n.tag){var t=n.memoizedState;if(null!==t&&(null===(t=t.dehydrated)||Jo(t)||Ko(t)))return n}else if(19===n.tag&&void 0!==n.memoizedProps.revealOrder){if(0!=(128&n.flags))return n}else if(null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function qe(){var e=As;null===Qs?Qs=[e]:Qs.push(e)}function Ye(){var e=As;if(null!==Qs&&(Os++,Qs[Os]!==e)){var n=c(Ts);if(!Cs.has(n)&&(Cs.add(n),null!==Qs)){for(var t="",r=0;r<=Os;r++){var l=Qs[r],a=r===Os?e:l;for(l=r+1+". "+l;30>l.length;)l+=" ";t+=l+=a+"\n"}console.error("React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://react.dev/link/rules-of-hooks\n\n   Previous render            Next render\n   ------------------------------------------------------\n%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n",n,t)}}}function Ge(e){null==e||lo(e)||console.error("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",As,typeof e)}function Je(){var e=c(Ts);_s.has(e)||(_s.add(e),console.error("ReactDOM.useFormState has been renamed to React.useActionState. Please update %s to use React.useActionState.",e))}function Ke(){throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.")}function Xe(e,n){if(Bs)return!1;if(null===n)return console.error("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",As),!1;e.length!==n.length&&console.error("The final argument passed to %s changed size between renders. The order and size of this array must remain constant.\n\nPrevious: %s\nIncoming: %s",As,"["+n.join(", ")+"]","["+e.join(", ")+"]");for(var t=0;t<n.length&&t<e.length;t++)if(!Ru(e[t],n[t]))return!1;return!0}function Ze(e,n,t,r,l,a){Rs=a,Ts=n,Qs=null!==e?e._debugHookTypes:null,Os=-1,Bs=null!==e&&e.type!==n.type,"[object AsyncFunction]"!==Object.prototype.toString.call(t)&&"[object AsyncGeneratorFunction]"!==Object.prototype.toString.call(t)||(a=c(Ts),Ps.has(a)||(Ps.add(a),console.error("async/await is not yet supported in Client Components, only Server Components. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server."))),n.memoizedState=null,n.updateQueue=null,n.lanes=0,Ja.H=null!==e&&null!==e.memoizedState?Gs:null!==Qs?Ys:qs,Is=a=(8&n.mode)!==$u;var o=ji(t,r,l);if(Is=!1,Ds&&(o=nn(n,t,r,l)),a){U(!0);try{o=nn(n,t,r,l)}finally{U(!1)}}return en(e,n),o}function en(e,n){n._debugHookTypes=Qs,null===n.dependencies?null!==Ws&&(n.dependencies={lanes:0,firstContext:null,_debugThenableState:Ws}):n.dependencies._debugThenableState=Ws,Ja.H=$s;var t=null!==Ns&&null!==Ns.next;if(Rs=0,Qs=As=Ls=Ns=Ts=null,Os=-1,null!==e&&(31457280&e.flags)!=(31457280&n.flags)&&console.error("Internal React error: Expected static flag was missing. Please notify the React team."),Us=!1,Ms=0,Ws=null,t)throw Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");null===e||pc||null!==(e=e.dependencies)&&ur(e)&&(pc=!0),Wi?(Wi=!1,e=!0):e=!1,e&&(n=c(n)||"Unknown",Es.has(n)||Ps.has(n)||(Es.add(n),console.error("`use` was called from inside a try/catch block. This is not allowed and can lead to unexpected behavior. To handle errors triggered by `use`, wrap your component in a error boundary.")))}function nn(e,n,t,r){Ts=e;var l=0;do{if(Ds&&(Ws=null),Ms=0,Ds=!1,l>=js)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(l+=1,Bs=!1,Ls=Ns=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}Os=-1,Ja.H=Js,a=ji(n,t,r)}while(Ds);return a}function tn(){var e=Ja.H,n=e.useState()[0];return n="function"==typeof n.then?sn(n):n,e=e.useState()[0],(null!==Ns?Ns.memoizedState:null)!==e&&(Ts.flags|=1024),n}function rn(){var e=0!==Fs;return Fs=0,e}function ln(e,n,t){n.updateQueue=e.updateQueue,n.flags=(16&n.mode)!==$u?-201328645&n.flags:-2053&n.flags,e.lanes&=~t}function an(e){if(Us){for(e=e.memoizedState;null!==e;){var n=e.queue;null!==n&&(n.pending=null),e=e.next}Us=!1}Rs=0,Qs=Ls=Ns=Ts=null,Os=-1,As=null,Ds=!1,Ms=Fs=0,Ws=null}function on(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Ls?Ts.memoizedState=Ls=e:Ls=Ls.next=e,Ls}function un(){if(null===Ns){var e=Ts.alternate;e=null!==e?e.memoizedState:null}else e=Ns.next;var n=null===Ls?Ts.memoizedState:Ls.next;if(null!==n)Ls=n,Ns=e;else{if(null===e){if(null===Ts.alternate)throw Error("Update hook called on initial render. This is likely a bug in React. Please file an issue.");throw Error("Rendered more hooks than during the previous render.")}e={memoizedState:(Ns=e).memoizedState,baseState:Ns.baseState,baseQueue:Ns.baseQueue,queue:Ns.queue,next:null},null===Ls?Ts.memoizedState=Ls=e:Ls=Ls.next=e}return Ls}function sn(e){var n=Ms;return Ms+=1,null===Ws&&(Ws={didWarnAboutUncachedPromise:!1,thenables:[]}),e=_e(Ws,e,n),n=Ts,null===(null===Ls?n.memoizedState:Ls.next)&&(n=n.alternate,Ja.H=null!==n&&null!==n.memoizedState?Gs:qs),e}function cn(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return sn(e);if(e.$$typeof===ja)return sr(e)}throw Error("An unsupported type was passed to use(): "+String(e))}function fn(e){var n=null,t=Ts.updateQueue;if(null!==t&&(n=t.memoCache),null==n){var r=Ts.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(n={data:r.data.map((function(e){return e.slice()})),index:0})}if(null==n&&(n={data:[],index:0}),null===t&&(t=Vs(),Ts.updateQueue=t),t.memoCache=n,void 0===(t=n.data[n.index])||Bs)for(t=n.data[n.index]=Array(e),r=0;r<e;r++)t[r]=qa;else t.length!==e&&console.error("Expected a constant size argument for each invocation of useMemoCache. The previous cache was allocated with size %s but size %s was requested.",t.length,e);return n.index++,t}function dn(e,n){return"function"==typeof n?n(e):n}function pn(e,n,t){var r=on();if(void 0!==t){var l=t(n);if(Is){U(!0);try{t(n)}finally{U(!1)}}}else l=n;return r.memoizedState=r.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},r.queue=e,e=e.dispatch=mt.bind(null,Ts,e),[r.memoizedState,e]}function mn(e){return hn(un(),Ns,e)}function hn(e,n,t){var r=e.queue;if(null===r)throw Error("Should have a queue. You are likely calling Hooks conditionally, which is not allowed. (https://react.dev/link/invalid-hook-call)");r.lastRenderedReducer=t;var l=e.baseQueue,a=r.pending;if(null!==a){if(null!==l){var o=l.next;l.next=a.next,a.next=o}n.baseQueue!==l&&console.error("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."),n.baseQueue=l=a,r.pending=null}if(a=e.baseState,null===l)e.memoizedState=a;else{var u=o=null,i=null,s=n=l.next,c=!1;do{var f=-536870913&s.lane;if(f!==s.lane?(df&f)===f:(Rs&f)===f){var d=s.revertLane;if(0===d)null!==i&&(i=i.next={lane:0,revertLane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),f===di&&(c=!0);else{if((Rs&d)===d){s=s.next,d===di&&(c=!0);continue}f={lane:0,revertLane:s.revertLane,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null},null===i?(u=i=f,o=a):i=i.next=f,Ts.lanes|=d,_f|=d}f=s.action,Is&&t(a,f),a=s.hasEagerState?s.eagerState:t(a,f)}else d={lane:f,revertLane:s.revertLane,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null},null===i?(u=i=d,o=a):i=i.next=d,Ts.lanes|=f,_f|=f;s=s.next}while(null!==s&&s!==n);if(null===i?o=a:i.next=u,!Ru(a,e.memoizedState)&&(pc=!0,c&&null!==(t=pi)))throw t;e.memoizedState=a,e.baseState=o,e.baseQueue=i,r.lastRenderedState=a}return null===l&&(r.lanes=0),[e.memoizedState,r.dispatch]}function gn(e){var n=un(),t=n.queue;if(null===t)throw Error("Should have a queue. You are likely calling Hooks conditionally, which is not allowed. (https://react.dev/link/invalid-hook-call)");t.lastRenderedReducer=e;var r=t.dispatch,l=t.pending,a=n.memoizedState;if(null!==l){t.pending=null;var o=l=l.next;do{a=e(a,o.action),o=o.next}while(o!==l);Ru(a,n.memoizedState)||(pc=!0),n.memoizedState=a,null===n.baseQueue&&(n.baseState=a),t.lastRenderedState=a}return[a,r]}function yn(e,n,t){var r,l=Ts,a=on();if(r=n(),cs||(t=n(),Ru(r,t)||(console.error("The result of getSnapshot should be cached to avoid an infinite loop"),cs=!0)),null===cf)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");return 0!=(60&df)||vn(l,n,r),a.memoizedState=r,t={value:r,getSnapshot:n},a.queue=t,$n(kn.bind(null,l,t,e),[e]),l.flags|=2048,Qn(ks|zs,Sn.bind(null,l,t,r,n),{destroy:void 0},null),r}function bn(e,n,t){var r=Ts,l=un();if(t=n(),!cs){var a=n();Ru(t,a)||(console.error("The result of getSnapshot should be cached to avoid an infinite loop"),cs=!0)}(a=!Ru((Ns||l).memoizedState,t))&&(l.memoizedState=t,pc=!0),l=l.queue;var o=kn.bind(null,r,l,e);if(Vn(2048,zs,o,[e]),l.getSnapshot!==n||a||null!==Ls&&Ls.memoizedState.tag&ks){if(r.flags|=2048,Qn(ks|zs,Sn.bind(null,r,l,t,n),{destroy:void 0},null),null===cf)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");0!=(60&Rs)||vn(r,n,t)}return t}function vn(e,n,t){e.flags|=16384,e={getSnapshot:n,value:t},null===(n=Ts.updateQueue)?(n=Vs(),Ts.updateQueue=n,n.stores=[e]):null===(t=n.stores)?n.stores=[e]:t.push(e)}function Sn(e,n,t,r){n.value=t,n.getSnapshot=r,wn(n)&&xn(e)}function kn(e,n,t){return t((function(){wn(n)&&xn(e)}))}function wn(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!Ru(e,t)}catch(e){return!0}}function xn(e){var n=$(e,2);null!==n&&Cl(n,e,2)}function zn(e){var n=on();if("function"==typeof e){var t=e;if(e=t(),Is){U(!0);try{t()}finally{U(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:dn,lastRenderedState:e},n}function Cn(e){var n=(e=zn(e)).queue,t=ht.bind(null,Ts,n);return n.dispatch=t,[e.memoizedState,t]}function En(e){var n=on();n.memoizedState=n.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=t,n=yt.bind(null,Ts,!0,t),t.dispatch=n,[e,n]}function Pn(e,n){return _n(un(),0,e,n)}function _n(e,n,t,r){return e.baseState=t,hn(e,Ns,"function"==typeof r?r:dn)}function Rn(e,n){var t=un();return null!==Ns?_n(t,0,e,n):(t.baseState=e,[e,t.queue.dispatch])}function Tn(e,n,t,r,l){if(bt(e))throw Error("Cannot update form state while rendering.");if(null!==(e=n.action)){var a={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==Ja.T?t(!0):a.isTransition=!1,r(a),null===(t=n.pending)?(a.next=n.pending=a,Nn(n,a)):(a.next=t.next,n.pending=t.next=a)}}function Nn(e,n){var t=n.action,r=n.payload,l=e.state;if(n.isTransition){var a=Ja.T,o={};Ja.T=o,Ja.T._updatedFibers=new Set;try{var u=t(l,r),i=Ja.S;null!==i&&i(o,u),Ln(e,n,u)}catch(t){Dn(e,n,t)}finally{Ja.T=a,null===a&&o._updatedFibers&&(e=o._updatedFibers.size,o._updatedFibers.clear(),10<e&&console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."))}}else try{Ln(e,n,o=t(l,r))}catch(t){Dn(e,n,t)}}function Ln(e,n,t){null!==t&&"object"==typeof t&&"function"==typeof t.then?(t.then((function(t){Un(e,n,t)}),(function(t){return Dn(e,n,t)})),n.isTransition||console.error("An async function was passed to useActionState, but it was dispatched outside of an action context. This is likely not what you intended. Either pass the dispatch function to an `action` prop, or dispatch manually inside `startTransition`")):Un(e,n,t)}function Un(e,n,t){n.status="fulfilled",n.value=t,In(n),e.state=t,null!==(n=e.pending)&&((t=n.next)===n?e.pending=null:(t=t.next,n.next=t,Nn(e,t)))}function Dn(e,n,t){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{n.status="rejected",n.reason=t,In(n),n=n.next}while(n!==r)}e.action=null}function In(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function Fn(e,n){return n}function Mn(e,n){var t,r,l;(t=on()).memoizedState=t.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Fn,lastRenderedState:n},t.queue=r,t=ht.bind(null,Ts,r),r.dispatch=t,r=zn(!1);var a=yt.bind(null,Ts,!1,r.queue);return l={state:n,dispatch:null,action:e,pending:null},(r=on()).queue=l,t=Tn.bind(null,Ts,l,a,t),l.dispatch=t,r.memoizedState=e,[n,t,!1]}function Wn(e){return Hn(un(),Ns,e)}function Hn(e,n,t){n=hn(e,n,Fn)[0],e=mn(dn)[0],n="object"==typeof n&&null!==n&&"function"==typeof n.then?sn(n):n;var r=un(),l=r.queue,a=l.dispatch;return t!==r.memoizedState&&(Ts.flags|=2048,Qn(ks|zs,jn.bind(null,l,t),{destroy:void 0},null)),[n,a,e]}function jn(e,n){e.action=n}function An(e){var n=un(),t=Ns;if(null!==t)return Hn(n,t,e);un(),n=n.memoizedState;var r=(t=un()).queue.dispatch;return t.memoizedState=e,[n,r,!1]}function Qn(e,n,t,r){return e={tag:e,create:n,inst:t,deps:r,next:null},null===(n=Ts.updateQueue)&&(n=Vs(),Ts.updateQueue=n),null===(t=n.lastEffect)?n.lastEffect=e.next=e:(r=t.next,t.next=e,e.next=r,n.lastEffect=e),e}function On(e){return e={current:e},on().memoizedState=e}function Bn(e,n,t,r){var l=on();Ts.flags|=e,l.memoizedState=Qn(ks|n,t,{destroy:void 0},void 0===r?null:r)}function Vn(e,n,t,r){var l=un();r=void 0===r?null:r;var a=l.memoizedState.inst;null!==Ns&&null!==r&&Xe(r,Ns.memoizedState.deps)?l.memoizedState=Qn(n,t,a,r):(Ts.flags|=e,l.memoizedState=Qn(ks|n,t,a,r))}function $n(e,n){(16&Ts.mode)!==$u&&(64&Ts.mode)===$u?Bn(142608384,zs,e,n):Bn(8390656,zs,e,n)}function qn(e,n){var t=4194308;return(16&Ts.mode)!==$u&&(t|=67108864),Bn(t,xs,e,n)}function Yn(e,n){if("function"==typeof n){e=e();var t=n(e);return function(){"function"==typeof t?t():n(null)}}if(null!=n)return n.hasOwnProperty("current")||console.error("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(n).join(", ")+"}"),e=e(),n.current=e,function(){n.current=null}}function Gn(e,n,t){"function"!=typeof n&&console.error("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",null!==n?typeof n:"null"),t=null!=t?t.concat([e]):null;var r=4194308;(16&Ts.mode)!==$u&&(r|=67108864),Bn(r,xs,Yn.bind(null,n,e),t)}function Jn(e,n,t){"function"!=typeof n&&console.error("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",null!==n?typeof n:"null"),t=null!=t?t.concat([e]):null,Vn(4,xs,Yn.bind(null,n,e),t)}function Kn(e,n){return on().memoizedState=[e,void 0===n?null:n],e}function Xn(e,n){var t=un();n=void 0===n?null:n;var r=t.memoizedState;return null!==n&&Xe(n,r[1])?r[0]:(t.memoizedState=[e,n],e)}function Zn(e,n){var t=on();n=void 0===n?null:n;var r=e();if(Is){U(!0);try{e()}finally{U(!1)}}return t.memoizedState=[r,n],r}function et(e,n){var t=un();n=void 0===n?null:n;var r=t.memoizedState;if(null!==n&&Xe(n,r[1]))return r[0];if(r=e(),Is){U(!0);try{e()}finally{U(!1)}}return t.memoizedState=[r,n],r}function nt(e,n){return lt(on(),e,n)}function tt(e,n){return at(un(),Ns.memoizedState,e,n)}function rt(e,n){var t=un();return null===Ns?lt(t,e,n):at(t,Ns.memoizedState,e,n)}function lt(e,n,t){return void 0===t||0!=(1073741824&Rs)?e.memoizedState=n:(e.memoizedState=t,e=zl(),Ts.lanes|=e,_f|=e,t)}function at(e,n,t,r){return Ru(t,n)?t:null!==ps.current?(e=lt(e,t,r),Ru(e,n)||(pc=!0),e):0==(42&Rs)?(pc=!0,e.memoizedState=t):(e=zl(),Ts.lanes|=e,_f|=e,n)}function ot(e,n,t,r,l){var a=xo();wo(0!==a&&8>a?a:8);var o,u,i,s=Ja.T,c={};Ja.T=c,yt(e,!1,n,t),c._updatedFibers=new Set;try{var f=l(),d=Ja.S;null!==d&&d(c,f),null!==f&&"object"==typeof f&&"function"==typeof f.then?gt(e,n,(o=r,u=[],i={status:"pending",value:null,reason:null,then:function(e){u.push(e)}},f.then((function(){i.status="fulfilled",i.value=o;for(var e=0;e<u.length;e++)(0,u[e])(o)}),(function(e){for(i.status="rejected",i.reason=e,e=0;e<u.length;e++)(0,u[e])(void 0)})),i),xl(e)):gt(e,n,r,xl(e))}catch(t){gt(e,n,{then:function(){},status:"rejected",reason:t},xl(e))}finally{wo(a),Ja.T=s,null===s&&c._updatedFibers&&(e=c._updatedFibers.size,c._updatedFibers.clear(),10<e&&console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."))}}function ut(){var e=zn(!1);return e=ot.bind(null,Ts,e.queue,!0,!1),on().memoizedState=e,[!1,e]}function it(){var e=mn(dn)[0],n=un().memoizedState;return["boolean"==typeof e?e:sn(e),n]}function st(){var e=gn(dn)[0],n=un().memoizedState;return["boolean"==typeof e?e:sn(e),n]}function ct(){return sr(Uo)}function ft(){var e=on(),n=cf.identifierPrefix;return n=":"+n+"r"+(Hs++).toString(32)+":",e.memoizedState=n}function dt(){return on().memoizedState=pt.bind(null,Ts)}function pt(e,n){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var r=xl(t),l=ye(t,e=ge(r),r);return null!==l&&(Cl(l,t,r),be(l,t,r)),t=dr(),null!=n&&null!==l&&console.error("The seed argument is not enabled outside experimental channels."),void(e.payload={cache:t})}t=t.return}}function mt(e,n,t,r){"function"==typeof r&&console.error("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect()."),t={lane:r=xl(e),revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},bt(e)?vt(n,t):null!==(t=V(e,n,t,r))&&(Cl(t,e,r),St(t,n,r))}function ht(e,n,t,r){"function"==typeof r&&console.error("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect()."),gt(e,n,t,r=xl(e))}function gt(e,n,t,r){var l={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(bt(e))vt(n,l);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=n.lastRenderedReducer)){var o=Ja.H;Ja.H=Xs;try{var u=n.lastRenderedState,i=a(u,t);if(l.hasEagerState=!0,l.eagerState=i,Ru(i,u))return B(e,n,l,0),null===cf&&O(),!1}catch(e){}finally{Ja.H=o}}if(null!==(t=V(e,n,l,r)))return Cl(t,e,r),St(t,n,r),!0}return!1}function yt(e,n,t,r){if(null===Ja.T&&0===di&&console.error("An optimistic state update occurred outside a transition or action. To fix, move the update to an action, or wrap with startTransition."),r={lane:2,revertLane:de(),action:r,hasEagerState:!1,eagerState:null,next:null},bt(e)){if(n)throw Error("Cannot update optimistic state while rendering.");console.error("Cannot call startTransition while rendering.")}else null!==(n=V(e,t,r,2))&&Cl(n,e,2)}function bt(e){var n=e.alternate;return e===Ts||null!==n&&n===Ts}function vt(e,n){Ds=Us=!0;var t=e.pending;null===t?n.next=n:(n.next=t.next,t.next=n),e.pending=n}function St(e,n,t){if(0!=(4194176&t)){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,R(e,t)}}function kt(e){if(null!==e&&"function"!=typeof e){var n=String(e);cc.has(n)||(cc.add(n),console.error("Expected the last optional `callback` argument to be a function. Instead received: %s.",e))}}function wt(e,n,t,r){var l=e.memoizedState,a=t(r,l);if(8&e.mode){U(!0);try{a=t(r,l)}finally{U(!1)}}void 0===a&&(n=s(n)||"Component",oc.has(n)||(oc.add(n),console.error("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",n))),l=null==a?l:Na({},l,a),e.memoizedState=l,0===e.lanes&&(e.updateQueue.baseState=l)}function xt(e,n,t,r,l,a,o){var u=e.stateNode;if("function"==typeof u.shouldComponentUpdate){if(t=u.shouldComponentUpdate(r,a,o),8&e.mode){U(!0);try{t=u.shouldComponentUpdate(r,a,o)}finally{U(!1)}}return void 0===t&&console.error("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",s(n)||"Component"),t}return!(n.prototype&&n.prototype.isPureReactComponent&&Ce(t,r)&&Ce(l,a))}function zt(e,n,t,r){var l=n.state;"function"==typeof n.componentWillReceiveProps&&n.componentWillReceiveProps(t,r),"function"==typeof n.UNSAFE_componentWillReceiveProps&&n.UNSAFE_componentWillReceiveProps(t,r),n.state!==l&&(e=c(e)||"Component",nc.has(e)||(nc.add(e),console.error("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",e)),fc.enqueueReplaceState(n,n.state,null))}function Ct(e,n){var t=n;if("ref"in n)for(var r in t={},n)"ref"!==r&&(t[r]=n[r]);if(e=e.defaultProps)for(var l in t===n&&(t=Na({},t)),e)void 0===t[l]&&(t[l]=e[l]);return t}function Et(e,n){try{n.source&&c(n.source);var t=n.value;null!==Ja.actQueue?Ja.thrownErrors.push(t):(0,e.onUncaughtError)(t,{componentStack:n.stack})}catch(e){setTimeout((function(){throw e}))}}function Pt(e,n,t){try{t.source&&c(t.source),c(n),(0,e.onCaughtError)(t.value,{componentStack:t.stack,errorBoundary:1===n.tag?n.stateNode:null})}catch(e){setTimeout((function(){throw e}))}}function _t(e,n,t){return(t=ge(t)).tag=yi,t.payload={element:null},t.callback=function(){y(n.source,Et,e,n)},t}function Rt(e){return(e=ge(e)).tag=yi,e}function Tt(e,n,t,r){var l=t.type.getDerivedStateFromError;if("function"==typeof l){var a=r.value;e.payload=function(){return l(a)},e.callback=function(){y(r.source,Pt,n,t,r)}}var o=t.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(e.callback=function(){y(r.source,Pt,n,t,r),"function"!=typeof l&&(null===Af?Af=new Set([this]):Af.add(this)),Yi(this,r),"function"==typeof l||0==(2&t.lanes)&&console.error("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",c(t)||"Unknown")})}function Nt(e,n,t,r){n.child=null===e?ds(n,null,t,r):fs(n,e.child,t,r)}function Lt(e,n,t,r,l){t=t.render;var a=n.ref;if("ref"in r){var o={};for(var u in r)"ref"!==u&&(o[u]=r[u])}else o=r;return ir(n),r=Ze(e,n,t,o,a,l),u=rn(),null===e||pc?(n.flags|=1,Nt(e,n,r,l),n.child):(ln(e,n,l),Xt(e,n,l))}function Ut(e,n,t,r,l){if(null===e){var a=t.type;return"function"!=typeof a||ca(a)||void 0!==a.defaultProps||null!==t.compare?((e=pa(t.type,null,r,n,n.mode,l)).ref=n.ref,e.return=n,n.child=e):(t=a,n.tag=15,n.type=t,At(n,a),Dt(e,n,t,r,l))}if(a=e.child,!Zt(e,l)){var o=a.memoizedProps;if((t=null!==(t=t.compare)?t:Ce)(o,r)&&e.ref===n.ref)return Xt(e,n,l)}return n.flags|=1,(e=fa(a,r)).ref=n.ref,e.return=n,n.child=e}function Dt(e,n,t,r,l){if(null!==e){var a=e.memoizedProps;if(Ce(a,r)&&e.ref===n.ref&&n.type===e.type){if(pc=!1,n.pendingProps=r=a,!Zt(e,l))return n.lanes=e.lanes,Xt(e,n,l);0!=(131072&e.flags)&&(pc=!0)}}return Wt(e,n,t,r,l)}function It(e,n,t){var r=n.pendingProps,l=r.children,a=0!=(2&n.stateNode._pendingVisibility),o=null!==e?e.memoizedState:null;if(Mt(e,n),"hidden"===r.mode||a){if(0!=(128&n.flags)){if(r=null!==o?o.baseLanes|t:t,null!==e){for(l=n.child=e.child,a=0;null!==l;)a=a|l.lanes|l.childLanes,l=l.sibling;n.childLanes=a&~r}else n.childLanes=0,n.child=null;return Ft(e,n,r,t)}if(0==(536870912&t))return n.lanes=n.childLanes=536870912,Ft(e,n,null!==o?o.baseLanes|t:t,t);n.memoizedState={baseLanes:0,cachePool:null},null!==e&&gr(n,null!==o?o.cachePool:null),null!==o?He(n,o):je(n),Oe(n)}else null!==o?(gr(n,o.cachePool),He(n,o),Be(n),n.memoizedState=null):(null!==e&&gr(n,null),je(n),Be(n));return Nt(e,n,l,t),n.child}function Ft(e,n,t,r){var l=hr();return l=null===l?null:{parent:Uc._currentValue2,pool:l},n.memoizedState={baseLanes:t,cachePool:l},null!==e&&gr(n,null),je(n),Oe(n),null!==e&&or(e,n,r,!0),null}function Mt(e,n){var t=n.ref;if(null===t)null!==e&&null!==e.ref&&(n.flags|=2097664);else{if("function"!=typeof t&&"object"!=typeof t)throw Error("Expected ref to be a function, an object returned by React.createRef(), or undefined/null.");null!==e&&e.ref===t||(n.flags|=2097664)}}function Wt(e,n,t,r,l){if(t.prototype&&"function"==typeof t.prototype.render){var a=s(t)||"Unknown";mc[a]||(console.error("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",a,a),mc[a]=!0)}return 8&n.mode&&xi.recordLegacyContextWarning(n,null),null===e&&(At(n,n.type),t.contextTypes&&(a=s(t)||"Unknown",gc[a]||(gc[a]=!0,console.error("%s uses the legacy contextTypes API which was removed in React 19. Use React.createContext() with React.useContext() instead. (https://react.dev/link/legacy-context)",a)))),ir(n),t=Ze(e,n,t,r,void 0,l),r=rn(),null===e||pc?(n.flags|=1,Nt(e,n,t,l),n.child):(ln(e,n,l),Xt(e,n,l))}function Ht(e,n,t,r,l,a){return ir(n),Os=-1,Bs=null!==e&&e.type!==n.type,n.updateQueue=null,t=nn(n,r,t,l),en(e,n),r=rn(),null===e||pc?(n.flags|=1,Nt(e,n,t,a),n.child):(ln(e,n,a),Xt(e,n,a))}function jt(e,n,t,r,l){var a,o,u;if(ir(n),null===n.stateNode){if(o=su,a=t.contextType,"contextType"in t&&null!==a&&(void 0===a||a.$$typeof!==ja)&&!sc.has(t)&&(sc.add(t),u=void 0===a?" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":"object"!=typeof a?" However, it is set to a "+typeof a+".":a.$$typeof===Ha?" Did you accidentally pass the Context.Consumer instead?":" However, it is set to an object with keys {"+Object.keys(a).join(", ")+"}.",console.error("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",s(t)||"Component",u)),"object"==typeof a&&null!==a&&(o=sr(a)),a=new t(r,o),8&n.mode){U(!0);try{a=new t(r,o)}finally{U(!1)}}if(o=n.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=fc,n.stateNode=a,a._reactInternals=n,a._reactInternalInstance=ec,"function"==typeof t.getDerivedStateFromProps&&null===o&&(o=s(t)||"Component",tc.has(o)||(tc.add(o),console.error("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",o,null===a.state?"null":"undefined",o))),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate){var i=u=o=null;if("function"==typeof a.componentWillMount&&!0!==a.componentWillMount.__suppressDeprecationWarning?o="componentWillMount":"function"==typeof a.UNSAFE_componentWillMount&&(o="UNSAFE_componentWillMount"),"function"==typeof a.componentWillReceiveProps&&!0!==a.componentWillReceiveProps.__suppressDeprecationWarning?u="componentWillReceiveProps":"function"==typeof a.UNSAFE_componentWillReceiveProps&&(u="UNSAFE_componentWillReceiveProps"),"function"==typeof a.componentWillUpdate&&!0!==a.componentWillUpdate.__suppressDeprecationWarning?i="componentWillUpdate":"function"==typeof a.UNSAFE_componentWillUpdate&&(i="UNSAFE_componentWillUpdate"),null!==o||null!==u||null!==i){a=s(t)||"Component";var f="function"==typeof t.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";lc.has(a)||(lc.add(a),console.error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n%s uses %s but also contains the following legacy lifecycles:%s%s%s\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://react.dev/link/unsafe-component-lifecycles",a,f,null!==o?"\n  "+o:"",null!==u?"\n  "+u:"",null!==i?"\n  "+i:""))}}a=n.stateNode,o=s(t)||"Component",a.render||(t.prototype&&"function"==typeof t.prototype.render?console.error("No `render` method found on the %s instance: did you accidentally return an object from the constructor?",o):console.error("No `render` method found on the %s instance: you may have forgotten to define `render`.",o)),!a.getInitialState||a.getInitialState.isReactClassApproved||a.state||console.error("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",o),a.getDefaultProps&&!a.getDefaultProps.isReactClassApproved&&console.error("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",o),a.contextType&&console.error("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",o),t.childContextTypes&&!ic.has(t)&&(ic.add(t),console.error("%s uses the legacy childContextTypes API which was removed in React 19. Use React.createContext() instead. (https://react.dev/link/legacy-context)",o)),t.contextTypes&&!uc.has(t)&&(uc.add(t),console.error("%s uses the legacy contextTypes API which was removed in React 19. Use React.createContext() with static contextType instead. (https://react.dev/link/legacy-context)",o)),"function"==typeof a.componentShouldUpdate&&console.error("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",o),t.prototype&&t.prototype.isPureReactComponent&&void 0!==a.shouldComponentUpdate&&console.error("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",s(t)||"A pure component"),"function"==typeof a.componentDidUnmount&&console.error("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",o),"function"==typeof a.componentDidReceiveProps&&console.error("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",o),"function"==typeof a.componentWillRecieveProps&&console.error("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",o),"function"==typeof a.UNSAFE_componentWillRecieveProps&&console.error("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",o),u=a.props!==r,void 0!==a.props&&u&&console.error("When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",o),a.defaultProps&&console.error("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",o,o),"function"!=typeof a.getSnapshotBeforeUpdate||"function"==typeof a.componentDidUpdate||rc.has(t)||(rc.add(t),console.error("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",s(t))),"function"==typeof a.getDerivedStateFromProps&&console.error("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",o),"function"==typeof a.getDerivedStateFromError&&console.error("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",o),"function"==typeof t.getSnapshotBeforeUpdate&&console.error("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",o),(u=a.state)&&("object"!=typeof u||lo(u))&&console.error("%s.state: must be set to an object or null",o),"function"==typeof a.getChildContext&&"object"!=typeof t.childContextTypes&&console.error("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",o),(a=n.stateNode).props=r,a.state=n.memoizedState,a.refs={},me(n),o=t.contextType,a.context="object"==typeof o&&null!==o?sr(o):su,a.state===r&&(o=s(t)||"Component",ac.has(o)||(ac.add(o),console.error("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",o))),8&n.mode&&xi.recordLegacyContextWarning(n,a),xi.recordUnsafeLifecycleWarnings(n,a),a.state=n.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(wt(n,t,o,r),a.state=n.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(o=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),o!==a.state&&(console.error("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",c(n)||"Component"),fc.enqueueReplaceState(a,a.state,null)),ke(n,r,a,l),Se(),a.state=n.memoizedState),"function"==typeof a.componentDidMount&&(n.flags|=4194308),(16&n.mode)!==$u&&(n.flags|=67108864),a=!0}else if(null===e){a=n.stateNode;var d=n.memoizedProps;u=Ct(t,d),a.props=u;var p=a.context;i=t.contextType,o=su,"object"==typeof i&&null!==i&&(o=sr(i)),i="function"==typeof(f=t.getDerivedStateFromProps)||"function"==typeof a.getSnapshotBeforeUpdate,d=n.pendingProps!==d,i||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(d||p!==o)&&zt(n,a,r,o),bi=!1;var m=n.memoizedState;a.state=m,ke(n,r,a,l),Se(),p=n.memoizedState,d||m!==p||bi?("function"==typeof f&&(wt(n,t,f,r),p=n.memoizedState),(u=bi||xt(n,t,u,r,m,p,o))?(i||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(n.flags|=4194308),(16&n.mode)!==$u&&(n.flags|=67108864)):("function"==typeof a.componentDidMount&&(n.flags|=4194308),(16&n.mode)!==$u&&(n.flags|=67108864),n.memoizedProps=r,n.memoizedState=p),a.props=r,a.state=p,a.context=o,a=u):("function"==typeof a.componentDidMount&&(n.flags|=4194308),(16&n.mode)!==$u&&(n.flags|=67108864),a=!1)}else{a=n.stateNode,he(e,n),i=Ct(t,o=n.memoizedProps),a.props=i,f=n.pendingProps,m=a.context,p=t.contextType,u=su,"object"==typeof p&&null!==p&&(u=sr(p)),(p="function"==typeof(d=t.getDerivedStateFromProps)||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(o!==f||m!==u)&&zt(n,a,r,u),bi=!1,m=n.memoizedState,a.state=m,ke(n,r,a,l),Se();var h=n.memoizedState;o!==f||m!==h||bi||null!==e&&null!==e.dependencies&&ur(e.dependencies)?("function"==typeof d&&(wt(n,t,d,r),h=n.memoizedState),(i=bi||xt(n,t,i,r,m,h,u)||null!==e&&null!==e.dependencies&&ur(e.dependencies))?(p||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,h,u),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,h,u)),"function"==typeof a.componentDidUpdate&&(n.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(n.flags|=1024)):("function"!=typeof a.componentDidUpdate||o===e.memoizedProps&&m===e.memoizedState||(n.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&m===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=h),a.props=r,a.state=h,a.context=u,a=i):("function"!=typeof a.componentDidUpdate||o===e.memoizedProps&&m===e.memoizedState||(n.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&m===e.memoizedState||(n.flags|=1024),a=!1)}if(u=a,Mt(e,n),o=0!=(128&n.flags),u||o){if(u=n.stateNode,Ja.getCurrentStack=null===n?null:g,ro=!1,to=n,o&&"function"!=typeof t.getDerivedStateFromError)t=null,Xu=-1;else if(t=Qi(u),8&n.mode){U(!0);try{Qi(u)}finally{U(!1)}}n.flags|=1,null!==e&&o?(n.child=fs(n,e.child,null,l),n.child=fs(n,null,t,l)):Nt(e,n,t,l),n.memoizedState=u.state,e=n.child}else e=Xt(e,n,l);return l=n.stateNode,a&&l.props!==r&&(bc||console.error("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",c(n)||"a component"),bc=!0),e}function At(e,n){n&&n.childContextTypes&&console.error("childContextTypes cannot be defined on a function component.\n  %s.childContextTypes = ...",n.displayName||n.name||"Component"),"function"==typeof n.getDerivedStateFromProps&&(e=s(n)||"Unknown",yc[e]||(console.error("%s: Function components do not support getDerivedStateFromProps.",e),yc[e]=!0)),"object"==typeof n.contextType&&null!==n.contextType&&(n=s(n)||"Unknown",hc[n]||(console.error("%s: Function components do not support contextType.",n),hc[n]=!0))}function Qt(e){return{baseLanes:e,cachePool:yr()}}function Ot(e,n,t){return e=null!==e?e.childLanes&~t:0,n&&(e|=Nf),e}function Bt(e,n,t){var r,l,a,o,u=n.pendingProps,i=!1,s=0!=(128&n.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!=(vs.current&bs)),r&&(i=!0,n.flags&=-129),r=0!=(32&n.flags),n.flags&=-33,null===e)return l=u.children,u=u.fallback,i?(Be(n),l=$t({mode:"hidden",children:l},i=n.mode),u=ha(u,i,t,null),l.return=n,u.return=n,l.sibling=u,n.child=l,(i=n.child).memoizedState=Qt(t),i.childLanes=Ot(e,r,t),n.memoizedState=kc,u):(Qe(n),Vt(n,l));if(null!==(a=e.memoizedState)&&null!==(l=a.dehydrated)){if(s)256&n.flags?(Qe(n),n.flags&=-257,n=qt(e,n,t)):null!==n.memoizedState?(Be(n),n.child=e.child,n.flags|=128,n=null):(Be(n),i=u.fallback,l=n.mode,u=$t({mode:"visible",children:u.children},l),(i=ha(i,l,t,null)).flags|=2,u.return=n,i.return=n,u.sibling=i,n.child=u,fs(n,e.child,null,t),(u=n.child).memoizedState=Qt(t),u.childLanes=Ot(e,r,t),n.memoizedState=kc,n=i);else if(Qe(n),Ko(l))r=(l=Xo(l)).digest,i=l.message,u=l.stack,l=l.componentStack,(i=i?Error(i):Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering.")).stack=u||"",i.digest=r,u={value:i,source:null,stack:r=void 0===l?null:l},"string"==typeof r&&Tu.set(i,u),o=u,null===Vu?Vu=[o]:Vu.push(o),n=qt(e,n,t);else if(pc||or(e,n,t,!1),r=0!=(t&e.childLanes),pc||r){if(null!==(r=cf)){if(0!=(42&(u=t&-t)))u=1;else switch(u){case 2:u=1;break;case 8:u=4;break;case 32:u=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:u=64;break;case 268435456:u=134217728;break;default:u=0}if(0!==(u=0!=(u&(r.suspendedLanes|t))?0:u)&&u!==a.retryLane)throw a.retryLane=u,$(e,u),Cl(r,e,u),dc}Jo(l)||Ml(),n=qt(e,n,t)}else Jo(l)?(n.flags|=128,n.child=e.child,n=ta.bind(null,e),Zo(l,n),n=null):(e=a.treeContext,(n=Vt(n,u.children)).flags|=4096);return n}return i?(Be(n),i=u.fallback,l=n.mode,s=(a=e.child).sibling,(u=fa(a,{mode:"hidden",children:u.children})).subtreeFlags=31457280&a.subtreeFlags,null!==s?i=fa(s,i):(i=ha(i,l,t,null)).flags|=2,i.return=n,u.return=n,u.sibling=i,n.child=u,u=i,i=n.child,null===(l=e.child.memoizedState)?l=Qt(t):(null!==(a=l.cachePool)?(s=Uc._currentValue2,a=a.parent!==s?{parent:s,pool:s}:a):a=yr(),l={baseLanes:l.baseLanes|t,cachePool:a}),i.memoizedState=l,i.childLanes=Ot(e,r,t),n.memoizedState=kc,u):(Qe(n),e=(t=e.child).sibling,(t=fa(t,{mode:"visible",children:u.children})).return=n,t.sibling=null,null!==e&&(null===(r=n.deletions)?(n.deletions=[e],n.flags|=16):r.push(e)),n.child=t,n.memoizedState=null,t)}function Vt(e,n){return(n=$t({mode:"visible",children:n},e.mode)).return=e,e.child=n}function $t(e,n){return ga(e,n,0,null)}function qt(e,n,t){return fs(n,e.child,null,t),(e=Vt(n,n.pendingProps.children)).flags|=2,n.memoizedState=null,e}function Yt(e,n,t){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n),lr(e.return,n,t)}function Gt(e,n){var t=lo(e);return e=!t&&"function"==typeof i(e),!t&&!e||(t=t?"array":"iterable",console.error("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",t,n,t),!1)}function Jt(e,n,t,r,l){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:t,tailMode:l}:(a.isBackwards=n,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=t,a.tailMode=l)}function Kt(e,n,t){var r=n.pendingProps,l=r.revealOrder,a=r.tail;if(r=r.children,void 0!==l&&"forwards"!==l&&"backwards"!==l&&"together"!==l&&!vc[l])if(vc[l]=!0,"string"==typeof l)switch(l.toLowerCase()){case"together":case"forwards":case"backwards":console.error('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',l,l.toLowerCase());break;case"forward":case"backward":console.error('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',l,l.toLowerCase());break;default:console.error('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',l)}else console.error('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',l);void 0===a||Sc[a]||("collapsed"!==a&&"hidden"!==a?(Sc[a]=!0,console.error('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',a)):"forwards"!==l&&"backwards"!==l&&(Sc[a]=!0,console.error('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',a)));e:if(("forwards"===l||"backwards"===l)&&null!=r&&!1!==r)if(lo(r)){for(var o=0;o<r.length;o++)if(!Gt(r[o],o))break e}else if(o=i(r),"function"==typeof o){if(o=o.call(r))for(var u=o.next(),s=0;!u.done;u=o.next()){if(!Gt(u.value,s))break e;s++}}else console.error('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',l);if(Nt(e,n,r,t),0!=((r=vs.current)&bs))r=r&ys|bs,n.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=n.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Yt(e,t,n);else if(19===e.tag)Yt(e,t,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=ys}switch(S(vs,r,n),l){case"forwards":for(t=n.child,l=null;null!==t;)null!==(e=t.alternate)&&null===$e(e)&&(l=t),t=t.sibling;null===(t=l)?(l=n.child,n.child=null):(l=t.sibling,t.sibling=null),Jt(n,!1,l,t,a);break;case"backwards":for(t=null,l=n.child,n.child=null;null!==l;){if(null!==(e=l.alternate)&&null===$e(e)){n.child=l;break}e=l.sibling,l.sibling=t,t=l,l=e}Jt(n,!0,t,null,a);break;case"together":Jt(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Xt(e,n,t){if(null!==e&&(n.dependencies=e.dependencies),Xu=-1,_f|=n.lanes,0==(t&n.childLanes)){if(null===e)return null;if(or(e,n,t,!1),0==(t&n.childLanes))return null}if(null!==e&&n.child!==e.child)throw Error("Resuming work not yet implemented.");if(null!==n.child){for(t=fa(e=n.child,e.pendingProps),n.child=t,t.return=n;null!==e.sibling;)e=e.sibling,(t=t.sibling=fa(e,e.pendingProps)).return=n;t.sibling=null}return n.child}function Zt(e,n){return 0!=(e.lanes&n)||!(null===(e=e.dependencies)||!ur(e))}function er(e,n,t){if(n._debugNeedsRemount&&null!==e){t=pa(n.type,n.key,n.pendingProps,n._debugOwner||null,n.mode,n.lanes);var r=n.return;if(null===r)throw Error("Cannot swap the root fiber.");if(e.alternate=null,n.alternate=null,t.index=n.index,t.sibling=n.sibling,t.return=n.return,t.ref=n.ref,t._debugInfo=n._debugInfo,n===r.child)r.child=t;else{var l=r.child;if(null===l)throw Error("Expected parent to have a child.");for(;l.sibling!==n;)if(null===(l=l.sibling))throw Error("Expected to find the previous sibling.");l.sibling=t}return null===(n=r.deletions)?(r.deletions=[e],r.flags|=16):n.push(e),t.flags|=2,t}if(null!==e)if(e.memoizedProps!==n.pendingProps||n.type!==e.type)pc=!0;else{if(!Zt(e,t)&&0==(128&n.flags))return pc=!1,function(e,n,t){switch(n.tag){case 3:W(n,n.stateNode.containerInfo),tr(n,Uc,e.memoizedState.cache);break;case 27:case 5:A(n);break;case 4:W(n,n.stateNode.containerInfo);break;case 10:tr(n,n.type,n.memoizedProps.value);break;case 12:0!=(t&n.childLanes)&&(n.flags|=4),n.flags|=2048;var r=n.stateNode;r.effectDuration=-0,r.passiveEffectDuration=-0;break;case 13:if(null!==(r=n.memoizedState))return null!==r.dehydrated?(Qe(n),n.flags|=128,null):0!=(t&n.child.childLanes)?Bt(e,n,t):(Qe(n),null!==(e=Xt(e,n,t))?e.sibling:null);Qe(n);break;case 19:var l=0!=(128&e.flags);if((r=0!=(t&n.childLanes))||(or(e,n,t,!1),r=0!=(t&n.childLanes)),l){if(r)return Kt(e,n,t);n.flags|=128}if(null!==(l=n.memoizedState)&&(l.rendering=null,l.tail=null,l.lastEffect=null),S(vs,vs.current,n),r)break;return null;case 22:case 23:return n.lanes=0,It(e,n,t);case 24:tr(n,Uc,e.memoizedState.cache)}return Xt(e,n,t)}(e,n,t);pc=0!=(131072&e.flags)}else pc=!1,(r=Bu)&&(F(),r=0!=(1048576&n.flags)),r&&(r=n.index,F(),function(e,n,t){F(),Iu[Fu++]=Wu,Iu[Fu++]=Hu,Iu[Fu++]=Mu,Mu=e;var r=Wu;e=Hu;var l=32-cu(r)-1;r&=~(1<<l),t+=1;var a=32-cu(n)+l;if(30<a){var o=l-l%5;a=(r&(1<<o)-1).toString(32),r>>=o,l-=o,Wu=1<<32-cu(n)+l|t<<l|r,Hu=a+e}else Wu=1<<a|t<<l|r,Hu=e}(n,Du,r));switch(n.lanes=0,n.tag){case 16:e:{if(r=n.pendingProps,e=ts(n.elementType),n.type=e,"function"!=typeof e){if(null!=e){if((l=e.$$typeof)===Aa){n.tag=11,n.type=e,n=Lt(null,n,e,r,t);break e}if(l===Ba){n.tag=14,n=Ut(null,n,e,r,t);break e}}throw n="",null!==e&&"object"==typeof e&&e.$$typeof===Va&&(n=" Did you wrap a component in React.lazy() more than once?"),e=s(e)||e,Error("Element type is invalid. Received a promise that resolves to: "+e+". Lazy element type must resolve to a class or function."+n)}ca(e)?(r=Ct(e,r),n.tag=1,n.type=e,n=jt(null,n,e,r,t)):(n.tag=0,At(n,e),n.type=e,n=Wt(null,n,e,r,t))}return n;case 0:return Wt(e,n,n.type,n.pendingProps,t);case 1:return jt(e,n,r=n.type,l=Ct(r,n.pendingProps),t);case 3:if(W(n,n.stateNode.containerInfo),null===e)throw Error("Should have a current fiber. This is a bug in React.");var a=n.pendingProps;r=(l=n.memoizedState).element,he(e,n),ke(n,a,null,t);var o=n.memoizedState;return a=o.cache,tr(n,Uc,a),a!==l.cache&&ar(n,[Uc],t,!0),Se(),(a=o.element)!==r?(Nt(e,n,a,t),n=n.child):n=Xt(e,n,t),n;case 26:case 27:case 5:return A(n),l=n.type,a=n.pendingProps,o=null!==e?e.memoizedProps:null,r=a.children,mo(l,a)?r=null:null!==o&&mo(l,o)&&(n.flags|=32),null!==n.memoizedState&&(l=Ze(e,n,tn,null,null,t),Uo._currentValue2=l),Mt(e,n),Nt(e,n,r,t),n.child;case 6:return null;case 13:return Bt(e,n,t);case 4:return W(n,n.stateNode.containerInfo),r=n.pendingProps,null===e?n.child=fs(n,null,r,t):Nt(e,n,r,t),n.child;case 11:return Lt(e,n,n.type,n.pendingProps,t);case 7:return Nt(e,n,n.pendingProps,t),n.child;case 8:return Nt(e,n,n.pendingProps.children,t),n.child;case 12:return n.flags|=4,n.flags|=2048,(r=n.stateNode).effectDuration=-0,r.passiveEffectDuration=-0,Nt(e,n,n.pendingProps.children,t),n.child;case 10:return r=n.type,a=(l=n.pendingProps).value,"value"in l||wc||(wc=!0,console.error("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?")),tr(n,r,a),Nt(e,n,l.children,t),n.child;case 9:return l=n.type._context,"function"!=typeof(r=n.pendingProps.children)&&console.error("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),ir(n),l=sr(l),r=ji(r,l,void 0),n.flags|=1,Nt(e,n,r,t),n.child;case 14:return Ut(e,n,n.type,n.pendingProps,t);case 15:return Dt(e,n,n.type,n.pendingProps,t);case 19:return Kt(e,n,t);case 22:return It(e,n,t);case 24:return ir(n),r=sr(Uc),null===e?(null===(l=hr())&&(l=cf,a=dr(),l.pooledCache=a,pr(a),null!==a&&(l.pooledCacheLanes|=t),l=a),n.memoizedState={parent:r,cache:l},me(n),tr(n,Uc,l)):(0!=(e.lanes&t)&&(he(e,n),ke(n,null,null,t),Se()),l=e.memoizedState,a=n.memoizedState,l.parent!==r?(l={parent:r,cache:r},n.memoizedState=l,0===n.lanes&&(n.memoizedState=n.updateQueue.baseState=l),tr(n,Uc,r)):(r=a.cache,tr(n,Uc,r),r!==l.cache&&ar(n,[Uc],t,!0))),Nt(e,n,n.pendingProps.children,t),n.child;case 29:throw n.pendingProps}throw Error("Unknown unit of work tag ("+n.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function nr(){_c=Pc=Ec=null,Rc=!1}function tr(e,n,t){S(xc,n._currentValue2,e),n._currentValue2=t,S(zc,n._currentRenderer2,e),void 0!==n._currentRenderer2&&null!==n._currentRenderer2&&n._currentRenderer2!==Cc&&console.error("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),n._currentRenderer2=Cc}function rr(e,n){var t=xc.current;e._currentValue2=t,t=zc.current,v(zc,n),e._currentRenderer2=t,v(xc,n)}function lr(e,n,t){for(;null!==e;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,null!==r&&(r.childLanes|=n)):null!==r&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===t)break;e=e.return}e!==t&&console.error("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.")}function ar(e,n,t,r){var l=e.child;for(null!==l&&(l.return=e);null!==l;){var a=l.dependencies;if(null!==a){var o=l.child;a=a.firstContext;e:for(;null!==a;){var u=a;a=l;for(var i=0;i<n.length;i++)if(u.context===n[i]){a.lanes|=t,null!==(u=a.alternate)&&(u.lanes|=t),lr(a.return,t,e),r||(o=null);break e}a=u.next}}else if(18===l.tag){if(null===(o=l.return))throw Error("We just came from a parent so we must have had a parent. This is a bug in React.");o.lanes|=t,null!==(a=o.alternate)&&(a.lanes|=t),lr(o,t,e),o=null}else o=l.child;if(null!==o)o.return=l;else for(o=l;null!==o;){if(o===e){o=null;break}if(null!==(l=o.sibling)){l.return=o.return,o=l;break}o=o.return}l=o}}function or(e,n,t,r){e=null;for(var l=n,a=!1;null!==l;){if(!a)if(0!=(524288&l.flags))a=!0;else if(0!=(262144&l.flags))break;if(10===l.tag){var o=l.alternate;if(null===o)throw Error("Should have a current fiber. This is a bug in React.");if(null!==(o=o.memoizedProps)){var u=l.type;Ru(l.pendingProps.value,o.value)||(null!==e?e.push(u):e=[u])}}else if(l===Ou.current){if(null===(o=l.alternate))throw Error("Should have a current fiber. This is a bug in React.");o.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(null!==e?e.push(Uo):e=[Uo])}l=l.return}null!==e&&ar(n,e,t,r),n.flags|=262144}function ur(e){for(e=e.firstContext;null!==e;){var n=e.context;if(!Ru(n._currentValue2,e.memoizedValue))return!0;e=e.next}return!1}function ir(e){Ec=e,_c=Pc=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function sr(e){return Rc&&console.error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo()."),fr(Ec,e)}function cr(e,n){return null===Ec&&ir(e),fr(e,n)}function fr(e,n){var t=n._currentValue2;if(_c!==n)if(n={context:n,memoizedValue:t,next:null},null===Pc){if(null===e)throw Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");Pc=n,e.dependencies={lanes:0,firstContext:n,_debugThenableState:null},e.flags|=524288}else Pc=Pc.next=n;return t}function dr(){return{controller:new Tc,data:new Map,refCount:0}}function pr(e){e.controller.signal.aborted&&console.warn("A cache instance was retained after it was already freed. This likely indicates a bug in React."),e.refCount++}function mr(e){e.refCount--,0>e.refCount&&console.warn("A cache instance was released after it was already freed. This likely indicates a bug in React."),0===e.refCount&&Nc(Lc,(function(){e.controller.abort()}))}function hr(){var e=Ic.current;return null!==e?e:cf.pooledCache}function gr(e,n){S(Ic,null===n?Ic.current:n.pool,e)}function yr(){var e=hr();return null===e?null:{parent:Uc._currentValue2,pool:e}}function br(e){e.flags|=4}function vr(e,n){null!==n&&(e.flags|=4),16384&e.flags&&(n=22!==e.tag?C():536870912,e.lanes|=n)}function Sr(e,n){switch(e.tailMode){case"hidden":n=e.tail;for(var t=null;null!==n;)null!==n.alternate&&(t=n),n=n.sibling;null===t?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?n||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function kr(e){var n=null!==e.alternate&&e.alternate.child===e.child,t=0,r=0;if(n)if((2&e.mode)!==$u){for(var l=e.selfBaseDuration,a=e.child;null!==a;)t|=a.lanes|a.childLanes,r|=31457280&a.subtreeFlags,r|=31457280&a.flags,l+=a.treeBaseDuration,a=a.sibling;e.treeBaseDuration=l}else for(l=e.child;null!==l;)t|=l.lanes|l.childLanes,r|=31457280&l.subtreeFlags,r|=31457280&l.flags,l.return=e,l=l.sibling;else if((2&e.mode)!==$u){l=e.actualDuration,a=e.selfBaseDuration;for(var o=e.child;null!==o;)t|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,l+=o.actualDuration,a+=o.treeBaseDuration,o=o.sibling;e.actualDuration=l,e.treeBaseDuration=a}else for(l=e.child;null!==l;)t|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=t,n}function wr(e,n,t){var r=n.pendingProps;switch(I(n),n.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return kr(n),null;case 3:return t=n.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),n.memoizedState.cache!==r&&(n.flags|=2048),rr(Uc,n),H(n),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),null!==e&&null!==e.child||null===e||e.memoizedState.isDehydrated&&0==(256&n.flags)||(n.flags|=1024,null!==Vu&&(Pl(Vu),Vu=null)),kr(n),null;case 26:var l;case 27:case 5:if(Q(n),t=n.type,null!==e&&null!=n.stateNode)!function(e,n,t,r){e.memoizedProps!==r&&br(n)}(e,n,0,r);else{if(!r){if(null===n.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return kr(n),null}e=j(),l=M(Qu.current),function(e,n,t,r){for(t=n.child;null!==t;){if(5===t.tag||6===t.tag)fo(e,t.stateNode);else if(4!==t.tag&&!au&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===n)break;for(;null===t.sibling;){if(null===t.return||t.return===n)return;t=t.return}t.sibling.return=t.return,t=t.sibling}}(l=co(t,r,l,e,n),n,!1),n.stateNode=l,po(l,t,r,e)&&br(n)}return kr(n),function(e,n,t){if(Po(n,t)){if(e.flags|=16777216,!_o(n,t)){if(!Dl())throw Mi=Fi,Ii;e.flags|=8192}}else e.flags&=-16777217}(n,n.type,n.pendingProps),null;case 6:if(e&&null!=n.stateNode)(t=e.memoizedProps)!==r&&br(n);else{if("string"!=typeof r&&null===n.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");var a;e=M(Qu.current),t=j(),n.stateNode=ho(r,e,t,n)}return kr(n),null;case 13:if(r=n.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(l=!1,null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");throw Error("Expected prepareToHydrateHostSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.")}0==(128&n.flags)&&(n.memoizedState=null),n.flags|=4,kr(n),(2&n.mode)!==$u&&null!==r&&null!==(l=n.child)&&(n.treeBaseDuration-=l.treeBaseDuration),l=!1}else null!==Vu&&(Pl(Vu),Vu=null),l=!0;if(!l)return 256&n.flags?(Ve(n),n):(Ve(n),null)}return Ve(n),0!=(128&n.flags)?(n.lanes=t,(2&n.mode)!==$u&&re(n),n):(t=null!==r,e=null!==e&&null!==e.memoizedState,t&&(l=null,null!==(r=n.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(l=r.alternate.memoizedState.cachePool.pool),a=null,null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==l&&(r.flags|=2048)),t!==e&&t&&(n.child.flags|=8192),vr(n,n.updateQueue),kr(n),(2&n.mode)!==$u&&t&&null!==(e=n.child)&&(n.treeBaseDuration-=e.treeBaseDuration),null);case 4:return H(n),null===e&&ko(n.stateNode.containerInfo),kr(n),null;case 10:return rr(n.type,n),kr(n),null;case 19:if(v(vs,n),null===(l=n.memoizedState))return kr(n),null;if(r=0!=(128&n.flags),null===(a=l.rendering))if(r)Sr(l,!1);else{if(Pf!==nf||null!==e&&0!=(128&e.flags))for(e=n.child;null!==e;){if(null!==(a=$e(e))){for(n.flags|=128,Sr(l,!1),e=a.updateQueue,n.updateQueue=e,vr(n,e),n.subtreeFlags=0,e=t,t=n.child;null!==t;)da(t,e),t=t.sibling;return S(vs,vs.current&ys|bs,n),n.child}e=e.sibling}null!==l.tail&&vu()>Wf&&(n.flags|=128,r=!0,Sr(l,!1),n.lanes=4194304)}else{if(!r)if(null!==(e=$e(a))){if(n.flags|=128,r=!0,e=e.updateQueue,n.updateQueue=e,vr(n,e),Sr(l,!0),null===l.tail&&"hidden"===l.tailMode&&!a.alternate)return kr(n),null}else 2*vu()-l.renderingStartTime>Wf&&536870912!==t&&(n.flags|=128,r=!0,Sr(l,!1),n.lanes=4194304);l.isBackwards?(a.sibling=n.child,n.child=a):(null!==(e=l.last)?e.sibling=a:n.child=a,l.last=a)}return null!==l.tail?(e=l.tail,l.rendering=e,l.tail=e.sibling,l.renderingStartTime=vu(),e.sibling=null,t=vs.current,S(vs,t=r?t&ys|bs:t&ys,n),e):(kr(n),null);case 22:case 23:return Ve(n),Ae(n),r=null!==n.memoizedState,null!==e?null!==e.memoizedState!==r&&(n.flags|=8192):r&&(n.flags|=8192),r?0!=(536870912&t)&&0==(128&n.flags)&&(kr(n),6&n.subtreeFlags&&(n.flags|=8192)):kr(n),null!==(t=n.updateQueue)&&vr(n,t.retryQueue),t=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),r=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(r=n.memoizedState.cachePool.pool),r!==t&&(n.flags|=2048),null!==e&&v(Ic,n),null;case 24:return t=null,null!==e&&(t=e.memoizedState.cache),n.memoizedState.cache!==t&&(n.flags|=2048),rr(Uc,n),kr(n),null;case 25:return null}throw Error("Unknown unit of work tag ("+n.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function xr(e,n){switch(I(n),n.tag){case 1:return 65536&(e=n.flags)?(n.flags=-65537&e|128,(2&n.mode)!==$u&&re(n),n):null;case 3:return rr(Uc,n),H(n),0!=(65536&(e=n.flags))&&0==(128&e)?(n.flags=-65537&e|128,n):null;case 26:case 27:case 5:return Q(n),null;case 13:if(Ve(n),null!==(e=n.memoizedState)&&null!==e.dehydrated&&null===n.alternate)throw Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");return 65536&(e=n.flags)?(n.flags=-65537&e|128,(2&n.mode)!==$u&&re(n),n):null;case 19:return v(vs,n),null;case 4:return H(n),null;case 10:return rr(n.type,n),null;case 22:case 23:return Ve(n),Ae(n),null!==e&&v(Ic,n),65536&(e=n.flags)?(n.flags=-65537&e|128,(2&n.mode)!==$u&&re(n),n):null;case 24:return rr(Uc,n),null;default:return null}}function zr(e,n){switch(I(n),n.tag){case 3:rr(Uc,n),H(n);break;case 26:case 27:case 5:Q(n);break;case 4:H(n);break;case 13:Ve(n);break;case 19:v(vs,n);break;case 10:rr(n.type,n);break;case 22:case 23:Ve(n),Ae(n),null!==e&&v(Ic,n);break;case 24:rr(Uc,n)}}function Cr(e){return(2&e.mode)!==$u}function Er(e,n){Cr(e)?(te(),_r(n,e),ne()):_r(n,e)}function Pr(e,n,t){Cr(e)?(te(),Rr(t,e,n),ne()):Rr(t,e,n)}function _r(e,n){try{var t=n.updateQueue,r=null!==t?t.lastEffect:null;if(null!==r){var l=r.next;t=l;do{var a;(t.tag&e)===e&&((e&zs)!==Ss?null!==Pu&&"function"==typeof Pu.markComponentPassiveEffectMountStarted&&Pu.markComponentPassiveEffectMountStarted(n):(e&xs)!==Ss&&null!==Pu&&"function"==typeof Pu.markComponentLayoutEffectMountStarted&&Pu.markComponentLayoutEffectMountStarted(n),r=void 0,(e&ws)!==Ss&&(nd=!0),r=y(n,Xi,t),(e&ws)!==Ss&&(nd=!1),(e&zs)!==Ss?null!==Pu&&"function"==typeof Pu.markComponentPassiveEffectMountStopped&&Pu.markComponentPassiveEffectMountStopped():(e&xs)!==Ss&&null!==Pu&&"function"==typeof Pu.markComponentLayoutEffectMountStopped&&Pu.markComponentLayoutEffectMountStopped(),void 0!==r&&"function"!=typeof r)&&y(n,(function(e,n){console.error("%s must not return anything besides a function, which is used for clean-up.%s",e,n)}),a=0!=(t.tag&xs)?"useLayoutEffect":0!=(t.tag&ws)?"useInsertionEffect":"useEffect",null===r?" You returned null. If your effect does not require clean up, return undefined (or nothing).":"function"==typeof r.then?"\n\nIt looks like you wrote "+a+"(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:\n\n"+a+"(() => {\n  async function fetchData() {\n    // You can await here\n    const response = await MyAPI.getData(someId);\n    // ...\n  }\n  fetchData();\n}, [someId]); // Or [] if effect doesn't need props or state\n\nLearn more about data fetching with Hooks: https://react.dev/link/hooks-data-fetching":" You returned: "+r),t=t.next}while(t!==l)}}catch(e){Xl(n,n.return,e)}}function Rr(e,n,t){try{var r=n.updateQueue,l=null!==r?r.lastEffect:null;if(null!==l){var a=l.next;r=a;do{if((r.tag&e)===e){var o=r.inst,u=o.destroy;void 0!==u&&(o.destroy=void 0,(e&zs)!==Ss?null!==Pu&&"function"==typeof Pu.markComponentPassiveEffectUnmountStarted&&Pu.markComponentPassiveEffectUnmountStarted(n):(e&xs)!==Ss&&null!==Pu&&"function"==typeof Pu.markComponentLayoutEffectUnmountStarted&&Pu.markComponentLayoutEffectUnmountStarted(n),(e&ws)!==Ss&&(nd=!0),y(n,es,n,t,u),(e&ws)!==Ss&&(nd=!1),(e&zs)!==Ss?null!==Pu&&"function"==typeof Pu.markComponentPassiveEffectUnmountStopped&&Pu.markComponentPassiveEffectUnmountStopped():(e&xs)!==Ss&&null!==Pu&&"function"==typeof Pu.markComponentLayoutEffectUnmountStopped&&Pu.markComponentLayoutEffectUnmountStopped())}r=r.next}while(r!==a)}}catch(e){Xl(n,n.return,e)}}function Tr(e,n){Cr(e)?(te(),_r(n,e),ne()):_r(n,e)}function Nr(e,n,t){Cr(e)?(te(),Rr(t,e,n),ne()):Rr(t,e,n)}function Lr(e){var n=e.updateQueue;if(null!==n){var t=e.stateNode;e.type.defaultProps||"ref"in e.memoizedProps||bc||(t.props!==e.memoizedProps&&console.error("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",c(e)||"instance"),t.state!==e.memoizedState&&console.error("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",c(e)||"instance"));try{y(e,ze,n,t)}catch(n){Xl(e,e.return,n)}}}function Ur(e,n,t){return e.getSnapshotBeforeUpdate(n,t)}function Dr(e,n){var t=n.memoizedProps,r=n.memoizedState;n=e.stateNode,e.type.defaultProps||"ref"in e.memoizedProps||bc||(n.props!==e.memoizedProps&&console.error("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",c(e)||"instance"),n.state!==e.memoizedState&&console.error("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",c(e)||"instance"));try{var l=Ct(e.type,t,(e.elementType,e.type)),a=y(e,Ur,n,l,r);t=Fc,void 0!==a||t.has(e.type)||(t.add(e.type),y(e,(function(){console.error("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",c(e))}))),n.__reactInternalSnapshotBeforeUpdate=a}catch(n){Xl(e,e.return,n)}}function Ir(e,n,t){t.props=Ct(e.type,e.memoizedProps),t.state=e.memoizedState,Cr(e)?(te(),y(e,Ji,e,n,t),ne()):y(e,Ji,e,n,t)}function Fr(e){var n=e.ref;if(null!==n){var t=e.stateNode;switch(e.tag){case 26:case 27:case 5:t=ao(t)}if("function"==typeof n)if(Cr(e))try{te(),e.refCleanup=n(t)}finally{ne()}else e.refCleanup=n(t);else"string"==typeof n?console.error("String refs are no longer supported."):n.hasOwnProperty("current")||console.error("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().",c(e)),n.current=t}}function Mr(e,n){try{y(e,Fr,e)}catch(t){Xl(e,n,t)}}function Wr(e,n){var t=e.ref,r=e.refCleanup;if(null!==t)if("function"==typeof r)try{if(Cr(e))try{te(),y(e,r)}finally{ne()}else y(e,r)}catch(t){Xl(e,n,t)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof t)try{if(Cr(e))try{te(),y(e,t,null)}finally{ne()}else y(e,t,null)}catch(t){Xl(e,n,t)}else t.current=null}function Hr(e,n,t,r){var l=e.memoizedProps,a=l.id,o=l.onCommit;l=l.onRender,n=null===n?"mount":"update",ei&&(n="nested-update"),"function"==typeof l&&l(a,n,e.actualDuration,e.treeBaseDuration,e.actualStartTime,t),"function"==typeof o&&o(e.memoizedProps.id,n,r,t)}function jr(e,n,t,r){var l=e.memoizedProps;e=l.id,l=l.onPostCommit,n=null===n?"mount":"update",ei&&(n="nested-update"),"function"==typeof l&&l(e,n,r,t)}function Ar(e){var n=e.type,t=e.memoizedProps,r=e.stateNode;try{y(e,Wo,r,n,t,e)}catch(n){Xl(e,e.return,n)}}function Qr(e){return 5===e.tag||3===e.tag||4===e.tag}function Or(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||Qr(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function Br(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?Ao(t,e,n):Fo(t,e);else if(4!==r&&!au&&null!==(e=e.child))for(Br(e,n,t),e=e.sibling;null!==e;)Br(e,n,t),e=e.sibling}function Vr(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?jo(t,e,n):Io(t,e);else if(4!==r&&!au&&null!==(e=e.child))for(Vr(e,n,t),e=e.sibling;null!==e;)Vr(e,n,t),e=e.sibling}function $r(e){e:{for(var n=e.return;null!==n;){if(Qr(n)){var t=n;break e}n=n.return}throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.")}switch(t.tag){case 27:case 5:n=t.stateNode,32&t.flags&&(Bo(n),t.flags&=-33),Vr(e,t=Or(e),n);break;case 3:case 4:n=t.stateNode.containerInfo,Br(e,t=Or(e),n);break;default:throw Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}}function qr(e,n,t){var r=t.flags;switch(t.tag){case 0:case 11:case 15:tl(e,t),4&r&&Er(t,xs|ks);break;case 1:if(tl(e,t),4&r)if(e=t.stateNode,null===n)t.type.defaultProps||"ref"in t.memoizedProps||bc||(e.props!==t.memoizedProps&&console.error("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",c(t)||"instance"),e.state!==t.memoizedState&&console.error("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",c(t)||"instance")),Cr(t)?(te(),y(t,Bi,t,e),ne()):y(t,Bi,t,e);else{var l=Ct(t.type,n.memoizedProps);n=n.memoizedState,t.type.defaultProps||"ref"in t.memoizedProps||bc||(e.props!==t.memoizedProps&&console.error("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",c(t)||"instance"),e.state!==t.memoizedState&&console.error("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",c(t)||"instance")),Cr(t)?(te(),y(t,$i,t,e,l,n,e.__reactInternalSnapshotBeforeUpdate),ne()):y(t,$i,t,e,l,n,e.__reactInternalSnapshotBeforeUpdate)}64&r&&Lr(t),512&r&&Mr(t,t.return);break;case 3:if(n=G(),tl(e,t),64&r&&null!==(r=t.updateQueue)){if(l=null,null!==t.child)switch(t.child.tag){case 27:case 5:l=ao(t.child.stateNode);break;case 1:l=t.child.stateNode}try{y(t,ze,r,l)}catch(e){Xl(t,t.return,e)}}e.effectDuration+=J(n);break;case 26:case 27:case 5:tl(e,t),null===n&&4&r&&Ar(t),512&r&&Mr(t,t.return);break;case 12:if(4&r){r=G(),tl(e,t),(e=t.stateNode).effectDuration+=K(r);try{y(t,Hr,t,n,Ku,e.effectDuration)}catch(e){Xl(t,t.return,e)}}else tl(e,t);break;case 13:default:tl(e,t);break;case 22:if(!(l=null!==t.memoizedState||Mc)){n=null!==n&&null!==n.memoizedState||Wc;var a=Mc,o=Wc;Mc=l,(Wc=n)&&!o?ol(e,t,0!=(8772&t.subtreeFlags)):tl(e,t),Mc=a,Wc=o}512&r&&("manual"===t.memoizedProps.mode?Mr(t,t.return):Wr(t,t.return))}}function Yr(e){var n=e.alternate;null!==n&&(e.alternate=null,Yr(n)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(n=e.stateNode)&&Eo(n),e.stateNode=null,e._debugOwner=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Gr(e,n,t){for(t=t.child;null!==t;)Jr(e,n,t),t=t.sibling}function Jr(e,n,t){switch(t.tag){case 26:case 27:var r,l;case 5:Wc||Wr(t,n);case 6:if(r=Vc,l=$c,Vc=null,Gr(e,n,t),$c=l,null!==(Vc=r))if($c)try{y(t,Oo,Vc,t.stateNode)}catch(e){Xl(t,n,e)}else try{y(t,Qo,Vc,t.stateNode)}catch(e){Xl(t,n,e)}break;case 18:null!==Vc&&($c?nu(Vc,t.stateNode):eu(Vc,t.stateNode));break;case 4:r=Vc,l=$c,Vc=t.stateNode.containerInfo,$c=!0,Gr(e,n,t),Vc=r,$c=l;break;case 0:case 11:case 14:case 15:Wc||Rr(ws,t,n),Wc||Pr(t,n,xs),Gr(e,n,t);break;case 1:Wc||(Wr(t,n),"function"==typeof(r=t.stateNode).componentWillUnmount&&Ir(t,n,r)),Gr(e,n,t);break;case 21:Gr(e,n,t);break;case 22:Wr(t,n),Wc=(r=Wc)||null!==t.memoizedState,Gr(e,n,t),Wc=r;break;default:Gr(e,n,t)}}function Kr(e,n){var t=function(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return null===n&&(n=e.stateNode=new jc),n;case 22:return null===(n=(e=e.stateNode)._retryCache)&&(n=e._retryCache=new jc),n;default:throw Error("Unexpected Suspense handler tag ("+e.tag+"). This is a bug in React.")}}(e);n.forEach((function(n){var r=ra.bind(null,e,n);if(!t.has(n)){if(t.add(n),_u){if(null===Qc||null===Oc)throw Error("Expected finished root and lanes to be set. This is a bug in React.");ia(Oc,Qc)}n.then(r,r)}}))}function Xr(e,n){var t=n.deletions;if(null!==t)for(var r=0;r<t.length;r++){var l=e,a=n,o=t[r],u=a;e:for(;null!==u;){switch(u.tag){case 27:case 5:Vc=u.stateNode,$c=!1;break e;case 3:case 4:Vc=u.stateNode.containerInfo,$c=!0;break e}u=u.return}if(null===Vc)throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");Jr(l,a,o),Vc=null,$c=!1,null!==(a=(l=o).alternate)&&(a.return=null),l.return=null}if(13878&n.subtreeFlags)for(n=n.child;null!==n;)Zr(n,e),n=n.sibling}function Zr(e,n){var t=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Xr(n,e),el(e),4&r&&(Rr(ws|ks,e,e.return),_r(ws|ks,e),Pr(e,e.return,xs|ks));break;case 1:Xr(n,e),el(e),512&r&&null!==t&&Wr(t,t.return),64&r&&Mc&&null!==(e=e.updateQueue)&&null!==(r=e.callbacks)&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===t?r:t.concat(r));break;case 26:var l;case 27:var a;case 5:if(Xr(n,e),el(e),512&r&&null!==t&&Wr(t,t.return),32&e.flags){n=e.stateNode;try{y(e,Bo,n)}catch(n){Xl(e,e.return,n)}}4&r&&null!=e.stateNode&&function(e,n,t){try{y(e,Ho,e.stateNode,e.type,t,n,e)}catch(n){Xl(e,e.return,n)}}(e,n=e.memoizedProps,null!==t?t.memoizedProps:n),1024&r&&(Hc=!0,"form"!==e.type&&console.error("Unexpected host component type. Expected a form. This is a bug in React."));break;case 6:if(Xr(n,e),el(e),4&r&&vo){if(null===e.stateNode)throw Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");r=e.memoizedProps,t=null!==t?t.memoizedProps:r,n=e.stateNode;try{y(e,Mo,n,t,r)}catch(n){Xl(e,e.return,n)}}break;case 3:l=G(),Xr(n,e),el(e),Hc&&(Hc=!1,nl(e)),n.effectDuration+=J(l);break;case 4:Xr(n,e),el(e);break;case 12:r=G(),Xr(n,e),el(e),e.stateNode.effectDuration+=K(r);break;case 13:Xr(n,e),el(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==t&&null!==t.memoizedState)&&(Ff=vu()),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Kr(e,r));break;case 22:512&r&&null!==t&&Wr(t,t.return),l=null!==e.memoizedState;var o=null!==t&&null!==t.memoizedState,u=Mc,i=Wc;if(Mc=u||l,Wc=i||o,Xr(n,e),Wc=i,Mc=u,el(e),(n=e.stateNode)._current=e,n._visibility&=-3,n._visibility|=2&n._pendingVisibility,8192&r&&(n._visibility=l?-2&n._visibility:1|n._visibility,l&&(n=Mc||Wc,null===t||o||n||ll(e)),null===e.memoizedProps||"manual"!==e.memoizedProps.mode))e:if(t=null,vo)for(n=e;;){if(5===n.tag||tu||au){if(null===t){o=t=n;try{a=o.stateNode,l?y(o,Vo,a):y(o,qo,o.stateNode,o.memoizedProps)}catch(e){Xl(o,o.return,e)}}}else if(6===n.tag){if(null===t){o=n;try{var s=o.stateNode;l?y(o,$o,s):y(o,Yo,s,o.memoizedProps)}catch(e){Xl(o,o.return,e)}}}else if((22!==n.tag&&23!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;null===n.sibling;){if(null===n.return||n.return===e)break e;t===n&&(t=null),n=n.return}t===n&&(t=null),n.sibling.return=n.return,n=n.sibling}4&r&&null!==(r=e.updateQueue)&&null!==(t=r.retryQueue)&&(r.retryQueue=null,Kr(e,t));break;case 19:Xr(n,e),el(e),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Kr(e,r));break;case 21:break;default:Xr(n,e),el(e)}}function el(e){var n=e.flags;if(2&n){try{y(e,$r,e)}catch(n){Xl(e,e.return,n)}e.flags&=-3}4096&n&&(e.flags&=-4097)}function nl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var n=e;nl(n),5===n.tag&&1024&n.flags&&Do(n.stateNode),e=e.sibling}}function tl(e,n){if(8772&n.subtreeFlags)for(n=n.child;null!==n;)qr(e,n.alternate,n),n=n.sibling}function rl(e){switch(e.tag){case 0:case 11:case 14:case 15:Pr(e,e.return,xs),ll(e);break;case 1:Wr(e,e.return);var n=e.stateNode;"function"==typeof n.componentWillUnmount&&Ir(e,e.return,n),ll(e);break;case 26:case 27:case 5:Wr(e,e.return),ll(e);break;case 22:Wr(e,e.return),null===e.memoizedState&&ll(e);break;default:ll(e)}}function ll(e){for(e=e.child;null!==e;)rl(e),e=e.sibling}function al(e,n,t,r){var l=t.flags;switch(t.tag){case 0:case 11:case 15:ol(e,t,r),Er(t,xs);break;case 1:if(ol(e,t,r),"function"==typeof(n=t.stateNode).componentDidMount&&y(t,Bi,t,n),null!==(n=t.updateQueue)){e=t.stateNode;try{y(t,xe,n,e)}catch(e){Xl(t,t.return,e)}}r&&64&l&&Lr(t),Mr(t,t.return);break;case 26:case 27:case 5:ol(e,t,r),r&&null===n&&4&l&&Ar(t),Mr(t,t.return);break;case 12:if(r&&4&l){l=G(),ol(e,t,r),(r=t.stateNode).effectDuration+=K(l);try{y(t,Hr,t,n,Ku,r.effectDuration)}catch(e){Xl(t,t.return,e)}}else ol(e,t,r);break;case 13:default:ol(e,t,r);break;case 22:null===t.memoizedState&&ol(e,t,r),Mr(t,t.return)}}function ol(e,n,t){for(t=t&&0!=(8772&n.subtreeFlags),n=n.child;null!==n;)al(e,n.alternate,n,t),n=n.sibling}function ul(e,n){var t=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),e=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(e=n.memoizedState.cachePool.pool),e!==t&&(null!=e&&pr(e),null!=t&&mr(t))}function il(e,n){e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(pr(n),null!=e&&mr(e))}function sl(e,n,t,r){if(10256&n.subtreeFlags)for(n=n.child;null!==n;)cl(e,n,t,r),n=n.sibling}function cl(e,n,t,r){var l=n.flags;switch(n.tag){case 0:case 11:case 15:sl(e,n,t,r),2048&l&&Tr(n,zs|ks);break;case 3:var a=G();sl(e,n,t,r),2048&l&&(t=null,null!==n.alternate&&(t=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==t&&(pr(n),null!=t&&mr(t))),e.passiveEffectDuration+=J(a);break;case 12:if(2048&l){a=G(),sl(e,n,t,r),(e=n.stateNode).passiveEffectDuration+=K(a);try{y(n,jr,n,n.alternate,Ku,e.passiveEffectDuration)}catch(e){Xl(n,n.return,e)}}else sl(e,n,t,r);break;case 23:break;case 22:a=n.stateNode,null!==n.memoizedState?4&a._visibility?sl(e,n,t,r):pl(e,n):4&a._visibility?sl(e,n,t,r):(a._visibility|=4,fl(e,n,t,r,0!=(10256&n.subtreeFlags))),2048&l&&ul(n.alternate,n);break;case 24:sl(e,n,t,r),2048&l&&il(n.alternate,n);break;default:sl(e,n,t,r)}}function fl(e,n,t,r,l){for(l=l&&0!=(10256&n.subtreeFlags),n=n.child;null!==n;)dl(e,n,t,r,l),n=n.sibling}function dl(e,n,t,r,l){var a=n.flags;switch(n.tag){case 0:case 11:case 15:fl(e,n,t,r,l),Tr(n,zs);break;case 23:break;case 22:var o=n.stateNode;null!==n.memoizedState?4&o._visibility?fl(e,n,t,r,l):pl(e,n):(o._visibility|=4,fl(e,n,t,r,l)),l&&2048&a&&ul(n.alternate,n);break;case 24:fl(e,n,t,r,l),l&&2048&a&&il(n.alternate,n);break;default:fl(e,n,t,r,l)}}function pl(e,n){if(10256&n.subtreeFlags)for(n=n.child;null!==n;){var t=e,r=n,l=r.flags;switch(r.tag){case 22:pl(t,r),2048&l&&ul(r.alternate,r);break;case 24:pl(t,r),2048&l&&il(r.alternate,r);break;default:pl(t,r)}n=n.sibling}}function ml(e){if(e.subtreeFlags&Yc)for(e=e.child;null!==e;)hl(e),e=e.sibling}function hl(e){switch(e.tag){case 26:ml(e),e.flags&Yc&&(null!==e.memoizedState?lu(qc,e.memoizedState,e.memoizedProps):To(e.type,e.memoizedProps));break;case 5:ml(e),e.flags&Yc&&To(e.type,e.memoizedProps);break;case 3:case 4:var n;ml(e);break;case 22:null===e.memoizedState&&(null!==(n=e.alternate)&&null!==n.memoizedState?(n=Yc,Yc=16777216,ml(e),Yc=n):ml(e));break;default:ml(e)}}function gl(e){var n=e.alternate;if(null!==n&&null!==(e=n.child)){n.child=null;do{n=e.sibling,e.sibling=null,e=n}while(null!==e)}}function yl(e){var n=e.deletions;if(0!=(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Ac=r,kl(r,e)}gl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)bl(e),e=e.sibling}function bl(e){switch(e.tag){case 0:case 11:case 15:yl(e),2048&e.flags&&Nr(e,e.return,zs|ks);break;case 3:var n=G();yl(e),e.stateNode.passiveEffectDuration+=J(n);break;case 12:n=G(),yl(e),e.stateNode.passiveEffectDuration+=K(n);break;case 22:n=e.stateNode,null!==e.memoizedState&&4&n._visibility&&(null===e.return||13!==e.return.tag)?(n._visibility&=-5,vl(e)):yl(e);break;default:yl(e)}}function vl(e){var n=e.deletions;if(0!=(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Ac=r,kl(r,e)}gl(e)}for(e=e.child;null!==e;)Sl(e),e=e.sibling}function Sl(e){switch(e.tag){case 0:case 11:case 15:Nr(e,e.return,zs),vl(e);break;case 22:var n=e.stateNode;4&n._visibility&&(n._visibility&=-5,vl(e));break;default:vl(e)}}function kl(e,n){for(;null!==Ac;){var t=Ac,r=t;switch(r.tag){case 0:case 11:case 15:Nr(r,n,zs);break;case 23:case 22:null!==r.memoizedState&&null!==r.memoizedState.cachePool&&null!=(r=r.memoizedState.cachePool.pool)&&pr(r);break;case 24:mr(r.memoizedState.cache)}if(null!==(r=t.child))r.return=t,Ac=r;else e:for(t=e;null!==Ac;){var l=(r=Ac).sibling,a=r.return;if(Yr(r),r===t){Ac=null;break e}if(null!==l){l.return=a,Ac=l;break e}Ac=a}}}function wl(){var e="undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?IS_REACT_ACT_ENVIRONMENT:void 0;return e||null===Ja.actQueue||console.error("The current testing environment is not configured to support act(...)"),e}function xl(e){if((sf&Zc)!==Xc&&0!==df)return df&-df;var n=Ja.T;return null!==n?(n._updatedFibers||(n._updatedFibers=new Set),n._updatedFibers.add(e),0!==(e=di)?e:de()):zo()}function zl(){0===Nf&&(Nf=0==(536870912&df)||Bu?z():536870912);var e=hs.current;return null!==e&&(e.flags|=32),Nf}function Cl(e,n,t){if(nd&&console.error("useInsertionEffect must not schedule updates."),Jf&&(Kf=!0),(e===cf&&wf===hf||null!==e.cancelPendingCommit)&&(Ll(e,0),Tl(e,df,Nf)),P(e,t),0!=(sf&Zc)&&e===cf){if(ro)switch(n.tag){case 0:case 11:case 15:e=ff&&c(ff)||"Unknown",ud.has(e)||(ud.add(e),n=c(n)||"Unknown",console.error("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://react.dev/link/setstate-in-render",n,e,e));break;case 1:od||(console.error("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),od=!0)}}else _u&&T(e,n,t),function(e){wl()&&null===Ja.actQueue&&y(e,(function(){console.error("An update to %s inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act",c(e))}))}(n),e===cf&&((sf&Zc)===Xc&&(Rf|=t),Pf===af&&Tl(e,df,Nf)),le(e)}function El(e,n,t){if((sf&(Zc|ef))!==Xc)throw Error("Should not already be working.");var r=(t=!t&&0==(60&n)&&0==(n&e.expiredLanes))?function(e,n){var t=sf;sf|=Zc;var r=Il(),l=Fl();if(cf!==e||df!==n){if(_u){var a=e.memoizedUpdaters;0<a.size&&(ia(e,df),a.clear()),N(e,n)}jf=null,Wf=vu()+Hf,Ll(e,n)}e:for(;;)try{if(wf!==pf&&null!==ff)n:switch(n=ff,a=xf,wf){case mf:wf=pf,xf=null,Bl(e,n,a);break;case hf:if(Ee(a)){wf=pf,xf=null,Ql(n);break}n=function(){wf===hf&&cf===e&&(wf=Sf),le(e)},a.then(n,n);break e;case gf:wf=Sf;break e;case yf:wf=bf;break e;case Sf:Ee(a)?(wf=pf,xf=null,Ql(n)):(wf=pf,xf=null,Bl(e,n,a));break;case bf:var o=null;switch(ff.tag){case 26:o=ff.memoizedState;case 5:case 27:var u=ff,i=u.type,s=u.pendingProps;if(o?ru(o):_o(i,s)){wf=pf,xf=null;var c=u.sibling;if(null!==c)ff=c;else{var f=u.return;null!==f?(ff=f,Vl(f)):ff=null}break n}break;default:console.error("Unexpected type of fiber triggered a suspensey commit. This is a bug in React.")}wf=pf,xf=null,Bl(e,n,a);break;case vf:wf=pf,xf=null,Bl(e,n,a);break;case kf:Nl(),Pf=uf;break e;default:throw Error("Unexpected SuspendedReason. This is a bug in React.")}null!==Ja.actQueue?Hl():jl();break}catch(n){Ul(e,n)}return nr(),Ja.H=r,Ja.A=l,sf=t,null!==ff?nf:(cf=null,df=0,O(),Pf)}(e,n):Wl(e,n);if(r!==nf)for(var l=t;;){if(r===uf)Tl(e,n,0);else{if(t=e.current.alternate,l&&!Rl(t)){r=Wl(e,n),l=!1;continue}if(r===rf){if(l=n,e.errorRecoveryDisabledLanes&l)var a=0;else a=0!=(a=-536870913&e.pendingLanes)?a:536870912&a?536870912:0;if(0!==a){n=a;e:{r=e;var o=a;a=Uf;var u=So;if(u&&(Ll(r,o).flags|=256),(o=Wl(r,o))!==rf){if(Cf&&!u){r.errorRecoveryDisabledLanes|=l,Rf|=l,r=af;break e}r=Df,Df=a,null!==r&&Pl(r)}r=o}if(l=!1,r!==rf)continue}}if(r===tf){Ll(e,0),Tl(e,n,0);break}e:{switch(l=e,r){case nf:case tf:throw Error("Root did not complete. This is a bug in React.");case af:if((4194176&n)===n){Tl(l,n,Nf);break e}break;case rf:Df=null;break;case lf:case of:break;default:throw Error("Unknown root exit status.")}if(l.finishedWork=t,l.finishedLanes=n,null!==Ja.actQueue)ql(l,Df,jf,If,Nf);else{if((62914560&n)===n&&10<(r=Ff+Mf-vu())){if(Tl(l,n,Nf),0!==w(l,0))break e;l.timeoutHandle=go(_l.bind(null,l,t,Df,jf,If,n,Nf,Rf,Lf,zf,ld,-0,0),r);break e}_l(l,t,Df,jf,If,n,Nf,Rf,Lf,zf,td,-0,0)}}}break}le(e)}function Pl(e){null===Df?Df=e:Df.push.apply(Df,e)}function _l(e,n,t,r,l,a,o,u,i,s,c,f,d){if((8192&(s=n.subtreeFlags)||16785408==(16785408&s))&&(Ro(),hl(n),null!==(n=No())))return e.cancelPendingCommit=n(ql.bind(null,e,t,r,l,o,u,i,rd,f,d)),void Tl(e,a,o);ql(e,t,r,l,o)}function Rl(e){for(var n=e;;){var t=n.tag;if((0===t||11===t||15===t)&&16384&n.flags&&null!==(t=n.updateQueue)&&null!==(t=t.stores))for(var r=0;r<t.length;r++){var l=t[r],a=l.getSnapshot;l=l.value;try{if(!Ru(a(),l))return!1}catch(e){return!1}}if(t=n.child,16384&n.subtreeFlags&&null!==t)t.return=n,n=t;else{if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Tl(e,n,t){n&=~Tf,n&=~Rf,e.suspendedLanes|=n,e.pingedLanes&=~n;for(var r=e.expirationTimes,l=n;0<l;){var a=31-cu(l),o=1<<a;r[a]=-1,l&=~o}0!==t&&_(e,t,n)}function Nl(){if(null!==ff){if(wf===pf)var e=ff.return;else e=ff,nr(),an(e),rs=null,ls=0,e=ff;for(;null!==e;)zr(e.alternate,e),e=e.return;ff=null}}function Ll(e,n){e.finishedWork=null,e.finishedLanes=0;var t=e.timeoutHandle;t!==bo&&(e.timeoutHandle=bo,yo(t)),null!==(t=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,t()),Nl(),cf=e,ff=t=fa(e.current,null),df=n,wf=pf,xf=null,Cf=zf=!1,Pf=nf,Lf=Nf=Tf=Rf=_f=0,Df=Uf=null,If=!1,0!=(8&n)&&(n|=32&n);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=n;0<r;){var l=31-cu(r),a=1<<l;n|=e[l],r&=~a}return Ef=n,O(),xi.discardPendingWarnings(),t}function Ul(e,n){Ts=null,Ja.H=$s,Ja.getCurrentStack=null,ro=!1,to=null,n===Di?(n=Re(),wf=Dl()&&0==(134217727&_f)&&0==(134217727&Rf)?hf:gf):n===Ii?(n=Re(),wf=yf):wf=n===dc?kf:null!==n&&"object"==typeof n&&"function"==typeof n.then?vf:mf,xf=n;var t=ff;null===t?(Pf=tf,Et(e,D(n,e.current))):2&t.mode&&Z(t)}function Dl(){var e=hs.current;return null===e||((4194176&df)===df?null===gs:((62914560&df)===df||0!=(536870912&df))&&e===gs)}function Il(){var e=Ja.H;return Ja.H=$s,null===e?$s:e}function Fl(){var e=Ja.A;return Ja.A=Gc,e}function Ml(){Pf=af,0==(134217727&_f)&&0==(134217727&Rf)||null===cf||Tl(cf,df,Nf)}function Wl(e,n){var t=sf;sf|=Zc;var r=Il(),l=Fl();if(cf!==e||df!==n){if(_u){var a=e.memoizedUpdaters;0<a.size&&(ia(e,df),a.clear()),N(e,n)}jf=null,Ll(e,n)}n=!1;e:for(;;)try{if(wf!==pf&&null!==ff){a=ff;var o=xf;switch(wf){case kf:Nl(),Pf=uf;break e;case gf:case hf:n||null!==hs.current||(n=!0);default:wf=pf,xf=null,Bl(e,a,o)}}Hl();break}catch(n){Ul(e,n)}if(n&&e.shellSuspendCounter++,nr(),sf=t,Ja.H=r,Ja.A=l,null!==ff)throw Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");return cf=null,df=0,O(),Pf}function Hl(){for(;null!==ff;)Al(ff)}function jl(){for(;null!==ff&&!yu();)Al(ff)}function Al(e){var n=e.alternate;(2&e.mode)!==$u?(X(e),n=y(e,er,n,e,Ef),Z(e)):n=y(e,er,n,e,Ef),e.memoizedProps=e.pendingProps,null===n?Vl(e):ff=n}function Ql(e){var n=y(e,Ol,e);e.memoizedProps=e.pendingProps,null===n?Vl(e):ff=n}function Ol(e){var n=e.alternate,t=(2&e.mode)!==$u;switch(t&&X(e),e.tag){case 15:case 0:n=Ht(n,e,e.pendingProps,e.type,void 0,df);break;case 11:n=Ht(n,e,e.pendingProps,e.type.render,e.ref,df);break;case 5:an(e);default:zr(n,e),n=er(n,e=ff=da(e,Ef),Ef)}return t&&Z(e),n}function Bl(e,n,t){nr(),an(n),rs=null,ls=0;var r=n.return;try{if(function(e,n,t,r,l){if(t.flags|=32768,_u&&ia(e,l),null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(n=t.alternate)&&or(n,t,l,!0),null!==(t=hs.current)){switch(t.tag){case 13:return null===gs?Ml():null===t.alternate&&Pf===nf&&(Pf=lf),t.flags&=-257,t.flags|=65536,t.lanes=l,r===Fi?t.flags|=16384:(null===(n=t.updateQueue)?t.updateQueue=new Set([r]):n.add(r),Zl(e,r,l)),!1;case 22:return t.flags|=65536,r===Fi?t.flags|=16384:(null===(n=t.updateQueue)?(n={transitions:null,markerInstances:null,retryQueue:new Set([r])},t.updateQueue=n):null===(t=n.retryQueue)?n.retryQueue=new Set([r]):t.add(r),Zl(e,r,l)),!1}throw Error("Unexpected Suspense handler tag ("+t.tag+"). This is a bug in React.")}return Zl(e,r,l),Ml(),!1}var a=D(Error("There was an error during concurrent rendering but React was able to recover by instead synchronously rendering the entire root.",{cause:r}),t);if(null===Uf?Uf=[a]:Uf.push(a),Pf!==af&&(Pf=rf),null===n)return!0;r=D(r,t),t=n;do{switch(t.tag){case 3:return t.flags|=65536,e=l&-l,t.lanes|=e,ve(t,e=_t(t.stateNode,r,e)),!1;case 1:if(n=t.type,a=t.stateNode,0==(128&t.flags)&&("function"==typeof n.getDerivedStateFromError||null!==a&&"function"==typeof a.componentDidCatch&&(null===Af||!Af.has(a))))return t.flags|=65536,l&=-l,t.lanes|=l,Tt(l=Rt(l),e,t,r),ve(t,l),!1}t=t.return}while(null!==t);return!1}(e,r,n,t,df))return Pf=tf,Et(e,D(t,e.current)),void(ff=null)}catch(n){if(null!==r)throw ff=r,n;return Pf=tf,Et(e,D(t,e.current)),void(ff=null)}32768&n.flags?$l(n,!0):Vl(n)}function Vl(e){var n=e;do{if(0!=(32768&n.flags))return void $l(n,zf);var t=n.alternate;if(e=n.return,X(n),t=y(n,wr,t,n,Ef),(2&n.mode)!==$u&&ee(n),null!==t)return void(ff=t);if(null!==(n=n.sibling))return void(ff=n);ff=n=e}while(null!==n);Pf===nf&&(Pf=of)}function $l(e,n){do{var t=xr(e.alternate,e);if(null!==t)return t.flags&=32767,void(ff=t);if((2&e.mode)!==$u){ee(e),t=e.actualDuration;for(var r=e.child;null!==r;)t+=r.actualDuration,r=r.sibling;e.actualDuration=t}if(null!==(t=e.return)&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!n&&null!==(e=e.sibling))return void(ff=e);ff=e=t}while(null!==e);Pf=uf,ff=null}function ql(e,n,t,r,l,a,o,u,i,s){var c=Ja.T,f=xo();try{wo(2),Ja.T=null,function(e,n,t,r,l,a){do{Jl()}while(null!==Of);if(xi.flushLegacyContextWarning(),xi.flushPendingUnsafeLifecycleWarnings(),(sf&(Zc|ef))!==Xc)throw Error("Should not already be working.");var o=e.finishedWork;if(r=e.finishedLanes,null===o)return null;if(0===r&&console.error("root.finishedLanes should not be empty during a commit. This is a bug in React."),e.finishedWork=null,e.finishedLanes=0,o===e.current)throw Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var u,i,s,c=o.lanes|o.childLanes;if(function(e,n,t,r){var l=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0,n=e.entanglements;var a=e.expirationTimes,o=e.hiddenUpdates;for(t=l&~t;0<t;){var u=31-cu(t);l=1<<u,n[u]=0,a[u]=-1;var i=o[u];if(null!==i)for(o[u]=null,u=0;u<i.length;u++){var s=i[u];null!==s&&(s.lane&=-536870913)}t&=~l}0!==r&&_(e,r,0)}(e,r,c|=Gu,a),e===cf&&(ff=cf=null,df=0),0==(10256&o.subtreeFlags)&&0==(10256&o.flags)||Qf||(Qf=!0,Vf=c,$f=t,u=wu,i=function(){return Jl(),null},null!==(s=Ja.actQueue)?s.push(i):hu(u,i)),Ku=Ju(),t=0!=(15990&o.flags),0!=(15990&o.subtreeFlags)||t){t=Ja.T,Ja.T=null,a=xo(),wo(2);var f=sf;sf|=ef,function(e,n){for(io(e.containerInfo),Ac=n;null!==Ac;)if(n=(e=Ac).child,0!=(1028&e.subtreeFlags)&&null!==n)n.return=e,Ac=n;else for(;null!==Ac;){var t=(n=e=Ac).alternate,r=n.flags;switch(n.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:0!=(1024&r)&&null!==t&&Dr(n,t);break;case 3:0!=(1024&r)&&vo&&Go(n.stateNode.containerInfo);break;default:if(0!=(1024&r))throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}if(null!==(n=e.sibling)){n.return=e.return,Ac=n;break}Ac=e.return}e=Bc,Bc=!1}(e,o),function(e,n,t){Qc=t,Oc=e,Zr(n,e),Oc=Qc=null}(e,o,r),so(e.containerInfo),e.current=o,function(e,n,t){Qc=t,Oc=n,qr(n,e.alternate,e),Oc=Qc=null}(o,e,r),bu(),sf=f,wo(a),Ja.T=t}else e.current=o;if((t=Qf)?(Qf=!1,Of=e,Bf=r):(Gl(e,c),Zf=0,ed=null),0===(c=e.pendingLanes)&&(Af=null),t||oa(e),o.stateNode,_u&&e.memoizedUpdaters.clear(),le(e),null!==n)for(l=e.onRecoverableError,o=0;o<n.length;o++)t=Yl((c=n[o]).stack),y(c.source,l,c.value,t);0!=(3&Bf)&&Jl(),c=e.pendingLanes,0!=(4194218&r)&&0!=(42&c)?(ni=!0,e===Gf?Yf++:(Yf=0,Gf=e)):Yf=0,ae(0)}(e,n,t,r,f,l)}finally{Ja.T=c,wo(f)}}function Yl(e){return e={componentStack:e},Object.defineProperty(e,"digest",{get:function(){console.error('You are accessing "digest" from the errorInfo object passed to onRecoverableError. This property is no longer provided as part of errorInfo but can be accessed as a property of the Error instance itself.')}}),e}function Gl(e,n){0==(e.pooledCacheLanes&=n)&&null!=(n=e.pooledCache)&&(e.pooledCache=null,mr(n))}function Jl(){if(null!==Of){var e=Of,n=Vf;Vf=0;var t=L(Bf),r=32>t?32:t;t=Ja.T;var l=xo();try{if(wo(r),Ja.T=null,null===Of)var a=!1;else{r=$f,$f=null;var o=Of,u=Bf;if(Of=null,Bf=0,(sf&(Zc|ef))!==Xc)throw Error("Cannot flush passive effects while already rendering.");Jf=!0,Kf=!1,null!==Pu&&"function"==typeof Pu.markPassiveEffectsStarted&&Pu.markPassiveEffectsStarted(u);var i=sf;sf|=ef,bl(o.current),cl(o,o.current,u,r),null!==Pu&&"function"==typeof Pu.markPassiveEffectsStopped&&Pu.markPassiveEffectsStopped(),oa(o),sf=i,ae(0),Kf?o===ed?Zf++:(Zf=0,ed=o):Zf=0,Kf=Jf=!1,Eu&&Eu.onPostCommitFiberRoot;var s=o.current.stateNode;s.effectDuration=0,s.passiveEffectDuration=0,a=!0}return a}finally{wo(l),Ja.T=t,Gl(e,n)}}return!1}function Kl(e,n,t){n=D(t,n),null!==(e=ye(e,n=_t(e.stateNode,n,2),2))&&(P(e,2),le(e))}function Xl(e,n,t){if(nd=!1,3===e.tag)Kl(e,e,t);else{for(;null!==n;){if(3===n.tag)return void Kl(n,e,t);if(1===n.tag){var r=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Af||!Af.has(r)))return e=D(t,e),void(null!==(r=ye(n,t=Rt(2),2))&&(Tt(t,r,n,e),P(r,2),le(r)))}n=n.return}console.error("Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Potential causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.\n\nError message:\n\n%s",t)}}function Zl(e,n,t){var r=e.pingCache;if(null===r){r=e.pingCache=new Kc;var l=new Set;r.set(n,l)}else void 0===(l=r.get(n))&&(l=new Set,r.set(n,l));l.has(t)||(Cf=!0,l.add(t),r=ea.bind(null,e,n,t),_u&&ia(e,t),n.then(r,r))}function ea(e,n,t){var r=e.pingCache;null!==r&&r.delete(n),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,wl()&&null===Ja.actQueue&&console.error("A suspended resource finished loading inside a test, but the event was not wrapped in act(...).\n\nWhen testing, code that resolves suspended data should be wrapped into act(...):\n\nact(() => {\n  /* finish loading suspended data */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act"),cf===e&&(df&t)===t&&(Pf===af||Pf===lf&&(62914560&df)===df&&vu()-Ff<Mf?(sf&Zc)===Xc&&Ll(e,0):Tf|=t,Lf===df&&(Lf=0)),le(e)}function na(e,n){0===n&&(n=C()),null!==(e=$(e,n))&&(P(e,n),le(e))}function ta(e){var n=e.memoizedState,t=0;null!==n&&(t=n.retryLane),na(e,t)}function ra(e,n){var t=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;null!==l&&(t=l.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error("Pinged unknown suspense boundary type. This is probably a bug in React.")}null!==r&&r.delete(n),na(e,t)}function la(e,n,t){if(0!=(33562624&n.subtreeFlags))for(n=n.child;null!==n;){var r=e,l=n,a=l.type===Fa;a=t||a,22!==l.tag?33554432&l.flags?a&&y(l,aa,r,l,(64&l.mode)===$u):la(r,l,a):null===l.memoizedState&&(a&&8192&l.flags?y(l,aa,r,l):33554432&l.subtreeFlags&&y(l,la,r,l,a)),n=n.sibling}}function aa(e,n){var t=!(2<arguments.length&&void 0!==arguments[2])||arguments[2];U(!0);try{rl(n),t&&Sl(n),al(e,n.alternate,n,!1),t&&dl(e,n,0,null,!1)}finally{U(!1)}}function oa(e){var n=!0;24&e.current.mode||(n=!1),la(e,e.current,n)}function ua(e){if((sf&Zc)===Xc){var n=e.tag;if(3===n||1===n||0===n||11===n||14===n||15===n){if(n=c(e)||"ReactComponent",null!==ad){if(ad.has(n))return;ad.add(n)}else ad=new Set([n]);y(e,(function(){console.error("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.")}))}}}function ia(e,n){_u&&e.memoizedUpdaters.forEach((function(t){T(e,t,n)}))}function sa(e,n,t,r){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null,this.actualDuration=-0,this.actualStartTime=-1.1,this.treeBaseDuration=this.selfBaseDuration=-0,this._debugOwner=this._debugInfo=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,id||"function"!=typeof Object.preventExtensions||Object.preventExtensions(this)}function ca(e){return!(!(e=e.prototype)||!e.isReactComponent)}function fa(e,n){var r=e.alternate;switch(null===r?((r=t(e.tag,n,e.key,e.mode)).elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r._debugOwner=e._debugOwner,r._debugHookTypes=e._debugHookTypes,r.alternate=e,e.alternate=r):(r.pendingProps=n,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null,r.actualDuration=-0,r.actualStartTime=-1.1),r.flags=31457280&e.flags,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,n=e.dependencies,r.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext,_debugThenableState:n._debugThenableState},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r.refCleanup=e.refCleanup,r.selfBaseDuration=e.selfBaseDuration,r.treeBaseDuration=e.treeBaseDuration,r._debugInfo=e._debugInfo,r._debugNeedsRemount=e._debugNeedsRemount,r.tag){case 0:case 15:case 1:case 11:r.type=e.type}return r}function da(e,n){e.flags&=31457282;var t=e.alternate;return null===t?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null,e.selfBaseDuration=0,e.treeBaseDuration=0):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,n=t.dependencies,e.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext,_debugThenableState:n._debugThenableState},e.selfBaseDuration=t.selfBaseDuration,e.treeBaseDuration=t.treeBaseDuration),e}function pa(e,n,r,l,a,o){var u=0,i=e;if("function"==typeof e)ca(e)&&(u=1);else if("string"==typeof e)u=5;else e:switch(e){case Ia:return ha(r.children,a,o,n);case Fa:u=8,a|=24;break;case Ma:return l=a,"string"!=typeof(e=r).id&&console.error('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.',typeof e.id),(n=t(12,e,n,2|l)).elementType=Ma,n.lanes=o,n.stateNode={effectDuration:0,passiveEffectDuration:0},n;case Qa:return(n=t(13,r,n,a)).elementType=Qa,n.lanes=o,n;case Oa:return(n=t(19,r,n,a)).elementType=Oa,n.lanes=o,n;case $a:return ga(r,a,o,n);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case Wa:case ja:u=10;break e;case Ha:u=9;break e;case Aa:u=11;break e;case Ba:u=14;break e;case Va:u=16,i=null;break e}i="",(void 0===e||"object"==typeof e&&null!==e&&0===Object.keys(e).length)&&(i+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports."),null===e?r="null":lo(e)?r="array":void 0!==e&&e.$$typeof===Ua?(r="<"+(s(e.type)||"Unknown")+" />",i=" Did you accidentally export a JSX literal instead of a component?"):r=typeof e,(u=l?"number"==typeof l.tag?c(l):"string"==typeof l.name?l.name:null:null)&&(i+="\n\nCheck the render method of `"+u+"`."),u=29,r=Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+r+"."+i),i=null}return(n=t(u,r,n,a)).elementType=e,n.type=i,n.lanes=o,n._debugOwner=l,n}function ma(e,n,t){return(n=pa(e.type,e.key,e.props,e._owner,n,t))._debugOwner=e._owner,n}function ha(e,n,r,l){return(e=t(7,e,l,n)).lanes=r,e}function ga(e,n,r,l){(e=t(22,e,l,n)).elementType=$a,e.lanes=r;var a={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var e=a,n=e._current;if(null===n)throw Error("Calling Offscreen.detach before instance handle has been set.");if(0==(2&e._pendingVisibility)){var t=$(n,2);null!==t&&(e._pendingVisibility|=2,Cl(t,n,2))}},attach:function(){var e=a,n=e._current;if(null===n)throw Error("Calling Offscreen.detach before instance handle has been set.");if(0!=(2&e._pendingVisibility)){var t=$(n,2);null!==t&&(e._pendingVisibility&=-3,Cl(t,n,2))}}};return e.stateNode=a,e}function ya(e,n,r){return(e=t(6,e,null,n)).lanes=r,e}function ba(e,n,r){return(n=t(4,null!==e.children?e.children:[],e.key,n)).lanes=r,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function va(e,n,t,r,l,a,o,u){for(this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=bo,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=E(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=E(0),this.hiddenUpdates=E(null),this.identifierPrefix=r,this.onUncaughtError=l,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map,this.passiveEffectDuration=this.effectDuration=-0,this.memoizedUpdaters=new Set,e=this.pendingUpdatersLaneMap=[],n=0;31>n;n++)e.push(new Set);this._debugRootType=t?"hydrateRoot()":"createRoot()"}function Sa(e,n,t,r,l,a){l=function(e){return e?e=su:su}(l),null===r.context?r.context=l:r.pendingContext=l,ro&&null!==to&&!cd&&(cd=!0,console.error("Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.\n\nCheck the render method of %s.",c(to)||"Unknown")),(r=ge(n)).payload={element:t},null!==(a=void 0===a?null:a)&&("function"!=typeof a&&console.error("Expected the last optional `callback` argument to be a function. Instead received: %s.",a),r.callback=a),null!==(t=ye(e,r,n))&&(Cl(t,e,n),be(t,e,n))}var ka,wa,xa,za,Ca,Ea,Pa,_a={},Ra=e,Ta=u,Na=Object.assign,La=Symbol.for("react.element"),Ua=Symbol.for("react.transitional.element"),Da=Symbol.for("react.portal"),Ia=Symbol.for("react.fragment"),Fa=Symbol.for("react.strict_mode"),Ma=Symbol.for("react.profiler"),Wa=Symbol.for("react.provider"),Ha=Symbol.for("react.consumer"),ja=Symbol.for("react.context"),Aa=Symbol.for("react.forward_ref"),Qa=Symbol.for("react.suspense"),Oa=Symbol.for("react.suspense_list"),Ba=Symbol.for("react.memo"),Va=Symbol.for("react.lazy"),$a=Symbol.for("react.offscreen"),qa=Symbol.for("react.memo_cache_sentinel"),Ya=Symbol.iterator,Ga=Symbol.for("react.client.reference"),Ja=Ra.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Ka=0;f.__reactDisabledLog=!0;var Xa,Za,eo=!1,no=new("function"==typeof WeakMap?WeakMap:Map),to=null,ro=!1,lo=Array.isArray,ao=n.getPublicInstance,oo=n.getRootHostContext,uo=n.getChildHostContext,io=n.prepareForCommit,so=n.resetAfterCommit,co=n.createInstance,fo=n.appendInitialChild,po=n.finalizeInitialChildren,mo=n.shouldSetTextContent,ho=n.createTextInstance,go=null,yo=null,bo=n.noTimeout,vo=!0,So=null,ko=null,wo=n.setCurrentUpdatePriority,xo=n.getCurrentUpdatePriority,zo=n.resolveUpdatePriority,Co=n.shouldAttemptEagerTransition,Eo=null;n.requestPostPaintCallback;var Po=n.maySuspendCommit,_o=null,Ro=null,To=null,No=null,Lo=null,Uo=null,Do=null,Io=n.appendChild,Fo=n.appendChildToContainer,Mo=n.commitTextUpdate,Wo=null,Ho=n.commitUpdate,jo=n.insertBefore,Ao=null,Qo=n.removeChild,Oo=n.removeChildFromContainer,Bo=n.resetTextContent,Vo=null,$o=null,qo=null,Yo=null,Go=n.clearContainer,Jo=null,Ko=null,Xo=null,Zo=null,eu=null,nu=null,tu=null,ru=null,lu=null,au=null,ou=[],uu=[],iu=-1,su={};Object.freeze(su);var cu=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(fu(e)/du|0)|0},fu=Math.log,du=Math.LN2,pu=128,mu=4194304,hu=Ta.unstable_scheduleCallback,gu=Ta.unstable_cancelCallback,yu=Ta.unstable_shouldYield,bu=Ta.unstable_requestPaint,vu=Ta.unstable_now,Su=Ta.unstable_ImmediatePriority,ku=Ta.unstable_UserBlockingPriority,wu=Ta.unstable_NormalPriority,xu=Ta.unstable_IdlePriority,zu=Ta.log,Cu=Ta.unstable_setDisableYieldValue,Eu=null,Pu=null,_u="undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__,Ru="function"==typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e==1/n)||e!=e&&n!=n},Tu=new WeakMap,Nu=[],Lu=0,Uu=null,Du=0,Iu=[],Fu=0,Mu=null,Wu=1,Hu="",ju=b(null),Au=b(null),Qu=b(null),Ou=b(null),Bu=!1,Vu=null,$u=0,qu=[],Yu=0,Gu=0,Ju=Ta.unstable_now,Ku=-0,Xu=-1.1,Zu=-0,ei=!1,ni=!1,ti=null,ri=null,li=!1,ai=!1,oi=!1,ui=!1,ii=0,si={},ci=null,fi=0,di=0,pi=null,mi=0,hi=1,gi=2,yi=3,bi=!1,vi=!1,Si=null,ki=!1,wi=Object.prototype.hasOwnProperty,xi={recordUnsafeLifecycleWarnings:function(){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}},zi=[],Ci=[],Ei=[],Pi=[],_i=[],Ri=[],Ti=new Set;xi.recordUnsafeLifecycleWarnings=function(e,n){Ti.has(e.type)||("function"==typeof n.componentWillMount&&!0!==n.componentWillMount.__suppressDeprecationWarning&&zi.push(e),8&e.mode&&"function"==typeof n.UNSAFE_componentWillMount&&Ci.push(e),"function"==typeof n.componentWillReceiveProps&&!0!==n.componentWillReceiveProps.__suppressDeprecationWarning&&Ei.push(e),8&e.mode&&"function"==typeof n.UNSAFE_componentWillReceiveProps&&Pi.push(e),"function"==typeof n.componentWillUpdate&&!0!==n.componentWillUpdate.__suppressDeprecationWarning&&_i.push(e),8&e.mode&&"function"==typeof n.UNSAFE_componentWillUpdate&&Ri.push(e))},xi.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;0<zi.length&&(zi.forEach((function(n){e.add(c(n)||"Component"),Ti.add(n.type)})),zi=[]);var n=new Set;0<Ci.length&&(Ci.forEach((function(e){n.add(c(e)||"Component"),Ti.add(e.type)})),Ci=[]);var t=new Set;0<Ei.length&&(Ei.forEach((function(e){t.add(c(e)||"Component"),Ti.add(e.type)})),Ei=[]);var r=new Set;0<Pi.length&&(Pi.forEach((function(e){r.add(c(e)||"Component"),Ti.add(e.type)})),Pi=[]);var l=new Set;0<_i.length&&(_i.forEach((function(e){l.add(c(e)||"Component"),Ti.add(e.type)})),_i=[]);var a=new Set;if(0<Ri.length&&(Ri.forEach((function(e){a.add(c(e)||"Component"),Ti.add(e.type)})),Ri=[]),0<n.size){var u=o(n);console.error("Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move code with side effects to componentDidMount, and set initial state in the constructor.\n\nPlease update the following components: %s",u)}0<r.size&&(u=o(r),console.error("Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state\n\nPlease update the following components: %s",u)),0<a.size&&(u=o(a),console.error("Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n\nPlease update the following components: %s",u)),0<e.size&&(u=o(e),console.warn("componentWillMount has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move code with side effects to componentDidMount, and set initial state in the constructor.\n* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s",u)),0<t.size&&(u=o(t),console.warn("componentWillReceiveProps has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state\n* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s",u)),0<l.size&&(u=o(l),console.warn("componentWillUpdate has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s",u))};var Ni=new Map,Li=new Set;xi.recordLegacyContextWarning=function(e,n){for(var t=null,r=e;null!==r;)8&r.mode&&(t=r),r=r.return;null===t?console.error("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue."):!Li.has(e.type)&&(r=Ni.get(t),null!=e.type.contextTypes||null!=e.type.childContextTypes||null!==n&&"function"==typeof n.getChildContext)&&(void 0===r&&(r=[],Ni.set(t,r)),r.push(e))},xi.flushLegacyContextWarning=function(){Ni.forEach((function(e){if(0!==e.length){var n=e[0],t=new Set;e.forEach((function(e){t.add(c(e)||"Component"),Li.add(e.type)}));var r=o(t);y(n,(function(){console.error("Legacy context API has been detected within a strict-mode tree.\n\nThe old API will be supported in all 16.x releases, but applications using it should migrate to the new version.\n\nPlease update the following components: %s\n\nLearn more about this warning here: https://react.dev/link/legacy-context",r)}))}}))},xi.discardPendingWarnings=function(){zi=[],Ci=[],Ei=[],Pi=[],_i=[],Ri=[],Ni=new Map};var Ui,Di=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`"),Ii=Error("Suspense Exception: This is not a real error, and should not leak into userspace. If you're seeing this, it's likely a bug in React."),Fi={then:function(){console.error('Internal React error: A listener was unexpectedly attached to a "noop" thenable. This is a bug in React. Please file an issue.')}},Mi=null,Wi=!1,Hi={"react-stack-bottom-frame":function(e,n,t){var r=ro;ro=!0;try{return e(n,t)}finally{ro=r}}},ji=Hi["react-stack-bottom-frame"].bind(Hi),Ai={"react-stack-bottom-frame":function(e){var n=ro;ro=!0;try{return e.render()}finally{ro=n}}},Qi=Ai["react-stack-bottom-frame"].bind(Ai),Oi={"react-stack-bottom-frame":function(e,n){try{n.componentDidMount()}catch(n){Xl(e,e.return,n)}}},Bi=Oi["react-stack-bottom-frame"].bind(Oi),Vi={"react-stack-bottom-frame":function(e,n,t,r,l){try{n.componentDidUpdate(t,r,l)}catch(n){Xl(e,e.return,n)}}},$i=Vi["react-stack-bottom-frame"].bind(Vi),qi={"react-stack-bottom-frame":function(e,n){var t=n.stack;e.componentDidCatch(n.value,{componentStack:null!==t?t:""})}},Yi=qi["react-stack-bottom-frame"].bind(qi),Gi={"react-stack-bottom-frame":function(e,n,t){try{t.componentWillUnmount()}catch(t){Xl(e,n,t)}}},Ji=Gi["react-stack-bottom-frame"].bind(Gi),Ki={"react-stack-bottom-frame":function(e){var n=e.create;return e=e.inst,n=n(),e.destroy=n}},Xi=Ki["react-stack-bottom-frame"].bind(Ki),Zi={"react-stack-bottom-frame":function(e,n,t){try{t()}catch(t){Xl(e,n,t)}}},es=Zi["react-stack-bottom-frame"].bind(Zi),ns={"react-stack-bottom-frame":function(e){return(0,e._init)(e._payload)}},ts=ns["react-stack-bottom-frame"].bind(ns),rs=null,ls=0,as=null,os=Ui=!1,us={},is={},ss={};a=function(e,n,t){if(null!==t&&"object"==typeof t&&t._store&&(!t._store.validated&&null==t.key||2===t._store.validated)){if("object"!=typeof t._store)throw Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");t._store.validated=1;var r=c(e),l=r||"null";if(!us[l]){us[l]=!0,t=t._owner,e=e._debugOwner;var a="";e&&"number"==typeof e.tag&&(l=c(e))&&(a="\n\nCheck the render method of `"+l+"`."),a||r&&(a="\n\nCheck the top-level render call using <"+r+">.");var o="";null!=t&&e!==t&&(r=null,"number"==typeof t.tag?r=c(t):"string"==typeof t.name&&(r=t.name),r&&(o=" It was passed a child from "+r+".")),y(n,(function(){console.error('Each child in a list should have a unique "key" prop.%s%s See https://react.dev/link/warning-keys for more information.',a,o)}))}}};var cs,fs=We(!0),ds=We(!1),ps=b(null),ms=b(0),hs=b(null),gs=null,ys=1,bs=2,vs=b(0),Ss=0,ks=1,ws=2,xs=4,zs=8,Cs=new Set,Es=new Set,Ps=new Set,_s=new Set,Rs=0,Ts=null,Ns=null,Ls=null,Us=!1,Ds=!1,Is=!1,Fs=0,Ms=0,Ws=null,Hs=0,js=25,As=null,Qs=null,Os=-1,Bs=!1,Vs=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}},$s={readContext:sr,use:cn,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useLayoutEffect:Ke,useInsertionEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useSyncExternalStore:Ke,useId:Ke};$s.useCacheRefresh=Ke,$s.useMemoCache=Ke,$s.useHostTransitionStatus=Ke,$s.useFormState=Ke,$s.useActionState=Ke,$s.useOptimistic=Ke;var qs=null,Ys=null,Gs=null,Js=null,Ks=null,Xs=null,Zs=null;(qs={readContext:function(e){return sr(e)},use:cn,useCallback:function(e,n){return As="useCallback",qe(),Ge(n),Kn(e,n)},useContext:function(e){return As="useContext",qe(),sr(e)},useEffect:function(e,n){return As="useEffect",qe(),Ge(n),$n(e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",qe(),Ge(t),Gn(e,n,t)},useInsertionEffect:function(e,n){As="useInsertionEffect",qe(),Ge(n),Bn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",qe(),Ge(n),qn(e,n)},useMemo:function(e,n){As="useMemo",qe(),Ge(n);var t=Ja.H;Ja.H=Ks;try{return Zn(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",qe();var r=Ja.H;Ja.H=Ks;try{return pn(e,n,t)}finally{Ja.H=r}},useRef:function(e){return As="useRef",qe(),On(e)},useState:function(e){As="useState",qe();var n=Ja.H;Ja.H=Ks;try{return Cn(e)}finally{Ja.H=n}},useDebugValue:function(){As="useDebugValue",qe()},useDeferredValue:function(e,n){return As="useDeferredValue",qe(),nt(e,n)},useTransition:function(){return As="useTransition",qe(),ut()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",qe(),yn(e,n,t)},useId:function(){return As="useId",qe(),ft()},useCacheRefresh:function(){return As="useCacheRefresh",qe(),dt()}}).useMemoCache=fn,qs.useHostTransitionStatus=ct,qs.useFormState=function(e,n){return As="useFormState",qe(),Je(),Mn(e,n)},qs.useActionState=function(e,n){return As="useActionState",qe(),Mn(e,n)},qs.useOptimistic=function(e){return As="useOptimistic",qe(),En(e)},(Ys={readContext:function(e){return sr(e)},use:cn,useCallback:function(e,n){return As="useCallback",Ye(),Kn(e,n)},useContext:function(e){return As="useContext",Ye(),sr(e)},useEffect:function(e,n){return As="useEffect",Ye(),$n(e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",Ye(),Gn(e,n,t)},useInsertionEffect:function(e,n){As="useInsertionEffect",Ye(),Bn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",Ye(),qn(e,n)},useMemo:function(e,n){As="useMemo",Ye();var t=Ja.H;Ja.H=Ks;try{return Zn(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",Ye();var r=Ja.H;Ja.H=Ks;try{return pn(e,n,t)}finally{Ja.H=r}},useRef:function(e){return As="useRef",Ye(),On(e)},useState:function(e){As="useState",Ye();var n=Ja.H;Ja.H=Ks;try{return Cn(e)}finally{Ja.H=n}},useDebugValue:function(){As="useDebugValue",Ye()},useDeferredValue:function(e,n){return As="useDeferredValue",Ye(),nt(e,n)},useTransition:function(){return As="useTransition",Ye(),ut()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",Ye(),yn(e,n,t)},useId:function(){return As="useId",Ye(),ft()},useCacheRefresh:function(){return As="useCacheRefresh",Ye(),dt()}}).useMemoCache=fn,Ys.useHostTransitionStatus=ct,Ys.useFormState=function(e,n){return As="useFormState",Ye(),Je(),Mn(e,n)},Ys.useActionState=function(e,n){return As="useActionState",Ye(),Mn(e,n)},Ys.useOptimistic=function(e){return As="useOptimistic",Ye(),En(e)},(Gs={readContext:function(e){return sr(e)},use:cn,useCallback:function(e,n){return As="useCallback",Ye(),Xn(e,n)},useContext:function(e){return As="useContext",Ye(),sr(e)},useEffect:function(e,n){As="useEffect",Ye(),Vn(2048,zs,e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",Ye(),Jn(e,n,t)},useInsertionEffect:function(e,n){return As="useInsertionEffect",Ye(),Vn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",Ye(),Vn(4,xs,e,n)},useMemo:function(e,n){As="useMemo",Ye();var t=Ja.H;Ja.H=Xs;try{return et(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",Ye();var r=Ja.H;Ja.H=Xs;try{return mn(e)}finally{Ja.H=r}},useRef:function(){return As="useRef",Ye(),un().memoizedState},useState:function(){As="useState",Ye();var e=Ja.H;Ja.H=Xs;try{return mn(dn)}finally{Ja.H=e}},useDebugValue:function(){As="useDebugValue",Ye()},useDeferredValue:function(e,n){return As="useDeferredValue",Ye(),tt(e,n)},useTransition:function(){return As="useTransition",Ye(),it()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",Ye(),bn(e,n,t)},useId:function(){return As="useId",Ye(),un().memoizedState},useCacheRefresh:function(){return As="useCacheRefresh",Ye(),un().memoizedState}}).useMemoCache=fn,Gs.useHostTransitionStatus=ct,Gs.useFormState=function(e){return As="useFormState",Ye(),Je(),Wn(e)},Gs.useActionState=function(e){return As="useActionState",Ye(),Wn(e)},Gs.useOptimistic=function(e,n){return As="useOptimistic",Ye(),Pn(e,n)},(Js={readContext:function(e){return sr(e)},use:cn,useCallback:function(e,n){return As="useCallback",Ye(),Xn(e,n)},useContext:function(e){return As="useContext",Ye(),sr(e)},useEffect:function(e,n){As="useEffect",Ye(),Vn(2048,zs,e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",Ye(),Jn(e,n,t)},useInsertionEffect:function(e,n){return As="useInsertionEffect",Ye(),Vn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",Ye(),Vn(4,xs,e,n)},useMemo:function(e,n){As="useMemo",Ye();var t=Ja.H;Ja.H=Zs;try{return et(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",Ye();var r=Ja.H;Ja.H=Zs;try{return gn(e)}finally{Ja.H=r}},useRef:function(){return As="useRef",Ye(),un().memoizedState},useState:function(){As="useState",Ye();var e=Ja.H;Ja.H=Zs;try{return gn(dn)}finally{Ja.H=e}},useDebugValue:function(){As="useDebugValue",Ye()},useDeferredValue:function(e,n){return As="useDeferredValue",Ye(),rt(e,n)},useTransition:function(){return As="useTransition",Ye(),st()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",Ye(),bn(e,n,t)},useId:function(){return As="useId",Ye(),un().memoizedState},useCacheRefresh:function(){return As="useCacheRefresh",Ye(),un().memoizedState}}).useMemoCache=fn,Js.useHostTransitionStatus=ct,Js.useFormState=function(e){return As="useFormState",Ye(),Je(),An(e)},Js.useActionState=function(e){return As="useActionState",Ye(),An(e)},Js.useOptimistic=function(e,n){return As="useOptimistic",Ye(),Rn(e,n)},(Ks={readContext:function(e){return l(),sr(e)},use:function(e){return r(),cn(e)},useCallback:function(e,n){return As="useCallback",r(),qe(),Kn(e,n)},useContext:function(e){return As="useContext",r(),qe(),sr(e)},useEffect:function(e,n){return As="useEffect",r(),qe(),$n(e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",r(),qe(),Gn(e,n,t)},useInsertionEffect:function(e,n){As="useInsertionEffect",r(),qe(),Bn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",r(),qe(),qn(e,n)},useMemo:function(e,n){As="useMemo",r(),qe();var t=Ja.H;Ja.H=Ks;try{return Zn(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",r(),qe();var l=Ja.H;Ja.H=Ks;try{return pn(e,n,t)}finally{Ja.H=l}},useRef:function(e){return As="useRef",r(),qe(),On(e)},useState:function(e){As="useState",r(),qe();var n=Ja.H;Ja.H=Ks;try{return Cn(e)}finally{Ja.H=n}},useDebugValue:function(){As="useDebugValue",r(),qe()},useDeferredValue:function(e,n){return As="useDeferredValue",r(),qe(),nt(e,n)},useTransition:function(){return As="useTransition",r(),qe(),ut()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",r(),qe(),yn(e,n,t)},useId:function(){return As="useId",r(),qe(),ft()},useCacheRefresh:function(){return As="useCacheRefresh",qe(),dt()},useMemoCache:function(e){return r(),fn(e)}}).useHostTransitionStatus=ct,Ks.useFormState=function(e,n){return As="useFormState",r(),qe(),Mn(e,n)},Ks.useActionState=function(e,n){return As="useActionState",r(),qe(),Mn(e,n)},Ks.useOptimistic=function(e){return As="useOptimistic",r(),qe(),En(e)},(Xs={readContext:function(e){return l(),sr(e)},use:function(e){return r(),cn(e)},useCallback:function(e,n){return As="useCallback",r(),Ye(),Xn(e,n)},useContext:function(e){return As="useContext",r(),Ye(),sr(e)},useEffect:function(e,n){As="useEffect",r(),Ye(),Vn(2048,zs,e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",r(),Ye(),Jn(e,n,t)},useInsertionEffect:function(e,n){return As="useInsertionEffect",r(),Ye(),Vn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",r(),Ye(),Vn(4,xs,e,n)},useMemo:function(e,n){As="useMemo",r(),Ye();var t=Ja.H;Ja.H=Xs;try{return et(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",r(),Ye();var l=Ja.H;Ja.H=Xs;try{return mn(e)}finally{Ja.H=l}},useRef:function(){return As="useRef",r(),Ye(),un().memoizedState},useState:function(){As="useState",r(),Ye();var e=Ja.H;Ja.H=Xs;try{return mn(dn)}finally{Ja.H=e}},useDebugValue:function(){As="useDebugValue",r(),Ye()},useDeferredValue:function(e,n){return As="useDeferredValue",r(),Ye(),tt(e,n)},useTransition:function(){return As="useTransition",r(),Ye(),it()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",r(),Ye(),bn(e,n,t)},useId:function(){return As="useId",r(),Ye(),un().memoizedState},useCacheRefresh:function(){return As="useCacheRefresh",Ye(),un().memoizedState},useMemoCache:function(e){return r(),fn(e)}}).useHostTransitionStatus=ct,Xs.useFormState=function(e){return As="useFormState",r(),Ye(),Wn(e)},Xs.useActionState=function(e){return As="useActionState",r(),Ye(),Wn(e)},Xs.useOptimistic=function(e,n){return As="useOptimistic",r(),Ye(),Pn(e,n)},(Zs={readContext:function(e){return l(),sr(e)},use:function(e){return r(),cn(e)},useCallback:function(e,n){return As="useCallback",r(),Ye(),Xn(e,n)},useContext:function(e){return As="useContext",r(),Ye(),sr(e)},useEffect:function(e,n){As="useEffect",r(),Ye(),Vn(2048,zs,e,n)},useImperativeHandle:function(e,n,t){return As="useImperativeHandle",r(),Ye(),Jn(e,n,t)},useInsertionEffect:function(e,n){return As="useInsertionEffect",r(),Ye(),Vn(4,ws,e,n)},useLayoutEffect:function(e,n){return As="useLayoutEffect",r(),Ye(),Vn(4,xs,e,n)},useMemo:function(e,n){As="useMemo",r(),Ye();var t=Ja.H;Ja.H=Xs;try{return et(e,n)}finally{Ja.H=t}},useReducer:function(e,n,t){As="useReducer",r(),Ye();var l=Ja.H;Ja.H=Xs;try{return gn(e)}finally{Ja.H=l}},useRef:function(){return As="useRef",r(),Ye(),un().memoizedState},useState:function(){As="useState",r(),Ye();var e=Ja.H;Ja.H=Xs;try{return gn(dn)}finally{Ja.H=e}},useDebugValue:function(){As="useDebugValue",r(),Ye()},useDeferredValue:function(e,n){return As="useDeferredValue",r(),Ye(),rt(e,n)},useTransition:function(){return As="useTransition",r(),Ye(),st()},useSyncExternalStore:function(e,n,t){return As="useSyncExternalStore",r(),Ye(),bn(e,n,t)},useId:function(){return As="useId",r(),Ye(),un().memoizedState},useCacheRefresh:function(){return As="useCacheRefresh",Ye(),un().memoizedState},useMemoCache:function(e){return r(),fn(e)}}).useHostTransitionStatus=ct,Zs.useFormState=function(e){return As="useFormState",r(),Ye(),An(e)},Zs.useActionState=function(e){return As="useActionState",r(),Ye(),An(e)},Zs.useOptimistic=function(e,n){return As="useOptimistic",r(),Ye(),Rn(e,n)};var ec={},nc=new Set,tc=new Set,rc=new Set,lc=new Set,ac=new Set,oc=new Set,uc=new Set,ic=new Set,sc=new Set,cc=new Set;Object.freeze(ec);var fc={isMounted:function(e){var n=to;if(null!==n&&ro&&1===n.tag){var t=n.stateNode;t._warnedAboutRefsInRender||console.error("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",c(n)||"A component"),t._warnedAboutRefsInRender=!0}return!!(e=e._reactInternals)&&function(e){var n=e,t=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do{0!=(4098&(n=e).flags)&&(t=n.return),e=n.return}while(e)}return 3===n.tag?t:null}(e)===e},enqueueSetState:function(e,n,t){var r=xl(e=e._reactInternals),l=ge(r);l.payload=n,null!=t&&(kt(t),l.callback=t),null!==(n=ye(e,l,r))&&(Cl(n,e,r),be(n,e,r))},enqueueReplaceState:function(e,n,t){var r=xl(e=e._reactInternals),l=ge(r);l.tag=hi,l.payload=n,null!=t&&(kt(t),l.callback=t),null!==(n=ye(e,l,r))&&(Cl(n,e,r),be(n,e,r))},enqueueForceUpdate:function(e,n){var t=xl(e=e._reactInternals),r=ge(t);r.tag=gi,null!=n&&(kt(n),r.callback=n),null!==(n=ye(e,r,t))&&(Cl(n,e,t),be(n,e,t))}};"function"==typeof reportError&&reportError;var dc=Error("This is not a real error. It's an implementation detail of React's selective hydration feature. If this leaks into userspace, it's a bug in React. Please file an issue."),pc=!1,mc={},hc={},gc={},yc={},bc=!1,vc={},Sc={},kc={dehydrated:null,treeContext:null,retryLane:0},wc=!1,xc=b(null),zc=b(null),Cc={},Ec=null,Pc=null,_c=null,Rc=!1,Tc="undefined"!=typeof AbortController?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(n,t){e.push(t)}};this.abort=function(){n.aborted=!0,e.forEach((function(e){return e()}))}},Nc=Ta.unstable_scheduleCallback,Lc=Ta.unstable_NormalPriority,Uc={$$typeof:ja,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0,_currentRenderer:null,_currentRenderer2:null},Dc=Ja.S;Ja.S=function(e,n){"object"==typeof n&&null!==n&&"function"==typeof n.then&&function(e,n){if(null===ci){var t=ci=[];fi=0,di=de(),pi={status:"pending",value:void 0,then:function(e){t.push(e)}}}fi++,n.then(pe,pe)}(0,n),null!==Dc&&Dc(e,n)};var Ic=b(null),Fc=null;Fc=new Set;var Mc=!1,Wc=!1,Hc=!1,jc="function"==typeof WeakSet?WeakSet:Set,Ac=null,Qc=null,Oc=null,Bc=!1,Vc=null,$c=!1,qc=null,Yc=8192,Gc={getCacheForType:function(e){var n=sr(Uc),t=n.data.get(e);return void 0===t&&(t=e(),n.data.set(e,t)),t},getOwner:function(){return to}};if("function"==typeof Symbol&&Symbol.for){var Jc=Symbol.for;Jc("selector.component"),Jc("selector.has_pseudo_class"),Jc("selector.role"),Jc("selector.test_id"),Jc("selector.text")}var Kc="function"==typeof WeakMap?WeakMap:Map,Xc=0,Zc=2,ef=4,nf=0,tf=1,rf=2,lf=3,af=4,of=5,uf=6,sf=Xc,cf=null,ff=null,df=0,pf=0,mf=1,hf=2,gf=3,yf=4,bf=5,vf=6,Sf=7,kf=8,wf=pf,xf=null,zf=!1,Cf=!1,Ef=0,Pf=nf,_f=0,Rf=0,Tf=0,Nf=0,Lf=0,Uf=null,Df=null,If=!1,Ff=0,Mf=300,Wf=1/0,Hf=500,jf=null,Af=null,Qf=!1,Of=null,Bf=0,Vf=0,$f=null,qf=50,Yf=0,Gf=null,Jf=!1,Kf=!1,Xf=50,Zf=0,ed=null,nd=!1,td=0,rd=1,ld=2,ad=null,od=!1,ud=new Set,id=!1;try{var sd=Object.preventExtensions({});new Map([[sd,null]]),new Set([sd])}catch(e){id=!0}var cd=!1;return _a.createContainer=function(e,n,r,l,a,o,u,i,s,c){return function(e,n,r,l,a,o,u,i,s,c,f,d){return e=new va(e,n,r,u,i,s,c,null),n=1,!0===o&&(n|=24),_u&&(n|=2),o=t(3,null,null,n),e.current=o,o.stateNode=e,pr(n=dr()),e.pooledCache=n,pr(n),o.memoizedState={element:l,isDehydrated:r,cache:n},me(o),e}(e,n,!1,null,0,l,o,u,i,s)},_a.flushSyncWork=function(){return(sf&(Zc|ef))!==Xc||(ae(0),!1)},_a.updateContainer=function(e,n,t,r){var l=n.current,a=xl(l);return Sa(l,a,e,n,t,r),a},_a.updateContainerSync=function(e,n,t,r){return 0===n.tag&&Jl(),Sa(n.current,2,e,n,t,r),2},_a},n.exports.default=n.exports,Object.defineProperty(n.exports,"__esModule",{value:!0}))),c.exports;var n}();var f,d=t(a.exports),p={exports:{}},m={};var h,g={};
/**
 * @license React
 * react-reconciler-constants.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */"production"===process.env.NODE_ENV?p.exports=(f||(f=1,m.ConcurrentRoot=1,m.ContinuousEventPriority=8,m.DefaultEventPriority=32,m.DiscreteEventPriority=2,m.IdleEventPriority=268435456,m.LegacyRoot=0,m.NoEventPriority=0),m):p.exports=(h||(h=1,"production"!==process.env.NODE_ENV&&(g.ConcurrentRoot=1,g.ContinuousEventPriority=8,g.DefaultEventPriority=32,g.DiscreteEventPriority=2,g.IdleEventPriority=268435456,g.LegacyRoot=0,g.NoEventPriority=0)),g);var y=p.exports;const b=(e,n)=>{const t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(let r=0;r<t.length;r+=1){const l=t[r];if("render"===l&&!e[l]!=!n[l])return!1;if("children"!==l&&e[l]!==n[l]){if("object"==typeof e[l]&&"object"==typeof n[l]&&b(e[l],n[l]))continue;return!1}if("children"===l&&("string"==typeof e[l]||"string"==typeof n[l]))return e[l]===n[l]}return!0},v={},S=console.error,k=({appendChild:e,appendChildToContainer:n,commitTextUpdate:t,commitUpdate:r,createInstance:l,createTextInstance:a,insertBefore:o,removeChild:u,removeChildFromContainer:i,resetAfterCommit:s})=>{const c=d({appendChild:e,appendChildToContainer:n,appendInitialChild:e,createInstance:l,createTextInstance:a,insertBefore:o,commitUpdate:(e,n,t,l)=>{b(t,l)||r(e,null,n,t,l)},commitTextUpdate:t,removeChild:u,removeChildFromContainer:i,resetAfterCommit:s,noTimeout:-1,shouldSetTextContent:()=>!1,finalizeInitialChildren:()=>!1,getPublicInstance:e=>e,getRootHostContext:()=>v,getChildHostContext:()=>v,prepareForCommit(){},clearContainer(){},resetTextContent(){},getCurrentUpdatePriority:()=>y.DefaultEventPriority,maySuspendCommit:()=>!1,requestPostPaintCallback:()=>{},resolveUpdatePriority:()=>y.DefaultEventPriority,setCurrentUpdatePriority:()=>{},shouldAttemptEagerTransition:()=>!1});return{createContainer:e=>c.createContainer(e,y.ConcurrentRoot,null,!1,null,"",S,S,S,null),updateContainer:(e,n,t,r)=>{c.updateContainerSync(e,n,t,r),c.flushSyncWork()}}};export{k as default};
