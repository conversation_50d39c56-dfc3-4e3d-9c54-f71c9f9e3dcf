{"name": "@react-pdf/render", "version": "4.3.0", "license": "MIT", "description": "A render engine for Node and the browser", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/diegomura/react-pdf#readme", "type": "module", "main": "./lib/index.js", "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/render"}, "scripts": {"test": "vitest", "build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w", "typecheck": "tsc --noEmit"}, "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/fns": "3.1.2", "@react-pdf/primitives": "^4.1.1", "@react-pdf/textkit": "^6.0.0", "@react-pdf/types": "^2.9.0", "abs-svg-path": "^0.1.1", "color-string": "^1.9.1", "normalize-svg-path": "^1.1.0", "parse-svg-path": "^0.1.2", "svg-arc-to-cubic-bezier": "^3.2.0"}, "devDependencies": {"@react-pdf/layout": "^4.4.0", "@types/abs-svg-path": "^0.1.3", "@types/color-string": "^1.5.5", "@types/pdfkit": "^0.13.9"}, "files": ["lib"]}