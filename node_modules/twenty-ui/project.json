{"name": "twenty-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/twenty-ui/src", "projectType": "library", "tags": ["scope:frontend"], "targets": {"build": {"dependsOn": ["^build", "generateBarrels"], "outputs": ["{projectRoot}/dist", "{projectRoot}/accessibility/package.json", "{projectRoot}/accessibility/dist", "{projectRoot}/components/package.json", "{projectRoot}/components/dist", "{projectRoot}/display/package.json", "{projectRoot}/display/dist", "{projectRoot}/feedback/package.json", "{projectRoot}/feedback/dist", "{projectRoot}/input/package.json", "{projectRoot}/input/dist", "{projectRoot}/json-visualizer/package.json", "{projectRoot}/json-visualizer/dist", "{projectRoot}/layout/package.json", "{projectRoot}/layout/dist", "{projectRoot}/navigation/package.json", "{projectRoot}/navigation/dist", "{projectRoot}/testing/package.json", "{projectRoot}/testing/dist", "{projectRoot}/theme/package.json", "{projectRoot}/theme/dist", "{projectRoot}/utilities/package.json", "{projectRoot}/utilities/dist"]}, "generateBarrels": {"executor": "nx:run-commands", "cache": true, "inputs": ["production", "{projectRoot}/scripts/generateBarrels.ts"], "outputs": ["{projectRoot}/src/**/*/index.ts", "{projectRoot}/package.json"], "options": {"command": "tsx {projectRoot}/scripts/generateBarrels.ts"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "rimraf {projectRoot}/dist"}}, "lint": {"options": {"lintFilePatterns": ["{projectRoot}/src/**/*.{ts,tsx,json}", "{projectRoot}/package.json"]}, "configurations": {"fix": {}}}, "fmt": {"options": {"files": "src"}, "configurations": {"fix": {}}}, "test": {}, "typecheck": {}, "storybook:build": {"configurations": {"test": {}}}, "storybook:serve:dev": {"options": {"port": 6007}}, "storybook:serve:static": {"options": {"buildTarget": "twenty-ui:storybook:build", "port": 6007}, "configurations": {"test": {}}}, "storybook:coverage": {}, "storybook:test": {"options": {"port": 6007}}, "storybook:serve-and-test:static": {"options": {"port": 6007}}}}