import { PaginationConfiguration, Paginator } from "@smithy/types";
/**
 * @internal
 *
 * Creates a paginator.
 */
export declare function createPaginator<PaginationConfigType extends PaginationConfiguration, InputType extends object, OutputType extends object>(ClientCtor: any, CommandCtor: any, inputTokenName: string, outputTokenName: string, pageSizeTokenName?: string): (config: PaginationConfigType, input: InputType, ...additionalArguments: any[]) => Paginator<OutputType>;
