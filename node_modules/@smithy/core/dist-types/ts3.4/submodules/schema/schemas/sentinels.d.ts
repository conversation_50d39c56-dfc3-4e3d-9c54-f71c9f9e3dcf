import { BigDecimalSchema, BigIntegerSchema, BlobSchema, BooleanSchema, DocumentSchema, ListSchemaModifier, MapSchemaModifier, NumericSchema, StreamingBlobSchema, StringSchema, TimestampDateTimeSchema, TimestampDefaultSchema, TimestampEpochSecondsSchema, TimestampHttpDateSchema } from "@smithy/types";
/**
 * Schema sentinel runtime values.
 * @alpha
 */
export declare const SCHEMA: {
    BLOB: BlobSchema;
    STREAMING_BLOB: StreamingBlobSchema;
    BOOLEAN: BooleanSchema;
    STRING: StringSchema;
    NUMERIC: NumericSchema;
    BIG_INTEGER: BigIntegerSchema;
    BIG_DECIMAL: BigDecimalSchema;
    DOCUMENT: DocumentSchema;
    TIMESTAMP_DEFAULT: TimestampDefaultSchema;
    TIMESTAMP_DATE_TIME: TimestampDateTimeSchema;
    TIMESTAMP_HTTP_DATE: TimestampHttpDateSchema;
    TIMESTAMP_EPOCH_SECONDS: TimestampEpochSecondsSchema;
    LIST_MODIFIER: ListSchemaModifier;
    MAP_MODIFIER: MapSchemaModifier;
};
