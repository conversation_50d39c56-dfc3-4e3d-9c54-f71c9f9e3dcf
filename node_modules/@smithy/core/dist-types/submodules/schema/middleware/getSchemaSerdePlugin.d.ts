import { DeserializeHandlerOptions, MetadataBearer, Pluggable, SerializeHandlerOptions } from "@smithy/types";
import { PreviouslyResolved } from "./schema-middleware-types";
/**
 * @internal
 */
export declare const deserializerMiddlewareOption: DeserializeHandlerOptions;
/**
 * @internal
 */
export declare const serializerMiddlewareOption: SerializeHandlerOptions;
/**
 * @internal
 */
export declare function getSchemaSerdePlugin<InputType extends object = any, OutputType extends MetadataBearer = any>(config: PreviouslyResolved): Pluggable<InputType, OutputType>;
