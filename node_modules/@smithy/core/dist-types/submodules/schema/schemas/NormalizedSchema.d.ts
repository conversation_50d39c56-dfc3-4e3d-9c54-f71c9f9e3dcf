import type { MemberSchema, NormalizedSchema as INormalizedSchema, Schema as ISchema, SchemaRef, SchemaTraits, SchemaTraitsObject } from "@smithy/types";
/**
 * Wraps both class instances, numeric sentinel values, and member schema pairs.
 * Presents a consistent interface for interacting with polymorphic schema representations.
 *
 * @alpha
 */
export declare class NormalizedSchema implements INormalizedSchema {
    private readonly ref;
    private memberName?;
    private readonly name;
    private readonly traits;
    private _isMemberSchema;
    private schema;
    private memberTraits;
    private normalizedTraits?;
    /**
     * @param ref - a polymorphic SchemaRef to be dereferenced/normalized.
     * @param memberName - optional memberName if this NormalizedSchema should be considered a member schema.
     */
    constructor(ref: SchemaRef, memberName?: string | undefined);
    /**
     * Static constructor that attempts to avoid wrapping a NormalizedSchema within another.
     */
    static of(ref: SchemaRef, memberName?: string): NormalizedSchema;
    /**
     * @param indicator - numeric indicator for preset trait combination.
     * @returns equivalent trait object.
     */
    static translateTraits(indicator: SchemaTraits): SchemaTraitsObject;
    /**
     * Creates a normalized member schema from the given schema and member name.
     */
    private static memberFrom;
    /**
     * @returns the underlying non-normalized schema.
     */
    getSchema(): Exclude<ISchema, MemberSchema | INormalizedSchema>;
    /**
     * @param withNamespace - qualifies the name.
     * @returns e.g. `MyShape` or `com.namespace#MyShape`.
     */
    getName(withNamespace?: boolean): string | undefined;
    /**
     * @returns the member name if the schema is a member schema.
     * @throws Error when the schema isn't a member schema.
     */
    getMemberName(): string;
    isMemberSchema(): boolean;
    isUnitSchema(): boolean;
    /**
     * boolean methods on this class help control flow in shape serialization and deserialization.
     */
    isListSchema(): boolean;
    isMapSchema(): boolean;
    isDocumentSchema(): boolean;
    isStructSchema(): boolean;
    isBlobSchema(): boolean;
    isTimestampSchema(): boolean;
    isStringSchema(): boolean;
    isBooleanSchema(): boolean;
    isNumericSchema(): boolean;
    isBigIntegerSchema(): boolean;
    isBigDecimalSchema(): boolean;
    isStreaming(): boolean;
    /**
     * @returns own traits merged with member traits, where member traits of the same trait key take priority.
     * This method is cached.
     */
    getMergedTraits(): SchemaTraitsObject;
    /**
     * @returns only the member traits. If the schema is not a member, this returns empty.
     */
    getMemberTraits(): SchemaTraitsObject;
    /**
     * @returns only the traits inherent to the shape or member target shape if this schema is a member.
     * If there are any member traits they are excluded.
     */
    getOwnTraits(): SchemaTraitsObject;
    /**
     * @returns the map's key's schema. Returns a dummy Document schema if this schema is a Document.
     *
     * @throws Error if the schema is not a Map or Document.
     */
    getKeySchema(): NormalizedSchema;
    /**
     * @returns the schema of the map's value or list's member.
     * Returns a dummy Document schema if this schema is a Document.
     *
     * @throws Error if the schema is not a Map, List, nor Document.
     */
    getValueSchema(): NormalizedSchema;
    /**
     * @returns the NormalizedSchema for the given member name. The returned instance will return true for `isMemberSchema()`
     * and will have the member name given.
     * @param member - which member to retrieve and wrap.
     *
     * @throws Error if member does not exist or the schema is neither a document nor structure.
     * Note that errors are assumed to be structures and unions are considered structures for these purposes.
     */
    getMemberSchema(member: string): NormalizedSchema;
    /**
     * This can be used for checking the members as a hashmap.
     * Prefer the structIterator method for iteration.
     *
     * This does NOT return list and map members, it is only for structures.
     *
     * @returns a map of member names to member schemas (normalized).
     */
    getMemberSchemas(): Record<string, NormalizedSchema>;
    /**
     * Allows iteration over members of a structure schema.
     * Each yield is a pair of the member name and member schema.
     *
     * This avoids the overhead of calling Object.entries(ns.getMemberSchemas()).
     */
    structIterator(): Generator<[string, NormalizedSchema], undefined, undefined>;
    /**
     * @returns a last-resort human-readable name for the schema if it has no other identifiers.
     */
    private getSchemaName;
}
