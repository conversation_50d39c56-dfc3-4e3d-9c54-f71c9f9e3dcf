{"version": 3, "file": "envelope.js", "sourceRoot": "", "sources": ["../../src/envelope.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,MAAM,CAAC;AAEnC;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAqB,OAAa,EAAE,KAAgB;IAAhB,sBAAA,EAAA,UAAgB;IAChF,OAAO,CAAC,OAAO,EAAE,KAAK,CAAM,CAAC;AAC/B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAAqB,QAAW,EAAE,OAAqB;IAChF,IAAA,wBAA2B,EAA1B,eAAO,EAAE,aAAiB,CAAC;IAClC,OAAO,CAAC,OAAO,WAAM,KAAK,GAAE,OAAO,GAAO,CAAC;AAC7C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAqB,QAAW;IACvD,IAAA,wBAAkC,EAA/B,qBAAmB,EAAlB,qBAAiB,EAAhB,uBAA6B,CAAC;IACzC,OAAO,eAAe,CAAC,IAAI,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,QAAkB;IAC5C,IAAA,wBAA2B,EAA1B,eAAO,EAAE,aAAiB,CAAC;IAClC,IAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAElD,gEAAgE;IAChE,0BAA0B;IAC1B,oDAAoD;IACpD,uDAAuD;IACvD,8DAA8D;IAC9D,OAAQ,KAAe,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAA0B;QACvD,IAAA,oBAA6B,EAA5B,mBAAW,EAAE,eAAe,CAAC;QACpC,mDAAmD;QACnD,IAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3F,OAAU,GAAG,UAAK,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,UAAK,iBAAmB,CAAC;IACxE,CAAC,EAAE,iBAAiB,CAAC,CAAC;AACxB,CAAC", "sourcesContent": ["import { Envelope } from '@sentry/types';\n\nimport { isPrimitive } from './is';\n\n/**\n * Creates an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function createEnvelope<E extends Envelope>(headers: E[0], items: E[1] = []): E {\n  return [headers, items] as E;\n}\n\n/**\n * Add an item to an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function addItemToEnvelope<E extends Envelope>(envelope: E, newItem: E[1][number]): E {\n  const [headers, items] = envelope;\n  return [headers, [...items, newItem]] as E;\n}\n\n/**\n * Get the type of the envelope. Grabs the type from the first envelope item.\n */\nexport function getEnvelopeType<E extends Envelope>(envelope: E): string {\n  const [, [[firstItemHeader]]] = envelope;\n  return firstItemHeader.type;\n}\n\n/**\n * Serializes an envelope into a string.\n */\nexport function serializeEnvelope(envelope: Envelope): string {\n  const [headers, items] = envelope;\n  const serializedHeaders = JSON.stringify(headers);\n\n  // Have to cast items to any here since Envelope is a union type\n  // Fixed in Typescript 4.2\n  // TODO: Remove any[] cast when we upgrade to TS 4.2\n  // https://github.com/microsoft/TypeScript/issues/36390\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (items as any[]).reduce((acc, item: typeof items[number]) => {\n    const [itemHeaders, payload] = item;\n    // We do not serialize payloads that are primitives\n    const serializedPayload = isPrimitive(payload) ? String(payload) : JSON.stringify(payload);\n    return `${acc}\\n${JSON.stringify(itemHeaders)}\\n${serializedPayload}`;\n  }, serializedHeaders);\n}\n"]}