# Convex

TypeScript/JavaScript client libraries and CLI for Convex.

Convex is the backend application platform with everything you need to build
your product.

Get started at [docs.convex.dev](https://docs.convex.dev)!

Or see [Convex demos](https://github.com/get-convex/convex-demos).

Open discussions and issues in this repository about Convex
TypeScript/JavaScript clients, the Convex CLI, or the Convex platform in
general.

Also feel free to share feature requests, product feedback, or general questions
in the [Convex Discord Community](https://convex.dev/community).

# Structure

This package includes several entry points for building apps on Convex:

- [`convex/server`](https://docs.convex.dev/api/modules/server): Helpers for
  implementing Convex functions and defining a database schema.
- [`convex/react`](https://docs.convex.dev/api/modules/react): Hooks and a
  `ConvexReactClient` for integrating Convex into React applications.
- [`convex/browser`](https://docs.convex.dev/api/modules/browser): A
  `ConvexHttpClient` for using Convex in other browser environments.
- [`convex/values`](https://docs.convex.dev/api/modules/values): Utilities for
  working with values stored in Convex.
- [`convex/react-auth0`](https://docs.convex.dev/api/modules/react_auth0): A
  React component for authenticating users with Auth0.
- [`convex/react-clerk`](https://docs.convex.dev/api/modules/react_clerk): A
  React component for authenticating users with Clerk.

This package also includes [`convex`](https://docs.convex.dev/using/cli), the
command-line interface for managing Convex projects.

# Building

`npm pack` produces a public build with internal types removed.
