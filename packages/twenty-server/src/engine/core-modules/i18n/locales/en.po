msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-29 18:14+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#. js-lingui-id: Qyrd7v
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "(System) View Fields"
msgstr "(System) View Fields"

#. js-lingui-id: 9Y3fTB
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "(System) View Filter Groups"
msgstr "(System) View Filter Groups"

#. js-lingui-id: TB2jLV
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "(System) View Filters"
msgstr "(System) View Filters"

#. js-lingui-id: Y7M7Ro
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "(System) View Groups"
msgstr "(System) View Groups"

#. js-lingui-id: 9vliLw
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "(System) View Sorts"
msgstr "(System) View Sorts"

#. js-lingui-id: 5B59WE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "(System) Views"
msgstr "(System) Views"

#. js-lingui-id: Q0ISF1
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "{fieldName} and {fieldName}Id cannot be both provided."
msgstr "{fieldName} and {fieldName}Id cannot be both provided."

#. js-lingui-id: 8haj+G
#: src/engine/metadata-modules/utils/validate-metadata-name-is-camel-case.utils.ts
msgid "{name} should be in camelCase"
msgstr "{name} should be in camelCase"

#. js-lingui-id: kZR6+h
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "A company"
msgstr "A company"

#. js-lingui-id: +aeifv
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "A connected account"
msgstr "A connected account"

#. js-lingui-id: HCoswz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "A favorite that can be accessed from the left menu"
msgstr "A favorite that can be accessed from the left menu"

#. js-lingui-id: 6w8bHl
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "A Folder of favorites"
msgstr "A Folder of favorites"

#. js-lingui-id: sSGYmf
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "A group of related messages (e.g. email thread, chat thread)"
msgstr "A group of related messages (e.g. email thread, chat thread)"

#. js-lingui-id: vZj1Xc
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "A message sent or received through a messaging channel (email, chat, etc.)"
msgstr "A message sent or received through a messaging channel (email, chat, etc.)"

#. js-lingui-id: bufuBA
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "A note"
msgstr "A note"

#. js-lingui-id: 6kUkZW
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "A note target"
msgstr "A note target"

#. js-lingui-id: Io42ej
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "A person"
msgstr "A person"

#. js-lingui-id: Q2De+v
#: src/engine/metadata-modules/role/role.service.ts
msgid "A role with this label already exists."
msgstr "A role with this label already exists."

#. js-lingui-id: mkFXEH
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "A task"
msgstr "A task"

#. js-lingui-id: hk2NzW
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "A task target"
msgstr "A task target"

#. js-lingui-id: HTSJFW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "A webhook"
msgstr "A webhook"

#. js-lingui-id: ZIN9Ga
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "A workflow"
msgstr "A workflow"

#. js-lingui-id: YwBCp8
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "A workflow automated trigger"
msgstr "A workflow automated trigger"

#. js-lingui-id: juBVjt
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "A workflow event listener"
#~ msgstr "A workflow event listener"

#. js-lingui-id: 1+xDbI
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "A workflow run"
msgstr "A workflow run"

#. js-lingui-id: N0g7rp
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "A workflow version"
msgstr "A workflow version"

#. js-lingui-id: HpZ/I5
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "A workspace member"
msgstr "A workspace member"

#. js-lingui-id: GDKKxT
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Access Token"
msgstr "Access Token"

#. js-lingui-id: pd81Qb
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Account Owner"
msgstr "Account Owner"

#. js-lingui-id: loqL/f
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account owner for companies"
msgstr "Account owner for companies"

#. js-lingui-id: HZosRi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account Owner For Companies"
msgstr "Account Owner For Companies"

#. js-lingui-id: 48AxkT
#: src/engine/workspace-manager/workspace-cleaner/services/cleaner.workspace-service.ts
msgid "Action needed to prevent workspace deletion"
msgstr "Action needed to prevent workspace deletion"

#. js-lingui-id: j0DfGR
#: src/engine/core-modules/auth/services/reset-password.service.ts
msgid "Action Needed to Reset Password"
msgstr "Action Needed to Reset Password"

#. js-lingui-id: Du6bPw
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address"
msgstr "Address"

#. js-lingui-id: JiOJxf
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address (deprecated) "
msgstr "Address (deprecated) "

#. js-lingui-id: Knl3c9
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company"
msgstr "Address of the company"

#. js-lingui-id: zJhwjv
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company - deprecated in favor of new address field"
msgstr "Address of the company - deprecated in favor of new address field"

#. js-lingui-id: ZfVqbP
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Aggregate operation"
msgstr "Aggregate operation"

#. js-lingui-id: W58PBh
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Aggregated / filtered event to be displayed on the timeline"
msgstr "Aggregated / filtered event to be displayed on the timeline"

#. js-lingui-id: hehnjM
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Amount"
msgstr "Amount"

#. js-lingui-id: qeHcQj
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "An API key"
msgstr "An API key"

#. js-lingui-id: MjyFvC
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "An attachment"
msgstr "An attachment"

#. js-lingui-id: +bL++X
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "An audit log of actions performed in the system"
#~ msgstr "An audit log of actions performed in the system"

#. js-lingui-id: XyOToQ
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "An error occurred."
msgstr "An error occurred."

#. js-lingui-id: muVHgL
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "An event related to user behavior"
#~ msgstr "An event related to user behavior"

#. js-lingui-id: bZq8rL
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "An opportunity"
msgstr "An opportunity"

#. js-lingui-id: JKWicb
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Annual Recurring Revenue: The actual or estimated annual revenue of the company"
msgstr "Annual Recurring Revenue: The actual or estimated annual revenue of the company"

#. js-lingui-id: yRnk5W
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Key"
msgstr "API Key"

#. js-lingui-id: FfSJ1Y
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Keys"
msgstr "API Keys"

#. js-lingui-id: puNs/l
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey expiration date"
msgstr "ApiKey expiration date"

#. js-lingui-id: YHiNxr
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey name"
msgstr "ApiKey name"

#. js-lingui-id: xrndTt
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey revocation date"
msgstr "ApiKey revocation date"

#. js-lingui-id: DGW5iN
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain does not match email domain"
msgstr "Approved access domain does not match email domain"

#. js-lingui-id: BKvuAq
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain has already been validated"
msgstr "Approved access domain has already been validated"

#. js-lingui-id: 3EiOLz
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ARR"
msgstr "ARR"

#. js-lingui-id: Ek7xGj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Assigned tasks"
msgstr "Assigned tasks"

#. js-lingui-id: ojKCLU
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Assignee"
msgstr "Assignee"

#. js-lingui-id: Max2GU
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Associated User Id"
msgstr "Associated User Id"

#. js-lingui-id: UY1vmE
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment"
msgstr "Attachment"

#. js-lingui-id: JAefBH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment author"
msgstr "Attachment author"

#. js-lingui-id: bIaesZ
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment company"
msgstr "Attachment company"

#. js-lingui-id: gfGYcl
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment full path"
msgstr "Attachment full path"

#. js-lingui-id: wjocwa
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment name"
msgstr "Attachment name"

#. js-lingui-id: FWlOXr
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment note"
msgstr "Attachment note"

#. js-lingui-id: YASWpH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment opportunity"
msgstr "Attachment opportunity"

#. js-lingui-id: P38yED
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment person"
msgstr "Attachment person"

#. js-lingui-id: Tx1DxS
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment task"
msgstr "Attachment task"

#. js-lingui-id: 4qqxMt
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment type"
msgstr "Attachment type"

#. js-lingui-id: w/Sphq
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments"
msgstr "Attachments"

#. js-lingui-id: 2tQOVc
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Attachments created by the workspace member"
msgstr "Attachments created by the workspace member"

#. js-lingui-id: iVnA89
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Attachments linked to the company"
msgstr "Attachments linked to the company"

#. js-lingui-id: MuTXtT
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Attachments linked to the contact."
msgstr "Attachments linked to the contact."

#. js-lingui-id: kw0mLu
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Attachments linked to the opportunity"
msgstr "Attachments linked to the opportunity"

#. js-lingui-id: fCbqr7
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments tied to the {label}"
msgstr "Attachments tied to the {label}"

#. js-lingui-id: ilRCh1
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Log"
#~ msgstr "Audit Log"

#. js-lingui-id: EPEFrH
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Logs"
#~ msgstr "Audit Logs"

#. js-lingui-id: RqCC/0
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#~ msgid "Audit Logs linked to the workspace member"
#~ msgstr "Audit Logs linked to the workspace member"

#. js-lingui-id: cNBqH+
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Auth failed at"
msgstr "Auth failed at"

#. js-lingui-id: LB4qX/
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Authentication is not enabled with this provider."
msgstr "Authentication is not enabled with this provider."

#. js-lingui-id: VbeIOx
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Author"
msgstr "Author"

#. js-lingui-id: XJONK6
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Authored attachments"
msgstr "Authored attachments"

#. js-lingui-id: cvSznK
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Automated Trigger Type"
msgstr "Automated Trigger Type"

#. js-lingui-id: 2mtBXs
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Automated Triggers"
msgstr "Automated Triggers"

#. js-lingui-id: RpExX0
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Automatically create People records when receiving or sending emails"
msgstr "Automatically create People records when receiving or sending emails"

#. js-lingui-id: lXxdId
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Automatically create records for people you participated with in an event."
msgstr "Automatically create records for people you participated with in an event."

#. js-lingui-id: kfcRb0
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Avatar"
msgstr "Avatar"

#. js-lingui-id: S/mJUR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Avatar Url"
msgstr "Avatar Url"

#. js-lingui-id: 20B9kW
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Event"
#~ msgstr "Behavioral Event"

#. js-lingui-id: Jeh/Q/
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Events"
#~ msgstr "Behavioral Events"

#. js-lingui-id: K1172m
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklist"
msgstr "Blocklist"

#. js-lingui-id: Tv2hxv
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Blocklisted handles"
msgstr "Blocklisted handles"

#. js-lingui-id: L5JhJe
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklists"
msgstr "Blocklists"

#. js-lingui-id: bGQplw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body"
msgstr "Body"

#. js-lingui-id: Nl8eMw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body (deprecated)"
msgstr "Body (deprecated)"

#. js-lingui-id: 8mVqF7
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Cached record name"
msgstr "Cached record name"

#. js-lingui-id: Nh6GTX
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channel"
msgstr "Calendar Channel"

#. js-lingui-id: jfNQ0m
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Association"
msgstr "Calendar Channel Event Association"

#. js-lingui-id: kYNT3F
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Associations"
msgstr "Calendar Channel Event Associations"

#. js-lingui-id: Znix/S
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channels"
msgstr "Calendar Channels"

#. js-lingui-id: bRk+FR
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar event"
msgstr "Calendar event"

#. js-lingui-id: N2kMfO
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participant"
msgstr "Calendar event participant"

#. js-lingui-id: AWDqkQ
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participants"
msgstr "Calendar event participants"

#. js-lingui-id: ZI2UyM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Calendar Event Participants"
msgstr "Calendar Event Participants"

#. js-lingui-id: X9A2xC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar events"
msgstr "Calendar events"

#. js-lingui-id: vxbVwc
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Cannot activate non-draft or non-last-published version"
msgstr "Cannot activate non-draft or non-last-published version"

#. js-lingui-id: LIPsWu
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create multiple draft versions for the same workflow"
msgstr "Cannot create multiple draft versions for the same workflow"

#. js-lingui-id: RggZr4
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create workflow version with status other than draft"
msgstr "Cannot create workflow version with status other than draft"

#. js-lingui-id: AQyr9X
#: src/engine/metadata-modules/field-metadata/services/field-metadata.service.ts
msgid "Cannot delete, please update the label identifier field first"
msgstr "Cannot delete, please update the label identifier field first"

#. js-lingui-id: FrNWt0
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot disable non-active workflow version"
msgstr "Cannot disable non-active workflow version"

#. js-lingui-id: IDMgr/
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot have more than one active workflow version"
msgstr "Cannot have more than one active workflow version"

#. js-lingui-id: 4DKo3U
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot update workflow version status manually"
msgstr "Cannot update workflow version status manually"

#. js-lingui-id: 4IVK41
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Channel ID"
msgstr "Channel ID"

#. js-lingui-id: Ubg/B+
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Channel Type"
msgstr "Channel Type"

#. js-lingui-id: 3wV73y
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "City"
msgstr "City"

#. js-lingui-id: NRF7pg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Close date"
msgstr "Close date"

#. js-lingui-id: 96YB1a
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Color Scheme"
msgstr "Color Scheme"

#. js-lingui-id: 07OSD1
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Compact View"
msgstr "Compact View"

#. js-lingui-id: s2QZS6
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Companies"
msgstr "Companies"

#. js-lingui-id: 7i8j3G
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Company"
msgstr "Company"

#. js-lingui-id: yA1de7
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Company record position"
msgstr "Company record position"

#. js-lingui-id: AgktVC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Conference Solution"
msgstr "Conference Solution"

#. js-lingui-id: 1fFL4B
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Connect is not allowed for {connectFieldName} on {objectMetadataNameSingular}"
msgstr "Connect is not allowed for {connectFieldName} on {objectMetadataNameSingular}"

#. js-lingui-id: PQ1Dw2
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Connected Account"
msgstr "Connected Account"

#. js-lingui-id: 9TzudL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Connected accounts"
msgstr "Connected accounts"

#. js-lingui-id: AMDUqA
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Connected Accounts"
msgstr "Connected Accounts"

#. js-lingui-id: bA6O5B
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Contact auto creation policy"
msgstr "Contact auto creation policy"

#. js-lingui-id: RnsmQs
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s avatar"
msgstr "Contact’s avatar"

#. js-lingui-id: pL+pqi
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s city"
msgstr "Contact’s city"

#. js-lingui-id: VnWMlz
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s company"
msgstr "Contact’s company"

#. js-lingui-id: ITlFIB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Emails"
msgstr "Contact’s Emails"

#. js-lingui-id: GuRtLY
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s job title"
msgstr "Contact’s job title"

#. js-lingui-id: QrCvRQ
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Linkedin account"
msgstr "Contact’s Linkedin account"

#. js-lingui-id: 6xPSVt
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s name"
msgstr "Contact’s name"

#. js-lingui-id: Y37CZ4
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone number"
msgstr "Contact’s phone number"

#. js-lingui-id: zsW3gg
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone numbers"
msgstr "Contact’s phone numbers"

#. js-lingui-id: uuZ00G
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s X/Twitter account"
msgstr "Contact’s X/Twitter account"

#. js-lingui-id: M73whl
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Context"
msgstr "Context"

#. js-lingui-id: NCIYDF
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Created by"
msgstr "Created by"

#. js-lingui-id: wPvFAD
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Creation date"
msgstr "Creation date"

#. js-lingui-id: CJXWmO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Creation DateTime"
msgstr "Creation DateTime"

#. js-lingui-id: umhXiy
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Cron pattern '{pattern}' is invalid"
msgstr "Cron pattern '{pattern}' is invalid"

#. js-lingui-id: Mikszu
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Custom Connection Parameters"
msgstr "Custom Connection Parameters"

#. js-lingui-id: Lhd0oQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Date format"
msgstr "Date format"

#. js-lingui-id: 1lL5Iu
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Date when the record was deleted"
msgstr "Date when the record was deleted"

#. js-lingui-id: QN9ahV
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Deleted at"
msgstr "Deleted at"

#. js-lingui-id: U1bSSI
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Describes if the view is in compact mode"
msgstr "Describes if the view is in compact mode"

#. js-lingui-id: Nu4oKW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Description"
msgstr "Description"

#. js-lingui-id: MRB7nI
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Direction"
msgstr "Direction"

#. js-lingui-id: 0gS7M5
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Display Name"
msgstr "Display Name"

#. js-lingui-id: y7HJTU
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Display the records in a side panel or in a record page"
msgstr "Display the records in a side panel or in a record page"

#. js-lingui-id: 0H/D9K
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Display Value"
msgstr "Display Value"

#. js-lingui-id: wMncXq
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Domain Name"
msgstr "Domain Name"

#. js-lingui-id: YOowcq
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Due Date"
msgstr "Due Date"

#. js-lingui-id: BBXuRb
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email already verified."
msgstr "Email already verified."

#. js-lingui-id: xw6iqb
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Email is not verified."
msgstr "Email is not verified."

#. js-lingui-id: ZsZeV2
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Email is required"
msgstr "Email is required"

#. js-lingui-id: y8LSMh
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email verification not required."
msgstr "Email verification not required."

#. js-lingui-id: BXEcos
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Emails"
msgstr "Emails"

#. js-lingui-id: gqv5ZL
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Employees"
msgstr "Employees"

#. js-lingui-id: VFv2ZC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "End Date"
msgstr "End Date"

#. js-lingui-id: k//6Xs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event company"
msgstr "Event company"

#. js-lingui-id: FJ7QI4
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event context"
#~ msgstr "Event context"

#. js-lingui-id: kJDmsI
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event details"
msgstr "Event details"

#. js-lingui-id: 0JhmlM
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event external ID"
msgstr "Event external ID"

#. js-lingui-id: aZJLAR
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event ID"
msgstr "Event ID"

#. js-lingui-id: 81maJp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Event Listeners"
#~ msgstr "Event Listeners"

#. js-lingui-id: PYs3rP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event name"
msgstr "Event name"

#. js-lingui-id: evIGwh
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event name/type"
#~ msgstr "Event name/type"

#. js-lingui-id: vv99Wu
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event note"
msgstr "Event note"

#. js-lingui-id: eSt759
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event opportunity"
msgstr "Event opportunity"

#. js-lingui-id: aicVfT
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Event Participants"
msgstr "Event Participants"

#. js-lingui-id: 0mp45a
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event person"
msgstr "Event person"

#. js-lingui-id: mMq0Wy
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event task"
msgstr "Event task"

#. js-lingui-id: dCV1dS
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow"
msgstr "Event workflow"

#. js-lingui-id: W84pl6
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow run"
msgstr "Event workflow run"

#. js-lingui-id: vUpps9
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow version"
msgstr "Event workflow version"

#. js-lingui-id: LxOGsB
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workspace member"
msgstr "Event workspace member"

#. js-lingui-id: tst44n
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events"
msgstr "Events"

#. js-lingui-id: fHL+iH
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events linked to the person"
msgstr "Events linked to the person"

#. js-lingui-id: 3/O8MM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Events linked to the workspace member"
msgstr "Events linked to the workspace member"

#. js-lingui-id: QQlMid
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude group emails"
msgstr "Exclude group emails"

#. js-lingui-id: kenYGr
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude non professional emails"
msgstr "Exclude non professional emails"

#. js-lingui-id: Lo5U0b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Executed by"
msgstr "Executed by"

#. js-lingui-id: AiFXmG
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Expected the same constraint fields to be used consistently across all operations for {connectFieldName}."
msgstr "Expected the same constraint fields to be used consistently across all operations for {connectFieldName}."

#. js-lingui-id: sZg7s1
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Expiration date"
msgstr "Expiration date"

#. js-lingui-id: 6Ki4Pv
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite"
msgstr "Favorite"

#. js-lingui-id: aKUOPp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite company"
msgstr "Favorite company"

#. js-lingui-id: TDlZ/o
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite Folder"
msgstr "Favorite Folder"

#. js-lingui-id: WDVfUH
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite folder position"
msgstr "Favorite folder position"

#. js-lingui-id: SStz54
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite Folders"
msgstr "Favorite Folders"

#. js-lingui-id: dz/fFp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite note"
msgstr "Favorite note"

#. js-lingui-id: RRr9Bp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite opportunity"
msgstr "Favorite opportunity"

#. js-lingui-id: RM6B6V
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite person"
msgstr "Favorite person"

#. js-lingui-id: oVA9vM
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite position"
msgstr "Favorite position"

#. js-lingui-id: OaQVQH
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite task"
msgstr "Favorite task"

#. js-lingui-id: VkWV1s
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite view"
msgstr "Favorite view"

#. js-lingui-id: l+lx2f
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow"
msgstr "Favorite workflow"

#. js-lingui-id: BPBnux
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow run"
msgstr "Favorite workflow run"

#. js-lingui-id: 5X6Vlz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow version"
msgstr "Favorite workflow version"

#. js-lingui-id: FDpezg
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workspace member"
msgstr "Favorite workspace member"

#. js-lingui-id: X9kySA
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites"
msgstr "Favorites"

#. js-lingui-id: 0CzeFL
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorites in this folder"
msgstr "Favorites in this folder"

#. js-lingui-id: zQponA
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Favorites linked to the company"
msgstr "Favorites linked to the company"

#. js-lingui-id: VaKLrB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Favorites linked to the contact"
msgstr "Favorites linked to the contact"

#. js-lingui-id: GOfcBt
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Favorites linked to the note"
msgstr "Favorites linked to the note"

#. js-lingui-id: 9zd8hg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Favorites linked to the opportunity"
msgstr "Favorites linked to the opportunity"

#. js-lingui-id: L5ccWD
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Favorites linked to the task"
msgstr "Favorites linked to the task"

#. js-lingui-id: R+1ib/
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Favorites linked to the view"
msgstr "Favorites linked to the view"

#. js-lingui-id: ee0tmj
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Favorites linked to the workflow"
msgstr "Favorites linked to the workflow"

#. js-lingui-id: zar5jz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Favorites linked to the workflow run"
msgstr "Favorites linked to the workflow run"

#. js-lingui-id: 499/sw
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Favorites linked to the workflow version"
msgstr "Favorites linked to the workflow version"

#. js-lingui-id: rgmtb4
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Favorites linked to the workspace member"
msgstr "Favorites linked to the workspace member"

#. js-lingui-id: GyXrE1
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites tied to the {label}"
msgstr "Favorites tied to the {label}"

#. js-lingui-id: nSFFML
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Field Metadata Id"
msgstr "Field Metadata Id"

#. js-lingui-id: g+t8w9
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Field metadata used for aggregate operation"
msgstr "Field metadata used for aggregate operation"

#. js-lingui-id: J4hC2m
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Field used for full-text search"
msgstr "Field used for full-text search"

#. js-lingui-id: zuDgzc
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Field Value"
msgstr "Field Value"

#. js-lingui-id: 3oQ73E
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have a previous step"
#~ msgstr "Filter action must have a previous step"

#. js-lingui-id: Zllikb
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have only one previous step"
#~ msgstr "Filter action must have only one previous step"

#. js-lingui-id: lEpDue
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder for Message Channel"
msgstr "Folder for Message Channel"

#. js-lingui-id: cqQyPB
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder name"
msgstr "Folder name"

#. js-lingui-id: kuN9I4
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have a defined label and type"
msgstr "Form action fields must have a defined label and type"

#. js-lingui-id: QK3OvQ
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have unique names"
msgstr "Form action fields must have unique names"

#. js-lingui-id: KYP1t5
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action must have at least one field"
msgstr "Form action must have at least one field"

#. js-lingui-id: oQA+cA
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Full path"
msgstr "Full path"

#. js-lingui-id: UzpVUy
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Group by this field value"
msgstr "Group by this field value"

#. js-lingui-id: TkE8dW
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "handle"
msgstr "handle"

#. js-lingui-id: Nf7oXL
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Handle"
msgstr "Handle"

#. js-lingui-id: GvgxWx
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Handle Aliases"
msgstr "Handle Aliases"

#. js-lingui-id: VehAU2
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Header message Id"
msgstr "Header message Id"

#. js-lingui-id: NNJnBi
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "iCal UID"
msgstr "iCal UID"

#. js-lingui-id: wwu18a
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Icon"
msgstr "Icon"

#. js-lingui-id: CiyiKN
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ICP"
msgstr "ICP"

#. js-lingui-id: dFb5Nt
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Id"
msgstr "Id"

#. js-lingui-id: Ebc83S
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Ideal Customer Profile:  Indicates whether the company is the most suitable and valuable customer for you"
msgstr "Ideal Customer Profile:  Indicates whether the company is the most suitable and valuable customer for you"

#. js-lingui-id: NrA0WZ
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "If the event is related to a particular object"
#~ msgstr "If the event is related to a particular object"

#. js-lingui-id: 71VJzG
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-short.utils.ts
msgid "Input is too short: \"{name}\""
msgstr "Input is too short: \"{name}\""

#. js-lingui-id: X7+bC6
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid calling code {callingCode}"
msgstr "Invalid calling code {callingCode}"

#. js-lingui-id: 1PtrY0
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid country code {countryCode}"
msgstr "Invalid country code {countryCode}"

#. js-lingui-id: 4EtFrA
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid day value. Should be integer greater than 1"
msgstr "Invalid day value. Should be integer greater than 1"

#. js-lingui-id: MBpeQ6
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer between 0 and 23"
msgstr "Invalid hour value. Should be integer between 0 and 23"

#. js-lingui-id: 9xsjn9
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer greater than 1"
msgstr "Invalid hour value. Should be integer greater than 1"

#. js-lingui-id: FSunWx
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Invalid label: \"{label}\""
msgstr "Invalid label: \"{label}\""

#. js-lingui-id: iBBCnf
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer between 0 and 59"
msgstr "Invalid minute value. Should be integer between 0 and 59"

#. js-lingui-id: hYcFSd
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer greater than 1"
msgstr "Invalid minute value. Should be integer greater than 1"

#. js-lingui-id: ApP70c
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid setting type provided in cron trigger"
msgstr "Invalid setting type provided in cron trigger"

#. js-lingui-id: sHcAMD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid trigger type for enabling workflow trigger"
msgstr "Invalid trigger type for enabling workflow trigger"

#. js-lingui-id: grHYXZ
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is canceled"
msgstr "Is canceled"

#. js-lingui-id: k+g9Uh
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Contact Auto Creation Enabled"
msgstr "Is Contact Auto Creation Enabled"

#. js-lingui-id: uBx1xd
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is Full Day"
msgstr "Is Full Day"

#. js-lingui-id: 3iAfL2
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Is Organizer"
msgstr "Is Organizer"

#. js-lingui-id: Mqzqb8
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Sync Enabled"
msgstr "Is Sync Enabled"

#. js-lingui-id: 27z+FV
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Job Title"
msgstr "Job Title"

#. js-lingui-id: PviVyk
#: src/engine/core-modules/workspace-invitation/services/workspace-invitation.service.ts
msgid "Join your team on Twenty"
msgstr "Join your team on Twenty"

#. js-lingui-id: WNfJ8M
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "JSON object containing custom connection parameters"
msgstr "JSON object containing custom connection parameters"

#. js-lingui-id: shbh25
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Json object to provide context (user, device, workspace, etc.)"
#~ msgstr "Json object to provide context (user, device, workspace, etc.)"

#. js-lingui-id: HgJ9jQ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Json object to provide output of the workflow run"
msgstr "Json object to provide output of the workflow run"

#. js-lingui-id: AQuLwi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide steps"
msgstr "Json object to provide steps"

#. js-lingui-id: fOjmZ1
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide trigger"
msgstr "Json object to provide trigger"

#. js-lingui-id: TVc0/Q
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Json value for event details"
msgstr "Json value for event details"

#. js-lingui-id: qmarO2
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "kanbanfieldMetadataId"
msgstr "kanbanfieldMetadataId"

#. js-lingui-id: 7sMeHQ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Key"
msgstr "Key"

#. js-lingui-id: w8Rv8T
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Label is required"
msgstr "Label is required"

#. js-lingui-id: 4EGmy6
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not be empty"
msgstr "Label must not be empty"

#. js-lingui-id: k731jp
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not contain a comma"
msgstr "Label must not contain a comma"

#. js-lingui-id: vXIe7J
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Language"
msgstr "Language"

#. js-lingui-id: WvYp6o
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Last published Version Id"
msgstr "Last published Version Id"

#. js-lingui-id: Y60/DC
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Last sync cursor"
msgstr "Last sync cursor"

#. js-lingui-id: Zdx9Qq
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Last sync date"
msgstr "Last sync date"

#. js-lingui-id: c7uqNg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Last sync history ID"
msgstr "Last sync history ID"

#. js-lingui-id: tGwswq
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last time the record was changed"
msgstr "Last time the record was changed"

#. js-lingui-id: o1zvNS
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last update"
msgstr "Last update"

#. js-lingui-id: wR9USP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Object Metadata Id"
msgstr "Linked Object Metadata Id"

#. js-lingui-id: PV1Nm7
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Linked Opportunities"
msgstr "Linked Opportunities"

#. js-lingui-id: 6YiMr4
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record cached name"
msgstr "Linked Record cached name"

#. js-lingui-id: sgHAxx
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record id"
msgstr "Linked Record id"

#. js-lingui-id: uCA6be
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Linkedin"
msgstr "Linkedin"

#. js-lingui-id: LeFv/R
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "List of opportunities for which that person is the point of contact"
msgstr "List of opportunities for which that person is the point of contact"

#. js-lingui-id: wJijgU
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Location"
msgstr "Location"

#. js-lingui-id: y+q8Lv
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical Operator"
msgstr "Logical Operator"

#. js-lingui-id: 6kQXsS
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical operator for the filter group"
msgstr "Logical operator for the filter group"

#. js-lingui-id: EnfPm2
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Meet Link"
msgstr "Meet Link"

#. js-lingui-id: xDAtGP
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message"
msgstr "Message"

#. js-lingui-id: g+QGD6
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel"
msgstr "Message Channel"

#. js-lingui-id: DzU4a3
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel Association"
msgstr "Message Channel Association"

#. js-lingui-id: wd/R++
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Id"
msgstr "Message Channel Id"

#. js-lingui-id: disipM
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Association"
msgstr "Message Channel Message Association"

#. js-lingui-id: ijQY3P
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Associations"
msgstr "Message Channel Message Associations"

#. js-lingui-id: k7LXPQ
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Message Channels"
msgstr "Message Channels"

#. js-lingui-id: /4uGJc
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Direction"
msgstr "Message Direction"

#. js-lingui-id: fBw8fQ
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message External Id"
msgstr "Message External Id"

#. js-lingui-id: CStLnc
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Message Folder"
msgstr "Message Folder"

#. js-lingui-id: p4ANpY
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Folders"
msgstr "Message Folders"

#. js-lingui-id: 6icznk
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Id"
msgstr "Message Id"

#. js-lingui-id: 9FJSpK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message id from the message header"
msgstr "Message id from the message header"

#. js-lingui-id: yaC1Aq
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message id from the messaging provider"
msgstr "Message id from the messaging provider"

#. js-lingui-id: IUmVwu
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participant"
msgstr "Message Participant"

#. js-lingui-id: FhIFx7
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participants"
msgstr "Message Participants"

#. js-lingui-id: IC5A8V
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Synced with a Message Channel"
msgstr "Message Synced with a Message Channel"

#. js-lingui-id: de2nM/
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Thread"
msgstr "Message Thread"

#. js-lingui-id: km1jgD
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message Thread Id"
msgstr "Message Thread Id"

#. js-lingui-id: RD0ecC
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Threads"
msgstr "Message Threads"

#. js-lingui-id: t7TeQU
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages"
msgstr "Messages"

#. js-lingui-id: WoWdku
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Messages from the channel."
msgstr "Messages from the channel."

#. js-lingui-id: rYPBO7
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages from the thread."
msgstr "Messages from the thread."

#. js-lingui-id: XcKQrV
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider access token"
msgstr "Messaging provider access token"

#. js-lingui-id: 80EvIk
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider refresh token"
msgstr "Messaging provider refresh token"

#. js-lingui-id: SzCMUQ
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Missing required fields: at least one unique constraint have to be fully populated for '{connectFieldName}'."
msgstr "Missing required fields: at least one unique constraint have to be fully populated for '{connectFieldName}'."

#. js-lingui-id: 6YtxFj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Name"
msgstr "Name"

#. js-lingui-id: RGz1nY
#: src/engine/metadata-modules/field-metadata/services/field-metadata-validation.service.ts
msgid "Name is not available, it may be duplicating another field's name."
msgstr "Name is not available, it may be duplicating another field's name."

#. js-lingui-id: 0MobB1
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-long.utils.ts
msgid "Name is too long: it exceeds the 63 characters limit."
msgstr "Name is too long: it exceeds the 63 characters limit."

#. js-lingui-id: EEVPOx
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Name of the favorite folder"
msgstr "Name of the favorite folder"

#. js-lingui-id: csMjko
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Name of the workflow run"
msgstr "Name of the workflow run"

#. js-lingui-id: hQQZse
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No event name provided in database event trigger"
msgstr "No event name provided in database event trigger"

#. js-lingui-id: 9bu19u
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "No input provided in form step"
msgstr "No input provided in form step"

#. js-lingui-id: MWAUHo
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No pattern provided in CUSTOM cron trigger"
msgstr "No pattern provided in CUSTOM cron trigger"

#. js-lingui-id: pOu4u/
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No schedule provided in cron trigger"
msgstr "No schedule provided in cron trigger"

#. js-lingui-id: cLUBlS
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No setting type provided in cron trigger"
msgstr "No setting type provided in cron trigger"

#. js-lingui-id: QwbSxD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No steps provided in workflow version"
msgstr "No steps provided in workflow version"

#. js-lingui-id: 6dP8sB
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No trigger type provided"
msgstr "No trigger type provided"

#. js-lingui-id: KiJn9B
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Note"
msgstr "Note"

#. js-lingui-id: GGlkb7
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note attachments"
msgstr "Note attachments"

#. js-lingui-id: Q1Rz+6
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note body"
msgstr "Note body"

#. js-lingui-id: Yp057F
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note record position"
msgstr "Note record position"

#. js-lingui-id: spaO7l
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Target"
msgstr "Note Target"

#. js-lingui-id: mkchvJ
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note targets"
msgstr "Note targets"

#. js-lingui-id: tD4BxK
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Targets"
msgstr "Note Targets"

#. js-lingui-id: jDThel
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note title"
msgstr "Note title"

#. js-lingui-id: 1DBGsz
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes"
msgstr "Notes"

#. js-lingui-id: Ne73P/
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes tied to the {label}"
msgstr "Notes tied to the {label}"

#. js-lingui-id: fXBE74
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Notes tied to the company"
msgstr "Notes tied to the company"

#. js-lingui-id: iQ5AH6
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Notes tied to the contact"
msgstr "Notes tied to the contact"

#. js-lingui-id: 0bi/Eh
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Notes tied to the opportunity"
msgstr "Notes tied to the opportunity"

#. js-lingui-id: B2Y6QU
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget company"
msgstr "NoteTarget company"

#. js-lingui-id: /mH0jo
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget note"
msgstr "NoteTarget note"

#. js-lingui-id: DTs4tO
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget opportunity"
msgstr "NoteTarget opportunity"

#. js-lingui-id: gBwbnk
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget person"
msgstr "NoteTarget person"

#. js-lingui-id: ioJFzx
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Number of employees in the company"
msgstr "Number of employees in the company"

#. js-lingui-id: ECRKYR
#: src/engine/metadata-modules/utils/validate-no-other-object-with-same-name-exists-or-throw.util.ts
msgid "Object already exists"
msgstr "Object already exists"

#. js-lingui-id: hhe7Ce
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Object id"
#~ msgstr "Object id"

#. js-lingui-id: dnPgTI
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object metadata id"
#~ msgstr "Object metadata id"

#. js-lingui-id: T/nPf5
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Object Metadata Id"
msgstr "Object Metadata Id"

#. js-lingui-id: afsWF6
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object name"
#~ msgstr "Object name"

#. js-lingui-id: r/A6pA
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Open Record In"
msgstr "Open Record In"

#. js-lingui-id: Fzfj4N
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Operand"
msgstr "Operand"

#. js-lingui-id: FZg3wM
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operation"
msgstr "Operation"

#. js-lingui-id: B1MDds
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operations"
msgstr "Operations"

#. js-lingui-id: 4MyDFl
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities"
msgstr "Opportunities"

#. js-lingui-id: 5QgKbT
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities linked to the company."
msgstr "Opportunities linked to the company."

#. js-lingui-id: SV/iis
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Opportunity"
msgstr "Opportunity"

#. js-lingui-id: WnMlKn
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity amount"
msgstr "Opportunity amount"

#. js-lingui-id: aj3fnv
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity close date"
msgstr "Opportunity close date"

#. js-lingui-id: 3NYczb
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity company"
msgstr "Opportunity company"

#. js-lingui-id: as45IN
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity point of contact"
msgstr "Opportunity point of contact"

#. js-lingui-id: 5Nu7Uw
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity probability"
msgstr "Opportunity probability"

#. js-lingui-id: Q1dzBp
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity record position"
msgstr "Opportunity record position"

#. js-lingui-id: 5ugYS3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity stage"
msgstr "Opportunity stage"

#. js-lingui-id: //eyK1
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label \"{sanitizedLabel}\" is beneath 1 character"
msgstr "Option label \"{sanitizedLabel}\" is beneath 1 character"

#. js-lingui-id: mTFS0z
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label exceeds 63 characters"
msgstr "Option label exceeds 63 characters"

#. js-lingui-id: +E9N4m
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label is required"
msgstr "Option label is required"

#. js-lingui-id: nQqNzE
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value \"{sanitizedValue}\" is beneath 1 character"
msgstr "Option value \"{sanitizedValue}\" is beneath 1 character"

#. js-lingui-id: 5m/1Hh
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value exceeds 63 characters"
msgstr "Option value exceeds 63 characters"

#. js-lingui-id: /wsRPd
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value is required"
msgstr "Option value is required"

#. js-lingui-id: eLggyd
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Optional aggregate operation"
msgstr "Optional aggregate operation"

#. js-lingui-id: kdClJ/
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Optional secret used to compute the HMAC signature for webhook payloads. This secret is shared between Twenty and the webhook consumer to authenticate webhook requests."
msgstr "Optional secret used to compute the HMAC signature for webhook payloads. This secret is shared between Twenty and the webhook consumer to authenticate webhook requests."

#. js-lingui-id: gh06VD
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Output"
msgstr "Output"

#. js-lingui-id: pDUbN1
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group"
msgstr "Parent View Filter Group"

#. js-lingui-id: YKSmIP
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group Id"
msgstr "Parent View Filter Group Id"

#. js-lingui-id: wT0H4O
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Password too weak"
msgstr "Password too weak"

#. js-lingui-id: 1wdjme
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People"
msgstr "People"

#. js-lingui-id: E1zc7W
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People linked to the company."
msgstr "People linked to the company."

#. js-lingui-id: OZdaTZ
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Person"
msgstr "Person"

#. js-lingui-id: c3Qq6o
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Person record Position"
msgstr "Person record Position"

#. js-lingui-id: zmwvG2
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phone"
msgstr "Phone"

#. js-lingui-id: m2ivgq
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phones"
msgstr "Phones"

#. js-lingui-id: P04j61
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Point of Contact"
msgstr "Point of Contact"

#. js-lingui-id: p/78dY
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Position"
msgstr "Position"

#. js-lingui-id: HH1bMC
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in the parent view filter group"
msgstr "Position in the parent view filter group"

#. js-lingui-id: CvOSME
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Position in the view filter group"
msgstr "Position in the view filter group"

#. js-lingui-id: h8PGuF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in view filter group"
msgstr "Position in view filter group"

#. js-lingui-id: TTHIbk
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred color scheme"
msgstr "Preferred color scheme"

#. js-lingui-id: 5v4qYi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred language"
msgstr "Preferred language"

#. js-lingui-id: WJIL29
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Probability"
msgstr "Probability"

#. js-lingui-id: dtGxRz
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred calling code are conflicting"
msgstr "Provided and inferred calling code are conflicting"

#. js-lingui-id: Vq/afT
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred country code are conflicting"
msgstr "Provided and inferred country code are conflicting"

#. js-lingui-id: HZ45ox
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided country code and calling code are conflicting"
msgstr "Provided country code and calling code are conflicting"

#. js-lingui-id: A4Ohxb
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided phone number is invalid {number}"
msgstr "Provided phone number is invalid {number}"

#. js-lingui-id: CrfRPa
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "provider"
msgstr "provider"

#. js-lingui-id: YfbwOB
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Received At"
msgstr "Received At"

#. js-lingui-id: 4Tvtbu
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Record id"
#~ msgstr "Record id"

#. js-lingui-id: 9gXJw8
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Recurring Event ID"
msgstr "Recurring Event ID"

#. js-lingui-id: 2rvMKg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Refresh Token"
msgstr "Refresh Token"

#. js-lingui-id: 2LpFdR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Related user email address"
msgstr "Related user email address"

#. js-lingui-id: g87L9j
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Relations"
msgstr "Relations"

#. js-lingui-id: pCsIQQ
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Request has expired, please try again."
msgstr "Request has expired, please try again."

#. js-lingui-id: rnCndp
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Response Status"
msgstr "Response Status"

#. js-lingui-id: MCWKAU
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Revocation date"
msgstr "Revocation date"

#. js-lingui-id: GDvlUT
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Role"
msgstr "Role"

#. js-lingui-id: Tpm2G9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Runs"
msgstr "Runs"

#. js-lingui-id: N/rFzD
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Scopes"
msgstr "Scopes"

#. js-lingui-id: PdVIJC
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Search vector"
msgstr "Search vector"

#. js-lingui-id: 8VEDbV
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Secret"
msgstr "Secret"

#. js-lingui-id: Tz0i8g
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Settings"
msgstr "Settings"

#. js-lingui-id: Cj2Gtd
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Size"
msgstr "Size"

#. js-lingui-id: 3PRxO3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Stage"
msgstr "Stage"

#. js-lingui-id: D3iCkb
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Start Date"
msgstr "Start Date"

#. js-lingui-id: RS0o7b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State"
msgstr "State"

#. js-lingui-id: nY8GL/
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State of the workflow run"
msgstr "State of the workflow run"

#. js-lingui-id: uAQUqI
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Status"
msgstr "Status"

#. js-lingui-id: Db4W3/
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Statuses"
msgstr "Statuses"

#. js-lingui-id: S7DRR+
#: src/modules/workflow/common/utils/assert-workflow-statuses-not-set.ts
msgid "Statuses cannot be set manually."
msgstr "Statuses cannot be set manually."

#. js-lingui-id: u+rv4L
#: src/modules/workflow/workflow-builder/workflow-step/workflow-version-step.workspace-service.ts
msgid "Step is not a form"
msgstr "Step is not a form"

#. js-lingui-id: YFMPch
#: src/engine/metadata-modules/utils/validate-metadata-name-start-with-lowercase-letter-and-contain-digits-nor-letters.utils.ts
msgid "String \"{name}\" is not valid: must start with lowercase letter and contain only alphanumeric letters"
msgstr "String \"{name}\" is not valid: must start with lowercase letter and contain only alphanumeric letters"

#. js-lingui-id: g4jxXh
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Sub field name"
msgstr "Sub field name"

#. js-lingui-id: UJmAAK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Subject"
msgstr "Subject"

#. js-lingui-id: oyJYg7
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor"
msgstr "Sync Cursor"

#. js-lingui-id: awvBUx
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor. Used for syncing events from the calendar provider"
msgstr "Sync Cursor. Used for syncing events from the calendar provider"

#. js-lingui-id: dNAbG6
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage"
msgstr "Sync stage"

#. js-lingui-id: aqNjQE
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage started at"
msgstr "Sync stage started at"

#. js-lingui-id: bRUdLR
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync status"
msgstr "Sync status"

#. js-lingui-id: 5y3Wbw
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Target object metadata not found for {connectFieldName}"
msgstr "Target object metadata not found for {connectFieldName}"

#. js-lingui-id: 4SHJe4
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Target Url"
msgstr "Target Url"

#. js-lingui-id: Q3P/4s
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Task"
msgstr "Task"

#. js-lingui-id: kS+Ym6
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task assignee"
msgstr "Task assignee"

#. js-lingui-id: 7fYQ6E
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task attachments"
msgstr "Task attachments"

#. js-lingui-id: X8fs74
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task body"
msgstr "Task body"

#. js-lingui-id: EPxYHS
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task due date"
msgstr "Task due date"

#. js-lingui-id: fUw1j+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task record position"
msgstr "Task record position"

#. js-lingui-id: I6+0ph
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task status"
msgstr "Task status"

#. js-lingui-id: WSiiWf
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Target"
msgstr "Task Target"

#. js-lingui-id: khGQLP
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task targets"
msgstr "Task targets"

#. js-lingui-id: 836FiO
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Targets"
msgstr "Task Targets"

#. js-lingui-id: R+s8S+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task title"
msgstr "Task title"

#. js-lingui-id: GtycJ/
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks"
msgstr "Tasks"

#. js-lingui-id: HlDeG3
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Tasks assigned to the workspace member"
msgstr "Tasks assigned to the workspace member"

#. js-lingui-id: Ca/n4T
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks tied to the {label}"
msgstr "Tasks tied to the {label}"

#. js-lingui-id: M4rBti
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Tasks tied to the company"
msgstr "Tasks tied to the company"

#. js-lingui-id: /VaiDW
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Tasks tied to the contact"
msgstr "Tasks tied to the contact"

#. js-lingui-id: 1TfX9U
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Tasks tied to the opportunity"
msgstr "Tasks tied to the opportunity"

#. js-lingui-id: pP0Dt9
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget company"
msgstr "TaskTarget company"

#. js-lingui-id: UJ2aPi
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget opportunity"
msgstr "TaskTarget opportunity"

#. js-lingui-id: I1MiSs
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget person"
msgstr "TaskTarget person"

#. js-lingui-id: pciKLT
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget task"
msgstr "TaskTarget task"

#. js-lingui-id: xeiujy
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Text"
msgstr "Text"

#. js-lingui-id: 6PJbR2
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account handle (email, username, phone number, etc.)"
msgstr "The account handle (email, username, phone number, etc.)"

#. js-lingui-id: zUXOAB
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account provider"
msgstr "The account provider"

#. js-lingui-id: qnNFrW
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Linkedin account"
msgstr "The company Linkedin account"

#. js-lingui-id: N31Pso
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company name"
msgstr "The company name"

#. js-lingui-id: BHFCqB
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Twitter/X account"
msgstr "The company Twitter/X account"

#. js-lingui-id: OBmU0K
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company website URL. We use this url to fetch the company icon"
msgstr "The company website URL. We use this url to fetch the company icon"

#. js-lingui-id: zGBDEH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "The creator of the record"
msgstr "The creator of the record"

#. js-lingui-id: bMyVOx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The current statuses of the workflow versions"
msgstr "The current statuses of the workflow versions"

#. js-lingui-id: 0bo4Q0
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "The date the message was received"
msgstr "The date the message was received"

#. js-lingui-id: 8h4mhq
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "The executor of the workflow"
msgstr "The executor of the workflow"

#. js-lingui-id: W3raza
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "The folder this favorite belongs to"
msgstr "The folder this favorite belongs to"

#. js-lingui-id: EJUSll
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "The initial version of a workflow can not be deleted"
msgstr "The initial version of a workflow can not be deleted"

#. js-lingui-id: DbWmKZ
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "The opportunity name"
msgstr "The opportunity name"

#. js-lingui-id: 5N6QtE
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger settings"
msgstr "The workflow automated trigger settings"

#. js-lingui-id: SpKbfT
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger type"
msgstr "The workflow automated trigger type"

#. js-lingui-id: 7mPrpl
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "The workflow event listener name"
#~ msgstr "The workflow event listener name"

#. js-lingui-id: od0omS
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow last published version id"
msgstr "The workflow last published version id"

#. js-lingui-id: /EdWx6
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow name"
msgstr "The workflow name"

#. js-lingui-id: EhAsND
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version name"
msgstr "The workflow version name"

#. js-lingui-id: dhx13p
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version status"
msgstr "The workflow version status"

#. js-lingui-id: yIUmAs
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-reserved-keyword.ts
#: src/engine/metadata-modules/utils/validate-field-name-availability.utils.ts
msgid "This name is not available."
msgstr "This name is not available."

#. js-lingui-id: Zl0BJl
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread External Id"
msgstr "Thread External Id"

#. js-lingui-id: RSSbWN
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread id from the messaging provider"
msgstr "Thread id from the messaging provider"

#. js-lingui-id: sS8v5K
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Throttle Failure Count"
msgstr "Throttle Failure Count"

#. js-lingui-id: n9nSNJ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time format"
msgstr "Time format"

#. js-lingui-id: Mz2JN2
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time zone"
msgstr "Time zone"

#. js-lingui-id: az1boY
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities"
msgstr "Timeline Activities"

#. js-lingui-id: fqKMpF
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Timeline Activities linked to the company"
msgstr "Timeline Activities linked to the company"

#. js-lingui-id: 4/UzU5
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Timeline Activities linked to the note."
msgstr "Timeline Activities linked to the note."

#. js-lingui-id: p6feIz
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Timeline Activities linked to the opportunity."
msgstr "Timeline Activities linked to the opportunity."

#. js-lingui-id: yvPwuF
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Timeline activities linked to the run"
msgstr "Timeline activities linked to the run"

#. js-lingui-id: q96UvB
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Timeline Activities linked to the task."
msgstr "Timeline Activities linked to the task."

#. js-lingui-id: N9HMa/
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Timeline activities linked to the version"
msgstr "Timeline activities linked to the version"

#. js-lingui-id: B1CYKX
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Timeline activities linked to the workflow"
msgstr "Timeline activities linked to the workflow"

#. js-lingui-id: G0UmtQ
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities tied to the {label}"
msgstr "Timeline Activities tied to the {label}"

#. js-lingui-id: K/kU4E
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Timeline Activity"
msgstr "Timeline Activity"

#. js-lingui-id: MHrjPM
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Title"
msgstr "Title"

#. js-lingui-id: +zy2Nq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Type"
msgstr "Type"

#. js-lingui-id: EBZdX5
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Unsupported cron schedule type"
msgstr "Unsupported cron schedule type"

#. js-lingui-id: 0gY5lO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Update DateTime"
msgstr "Update DateTime"

#. js-lingui-id: l9dlVi
#: src/engine/core-modules/auth/strategies/jwt.auth.strategy.ts
msgid "User does not have access to this workspace"
msgstr "User does not have access to this workspace"

#. js-lingui-id: YFciqL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Email"
msgstr "User Email"

#. js-lingui-id: d1BTW8
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Id"
msgstr "User Id"

#. js-lingui-id: B43Kks
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "User is not part of the workspace"
msgstr "User is not part of the workspace"

#. js-lingui-id: 4juE7s
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User time zone"
msgstr "User time zone"

#. js-lingui-id: 43zCwQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred date format"
msgstr "User's preferred date format"

#. js-lingui-id: kJuoKm
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred time format"
msgstr "User's preferred time format"

#. js-lingui-id: wMHvYH
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Value"
msgstr "Value"

#. js-lingui-id: 7aatUy
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version status"
msgstr "Version status"

#. js-lingui-id: CdQeU7
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version steps"
msgstr "Version steps"

#. js-lingui-id: PGMPIi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version trigger"
msgstr "Version trigger"

#. js-lingui-id: IYNSdp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Versions"
msgstr "Versions"

#. js-lingui-id: jpctdh
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "View"
msgstr "View"

#. js-lingui-id: cZPDyy
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field"
msgstr "View Field"

#. js-lingui-id: 6jpoH4
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field position"
msgstr "View Field position"

#. js-lingui-id: Ju6gri
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field related view"
msgstr "View Field related view"

#. js-lingui-id: tvTr2y
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field size"
msgstr "View Field size"

#. js-lingui-id: BkII8t
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field target field"
msgstr "View Field target field"

#. js-lingui-id: Gd/LzL
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field visibility"
msgstr "View Field visibility"

#. js-lingui-id: GUFYyq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Fields"
msgstr "View Fields"

#. js-lingui-id: JRtI7Y
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter"
msgstr "View Filter"

#. js-lingui-id: L2gQ5q
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Display Value"
msgstr "View Filter Display Value"

#. js-lingui-id: l9/6pD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Group"
msgstr "View Filter Group"

#. js-lingui-id: Mbosm8
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Group Id"
msgstr "View Filter Group Id"

#. js-lingui-id: /aP3iG
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Groups"
msgstr "View Filter Groups"

#. js-lingui-id: 4MG8+B
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter operand"
msgstr "View Filter operand"

#. js-lingui-id: OrUkUF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter related view"
msgstr "View Filter related view"

#. js-lingui-id: TyXOtD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter target field"
msgstr "View Filter target field"

#. js-lingui-id: 3KzkxN
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter value"
msgstr "View Filter value"

#. js-lingui-id: vj5JsR
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filters"
msgstr "View Filters"

#. js-lingui-id: ziEP12
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group"
msgstr "View Group"

#. js-lingui-id: uQ3c2q
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group related view"
msgstr "View Group related view"

#. js-lingui-id: EFlLpQ
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group target field"
msgstr "View Group target field"

#. js-lingui-id: b1Vc+l
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group visibility"
msgstr "View Group visibility"

#. js-lingui-id: V4nZs/
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Groups"
msgstr "View Groups"

#. js-lingui-id: qXlovu
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View icon"
msgstr "View icon"

#. js-lingui-id: cd/+ZD
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View Kanban column field"
msgstr "View Kanban column field"

#. js-lingui-id: vdfZ+A
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View key"
msgstr "View key"

#. js-lingui-id: oOljSE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View name"
msgstr "View name"

#. js-lingui-id: rhO8zp
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View position"
msgstr "View position"

#. js-lingui-id: EUjpwJ
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort"
msgstr "View Sort"

#. js-lingui-id: 6ZUale
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort direction"
msgstr "View Sort direction"

#. js-lingui-id: /rCPqN
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort related view"
msgstr "View Sort related view"

#. js-lingui-id: +du2wy
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort target field"
msgstr "View Sort target field"

#. js-lingui-id: UsdY3K
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sorts"
msgstr "View Sorts"

#. js-lingui-id: clWwIZ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View target object"
msgstr "View target object"

#. js-lingui-id: bJAIqT
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View type"
msgstr "View type"

#. js-lingui-id: 1I6UoR
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Views"
msgstr "Views"

#. js-lingui-id: 2q/Q7x
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Visibility"
msgstr "Visibility"

#. js-lingui-id: oh8+os
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Visible"
msgstr "Visible"

#. js-lingui-id: TRDppN
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook"
msgstr "Webhook"

#. js-lingui-id: fyB2Wp
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operation"
msgstr "Webhook operation"

#. js-lingui-id: gONLmX
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operations"
msgstr "Webhook operations"

#. js-lingui-id: cPoSTF
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook target url"
msgstr "Webhook target url"

#. js-lingui-id: v1kQyJ
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhooks"
msgstr "Webhooks"

#. js-lingui-id: TVkCrJ
#: src/engine/core-modules/email-verification/services/email-verification.service.ts
msgid "Welcome to Twenty: Please Confirm Your Email"
msgstr "Welcome to Twenty: Please Confirm Your Email"

#. js-lingui-id: bLt/0J
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Workflow"
msgstr "Workflow"

#. js-lingui-id: hdtWQn
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow automated triggers linked to the workflow."
msgstr "Workflow automated triggers linked to the workflow."

#. js-lingui-id: E03XpH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Workflow event listeners linked to the workflow."
#~ msgstr "Workflow event listeners linked to the workflow."

#. js-lingui-id: vwSkSW
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow linked to the run."
msgstr "Workflow linked to the run."

#. js-lingui-id: y9tnFx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow record position"
msgstr "Workflow record position"

#. js-lingui-id: 5vIcqC
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Workflow Run"
msgstr "Workflow Run"

#. js-lingui-id: 3Iz+qz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run ended at"
msgstr "Workflow run ended at"

#. js-lingui-id: IUaK6s
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run position"
msgstr "Workflow run position"

#. js-lingui-id: zaN7tH
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run started at"
msgstr "Workflow run started at"

#. js-lingui-id: 1TU2A8
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run status"
msgstr "Workflow run status"

#. js-lingui-id: u6DF/V
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow Runs"
msgstr "Workflow Runs"

#. js-lingui-id: 9nOy7k
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow runs linked to the version."
msgstr "Workflow runs linked to the version."

#. js-lingui-id: c37F3j
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow runs linked to the workflow."
msgstr "Workflow runs linked to the workflow."

#. js-lingui-id: lTXctu
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version"
msgstr "Workflow version"

#. js-lingui-id: +wYPET
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Version"
msgstr "Workflow Version"

#. js-lingui-id: n4/IOE
#: src/modules/workflow/common/utils/assert-workflow-version-has-steps.ts
msgid "Workflow version does not contain at least one step"
msgstr "Workflow version does not contain at least one step"

#. js-lingui-id: k4DPlQ
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/common/utils/assert-workflow-version-trigger-is-defined.util.ts
msgid "Workflow version does not contain trigger"
msgstr "Workflow version does not contain trigger"

#. js-lingui-id: bqFL4g
#: src/modules/workflow/common/utils/assert-workflow-version-is-draft.util.ts
msgid "Workflow version is not in draft status"
msgstr "Workflow version is not in draft status"

#. js-lingui-id: CocTJJ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version linked to the run."
msgstr "Workflow version linked to the run."

#. js-lingui-id: 9l+pJT
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow version position"
msgstr "Workflow version position"

#. js-lingui-id: OCyhkn
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Versions"
msgstr "Workflow Versions"

#. js-lingui-id: 018fP9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow versions linked to the workflow."
msgstr "Workflow versions linked to the workflow."

#. js-lingui-id: s7p+TL
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger"
msgstr "WorkflowAutomatedTrigger"

#. js-lingui-id: 96bCgZ
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger workflow"
msgstr "WorkflowAutomatedTrigger workflow"

#. js-lingui-id: 5z92T9
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTriggers"
msgstr "WorkflowAutomatedTriggers"

#. js-lingui-id: ENOy6I
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener"
#~ msgstr "WorkflowEventListener"

#. js-lingui-id: HN90FO
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener workflow"
#~ msgstr "WorkflowEventListener workflow"

#. js-lingui-id: 3JA9se
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListeners"
#~ msgstr "WorkflowEventListeners"

#. js-lingui-id: woYYQq
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflows"
msgstr "Workflows"

#. js-lingui-id: urCUgs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "WorkflowVersion"
msgstr "WorkflowVersion"

#. js-lingui-id: b4kire
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "WorkflowVersion workflow"
msgstr "WorkflowVersion workflow"

#. js-lingui-id: rklt6M
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Workspace is not ready to welcome new members"
msgstr "Workspace is not ready to welcome new members"

#. js-lingui-id: CbGxon
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Workspace member"
msgstr "Workspace member"

#. js-lingui-id: qc38qR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Workspace Member"
msgstr "Workspace Member"

#. js-lingui-id: R1S9pO
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member avatar"
msgstr "Workspace member avatar"

#. js-lingui-id: 5VCX7o
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member name"
msgstr "Workspace member name"

#. js-lingui-id: NiUpuN
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member position"
msgstr "Workspace member position"

#. js-lingui-id: YCAEr+
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace Members"
msgstr "Workspace Members"

#. js-lingui-id: EtzFC0
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "WorkspaceMember"
msgstr "WorkspaceMember"

#. js-lingui-id: fV2Bu6
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Wrong password"
msgstr "Wrong password"

#. js-lingui-id: 0gv+T2
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "X"
msgstr "X"

#. js-lingui-id: z+7x/s
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "You must be authenticated to perform this action."
msgstr "You must be authenticated to perform this action."

#. js-lingui-id: bTyBrW
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Your Password Has Been Successfully Changed"
msgstr "Your Password Has Been Successfully Changed"

#. js-lingui-id: vPccnr
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Your team member responsible for managing the company account"
msgstr "Your team member responsible for managing the company account"

#. js-lingui-id: ej/tZF
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "Your workspace has been updated with a new data model. Please refresh the page."
msgstr "Your workspace has been updated with a new data model. Please refresh the page."
