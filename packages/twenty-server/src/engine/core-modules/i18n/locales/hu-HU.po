msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-29 18:14+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: hu\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Hungarian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: hu\n"
"X-Crowdin-File: /packages/twenty-server/src/engine/core-modules/i18n/locales/en.po\n"
"X-Crowdin-File-ID: 31\n"

#. js-lingui-id: Qyrd7v
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "(System) View Fields"
msgstr "(Rendszer) Nézet mezők"

#. js-lingui-id: 9Y3fTB
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "(System) View Filter Groups"
msgstr "(Rendszer) Nézet szűrőcsoportok"

#. js-lingui-id: TB2jLV
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "(System) View Filters"
msgstr "(Rendszer) Nézet szűrők"

#. js-lingui-id: Y7M7Ro
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "(System) View Groups"
msgstr "(Rendszer) Nézet csoportok"

#. js-lingui-id: 9vliLw
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "(System) View Sorts"
msgstr "(Rendszer) Nézet rendezések"

#. js-lingui-id: 5B59WE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "(System) Views"
msgstr "(Rendszer) Nézetek"

#. js-lingui-id: Q0ISF1
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "{fieldName} and {fieldName}Id cannot be both provided."
msgstr ""

#. js-lingui-id: 8haj+G
#: src/engine/metadata-modules/utils/validate-metadata-name-is-camel-case.utils.ts
msgid "{name} should be in camelCase"
msgstr ""

#. js-lingui-id: kZR6+h
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "A company"
msgstr "Egy cég"

#. js-lingui-id: +aeifv
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "A connected account"
msgstr "Egy összekapcsolt fiók"

#. js-lingui-id: HCoswz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "A favorite that can be accessed from the left menu"
msgstr "Egy kedvenc, amely elérhető a bal oldali menüből"

#. js-lingui-id: 6w8bHl
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "A Folder of favorites"
msgstr "Egy kedvencek mappája"

#. js-lingui-id: sSGYmf
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "A group of related messages (e.g. email thread, chat thread)"
msgstr "Egy kapcsolódó üzenetcsoport (pl. e-mail lánc, csevegőszál)"

#. js-lingui-id: vZj1Xc
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "A message sent or received through a messaging channel (email, chat, etc.)"
msgstr "Egy üzenetcsatornán keresztül küldött vagy fogadott üzenet (e-mail, csevegés stb.)"

#. js-lingui-id: bufuBA
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "A note"
msgstr "Egy jegyzet"

#. js-lingui-id: 6kUkZW
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "A note target"
msgstr "Egy jegyzet célja"

#. js-lingui-id: Io42ej
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "A person"
msgstr "Egy személy"

#. js-lingui-id: Q2De+v
#: src/engine/metadata-modules/role/role.service.ts
msgid "A role with this label already exists."
msgstr ""

#. js-lingui-id: mkFXEH
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "A task"
msgstr "Egy feladat"

#. js-lingui-id: hk2NzW
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "A task target"
msgstr "Egy feladat célja"

#. js-lingui-id: HTSJFW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "A webhook"
msgstr "Egy webhook"

#. js-lingui-id: ZIN9Ga
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "A workflow"
msgstr "Egy munkafolyamat"

#. js-lingui-id: YwBCp8
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "A workflow automated trigger"
msgstr ""

#. js-lingui-id: juBVjt
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "A workflow event listener"
#~ msgstr "Egy munkafolyamat eseményhallgatója"

#. js-lingui-id: 1+xDbI
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "A workflow run"
msgstr "Egy munkafolyamat futása"

#. js-lingui-id: N0g7rp
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "A workflow version"
msgstr "Egy munkafolyamat verziója"

#. js-lingui-id: HpZ/I5
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "A workspace member"
msgstr "Egy munkaterület tagja"

#. js-lingui-id: GDKKxT
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Access Token"
msgstr "Hozzáférési token"

#. js-lingui-id: pd81Qb
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Account Owner"
msgstr "Fióktulajdonos"

#. js-lingui-id: loqL/f
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account owner for companies"
msgstr "Fióktulajdonos cégek esetén"

#. js-lingui-id: HZosRi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account Owner For Companies"
msgstr "Fióktulajdonos cégek esetén"

#. js-lingui-id: 48AxkT
#: src/engine/workspace-manager/workspace-cleaner/services/cleaner.workspace-service.ts
msgid "Action needed to prevent workspace deletion"
msgstr "Művelet szükséges a munkaterület törlésének megakadályozásához"

#. js-lingui-id: j0DfGR
#: src/engine/core-modules/auth/services/reset-password.service.ts
msgid "Action Needed to Reset Password"
msgstr "Művelet szükséges a jelszó visszaállításához"

#. js-lingui-id: Du6bPw
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address"
msgstr "Cím"

#. js-lingui-id: JiOJxf
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address (deprecated) "
msgstr "Cím (elavult) "

#. js-lingui-id: Knl3c9
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company"
msgstr "A cég címe"

#. js-lingui-id: zJhwjv
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company - deprecated in favor of new address field"
msgstr "A cég címe - elavult, az új címeztési mező javára"

#. js-lingui-id: ZfVqbP
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Aggregate operation"
msgstr "Összegző művelet"

#. js-lingui-id: W58PBh
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Aggregated / filtered event to be displayed on the timeline"
msgstr "Összesített/szűrt események, amelyek a cronológiaon jelennek meg"

#. js-lingui-id: hehnjM
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Amount"
msgstr "Összeg"

#. js-lingui-id: qeHcQj
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "An API key"
msgstr "Egy API kulcs"

#. js-lingui-id: MjyFvC
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "An attachment"
msgstr "Egy csatolmány"

#. js-lingui-id: +bL++X
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "An audit log of actions performed in the system"
#~ msgstr "Az akciókról készült naplózás a rendszerben"

#. js-lingui-id: XyOToQ
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "An error occurred."
msgstr ""

#. js-lingui-id: muVHgL
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "An event related to user behavior"
#~ msgstr "A felhasználói magatartással kapcsolatos esemény"

#. js-lingui-id: bZq8rL
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "An opportunity"
msgstr "Egy lehetőség"

#. js-lingui-id: JKWicb
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Annual Recurring Revenue: The actual or estimated annual revenue of the company"
msgstr "Éves ismétlődő bevétel: A cég tényleges vagy becsült éves bevétel"

#. js-lingui-id: yRnk5W
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Key"
msgstr "API Kulcs"

#. js-lingui-id: FfSJ1Y
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Keys"
msgstr "API Kulcsok"

#. js-lingui-id: puNs/l
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey expiration date"
msgstr "Api kulcs lejárati dátuma"

#. js-lingui-id: YHiNxr
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey name"
msgstr "Api kulcs neve"

#. js-lingui-id: xrndTt
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey revocation date"
msgstr "Api kulcs visszavonásának dátuma"

#. js-lingui-id: DGW5iN
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain does not match email domain"
msgstr ""

#. js-lingui-id: BKvuAq
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain has already been validated"
msgstr ""

#. js-lingui-id: 3EiOLz
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ARR"
msgstr "ARR"

#. js-lingui-id: Ek7xGj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Assigned tasks"
msgstr "Kijelölt feladatok"

#. js-lingui-id: ojKCLU
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Assignee"
msgstr "Felelős"

#. js-lingui-id: Max2GU
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Associated User Id"
msgstr "Kapcsolódó Felhasználó Azonosítója"

#. js-lingui-id: UY1vmE
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment"
msgstr "Csatolmány"

#. js-lingui-id: JAefBH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment author"
msgstr "Csatolmány szerzője"

#. js-lingui-id: bIaesZ
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment company"
msgstr "Csatolmány cég"

#. js-lingui-id: gfGYcl
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment full path"
msgstr "Csatolmány teljes elérési útja"

#. js-lingui-id: wjocwa
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment name"
msgstr "Csatolmány neve"

#. js-lingui-id: FWlOXr
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment note"
msgstr "Csatolmány megjegyzése"

#. js-lingui-id: YASWpH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment opportunity"
msgstr "Csatolmány lehetősége"

#. js-lingui-id: P38yED
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment person"
msgstr "Csatolmány személy"

#. js-lingui-id: Tx1DxS
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment task"
msgstr "Csatolmány feladat"

#. js-lingui-id: 4qqxMt
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment type"
msgstr "Csatolmány típusa"

#. js-lingui-id: w/Sphq
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments"
msgstr "Csatolmányok"

#. js-lingui-id: 2tQOVc
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Attachments created by the workspace member"
msgstr "A munkaterület tag által létrehozott csatolmányok"

#. js-lingui-id: iVnA89
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Attachments linked to the company"
msgstr "A céghez kapcsolódó csatolmányok"

#. js-lingui-id: MuTXtT
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Attachments linked to the contact."
msgstr "A kapcsolattartóhoz kapcsolódó csatolmányok."

#. js-lingui-id: kw0mLu
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Attachments linked to the opportunity"
msgstr "A lehetőséghez kapcsolódó csatolmányok"

#. js-lingui-id: fCbqr7
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments tied to the {label}"
msgstr "Csatolmányok, amelyek a(z) {label}-hez kapcsolódnak"

#. js-lingui-id: ilRCh1
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Log"
#~ msgstr "Audit Napló"

#. js-lingui-id: EPEFrH
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Logs"
#~ msgstr "Audit Naplók"

#. js-lingui-id: RqCC/0
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#~ msgid "Audit Logs linked to the workspace member"
#~ msgstr "A munkaterület taghoz kapcsolódó Audit Naplók"

#. js-lingui-id: cNBqH+
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Auth failed at"
msgstr "Sikertelen hitelesítés időpontja"

#. js-lingui-id: LB4qX/
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Authentication is not enabled with this provider."
msgstr ""

#. js-lingui-id: VbeIOx
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Author"
msgstr "Szerző"

#. js-lingui-id: XJONK6
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Authored attachments"
msgstr "Szerző által létrehozott csatolmányok"

#. js-lingui-id: cvSznK
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Automated Trigger Type"
msgstr ""

#. js-lingui-id: 2mtBXs
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Automated Triggers"
msgstr ""

#. js-lingui-id: RpExX0
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Automatically create People records when receiving or sending emails"
msgstr "Automatikusan hozzon létre Személy rekordokat email fogadásakor vagy küldésekor"

#. js-lingui-id: lXxdId
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Automatically create records for people you participated with in an event."
msgstr "Automatikusan hozzon létre rekordokat azokról a személyekről, akikkel egy eseményen részt vett."

#. js-lingui-id: kfcRb0
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Avatar"
msgstr "Avatár"

#. js-lingui-id: S/mJUR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Avatar Url"
msgstr "Avatár URL"

#. js-lingui-id: 20B9kW
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Event"
#~ msgstr "Viselkedésbeli Esemény"

#. js-lingui-id: Jeh/Q/
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Events"
#~ msgstr "Viselkedésbeli Események"

#. js-lingui-id: K1172m
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklist"
msgstr "Tiltólista"

#. js-lingui-id: Tv2hxv
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Blocklisted handles"
msgstr "Tiltólistára tett kezelők"

#. js-lingui-id: L5JhJe
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklists"
msgstr "Tiltólisták"

#. js-lingui-id: bGQplw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body"
msgstr "Szövegtörzs"

#. js-lingui-id: Nl8eMw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body (deprecated)"
msgstr "Törzs (elavult)"

#. js-lingui-id: 8mVqF7
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Cached record name"
msgstr "Gyorsítótárban lévő rekord neve"

#. js-lingui-id: Nh6GTX
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channel"
msgstr "Naptár Csatorna"

#. js-lingui-id: jfNQ0m
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Association"
msgstr "Naptár Csatorna Esemény Társítás"

#. js-lingui-id: kYNT3F
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Associations"
msgstr "Naptár Csatorna Esemény Társítások"

#. js-lingui-id: Znix/S
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channels"
msgstr "Naptár csatornák"

#. js-lingui-id: bRk+FR
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar event"
msgstr "Naptári esemény"

#. js-lingui-id: N2kMfO
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participant"
msgstr "Naptári esemény résztvevője"

#. js-lingui-id: AWDqkQ
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participants"
msgstr "Naptári esemény résztvevők"

#. js-lingui-id: ZI2UyM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Calendar Event Participants"
msgstr "Naptári Események Résztvevői"

#. js-lingui-id: X9A2xC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar events"
msgstr "Naptári események"

#. js-lingui-id: vxbVwc
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Cannot activate non-draft or non-last-published version"
msgstr ""

#. js-lingui-id: LIPsWu
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create multiple draft versions for the same workflow"
msgstr ""

#. js-lingui-id: RggZr4
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create workflow version with status other than draft"
msgstr ""

#. js-lingui-id: AQyr9X
#: src/engine/metadata-modules/field-metadata/services/field-metadata.service.ts
msgid "Cannot delete, please update the label identifier field first"
msgstr ""

#. js-lingui-id: FrNWt0
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot disable non-active workflow version"
msgstr ""

#. js-lingui-id: IDMgr/
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot have more than one active workflow version"
msgstr ""

#. js-lingui-id: 4DKo3U
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot update workflow version status manually"
msgstr ""

#. js-lingui-id: 4IVK41
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Channel ID"
msgstr "Csatornaazonosító"

#. js-lingui-id: Ubg/B+
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Channel Type"
msgstr "Csatornatípus"

#. js-lingui-id: 3wV73y
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "City"
msgstr "Város"

#. js-lingui-id: NRF7pg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Close date"
msgstr "Lezárás dátuma"

#. js-lingui-id: 96YB1a
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Color Scheme"
msgstr "Színséma"

#. js-lingui-id: 07OSD1
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Compact View"
msgstr "Kompakt nézet"

#. js-lingui-id: s2QZS6
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Companies"
msgstr "Cégek"

#. js-lingui-id: 7i8j3G
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Company"
msgstr "Cég"

#. js-lingui-id: yA1de7
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Company record position"
msgstr "Cégrekord pozíciója"

#. js-lingui-id: AgktVC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Conference Solution"
msgstr "Konferencia megoldás"

#. js-lingui-id: 1fFL4B
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Connect is not allowed for {connectFieldName} on {objectMetadataNameSingular}"
msgstr ""

#. js-lingui-id: PQ1Dw2
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Connected Account"
msgstr "Csatlakoztatott fiók"

#. js-lingui-id: 9TzudL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Connected accounts"
msgstr "Csatlakoztatott fiókok"

#. js-lingui-id: AMDUqA
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Connected Accounts"
msgstr "Csatlakoztatott Fiókok"

#. js-lingui-id: bA6O5B
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Contact auto creation policy"
msgstr "Kapcsolattartó automatikus létrehozásának politikája"

#. js-lingui-id: RnsmQs
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s avatar"
msgstr "Kapcsolattartó avatárja"

#. js-lingui-id: pL+pqi
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s city"
msgstr "Kapcsolattartó városa"

#. js-lingui-id: VnWMlz
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s company"
msgstr "Kapcsolattartó cége"

#. js-lingui-id: ITlFIB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Emails"
msgstr "Kapcsolattartó Email címei"

#. js-lingui-id: GuRtLY
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s job title"
msgstr "Kapcsolattartó munkaköre"

#. js-lingui-id: QrCvRQ
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Linkedin account"
msgstr "Kapcsolattartó Linkedin fiókja"

#. js-lingui-id: 6xPSVt
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s name"
msgstr "Kapcsolattartó neve"

#. js-lingui-id: Y37CZ4
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone number"
msgstr "Kapcsolattartó telefonszáma"

#. js-lingui-id: zsW3gg
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone numbers"
msgstr "Kapcsolattartó telefonszámai"

#. js-lingui-id: uuZ00G
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s X/Twitter account"
msgstr "Kapcsolattartó X/Twitter fiókja"

#. js-lingui-id: M73whl
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Context"
msgstr "Kontextus"

#. js-lingui-id: NCIYDF
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Created by"
msgstr "Létrehozta"

#. js-lingui-id: wPvFAD
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Creation date"
msgstr "Létrehozás dátuma"

#. js-lingui-id: CJXWmO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Creation DateTime"
msgstr "Létrehozás DateTime-ja"

#. js-lingui-id: umhXiy
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Cron pattern '{pattern}' is invalid"
msgstr ""

#. js-lingui-id: Mikszu
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Custom Connection Parameters"
msgstr ""

#. js-lingui-id: Lhd0oQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Date format"
msgstr "Dátumformátum"

#. js-lingui-id: 1lL5Iu
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Date when the record was deleted"
msgstr "Rekord törlésének dátuma"

#. js-lingui-id: QN9ahV
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Deleted at"
msgstr "Törölve"

#. js-lingui-id: U1bSSI
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Describes if the view is in compact mode"
msgstr "Leírja, hogy a nézet kompakt módban van-e"

#. js-lingui-id: Nu4oKW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Description"
msgstr "Leírás"

#. js-lingui-id: MRB7nI
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Direction"
msgstr "Irány"

#. js-lingui-id: 0gS7M5
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Display Name"
msgstr "Megjelenítendő név"

#. js-lingui-id: y7HJTU
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Display the records in a side panel or in a record page"
msgstr "A rekordok megjelenítése oldalsó panelen vagy rekordoldalon"

#. js-lingui-id: 0H/D9K
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Display Value"
msgstr "Megjelenítési érték"

#. js-lingui-id: wMncXq
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Domain Name"
msgstr "Domain név"

#. js-lingui-id: YOowcq
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Due Date"
msgstr "Esedékesség dátuma"

#. js-lingui-id: BBXuRb
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email already verified."
msgstr ""

#. js-lingui-id: xw6iqb
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Email is not verified."
msgstr ""

#. js-lingui-id: ZsZeV2
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Email is required"
msgstr ""

#. js-lingui-id: y8LSMh
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email verification not required."
msgstr ""

#. js-lingui-id: BXEcos
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Emails"
msgstr "Email címek"

#. js-lingui-id: gqv5ZL
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Employees"
msgstr "Alkalmazottak"

#. js-lingui-id: VFv2ZC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "End Date"
msgstr "Befejezés dátuma"

#. js-lingui-id: k//6Xs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event company"
msgstr "Esemény cég"

#. js-lingui-id: FJ7QI4
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event context"
#~ msgstr "Esemény kontextus"

#. js-lingui-id: kJDmsI
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event details"
msgstr "Esemény részletei"

#. js-lingui-id: 0JhmlM
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event external ID"
msgstr "Esemény külső azonosítója"

#. js-lingui-id: aZJLAR
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event ID"
msgstr "Esemény azonosító"

#. js-lingui-id: 81maJp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Event Listeners"
#~ msgstr "Eseményfigyelők"

#. js-lingui-id: PYs3rP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event name"
msgstr "Esemény neve"

#. js-lingui-id: evIGwh
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event name/type"
#~ msgstr "Esemény megnevezése/típusa"

#. js-lingui-id: vv99Wu
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event note"
msgstr "Esemény jegyzet"

#. js-lingui-id: eSt759
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event opportunity"
msgstr "Esemény lehetőség"

#. js-lingui-id: aicVfT
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Event Participants"
msgstr "Esemény résztvevői"

#. js-lingui-id: 0mp45a
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event person"
msgstr "Esemény személy"

#. js-lingui-id: mMq0Wy
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event task"
msgstr "Esemény feladat"

#. js-lingui-id: dCV1dS
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow"
msgstr "Esemény munkafolyamat"

#. js-lingui-id: W84pl6
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow run"
msgstr "Esemény munkafolyamat futtatása"

#. js-lingui-id: vUpps9
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow version"
msgstr "Esemény munkafolyamat verziója"

#. js-lingui-id: LxOGsB
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workspace member"
msgstr "Esemény munkaterület tag"

#. js-lingui-id: tst44n
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events"
msgstr "Események"

#. js-lingui-id: fHL+iH
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events linked to the person"
msgstr "Személyhez kötődő események"

#. js-lingui-id: 3/O8MM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Events linked to the workspace member"
msgstr "Munkaterületi taghoz kötődő események"

#. js-lingui-id: QQlMid
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude group emails"
msgstr "Csoportos email címek kizárása"

#. js-lingui-id: kenYGr
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude non professional emails"
msgstr "Nem professzionális email címek kizárása"

#. js-lingui-id: Lo5U0b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Executed by"
msgstr "Végrehajtotta"

#. js-lingui-id: AiFXmG
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Expected the same constraint fields to be used consistently across all operations for {connectFieldName}."
msgstr ""

#. js-lingui-id: sZg7s1
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Expiration date"
msgstr "Lejárat dátuma"

#. js-lingui-id: 6Ki4Pv
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite"
msgstr "Kedvenc"

#. js-lingui-id: aKUOPp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite company"
msgstr "Kedvenc cég"

#. js-lingui-id: TDlZ/o
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite Folder"
msgstr "Kedvenc Mappa"

#. js-lingui-id: WDVfUH
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite folder position"
msgstr "Kedvenc mappa pozíció"

#. js-lingui-id: SStz54
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite Folders"
msgstr "Kedvenc mappák"

#. js-lingui-id: dz/fFp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite note"
msgstr "Kedvenc jegyzet"

#. js-lingui-id: RRr9Bp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite opportunity"
msgstr "Kedvenc lehetőség"

#. js-lingui-id: RM6B6V
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite person"
msgstr "Kedvenc személy"

#. js-lingui-id: oVA9vM
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite position"
msgstr "Kedvenc pozíció"

#. js-lingui-id: OaQVQH
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite task"
msgstr "Kedvenc feladat"

#. js-lingui-id: VkWV1s
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite view"
msgstr "Kedvelt nézet"

#. js-lingui-id: l+lx2f
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow"
msgstr "Kedvenc folyamat"

#. js-lingui-id: BPBnux
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow run"
msgstr "Kedvenc folyamat futás"

#. js-lingui-id: 5X6Vlz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow version"
msgstr "Kedvenc folyamat változat"

#. js-lingui-id: FDpezg
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workspace member"
msgstr "Kedvenc munkaterület tag"

#. js-lingui-id: X9kySA
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites"
msgstr "Kedvencek"

#. js-lingui-id: 0CzeFL
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorites in this folder"
msgstr "Kedvencek ebben a mappában"

#. js-lingui-id: zQponA
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Favorites linked to the company"
msgstr "A társasághoz kötődő kedvencek"

#. js-lingui-id: VaKLrB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Favorites linked to the contact"
msgstr "A kapcsolattartóhoz kötődő kedvencek"

#. js-lingui-id: GOfcBt
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Favorites linked to the note"
msgstr "Jegyzethez kötődő kedvencek"

#. js-lingui-id: 9zd8hg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Favorites linked to the opportunity"
msgstr "Lehetőséghez kötődő kedvencek"

#. js-lingui-id: L5ccWD
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Favorites linked to the task"
msgstr "Feladathoz kötődő kedvencek"

#. js-lingui-id: R+1ib/
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Favorites linked to the view"
msgstr "Nézethez kötődő kedvencek"

#. js-lingui-id: ee0tmj
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Favorites linked to the workflow"
msgstr "Folyamathoz kötődő kedvencek"

#. js-lingui-id: zar5jz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Favorites linked to the workflow run"
msgstr "Folyamat futáshoz kötődő kedvencek"

#. js-lingui-id: 499/sw
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Favorites linked to the workflow version"
msgstr "Folyamat változathoz kötődő kedvencek"

#. js-lingui-id: rgmtb4
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Favorites linked to the workspace member"
msgstr "Munkaterület tagjához kötődő kedvencek"

#. js-lingui-id: GyXrE1
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites tied to the {label}"
msgstr "A {label} kapcsolódó kedvencek"

#. js-lingui-id: nSFFML
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Field Metadata Id"
msgstr "Mező Metadatázonosítója"

#. js-lingui-id: g+t8w9
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Field metadata used for aggregate operation"
msgstr "Összegző műveletre használt mező metadaták"

#. js-lingui-id: J4hC2m
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Field used for full-text search"
msgstr "Teljes szöveges keresésre felhasznált mező"

#. js-lingui-id: zuDgzc
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Field Value"
msgstr "Mezőérték"

#. js-lingui-id: 3oQ73E
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have a previous step"
#~ msgstr ""

#. js-lingui-id: Zllikb
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have only one previous step"
#~ msgstr ""

#. js-lingui-id: lEpDue
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder for Message Channel"
msgstr "Üzenetcsatorna mappa"

#. js-lingui-id: cqQyPB
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder name"
msgstr "Mappanév"

#. js-lingui-id: kuN9I4
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have a defined label and type"
msgstr ""

#. js-lingui-id: QK3OvQ
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have unique names"
msgstr ""

#. js-lingui-id: KYP1t5
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action must have at least one field"
msgstr ""

#. js-lingui-id: oQA+cA
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Full path"
msgstr "Teljes útvonal"

#. js-lingui-id: UzpVUy
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Group by this field value"
msgstr "Csoportosítsa a mezőérték alapján"

#. js-lingui-id: TkE8dW
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "handle"
msgstr "kezelő"

#. js-lingui-id: Nf7oXL
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Handle"
msgstr "Kezelő"

#. js-lingui-id: GvgxWx
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Handle Aliases"
msgstr "Kezelőnevek"

#. js-lingui-id: VehAU2
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Header message Id"
msgstr "Fejléc üzenetazonosítója"

#. js-lingui-id: NNJnBi
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "iCal UID"
msgstr "iCal azonosító"

#. js-lingui-id: wwu18a
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Icon"
msgstr "Ikon"

#. js-lingui-id: CiyiKN
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ICP"
msgstr "ICP"

#. js-lingui-id: dFb5Nt
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Id"
msgstr "Azonosító"

#. js-lingui-id: Ebc83S
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Ideal Customer Profile:  Indicates whether the company is the most suitable and valuable customer for you"
msgstr "Ideális vevőprofil: Jelzi, hogy a cég a legmegfelelőbb és legértékesebb ügyfél-e számotokra"

#. js-lingui-id: NrA0WZ
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "If the event is related to a particular object"
#~ msgstr "Ha az esemény egy adott objektumhoz kapcsolódik"

#. js-lingui-id: 71VJzG
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-short.utils.ts
msgid "Input is too short: \"{name}\""
msgstr ""

#. js-lingui-id: X7+bC6
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid calling code {callingCode}"
msgstr ""

#. js-lingui-id: 1PtrY0
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid country code {countryCode}"
msgstr ""

#. js-lingui-id: 4EtFrA
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid day value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: MBpeQ6
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer between 0 and 23"
msgstr ""

#. js-lingui-id: 9xsjn9
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: FSunWx
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Invalid label: \"{label}\""
msgstr ""

#. js-lingui-id: iBBCnf
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer between 0 and 59"
msgstr ""

#. js-lingui-id: hYcFSd
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: ApP70c
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: sHcAMD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid trigger type for enabling workflow trigger"
msgstr ""

#. js-lingui-id: grHYXZ
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is canceled"
msgstr "Törölve van"

#. js-lingui-id: k+g9Uh
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Contact Auto Creation Enabled"
msgstr "Kapcsolat Automatikus Létrehozása Aktiválva"

#. js-lingui-id: uBx1xd
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is Full Day"
msgstr "Egész napos"

#. js-lingui-id: 3iAfL2
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Is Organizer"
msgstr "Szervező"

#. js-lingui-id: Mqzqb8
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Sync Enabled"
msgstr "Szinkronizálás Aktiválva"

#. js-lingui-id: 27z+FV
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Job Title"
msgstr "Munkakör megnevezése"

#. js-lingui-id: PviVyk
#: src/engine/core-modules/workspace-invitation/services/workspace-invitation.service.ts
msgid "Join your team on Twenty"
msgstr "Csatlakozzon a csapatához a Twentyn"

#. js-lingui-id: WNfJ8M
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "JSON object containing custom connection parameters"
msgstr ""

#. js-lingui-id: shbh25
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Json object to provide context (user, device, workspace, etc.)"
#~ msgstr "Json objektum a kontextus megadásához (felhasználó, eszköz, munkaterület stb.)"

#. js-lingui-id: HgJ9jQ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Json object to provide output of the workflow run"
msgstr "Json objektum a munkafolyamat futásának eredményeinek megadásához"

#. js-lingui-id: AQuLwi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide steps"
msgstr "Json objektum a lépések megadásához"

#. js-lingui-id: fOjmZ1
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide trigger"
msgstr "Json objektum a trigger megadásához"

#. js-lingui-id: TVc0/Q
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Json value for event details"
msgstr "Json érték az esemény részleteihez"

#. js-lingui-id: qmarO2
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "kanbanfieldMetadataId"
msgstr "kanbanfieldMetadatId"

#. js-lingui-id: 7sMeHQ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Key"
msgstr "Kulcs"

#. js-lingui-id: w8Rv8T
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Label is required"
msgstr ""

#. js-lingui-id: 4EGmy6
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not be empty"
msgstr ""

#. js-lingui-id: k731jp
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not contain a comma"
msgstr ""

#. js-lingui-id: vXIe7J
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Language"
msgstr "Nyelv"

#. js-lingui-id: WvYp6o
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Last published Version Id"
msgstr "Utolsó közzétett verzió azonosítója"

#. js-lingui-id: Y60/DC
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Last sync cursor"
msgstr "Utolsó szinkronizációs kurzor"

#. js-lingui-id: Zdx9Qq
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Last sync date"
msgstr "Utolsó szinkronizációs dátum"

#. js-lingui-id: c7uqNg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Last sync history ID"
msgstr "Utolsó szinkronizációs történet azonosító"

#. js-lingui-id: tGwswq
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last time the record was changed"
msgstr "Utolsó módosítás időpontja"

#. js-lingui-id: o1zvNS
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last update"
msgstr "Utolsó frissítés"

#. js-lingui-id: wR9USP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Object Metadata Id"
msgstr "Kapcsolódó Objektum Metadat Azonosító"

#. js-lingui-id: PV1Nm7
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Linked Opportunities"
msgstr "Kapcsolódó Lehetőségek"

#. js-lingui-id: 6YiMr4
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record cached name"
msgstr "Kapcsolódó Rekord gyorsítótárban tárolt neve"

#. js-lingui-id: sgHAxx
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record id"
msgstr "Kapcsolódó Rekord azonosító"

#. js-lingui-id: uCA6be
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Linkedin"
msgstr "LinkedIn"

#. js-lingui-id: LeFv/R
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "List of opportunities for which that person is the point of contact"
msgstr "A lehetőségek listája, amelyekért az adott személy a kapcsolattartó"

#. js-lingui-id: wJijgU
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Location"
msgstr "Helyszín"

#. js-lingui-id: y+q8Lv
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical Operator"
msgstr "Logikai Operátor"

#. js-lingui-id: 6kQXsS
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical operator for the filter group"
msgstr "A szűrőcsoport logikai operátora"

#. js-lingui-id: EnfPm2
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Meet Link"
msgstr "Meet hivatkozás"

#. js-lingui-id: xDAtGP
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message"
msgstr "\\"

#. js-lingui-id: g+QGD6
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel"
msgstr "Üzenetcsatorna"

#. js-lingui-id: DzU4a3
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel Association"
msgstr "Üzenetcsatorna-társítás"

#. js-lingui-id: wd/R++
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Id"
msgstr "Üzenetcsatorna-azonosító"

#. js-lingui-id: disipM
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Association"
msgstr "Üzenetcsatorna üzenet-társítás"

#. js-lingui-id: ijQY3P
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Associations"
msgstr "Üzenetcsatorna üzenet-társítások"

#. js-lingui-id: k7LXPQ
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Message Channels"
msgstr "Üzenetcsatornák"

#. js-lingui-id: /4uGJc
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Direction"
msgstr "Üzenet irány"

#. js-lingui-id: fBw8fQ
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message External Id"
msgstr "Üzenet külső azonosítója"

#. js-lingui-id: CStLnc
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Message Folder"
msgstr "Üzenetmappa"

#. js-lingui-id: p4ANpY
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Folders"
msgstr "Üzenetmappák"

#. js-lingui-id: 6icznk
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Id"
msgstr "Üzenet-azonosító"

#. js-lingui-id: 9FJSpK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message id from the message header"
msgstr "Az üzenet fejlécből származó üzenet-azonosító"

#. js-lingui-id: yaC1Aq
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message id from the messaging provider"
msgstr "Az üzenetküldő szolgáltatótól származó üzenet-azonosító"

#. js-lingui-id: IUmVwu
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participant"
msgstr "Üzenet résztvevő"

#. js-lingui-id: FhIFx7
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participants"
msgstr "Üzenet résztvevők"

#. js-lingui-id: IC5A8V
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Synced with a Message Channel"
msgstr "Üzenet szinkronizálva egy üzenetcsatornával"

#. js-lingui-id: de2nM/
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Thread"
msgstr "Üzenetszál"

#. js-lingui-id: km1jgD
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message Thread Id"
msgstr "Üzenetszál-azonosító"

#. js-lingui-id: RD0ecC
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Threads"
msgstr "Üzenetszálak"

#. js-lingui-id: t7TeQU
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages"
msgstr "Üzenetek"

#. js-lingui-id: WoWdku
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Messages from the channel."
msgstr "A csatornából származó üzenetek."

#. js-lingui-id: rYPBO7
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages from the thread."
msgstr "A szálból származó üzenetek."

#. js-lingui-id: XcKQrV
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider access token"
msgstr "Üzenetküldő szolgáltató hozzáférési tokene"

#. js-lingui-id: 80EvIk
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider refresh token"
msgstr "Üzenetküldő szolgáltató frissítési tokene"

#. js-lingui-id: SzCMUQ
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Missing required fields: at least one unique constraint have to be fully populated for '{connectFieldName}'."
msgstr ""

#. js-lingui-id: 6YtxFj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Name"
msgstr "Név"

#. js-lingui-id: RGz1nY
#: src/engine/metadata-modules/field-metadata/services/field-metadata-validation.service.ts
msgid "Name is not available, it may be duplicating another field's name."
msgstr ""

#. js-lingui-id: 0MobB1
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-long.utils.ts
msgid "Name is too long: it exceeds the 63 characters limit."
msgstr ""

#. js-lingui-id: EEVPOx
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Name of the favorite folder"
msgstr "A kedvenc mappa neve"

#. js-lingui-id: csMjko
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Name of the workflow run"
msgstr "A munkafolyamat-futtatás neve"

#. js-lingui-id: hQQZse
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No event name provided in database event trigger"
msgstr ""

#. js-lingui-id: 9bu19u
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "No input provided in form step"
msgstr ""

#. js-lingui-id: MWAUHo
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No pattern provided in CUSTOM cron trigger"
msgstr ""

#. js-lingui-id: pOu4u/
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No schedule provided in cron trigger"
msgstr ""

#. js-lingui-id: cLUBlS
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: QwbSxD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No steps provided in workflow version"
msgstr ""

#. js-lingui-id: 6dP8sB
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No trigger type provided"
msgstr ""

#. js-lingui-id: KiJn9B
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Note"
msgstr "Megjegyzés"

#. js-lingui-id: GGlkb7
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note attachments"
msgstr "A jegyzet csatolmányai"

#. js-lingui-id: Q1Rz+6
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note body"
msgstr "Jegyzet törzse"

#. js-lingui-id: Yp057F
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note record position"
msgstr "Jegyzet rögzítési pozíció"

#. js-lingui-id: spaO7l
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Target"
msgstr "Jegyzet Célja"

#. js-lingui-id: mkchvJ
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note targets"
msgstr "Jegyzet célpontjai"

#. js-lingui-id: tD4BxK
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Targets"
msgstr "Jegyzet Célpontjai"

#. js-lingui-id: jDThel
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note title"
msgstr "Jegyzet címe"

#. js-lingui-id: 1DBGsz
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes"
msgstr "Jegyzetek"

#. js-lingui-id: Ne73P/
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes tied to the {label}"
msgstr "A {label} jegyzetei"

#. js-lingui-id: fXBE74
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Notes tied to the company"
msgstr "A társasághoz kapcsolódó jegyzetek"

#. js-lingui-id: iQ5AH6
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Notes tied to the contact"
msgstr "A kapcsolattartóhoz kapcsolódó jegyzetek"

#. js-lingui-id: 0bi/Eh
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Notes tied to the opportunity"
msgstr "Az lehetőséghez kapcsolódó jegyzetek"

#. js-lingui-id: B2Y6QU
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget company"
msgstr "JegyzetCél társaság"

#. js-lingui-id: /mH0jo
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget note"
msgstr "JegyzetCél jegyzet"

#. js-lingui-id: DTs4tO
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget opportunity"
msgstr "JegyzetCél lehetőség"

#. js-lingui-id: gBwbnk
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget person"
msgstr "JegyzetCél személy"

#. js-lingui-id: ioJFzx
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Number of employees in the company"
msgstr "A társaság alkalmazottainak száma"

#. js-lingui-id: ECRKYR
#: src/engine/metadata-modules/utils/validate-no-other-object-with-same-name-exists-or-throw.util.ts
msgid "Object already exists"
msgstr ""

#. js-lingui-id: hhe7Ce
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Object id"
#~ msgstr "Objektum azonosító"

#. js-lingui-id: dnPgTI
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object metadata id"
#~ msgstr "Objektum metaadat azonosító"

#. js-lingui-id: T/nPf5
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Object Metadata Id"
msgstr "Objektum Metaadat Azonosító"

#. js-lingui-id: afsWF6
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object name"
#~ msgstr "Objektum neve"

#. js-lingui-id: r/A6pA
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Open Record In"
msgstr "Rekord megnyitása"

#. js-lingui-id: Fzfj4N
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Operand"
msgstr "Operándus"

#. js-lingui-id: FZg3wM
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operation"
msgstr "Művelet"

#. js-lingui-id: B1MDds
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operations"
msgstr "Műveletek"

#. js-lingui-id: 4MyDFl
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities"
msgstr "Lehetőségek"

#. js-lingui-id: 5QgKbT
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities linked to the company."
msgstr "A társasághoz kapcsolódó lehetőségek."

#. js-lingui-id: SV/iis
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Opportunity"
msgstr "Lehetőség"

#. js-lingui-id: WnMlKn
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity amount"
msgstr "Lehetőség összege"

#. js-lingui-id: aj3fnv
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity close date"
msgstr "Lehetőség lezárásának dátuma"

#. js-lingui-id: 3NYczb
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity company"
msgstr "Lehetőség társasága"

#. js-lingui-id: as45IN
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity point of contact"
msgstr "Lehetőség elsődleges kapcsolattartója"

#. js-lingui-id: 5Nu7Uw
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity probability"
msgstr "Lehetőség valószínűsége"

#. js-lingui-id: Q1dzBp
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity record position"
msgstr "Lehetőség rögzítési pozíciója"

#. js-lingui-id: 5ugYS3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity stage"
msgstr "Lehetőség szakasza"

#. js-lingui-id: //eyK1
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label \"{sanitizedLabel}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: mTFS0z
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label exceeds 63 characters"
msgstr ""

#. js-lingui-id: +E9N4m
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label is required"
msgstr ""

#. js-lingui-id: nQqNzE
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value \"{sanitizedValue}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: 5m/1Hh
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value exceeds 63 characters"
msgstr ""

#. js-lingui-id: /wsRPd
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value is required"
msgstr ""

#. js-lingui-id: eLggyd
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Optional aggregate operation"
msgstr "Opcionális aggregációs művelet"

#. js-lingui-id: kdClJ/
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Optional secret used to compute the HMAC signature for webhook payloads. This secret is shared between Twenty and the webhook consumer to authenticate webhook requests."
msgstr "Opcionális titok, amelyet a webhook terhek HMAC aláírásának kiszámításához használnak. Ez a titok Twenty és a webhook fogyasztó között megosztásra kerül a webhook kérések hitelesítésére."

#. js-lingui-id: gh06VD
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Output"
msgstr "Kimenet"

#. js-lingui-id: pDUbN1
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group"
msgstr "Szülő Nézet Szűrő Csoport"

#. js-lingui-id: YKSmIP
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group Id"
msgstr "Szülő Nézet Szűrő Csoport Azonosítója"

#. js-lingui-id: wT0H4O
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Password too weak"
msgstr ""

#. js-lingui-id: 1wdjme
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People"
msgstr "Emberek"

#. js-lingui-id: E1zc7W
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People linked to the company."
msgstr "A társasághoz kapcsolódó emberek."

#. js-lingui-id: OZdaTZ
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Person"
msgstr "Személy"

#. js-lingui-id: c3Qq6o
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Person record Position"
msgstr "Személy nyilvántartási beosztása"

#. js-lingui-id: zmwvG2
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phone"
msgstr "Telefon"

#. js-lingui-id: m2ivgq
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phones"
msgstr "Telefonok"

#. js-lingui-id: P04j61
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Point of Contact"
msgstr "Kapcsolattartó"

#. js-lingui-id: p/78dY
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Position"
msgstr "Pozíció"

#. js-lingui-id: HH1bMC
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in the parent view filter group"
msgstr "Pozíció a szülő nézet szűrőcsoportban"

#. js-lingui-id: CvOSME
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Position in the view filter group"
msgstr "Pozíció a nézet szűrőcsoportban"

#. js-lingui-id: h8PGuF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in view filter group"
msgstr "Pozíció a nézet szűrőcsoportban"

#. js-lingui-id: TTHIbk
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred color scheme"
msgstr "Preferált színséma"

#. js-lingui-id: 5v4qYi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred language"
msgstr "Preferált nyelv"

#. js-lingui-id: WJIL29
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Probability"
msgstr "Valószínűség"

#. js-lingui-id: dtGxRz
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred calling code are conflicting"
msgstr ""

#. js-lingui-id: Vq/afT
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred country code are conflicting"
msgstr ""

#. js-lingui-id: HZ45ox
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided country code and calling code are conflicting"
msgstr ""

#. js-lingui-id: A4Ohxb
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided phone number is invalid {number}"
msgstr ""

#. js-lingui-id: CrfRPa
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "provider"
msgstr "szolgáltató"

#. js-lingui-id: YfbwOB
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Received At"
msgstr "Fogadás ideje"

#. js-lingui-id: 4Tvtbu
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Record id"
#~ msgstr "Rekord azonosító"

#. js-lingui-id: 9gXJw8
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Recurring Event ID"
msgstr "Ismétlődő Esemény Azonosító"

#. js-lingui-id: 2rvMKg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Refresh Token"
msgstr "Frissítési Token"

#. js-lingui-id: 2LpFdR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Related user email address"
msgstr "Kapcsolódó felhasználó e-mail címe"

#. js-lingui-id: g87L9j
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Relations"
msgstr "Kapcsolatok"

#. js-lingui-id: pCsIQQ
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Request has expired, please try again."
msgstr ""

#. js-lingui-id: rnCndp
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Response Status"
msgstr "Válasz státusza"

#. js-lingui-id: MCWKAU
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Revocation date"
msgstr "Visszavonás dátuma"

#. js-lingui-id: GDvlUT
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Role"
msgstr "Szerepkör"

#. js-lingui-id: Tpm2G9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Runs"
msgstr "Futások"

#. js-lingui-id: N/rFzD
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Scopes"
msgstr "Hatáskörök"

#. js-lingui-id: PdVIJC
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Search vector"
msgstr "Kereső vektor"

#. js-lingui-id: 8VEDbV
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Secret"
msgstr "Titok"

#. js-lingui-id: Tz0i8g
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Settings"
msgstr ""

#. js-lingui-id: Cj2Gtd
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Size"
msgstr "Méret"

#. js-lingui-id: 3PRxO3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Stage"
msgstr "Szakasz"

#. js-lingui-id: D3iCkb
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Start Date"
msgstr "Kezdés dátuma"

#. js-lingui-id: RS0o7b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State"
msgstr ""

#. js-lingui-id: nY8GL/
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State of the workflow run"
msgstr ""

#. js-lingui-id: uAQUqI
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Status"
msgstr "Állapot"

#. js-lingui-id: Db4W3/
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Statuses"
msgstr "Állapotok"

#. js-lingui-id: S7DRR+
#: src/modules/workflow/common/utils/assert-workflow-statuses-not-set.ts
msgid "Statuses cannot be set manually."
msgstr ""

#. js-lingui-id: u+rv4L
#: src/modules/workflow/workflow-builder/workflow-step/workflow-version-step.workspace-service.ts
msgid "Step is not a form"
msgstr ""

#. js-lingui-id: YFMPch
#: src/engine/metadata-modules/utils/validate-metadata-name-start-with-lowercase-letter-and-contain-digits-nor-letters.utils.ts
msgid "String \"{name}\" is not valid: must start with lowercase letter and contain only alphanumeric letters"
msgstr ""

#. js-lingui-id: g4jxXh
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Sub field name"
msgstr ""

#. js-lingui-id: UJmAAK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Subject"
msgstr "Tárgy"

#. js-lingui-id: oyJYg7
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor"
msgstr "Szinkronizációs kurzor"

#. js-lingui-id: awvBUx
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor. Used for syncing events from the calendar provider"
msgstr "Szinkronizációs kurzor. Használatos az események naptárszolgáltatótól való szinkronizálásához"

#. js-lingui-id: dNAbG6
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage"
msgstr "Szinkronizálási szakasz"

#. js-lingui-id: aqNjQE
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage started at"
msgstr "Szinkronizálási szakasz kezdete"

#. js-lingui-id: bRUdLR
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync status"
msgstr "Szinkron állapot"

#. js-lingui-id: 5y3Wbw
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Target object metadata not found for {connectFieldName}"
msgstr ""

#. js-lingui-id: 4SHJe4
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Target Url"
msgstr "Cél URL"

#. js-lingui-id: Q3P/4s
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Task"
msgstr "Feladat"

#. js-lingui-id: kS+Ym6
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task assignee"
msgstr "Feladat felelőse"

#. js-lingui-id: 7fYQ6E
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task attachments"
msgstr "Feladat mellékletei"

#. js-lingui-id: X8fs74
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task body"
msgstr "Feladat szövege"

#. js-lingui-id: EPxYHS
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task due date"
msgstr "Feladat határideje"

#. js-lingui-id: fUw1j+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task record position"
msgstr "Feladat rekord pozíciója"

#. js-lingui-id: I6+0ph
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task status"
msgstr "Feladat állapota"

#. js-lingui-id: WSiiWf
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Target"
msgstr "Feladat célja"

#. js-lingui-id: khGQLP
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task targets"
msgstr "Feladat célok"

#. js-lingui-id: 836FiO
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Targets"
msgstr "Feladat célok"

#. js-lingui-id: R+s8S+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task title"
msgstr "Feladat címe"

#. js-lingui-id: GtycJ/
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks"
msgstr "Feladatok"

#. js-lingui-id: HlDeG3
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Tasks assigned to the workspace member"
msgstr "A munkaterület tagjához rendelt feladatok"

#. js-lingui-id: Ca/n4T
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks tied to the {label}"
msgstr "A {label}-hoz/-hez kapcsolódó feladatok"

#. js-lingui-id: M4rBti
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Tasks tied to the company"
msgstr "A céghoz kapcsolódó feladatok"

#. js-lingui-id: /VaiDW
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Tasks tied to the contact"
msgstr "A kapcsolathoz kötődő feladatok"

#. js-lingui-id: 1TfX9U
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Tasks tied to the opportunity"
msgstr "A lehetőséghez kapcsolódó feladatok"

#. js-lingui-id: pP0Dt9
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget company"
msgstr "Feladat célpontjának a cége"

#. js-lingui-id: UJ2aPi
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget opportunity"
msgstr "Feladat célpontjának a lehetősége"

#. js-lingui-id: I1MiSs
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget person"
msgstr "Feladat célpontjának a személy"

#. js-lingui-id: pciKLT
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget task"
msgstr "Feladat célpontjának a feladat"

#. js-lingui-id: xeiujy
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Text"
msgstr "Szöveg"

#. js-lingui-id: 6PJbR2
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account handle (email, username, phone number, etc.)"
msgstr "A felhasználói fiók azonosítója (email, felhasználónév, telefonszám, stb.)"

#. js-lingui-id: zUXOAB
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account provider"
msgstr "A fiókszolgáltató"

#. js-lingui-id: qnNFrW
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Linkedin account"
msgstr "A cég Linkedin fiókja"

#. js-lingui-id: N31Pso
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company name"
msgstr "A cég neve"

#. js-lingui-id: BHFCqB
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Twitter/X account"
msgstr "A cég Twitter/X-fiókja"

#. js-lingui-id: OBmU0K
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company website URL. We use this url to fetch the company icon"
msgstr "A cég weboldalának URL-je. Ezt az URL-t használjuk a cég ikonjának lekérésekor"

#. js-lingui-id: zGBDEH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "The creator of the record"
msgstr "A rekord létrehozója"

#. js-lingui-id: bMyVOx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The current statuses of the workflow versions"
msgstr "A munkafolyamat-verziók jelenlegi állapotai"

#. js-lingui-id: 0bo4Q0
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "The date the message was received"
msgstr "Az üzenet fogadásának dátuma"

#. js-lingui-id: 8h4mhq
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "The executor of the workflow"
msgstr "A munkafolyamat végrehajtója"

#. js-lingui-id: W3raza
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "The folder this favorite belongs to"
msgstr "A kedvenc mappája"

#. js-lingui-id: EJUSll
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "The initial version of a workflow can not be deleted"
msgstr ""

#. js-lingui-id: DbWmKZ
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "The opportunity name"
msgstr "Az lehetőség neve"

#. js-lingui-id: 5N6QtE
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger settings"
msgstr ""

#. js-lingui-id: SpKbfT
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger type"
msgstr ""

#. js-lingui-id: 7mPrpl
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "The workflow event listener name"
#~ msgstr "A munkafolyamat eseményfigyelőjének neve"

#. js-lingui-id: od0omS
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow last published version id"
msgstr "A munkafolyamat utolsó publikált verziójának azonosítója"

#. js-lingui-id: /EdWx6
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow name"
msgstr "A munkafolyamat neve"

#. js-lingui-id: EhAsND
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version name"
msgstr "A munkafolyamat verzió neve"

#. js-lingui-id: dhx13p
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version status"
msgstr "A munkafolyamat verzió státusza"

#. js-lingui-id: yIUmAs
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-reserved-keyword.ts
#: src/engine/metadata-modules/utils/validate-field-name-availability.utils.ts
msgid "This name is not available."
msgstr ""

#. js-lingui-id: Zl0BJl
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread External Id"
msgstr "Külső szál azonosító"

#. js-lingui-id: RSSbWN
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread id from the messaging provider"
msgstr "Szál azonosítója az üzenetküldő szolgáltatótól"

#. js-lingui-id: sS8v5K
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Throttle Failure Count"
msgstr "Drosszelszelep hiba szám"

#. js-lingui-id: n9nSNJ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time format"
msgstr "Idő formátum"

#. js-lingui-id: Mz2JN2
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time zone"
msgstr "Időzóna"

#. js-lingui-id: az1boY
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities"
msgstr "Idővonal tevékenységek"

#. js-lingui-id: fqKMpF
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Timeline Activities linked to the company"
msgstr "Idővonal tevékenységek kapcsolódva a céghez"

#. js-lingui-id: 4/UzU5
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Timeline Activities linked to the note."
msgstr "Idővonal tevékenységek kapcsolódva a jegyzethez."

#. js-lingui-id: p6feIz
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Timeline Activities linked to the opportunity."
msgstr "Idővonal tevékenységek kapcsolódva a lehetőséghez."

#. js-lingui-id: yvPwuF
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Timeline activities linked to the run"
msgstr "Idővonal tevékenységek kapcsolódva a futáshoz"

#. js-lingui-id: q96UvB
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Timeline Activities linked to the task."
msgstr "Idővonal tevékenységek kapcsolódva a feladathoz."

#. js-lingui-id: N9HMa/
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Timeline activities linked to the version"
msgstr "Idővonal tevékenységek kapcsolódva a verzióhoz"

#. js-lingui-id: B1CYKX
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Timeline activities linked to the workflow"
msgstr "Idővonal tevékenységek kapcsolódva a munkafolyamathoz"

#. js-lingui-id: G0UmtQ
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities tied to the {label}"
msgstr "Idővonal tevékenységek kapcsolódva a(z) {label}-hez"

#. js-lingui-id: K/kU4E
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Timeline Activity"
msgstr "Idővonal tevékenység"

#. js-lingui-id: MHrjPM
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Title"
msgstr "Cím"

#. js-lingui-id: +zy2Nq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Type"
msgstr "Típus"

#. js-lingui-id: EBZdX5
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Unsupported cron schedule type"
msgstr ""

#. js-lingui-id: 0gY5lO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Update DateTime"
msgstr "Frissítési dátum és idő"

#. js-lingui-id: l9dlVi
#: src/engine/core-modules/auth/strategies/jwt.auth.strategy.ts
msgid "User does not have access to this workspace"
msgstr ""

#. js-lingui-id: YFciqL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Email"
msgstr "Felhasználó E-mail címe"

#. js-lingui-id: d1BTW8
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Id"
msgstr "Felhasználói azonosító"

#. js-lingui-id: B43Kks
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "User is not part of the workspace"
msgstr ""

#. js-lingui-id: 4juE7s
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User time zone"
msgstr "Felhasználói időzóna"

#. js-lingui-id: 43zCwQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred date format"
msgstr "Felhasználó preferált dátum formátuma"

#. js-lingui-id: kJuoKm
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred time format"
msgstr "Felhasználó preferált idő formátuma"

#. js-lingui-id: wMHvYH
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Value"
msgstr "Érték"

#. js-lingui-id: 7aatUy
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version status"
msgstr "Verzió státusza"

#. js-lingui-id: CdQeU7
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version steps"
msgstr "Verzió lépések"

#. js-lingui-id: PGMPIi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version trigger"
msgstr "Verzió kiváltó"

#. js-lingui-id: IYNSdp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Versions"
msgstr "Verziók"

#. js-lingui-id: jpctdh
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "View"
msgstr "Nézet"

#. js-lingui-id: cZPDyy
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field"
msgstr "Nézet mező"

#. js-lingui-id: 6jpoH4
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field position"
msgstr "Nézet mező pozíció"

#. js-lingui-id: Ju6gri
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field related view"
msgstr "Kapcsolódó nézetre vonatkozó mező megtekintése"

#. js-lingui-id: tvTr2y
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field size"
msgstr "Mező méretének megtekintése"

#. js-lingui-id: BkII8t
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field target field"
msgstr "Mező célmezőjének megtekintése"

#. js-lingui-id: Gd/LzL
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field visibility"
msgstr "Mező láthatóságának megtekintése"

#. js-lingui-id: GUFYyq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Fields"
msgstr "Mezők megtekintése"

#. js-lingui-id: JRtI7Y
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter"
msgstr "Szűrő megtekintése"

#. js-lingui-id: L2gQ5q
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Display Value"
msgstr "Szűrő megjelenítési értékének megtekintése"

#. js-lingui-id: l9/6pD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Group"
msgstr "Szűrőcsoport megtekintése"

#. js-lingui-id: Mbosm8
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Group Id"
msgstr "Szűrőcsoport azonosítójának megtekintése"

#. js-lingui-id: /aP3iG
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Groups"
msgstr "Szűrőcsoportok megtekintése"

#. js-lingui-id: 4MG8+B
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter operand"
msgstr "Szűrő operandusának megtekintése"

#. js-lingui-id: OrUkUF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter related view"
msgstr "Kapcsolódó nézet szűrőjének megtekintése"

#. js-lingui-id: TyXOtD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter target field"
msgstr "Szűrő célmezőjének megtekintése"

#. js-lingui-id: 3KzkxN
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter value"
msgstr "Szűrő értékének megtekintése"

#. js-lingui-id: vj5JsR
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filters"
msgstr "Szűrők megtekintése"

#. js-lingui-id: ziEP12
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group"
msgstr "Nézetcsoport megtekintése"

#. js-lingui-id: uQ3c2q
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group related view"
msgstr "Kapcsolódó nézet nézetcsoportjának megtekintése"

#. js-lingui-id: EFlLpQ
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group target field"
msgstr "Nézetcsoport célmezőjének megtekintése"

#. js-lingui-id: b1Vc+l
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group visibility"
msgstr "Nézetcsoport láthatóságának megtekintése"

#. js-lingui-id: V4nZs/
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Groups"
msgstr "Nézetcsoportok megtekintése"

#. js-lingui-id: qXlovu
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View icon"
msgstr "Ikon megtekintése"

#. js-lingui-id: cd/+ZD
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View Kanban column field"
msgstr "Kanban oszlopmező megtekintése"

#. js-lingui-id: vdfZ+A
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View key"
msgstr "Kulcs megtekintése"

#. js-lingui-id: oOljSE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View name"
msgstr "Név megtekintése"

#. js-lingui-id: rhO8zp
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View position"
msgstr "Pozíció megtekintése"

#. js-lingui-id: EUjpwJ
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort"
msgstr "Rendezés megtekintése"

#. js-lingui-id: 6ZUale
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort direction"
msgstr "Rendezés irányának megtekintése"

#. js-lingui-id: /rCPqN
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort related view"
msgstr "Kapcsolódó nézet rendezésének megtekintése"

#. js-lingui-id: +du2wy
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort target field"
msgstr "Rendezés célmezőjének megtekintése"

#. js-lingui-id: UsdY3K
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sorts"
msgstr "Rendezések megtekintése"

#. js-lingui-id: clWwIZ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View target object"
msgstr "Cél objektum megtekintése"

#. js-lingui-id: bJAIqT
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View type"
msgstr "Típus megtekintése"

#. js-lingui-id: 1I6UoR
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Views"
msgstr "Nézetek"

#. js-lingui-id: 2q/Q7x
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Visibility"
msgstr "Láthatóság"

#. js-lingui-id: oh8+os
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Visible"
msgstr "Látható"

#. js-lingui-id: TRDppN
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook"
msgstr "Webhorgok"

#. js-lingui-id: fyB2Wp
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operation"
msgstr "Webhook művelet"

#. js-lingui-id: gONLmX
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operations"
msgstr "Webhook műveletek"

#. js-lingui-id: cPoSTF
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook target url"
msgstr "Webhorgok c&eacute;l URL-je"

#. js-lingui-id: v1kQyJ
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhooks"
msgstr "Webhorgok"

#. js-lingui-id: TVkCrJ
#: src/engine/core-modules/email-verification/services/email-verification.service.ts
msgid "Welcome to Twenty: Please Confirm Your Email"
msgstr "Üdvözöljük a Twenty-ben: Kérjük, erősítse meg e-mail-címét"

#. js-lingui-id: bLt/0J
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Workflow"
msgstr "Munkafolyamatok"

#. js-lingui-id: hdtWQn
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow automated triggers linked to the workflow."
msgstr ""

#. js-lingui-id: E03XpH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Workflow event listeners linked to the workflow."
#~ msgstr "A munkafolyamat eseményfigyelői hozzá vannak kötve a munkafolyamathoz."

#. js-lingui-id: vwSkSW
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow linked to the run."
msgstr "A munkafolyamat össze van kötve a futtatással."

#. js-lingui-id: y9tnFx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow record position"
msgstr "Munkafolyamat rekord állása"

#. js-lingui-id: 5vIcqC
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Workflow Run"
msgstr "Munkafolyamat futtatása"

#. js-lingui-id: 3Iz+qz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run ended at"
msgstr "Munkafolyamat futtatása befejeződött"

#. js-lingui-id: IUaK6s
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run position"
msgstr "Munkafolyamat futtatásának állása"

#. js-lingui-id: zaN7tH
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run started at"
msgstr "Munkafolyamat futtatása elindult"

#. js-lingui-id: 1TU2A8
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run status"
msgstr "Munkafolyamat futtatásának állapota"

#. js-lingui-id: u6DF/V
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow Runs"
msgstr "Munkafolyamat futtatások"

#. js-lingui-id: 9nOy7k
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow runs linked to the version."
msgstr "A munkafolyamat futtatásai össze vannak kötve a verzióval."

#. js-lingui-id: c37F3j
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow runs linked to the workflow."
msgstr "A munkafolyamat futtatásai össze vannak kötve a munkafolyamattal."

#. js-lingui-id: lTXctu
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version"
msgstr "Munkafolyamat verziója"

#. js-lingui-id: +wYPET
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Version"
msgstr "Munkafolyamat Verziója"

#. js-lingui-id: n4/IOE
#: src/modules/workflow/common/utils/assert-workflow-version-has-steps.ts
msgid "Workflow version does not contain at least one step"
msgstr ""

#. js-lingui-id: k4DPlQ
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/common/utils/assert-workflow-version-trigger-is-defined.util.ts
msgid "Workflow version does not contain trigger"
msgstr ""

#. js-lingui-id: bqFL4g
#: src/modules/workflow/common/utils/assert-workflow-version-is-draft.util.ts
msgid "Workflow version is not in draft status"
msgstr ""

#. js-lingui-id: CocTJJ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version linked to the run."
msgstr "A munkafolyamat verziója össze van kötve a futtatással."

#. js-lingui-id: 9l+pJT
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow version position"
msgstr "Munkafolyamat verziójának állása"

#. js-lingui-id: OCyhkn
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Versions"
msgstr "Munkafolyamat Verziói"

#. js-lingui-id: 018fP9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow versions linked to the workflow."
msgstr "A munkafolyamat verziói össze vannak kötve a munkafolyamattal."

#. js-lingui-id: s7p+TL
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger"
msgstr ""

#. js-lingui-id: 96bCgZ
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger workflow"
msgstr ""

#. js-lingui-id: 5z92T9
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTriggers"
msgstr ""

#. js-lingui-id: ENOy6I
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener"
#~ msgstr "WorkflowEseményFigyelő"

#. js-lingui-id: HN90FO
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener workflow"
#~ msgstr "WorkflowEseményFigyelő munkafolyamat"

#. js-lingui-id: 3JA9se
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListeners"
#~ msgstr "WorkflowEseményFigyelők"

#. js-lingui-id: woYYQq
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflows"
msgstr "Munkafolyamatok"

#. js-lingui-id: urCUgs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "WorkflowVersion"
msgstr "MunkafolyamatVerzió"

#. js-lingui-id: b4kire
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "WorkflowVersion workflow"
msgstr "MunkafolyamatVerzió munkafolyamat"

#. js-lingui-id: rklt6M
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Workspace is not ready to welcome new members"
msgstr ""

#. js-lingui-id: CbGxon
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Workspace member"
msgstr "Munkaterület tag"

#. js-lingui-id: qc38qR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Workspace Member"
msgstr "Munkaterület tagja"

#. js-lingui-id: R1S9pO
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member avatar"
msgstr "Munkaterület tag avatárja"

#. js-lingui-id: 5VCX7o
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member name"
msgstr "Munkaterület tag neve"

#. js-lingui-id: NiUpuN
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member position"
msgstr "Munkaterület tag pozíciója"

#. js-lingui-id: YCAEr+
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace Members"
msgstr "Munkaterület Tagjai"

#. js-lingui-id: EtzFC0
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "WorkspaceMember"
msgstr "MunkaterületTag"

#. js-lingui-id: fV2Bu6
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Wrong password"
msgstr ""

#. js-lingui-id: 0gv+T2
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "X"
msgstr "X"

#. js-lingui-id: z+7x/s
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "You must be authenticated to perform this action."
msgstr ""

#. js-lingui-id: bTyBrW
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Your Password Has Been Successfully Changed"
msgstr "Jelszavát sikeresen megváltoztatta"

#. js-lingui-id: vPccnr
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Your team member responsible for managing the company account"
msgstr "Az Ön csapatának megbízott tagja, aki a cégfiókot kezeli"

#. js-lingui-id: ej/tZF
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "Your workspace has been updated with a new data model. Please refresh the page."
msgstr ""
