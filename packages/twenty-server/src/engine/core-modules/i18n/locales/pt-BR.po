msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-29 18:14+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: pt\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Portuguese, Brazilian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: pt-BR\n"
"X-Crowdin-File: /packages/twenty-server/src/engine/core-modules/i18n/locales/en.po\n"
"X-Crowdin-File-ID: 31\n"

#. js-lingui-id: Qyrd7v
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "(System) View Fields"
msgstr "(Sistema) Campos de Visualização"

#. js-lingui-id: 9Y3fTB
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "(System) View Filter Groups"
msgstr "(Sistema) Grupos de Filtros de Visualização"

#. js-lingui-id: TB2jLV
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "(System) View Filters"
msgstr "(Sistema) Filtros de Visualização"

#. js-lingui-id: Y7M7Ro
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "(System) View Groups"
msgstr "(Sistema) Grupos de Visualização"

#. js-lingui-id: 9vliLw
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "(System) View Sorts"
msgstr "(Sistema) Ordenações de Visualização"

#. js-lingui-id: 5B59WE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "(System) Views"
msgstr "(Sistema) Visualizações"

#. js-lingui-id: Q0ISF1
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "{fieldName} and {fieldName}Id cannot be both provided."
msgstr ""

#. js-lingui-id: 8haj+G
#: src/engine/metadata-modules/utils/validate-metadata-name-is-camel-case.utils.ts
msgid "{name} should be in camelCase"
msgstr ""

#. js-lingui-id: kZR6+h
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "A company"
msgstr "Uma Empresa"

#. js-lingui-id: +aeifv
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "A connected account"
msgstr "Uma Conta Conectada"

#. js-lingui-id: HCoswz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "A favorite that can be accessed from the left menu"
msgstr "Um Favorito que pode ser Acessado no Menu à Esquerda"

#. js-lingui-id: 6w8bHl
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "A Folder of favorites"
msgstr "Uma Pasta de Favoritos"

#. js-lingui-id: sSGYmf
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "A group of related messages (e.g. email thread, chat thread)"
msgstr "Um Grupo de Mensagens Relacionadas (ex.: thread de e-mail, thread de chat)"

#. js-lingui-id: vZj1Xc
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "A message sent or received through a messaging channel (email, chat, etc.)"
msgstr "Uma Mensagem Enviada ou Recebida por um Canal de Mensagens (e-mail, chat, etc.)"

#. js-lingui-id: bufuBA
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "A note"
msgstr "Uma Nota"

#. js-lingui-id: 6kUkZW
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "A note target"
msgstr "Um Alvo de Nota"

#. js-lingui-id: Io42ej
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "A person"
msgstr "Uma Pessoa"

#. js-lingui-id: Q2De+v
#: src/engine/metadata-modules/role/role.service.ts
msgid "A role with this label already exists."
msgstr ""

#. js-lingui-id: mkFXEH
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "A task"
msgstr "Uma Tarefa"

#. js-lingui-id: hk2NzW
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "A task target"
msgstr "Um Alvo de Tarefa"

#. js-lingui-id: HTSJFW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "A webhook"
msgstr "Um Webhook"

#. js-lingui-id: ZIN9Ga
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "A workflow"
msgstr "Um Workflow"

#. js-lingui-id: YwBCp8
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "A workflow automated trigger"
msgstr ""

#. js-lingui-id: juBVjt
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "A workflow event listener"
#~ msgstr "Um Ouvinte de Eventos de Workflow"

#. js-lingui-id: 1+xDbI
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "A workflow run"
msgstr "Uma Execução de Workflow"

#. js-lingui-id: N0g7rp
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "A workflow version"
msgstr "Uma Versão de Workflow"

#. js-lingui-id: HpZ/I5
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "A workspace member"
msgstr "Um Membro do Espaço de Trabalho"

#. js-lingui-id: GDKKxT
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Access Token"
msgstr "Token de Acesso"

#. js-lingui-id: pd81Qb
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Account Owner"
msgstr "Proprietário da Conta"

#. js-lingui-id: loqL/f
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account owner for companies"
msgstr "Proprietário da Conta para Empresas"

#. js-lingui-id: HZosRi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account Owner For Companies"
msgstr "Proprietário da Conta para Empresas"

#. js-lingui-id: 48AxkT
#: src/engine/workspace-manager/workspace-cleaner/services/cleaner.workspace-service.ts
msgid "Action needed to prevent workspace deletion"
msgstr "Ação necessária para evitar a exclusão do espaço de trabalho"

#. js-lingui-id: j0DfGR
#: src/engine/core-modules/auth/services/reset-password.service.ts
msgid "Action Needed to Reset Password"
msgstr "Ação necessária para redefinir a senha"

#. js-lingui-id: Du6bPw
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address"
msgstr "Endereço"

#. js-lingui-id: JiOJxf
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address (deprecated) "
msgstr "Endereço (obsoleto) "

#. js-lingui-id: Knl3c9
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company"
msgstr "Endereço da Empresa"

#. js-lingui-id: zJhwjv
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company - deprecated in favor of new address field"
msgstr "Endereço da Empresa - obsoleto em favor do novo campo de endereço"

#. js-lingui-id: ZfVqbP
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Aggregate operation"
msgstr "Operação Agregada"

#. js-lingui-id: W58PBh
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Aggregated / filtered event to be displayed on the timeline"
msgstr "Evento Agregado/Filtrado a ser Exibido na Linha do Tempo"

#. js-lingui-id: hehnjM
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Amount"
msgstr "Valor"

#. js-lingui-id: qeHcQj
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "An API key"
msgstr "Uma Chave API"

#. js-lingui-id: MjyFvC
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "An attachment"
msgstr "Um Anexo"

#. js-lingui-id: +bL++X
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "An audit log of actions performed in the system"
#~ msgstr "Um Registro de Auditoria das Ações Executadas no Sistema"

#. js-lingui-id: XyOToQ
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "An error occurred."
msgstr ""

#. js-lingui-id: muVHgL
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "An event related to user behavior"
#~ msgstr "Um Evento Relacionado ao Comportamento do Usuário"

#. js-lingui-id: bZq8rL
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "An opportunity"
msgstr "Uma Oportunidade"

#. js-lingui-id: JKWicb
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Annual Recurring Revenue: The actual or estimated annual revenue of the company"
msgstr "Receita Recorrente Anual: A Receita Anual Real ou Estimada da Empresa"

#. js-lingui-id: yRnk5W
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Key"
msgstr "Chave API"

#. js-lingui-id: FfSJ1Y
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Keys"
msgstr "Chaves API"

#. js-lingui-id: puNs/l
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey expiration date"
msgstr "Data de Expiração da Chave API"

#. js-lingui-id: YHiNxr
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey name"
msgstr "Nome da Chave API"

#. js-lingui-id: xrndTt
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey revocation date"
msgstr "Data de Revogação da Chave API"

#. js-lingui-id: DGW5iN
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain does not match email domain"
msgstr ""

#. js-lingui-id: BKvuAq
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain has already been validated"
msgstr ""

#. js-lingui-id: 3EiOLz
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ARR"
msgstr "ARR"

#. js-lingui-id: Ek7xGj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Assigned tasks"
msgstr "Tarefas Atribuídas"

#. js-lingui-id: ojKCLU
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Assignee"
msgstr "Responsável"

#. js-lingui-id: Max2GU
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Associated User Id"
msgstr "ID de Usuário Associado"

#. js-lingui-id: UY1vmE
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment"
msgstr "Anexo"

#. js-lingui-id: JAefBH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment author"
msgstr "Autor do Anexo"

#. js-lingui-id: bIaesZ
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment company"
msgstr "Empresa do Anexo"

#. js-lingui-id: gfGYcl
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment full path"
msgstr "Caminho Completo do Anexo"

#. js-lingui-id: wjocwa
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment name"
msgstr "Nome do Anexo"

#. js-lingui-id: FWlOXr
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment note"
msgstr "Nota do Anexo"

#. js-lingui-id: YASWpH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment opportunity"
msgstr "Oportunidade do Anexo"

#. js-lingui-id: P38yED
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment person"
msgstr "Pessoa do Anexo"

#. js-lingui-id: Tx1DxS
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment task"
msgstr "Tarefa do Anexo"

#. js-lingui-id: 4qqxMt
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment type"
msgstr "Tipo de Anexo"

#. js-lingui-id: w/Sphq
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments"
msgstr "Anexos"

#. js-lingui-id: 2tQOVc
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Attachments created by the workspace member"
msgstr "Anexos Criados pelo Membro do Espaço de Trabalho"

#. js-lingui-id: iVnA89
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Attachments linked to the company"
msgstr "Anexos Vinculados à Empresa"

#. js-lingui-id: MuTXtT
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Attachments linked to the contact."
msgstr "Anexos Vinculados ao Contato."

#. js-lingui-id: kw0mLu
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Attachments linked to the opportunity"
msgstr "Anexos Vinculados à Oportunidade"

#. js-lingui-id: fCbqr7
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments tied to the {label}"
msgstr "Anexos Vinculados ao {label}"

#. js-lingui-id: ilRCh1
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Log"
#~ msgstr "Registro de Auditoria"

#. js-lingui-id: EPEFrH
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Logs"
#~ msgstr "Registros de Auditoria"

#. js-lingui-id: RqCC/0
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#~ msgid "Audit Logs linked to the workspace member"
#~ msgstr "Registros de Auditoria Vinculados ao Membro do Espaço de Trabalho"

#. js-lingui-id: cNBqH+
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Auth failed at"
msgstr "Autenticação Falhou em"

#. js-lingui-id: LB4qX/
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Authentication is not enabled with this provider."
msgstr ""

#. js-lingui-id: VbeIOx
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Author"
msgstr "Autor"

#. js-lingui-id: XJONK6
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Authored attachments"
msgstr "Anexos de Autoria"

#. js-lingui-id: cvSznK
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Automated Trigger Type"
msgstr ""

#. js-lingui-id: 2mtBXs
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Automated Triggers"
msgstr ""

#. js-lingui-id: RpExX0
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Automatically create People records when receiving or sending emails"
msgstr "Criar Automaticamente Registros de Pessoas ao Receber ou Enviar E-mails"

#. js-lingui-id: lXxdId
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Automatically create records for people you participated with in an event."
msgstr "Criar Automaticamente Registros para as Pessoas com quem Você Participou de um Evento."

#. js-lingui-id: kfcRb0
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Avatar"
msgstr "Avatar"

#. js-lingui-id: S/mJUR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Avatar Url"
msgstr "URL do Avatar"

#. js-lingui-id: 20B9kW
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Event"
#~ msgstr "Evento Comportamental"

#. js-lingui-id: Jeh/Q/
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Events"
#~ msgstr "Eventos Comportamentais"

#. js-lingui-id: K1172m
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklist"
msgstr "Lista de Bloqueio"

#. js-lingui-id: Tv2hxv
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Blocklisted handles"
msgstr "Identificadores na Lista de Bloqueio"

#. js-lingui-id: L5JhJe
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklists"
msgstr "Listas de Bloqueio"

#. js-lingui-id: bGQplw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body"
msgstr "Corpo"

#. js-lingui-id: Nl8eMw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body (deprecated)"
msgstr "Corpo (obsoleto)"

#. js-lingui-id: 8mVqF7
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Cached record name"
msgstr "Nome do Registro em Cache"

#. js-lingui-id: Nh6GTX
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channel"
msgstr "Canal de Calendário"

#. js-lingui-id: jfNQ0m
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Association"
msgstr "Associação de Eventos do Canal de Calendário"

#. js-lingui-id: kYNT3F
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Associations"
msgstr "Associações de Eventos do Canal de Calendário"

#. js-lingui-id: Znix/S
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channels"
msgstr "Canais de Calendário"

#. js-lingui-id: bRk+FR
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar event"
msgstr "Evento de Calendário"

#. js-lingui-id: N2kMfO
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participant"
msgstr "Participante do Evento de Calendário"

#. js-lingui-id: AWDqkQ
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participants"
msgstr "Participantes do Evento de Calendário"

#. js-lingui-id: ZI2UyM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Calendar Event Participants"
msgstr "Participantes do Evento de Calendário"

#. js-lingui-id: X9A2xC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar events"
msgstr "Eventos de Calendário"

#. js-lingui-id: vxbVwc
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Cannot activate non-draft or non-last-published version"
msgstr ""

#. js-lingui-id: LIPsWu
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create multiple draft versions for the same workflow"
msgstr ""

#. js-lingui-id: RggZr4
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create workflow version with status other than draft"
msgstr ""

#. js-lingui-id: AQyr9X
#: src/engine/metadata-modules/field-metadata/services/field-metadata.service.ts
msgid "Cannot delete, please update the label identifier field first"
msgstr ""

#. js-lingui-id: FrNWt0
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot disable non-active workflow version"
msgstr ""

#. js-lingui-id: IDMgr/
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot have more than one active workflow version"
msgstr ""

#. js-lingui-id: 4DKo3U
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot update workflow version status manually"
msgstr ""

#. js-lingui-id: 4IVK41
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Channel ID"
msgstr "ID do Canal"

#. js-lingui-id: Ubg/B+
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Channel Type"
msgstr "Tipo de Canal"

#. js-lingui-id: 3wV73y
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "City"
msgstr "Cidade"

#. js-lingui-id: NRF7pg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Close date"
msgstr "Data de Fechamento"

#. js-lingui-id: 96YB1a
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Color Scheme"
msgstr "Esquema de Cores"

#. js-lingui-id: 07OSD1
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Compact View"
msgstr "Visualização Compacta"

#. js-lingui-id: s2QZS6
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Companies"
msgstr "Empresas"

#. js-lingui-id: 7i8j3G
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Company"
msgstr "Empresa"

#. js-lingui-id: yA1de7
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Company record position"
msgstr "Posição do Registro da Empresa"

#. js-lingui-id: AgktVC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Conference Solution"
msgstr "Solução de Conferência"

#. js-lingui-id: 1fFL4B
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Connect is not allowed for {connectFieldName} on {objectMetadataNameSingular}"
msgstr ""

#. js-lingui-id: PQ1Dw2
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Connected Account"
msgstr "Conta Conectada"

#. js-lingui-id: 9TzudL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Connected accounts"
msgstr "Contas Conectadas"

#. js-lingui-id: AMDUqA
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Connected Accounts"
msgstr "Contas Conectadas"

#. js-lingui-id: bA6O5B
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Contact auto creation policy"
msgstr "Política de Criação Automática de Contatos"

#. js-lingui-id: RnsmQs
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s avatar"
msgstr "Avatar do Contato"

#. js-lingui-id: pL+pqi
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s city"
msgstr "Cidade do Contato"

#. js-lingui-id: VnWMlz
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s company"
msgstr "Empresa do Contato"

#. js-lingui-id: ITlFIB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Emails"
msgstr "E-mails do Contato"

#. js-lingui-id: GuRtLY
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s job title"
msgstr "Cargo do Contato"

#. js-lingui-id: QrCvRQ
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Linkedin account"
msgstr "Conta do Contato no Linkedin"

#. js-lingui-id: 6xPSVt
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s name"
msgstr "Nome do Contato"

#. js-lingui-id: Y37CZ4
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone number"
msgstr "Número de Telefone do Contato"

#. js-lingui-id: zsW3gg
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone numbers"
msgstr "Números de Telefone do Contato"

#. js-lingui-id: uuZ00G
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s X/Twitter account"
msgstr "Conta X/Twitter do Contato"

#. js-lingui-id: M73whl
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Context"
msgstr "Contexto"

#. js-lingui-id: NCIYDF
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Created by"
msgstr "Criado por"

#. js-lingui-id: wPvFAD
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Creation date"
msgstr "Data de Criação"

#. js-lingui-id: CJXWmO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Creation DateTime"
msgstr "Data e Hora da Criação"

#. js-lingui-id: umhXiy
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Cron pattern '{pattern}' is invalid"
msgstr ""

#. js-lingui-id: Mikszu
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Custom Connection Parameters"
msgstr ""

#. js-lingui-id: Lhd0oQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Date format"
msgstr "Formato da Data"

#. js-lingui-id: 1lL5Iu
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Date when the record was deleted"
msgstr "Data em que o Registro foi Excluído"

#. js-lingui-id: QN9ahV
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Deleted at"
msgstr "Excluído em"

#. js-lingui-id: U1bSSI
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Describes if the view is in compact mode"
msgstr "Descreve se a Visualização está no Modo Compacto"

#. js-lingui-id: Nu4oKW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Description"
msgstr "Descrição"

#. js-lingui-id: MRB7nI
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Direction"
msgstr "Direção"

#. js-lingui-id: 0gS7M5
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Display Name"
msgstr "Nome de Exibição"

#. js-lingui-id: y7HJTU
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Display the records in a side panel or in a record page"
msgstr "Exibir os registros em um painel lateral ou em uma página de registro"

#. js-lingui-id: 0H/D9K
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Display Value"
msgstr "Valor de Exibição"

#. js-lingui-id: wMncXq
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Domain Name"
msgstr "Nome de Domínio"

#. js-lingui-id: YOowcq
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Due Date"
msgstr "Data de Vencimento"

#. js-lingui-id: BBXuRb
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email already verified."
msgstr ""

#. js-lingui-id: xw6iqb
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Email is not verified."
msgstr ""

#. js-lingui-id: ZsZeV2
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Email is required"
msgstr ""

#. js-lingui-id: y8LSMh
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email verification not required."
msgstr ""

#. js-lingui-id: BXEcos
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Emails"
msgstr "E-mails"

#. js-lingui-id: gqv5ZL
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Employees"
msgstr "Funcionários"

#. js-lingui-id: VFv2ZC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "End Date"
msgstr "Data Final"

#. js-lingui-id: k//6Xs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event company"
msgstr "Empresa do Evento"

#. js-lingui-id: FJ7QI4
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event context"
#~ msgstr "Contexto do Evento"

#. js-lingui-id: kJDmsI
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event details"
msgstr "Detalhes do Evento"

#. js-lingui-id: 0JhmlM
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event external ID"
msgstr "ID Externo do Evento"

#. js-lingui-id: aZJLAR
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event ID"
msgstr "ID do Evento"

#. js-lingui-id: 81maJp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Event Listeners"
#~ msgstr "Ouvintes de Eventos"

#. js-lingui-id: PYs3rP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event name"
msgstr "Nome do Evento"

#. js-lingui-id: evIGwh
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event name/type"
#~ msgstr "Nome/Tipo do Evento"

#. js-lingui-id: vv99Wu
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event note"
msgstr "Nota do Evento"

#. js-lingui-id: eSt759
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event opportunity"
msgstr "Oportunidade do Evento"

#. js-lingui-id: aicVfT
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Event Participants"
msgstr "Participantes do Evento"

#. js-lingui-id: 0mp45a
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event person"
msgstr "Pessoa do Evento"

#. js-lingui-id: mMq0Wy
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event task"
msgstr "Tarefa do Evento"

#. js-lingui-id: dCV1dS
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow"
msgstr "Workflow do Evento"

#. js-lingui-id: W84pl6
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow run"
msgstr "Execução do Workflow do Evento"

#. js-lingui-id: vUpps9
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow version"
msgstr "Versão do Workflow do Evento"

#. js-lingui-id: LxOGsB
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workspace member"
msgstr "Membro do Espaço de Trabalho do Evento"

#. js-lingui-id: tst44n
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events"
msgstr "Eventos"

#. js-lingui-id: fHL+iH
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events linked to the person"
msgstr "Eventos Vinculados à Pessoa"

#. js-lingui-id: 3/O8MM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Events linked to the workspace member"
msgstr "Eventos Vinculados ao Membro do Espaço de Trabalho"

#. js-lingui-id: QQlMid
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude group emails"
msgstr "Excluir E-mails de Grupo"

#. js-lingui-id: kenYGr
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude non professional emails"
msgstr "Excluir E-mails Não Profissionais"

#. js-lingui-id: Lo5U0b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Executed by"
msgstr "Executado por"

#. js-lingui-id: AiFXmG
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Expected the same constraint fields to be used consistently across all operations for {connectFieldName}."
msgstr ""

#. js-lingui-id: sZg7s1
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Expiration date"
msgstr "Data de Expiração"

#. js-lingui-id: 6Ki4Pv
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite"
msgstr "Favorito"

#. js-lingui-id: aKUOPp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite company"
msgstr "Empresa Favorita"

#. js-lingui-id: TDlZ/o
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite Folder"
msgstr "Pasta de Favoritos"

#. js-lingui-id: WDVfUH
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite folder position"
msgstr "Posição da Pasta de Favoritos"

#. js-lingui-id: SStz54
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite Folders"
msgstr "Pastas de Favoritos"

#. js-lingui-id: dz/fFp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite note"
msgstr "Nota Favorita"

#. js-lingui-id: RRr9Bp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite opportunity"
msgstr "Oportunidade Favorita"

#. js-lingui-id: RM6B6V
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite person"
msgstr "Pessoa Favorita"

#. js-lingui-id: oVA9vM
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite position"
msgstr "Posição Favorita"

#. js-lingui-id: OaQVQH
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite task"
msgstr "Tarefa Favorita"

#. js-lingui-id: VkWV1s
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite view"
msgstr "Visualização Favorita"

#. js-lingui-id: l+lx2f
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow"
msgstr "Workflow Favorito"

#. js-lingui-id: BPBnux
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow run"
msgstr "Execução de Workflow Favorita"

#. js-lingui-id: 5X6Vlz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow version"
msgstr "Versão de Workflow Favorita"

#. js-lingui-id: FDpezg
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workspace member"
msgstr "Membro Favorito do Espaço de Trabalho"

#. js-lingui-id: X9kySA
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites"
msgstr "Favoritos"

#. js-lingui-id: 0CzeFL
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorites in this folder"
msgstr "Favoritos nesta Pasta"

#. js-lingui-id: zQponA
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Favorites linked to the company"
msgstr "Favoritos Vinculados à Empresa"

#. js-lingui-id: VaKLrB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Favorites linked to the contact"
msgstr "Favoritos Vinculados ao Contato"

#. js-lingui-id: GOfcBt
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Favorites linked to the note"
msgstr "Favoritos Vinculados à Nota"

#. js-lingui-id: 9zd8hg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Favorites linked to the opportunity"
msgstr "Favoritos Vinculados à Oportunidade"

#. js-lingui-id: L5ccWD
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Favorites linked to the task"
msgstr "Favoritos Vinculados à Tarefa"

#. js-lingui-id: R+1ib/
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Favorites linked to the view"
msgstr "Favoritos Vinculados à Visualização"

#. js-lingui-id: ee0tmj
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Favorites linked to the workflow"
msgstr "Favoritos Vinculados ao Workflow"

#. js-lingui-id: zar5jz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Favorites linked to the workflow run"
msgstr "Favoritos Vinculados à Execução de Workflow"

#. js-lingui-id: 499/sw
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Favorites linked to the workflow version"
msgstr "Favoritos Vinculados à Versão de Workflow"

#. js-lingui-id: rgmtb4
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Favorites linked to the workspace member"
msgstr "Favoritos Vinculados ao Membro do Espaço de Trabalho"

#. js-lingui-id: GyXrE1
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites tied to the {label}"
msgstr "Favoritos Vinculados ao {label}"

#. js-lingui-id: nSFFML
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Field Metadata Id"
msgstr "ID de Metadados do Campo"

#. js-lingui-id: g+t8w9
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Field metadata used for aggregate operation"
msgstr "Metadados do campo usados para operação de agregação"

#. js-lingui-id: J4hC2m
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Field used for full-text search"
msgstr "Campo usado para busca de texto completo"

#. js-lingui-id: zuDgzc
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Field Value"
msgstr "Valor do Campo"

#. js-lingui-id: 3oQ73E
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have a previous step"
#~ msgstr ""

#. js-lingui-id: Zllikb
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have only one previous step"
#~ msgstr ""

#. js-lingui-id: lEpDue
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder for Message Channel"
msgstr "Pasta para Canal de Mensagens"

#. js-lingui-id: cqQyPB
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder name"
msgstr "Nome da Pasta"

#. js-lingui-id: kuN9I4
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have a defined label and type"
msgstr ""

#. js-lingui-id: QK3OvQ
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have unique names"
msgstr ""

#. js-lingui-id: KYP1t5
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action must have at least one field"
msgstr ""

#. js-lingui-id: oQA+cA
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Full path"
msgstr "Caminho Completo"

#. js-lingui-id: UzpVUy
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Group by this field value"
msgstr "Agrupar por este valor de campo"

#. js-lingui-id: TkE8dW
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "handle"
msgstr "identificador"

#. js-lingui-id: Nf7oXL
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Handle"
msgstr "Identificador"

#. js-lingui-id: GvgxWx
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Handle Aliases"
msgstr "Apelidos do Identificador"

#. js-lingui-id: VehAU2
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Header message Id"
msgstr "ID da Mensagem de Cabeçalho"

#. js-lingui-id: NNJnBi
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "iCal UID"
msgstr "UID do iCal"

#. js-lingui-id: wwu18a
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Icon"
msgstr "Ícone"

#. js-lingui-id: CiyiKN
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ICP"
msgstr "ICP"

#. js-lingui-id: dFb5Nt
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Id"
msgstr "ID"

#. js-lingui-id: Ebc83S
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Ideal Customer Profile:  Indicates whether the company is the most suitable and valuable customer for you"
msgstr "Perfil de Cliente Ideal: Indica se a empresa é o cliente mais adequado e valioso para você"

#. js-lingui-id: NrA0WZ
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "If the event is related to a particular object"
#~ msgstr "Se o evento está relacionado a um objeto específico"

#. js-lingui-id: 71VJzG
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-short.utils.ts
msgid "Input is too short: \"{name}\""
msgstr ""

#. js-lingui-id: X7+bC6
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid calling code {callingCode}"
msgstr ""

#. js-lingui-id: 1PtrY0
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid country code {countryCode}"
msgstr ""

#. js-lingui-id: 4EtFrA
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid day value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: MBpeQ6
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer between 0 and 23"
msgstr ""

#. js-lingui-id: 9xsjn9
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: FSunWx
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Invalid label: \"{label}\""
msgstr ""

#. js-lingui-id: iBBCnf
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer between 0 and 59"
msgstr ""

#. js-lingui-id: hYcFSd
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: ApP70c
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: sHcAMD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid trigger type for enabling workflow trigger"
msgstr ""

#. js-lingui-id: grHYXZ
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is canceled"
msgstr "Está Cancelado"

#. js-lingui-id: k+g9Uh
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Contact Auto Creation Enabled"
msgstr "Criação Automática de Contatos Ativada"

#. js-lingui-id: uBx1xd
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is Full Day"
msgstr "É Dia Inteiro"

#. js-lingui-id: 3iAfL2
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Is Organizer"
msgstr "É Organizador"

#. js-lingui-id: Mqzqb8
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Sync Enabled"
msgstr "Sincronização Ativada"

#. js-lingui-id: 27z+FV
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Job Title"
msgstr "Cargo"

#. js-lingui-id: PviVyk
#: src/engine/core-modules/workspace-invitation/services/workspace-invitation.service.ts
msgid "Join your team on Twenty"
msgstr "Junte-se à sua equipe no Twenty"

#. js-lingui-id: WNfJ8M
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "JSON object containing custom connection parameters"
msgstr ""

#. js-lingui-id: shbh25
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Json object to provide context (user, device, workspace, etc.)"
#~ msgstr "Objeto JSON para fornecer contexto (usuário, dispositivo, espaço de trabalho, etc.)"

#. js-lingui-id: HgJ9jQ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Json object to provide output of the workflow run"
msgstr "Objeto JSON para fornecer a saída da execução do workflow"

#. js-lingui-id: AQuLwi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide steps"
msgstr "Objeto JSON para fornecer etapas"

#. js-lingui-id: fOjmZ1
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide trigger"
msgstr "Objeto JSON para fornecer o gatilho"

#. js-lingui-id: TVc0/Q
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Json value for event details"
msgstr "Valor JSON para detalhes do evento"

#. js-lingui-id: qmarO2
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "kanbanfieldMetadataId"
msgstr "kanbanfieldMetadataId"

#. js-lingui-id: 7sMeHQ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Key"
msgstr "Chave"

#. js-lingui-id: w8Rv8T
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Label is required"
msgstr ""

#. js-lingui-id: 4EGmy6
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not be empty"
msgstr ""

#. js-lingui-id: k731jp
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not contain a comma"
msgstr ""

#. js-lingui-id: vXIe7J
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Language"
msgstr "Idioma"

#. js-lingui-id: WvYp6o
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Last published Version Id"
msgstr "ID da Última Versão Publicada"

#. js-lingui-id: Y60/DC
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Last sync cursor"
msgstr "Último Cursor de Sincronização"

#. js-lingui-id: Zdx9Qq
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Last sync date"
msgstr "Data da Última Sincronização"

#. js-lingui-id: c7uqNg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Last sync history ID"
msgstr "ID do Histórico da Última Sincronização"

#. js-lingui-id: tGwswq
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last time the record was changed"
msgstr "Última vez que o registro foi alterado"

#. js-lingui-id: o1zvNS
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last update"
msgstr "Última Atualização"

#. js-lingui-id: wR9USP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Object Metadata Id"
msgstr "ID de Metadados do Objeto Vinculado"

#. js-lingui-id: PV1Nm7
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Linked Opportunities"
msgstr "Oportunidades Vinculadas"

#. js-lingui-id: 6YiMr4
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record cached name"
msgstr "Nome em Cache do Registro Vinculado"

#. js-lingui-id: sgHAxx
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record id"
msgstr "ID do Registro Vinculado"

#. js-lingui-id: uCA6be
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Linkedin"
msgstr "Linkedin"

#. js-lingui-id: LeFv/R
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "List of opportunities for which that person is the point of contact"
msgstr "Lista de oportunidades para as quais essa pessoa é o ponto de contato"

#. js-lingui-id: wJijgU
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Location"
msgstr "Localização"

#. js-lingui-id: y+q8Lv
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical Operator"
msgstr "Operador Lógico"

#. js-lingui-id: 6kQXsS
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical operator for the filter group"
msgstr "Operador lógico para o grupo de filtros"

#. js-lingui-id: EnfPm2
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Meet Link"
msgstr "Link de Reunião"

#. js-lingui-id: xDAtGP
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message"
msgstr "Mensagem"

#. js-lingui-id: g+QGD6
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel"
msgstr "Canal de Mensagens"

#. js-lingui-id: DzU4a3
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel Association"
msgstr "Associação de Canal de Mensagens"

#. js-lingui-id: wd/R++
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Id"
msgstr "ID do Canal de Mensagens"

#. js-lingui-id: disipM
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Association"
msgstr "Associação de Mensagem do Canal de Mensagens"

#. js-lingui-id: ijQY3P
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Associations"
msgstr "Associações de Mensagem do Canal de Mensagens"

#. js-lingui-id: k7LXPQ
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Message Channels"
msgstr "Canais de Mensagens"

#. js-lingui-id: /4uGJc
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Direction"
msgstr "Direção da Mensagem"

#. js-lingui-id: fBw8fQ
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message External Id"
msgstr "ID Externo da Mensagem"

#. js-lingui-id: CStLnc
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Message Folder"
msgstr "Pasta de Mensagens"

#. js-lingui-id: p4ANpY
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Folders"
msgstr "Pastas de Mensagens"

#. js-lingui-id: 6icznk
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Id"
msgstr "ID da Mensagem"

#. js-lingui-id: 9FJSpK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message id from the message header"
msgstr "ID da Mensagem do Cabeçalho da Mensagem"

#. js-lingui-id: yaC1Aq
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message id from the messaging provider"
msgstr "ID da Mensagem do Provedor de Mensagens"

#. js-lingui-id: IUmVwu
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participant"
msgstr "Participante da Mensagem"

#. js-lingui-id: FhIFx7
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participants"
msgstr "Participantes da Mensagem"

#. js-lingui-id: IC5A8V
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Synced with a Message Channel"
msgstr "Mensagem Sincronizada com um Canal de Mensagens"

#. js-lingui-id: de2nM/
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Thread"
msgstr "Tópico de Mensagem"

#. js-lingui-id: km1jgD
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message Thread Id"
msgstr "ID do Tópico de Mensagem"

#. js-lingui-id: RD0ecC
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Threads"
msgstr "Tópicos de Mensagem"

#. js-lingui-id: t7TeQU
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages"
msgstr "Mensagens"

#. js-lingui-id: WoWdku
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Messages from the channel."
msgstr "Mensagens do Canal."

#. js-lingui-id: rYPBO7
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages from the thread."
msgstr "Mensagens do Tópico."

#. js-lingui-id: XcKQrV
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider access token"
msgstr "Token de Acesso do Provedor de Mensagens"

#. js-lingui-id: 80EvIk
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider refresh token"
msgstr "Token de Atualização do Provedor de Mensagens"

#. js-lingui-id: SzCMUQ
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Missing required fields: at least one unique constraint have to be fully populated for '{connectFieldName}'."
msgstr ""

#. js-lingui-id: 6YtxFj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Name"
msgstr "Nome"

#. js-lingui-id: RGz1nY
#: src/engine/metadata-modules/field-metadata/services/field-metadata-validation.service.ts
msgid "Name is not available, it may be duplicating another field's name."
msgstr ""

#. js-lingui-id: 0MobB1
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-long.utils.ts
msgid "Name is too long: it exceeds the 63 characters limit."
msgstr ""

#. js-lingui-id: EEVPOx
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Name of the favorite folder"
msgstr "Nome da Pasta Favorita"

#. js-lingui-id: csMjko
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Name of the workflow run"
msgstr "Nome da Execução do Workflow"

#. js-lingui-id: hQQZse
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No event name provided in database event trigger"
msgstr ""

#. js-lingui-id: 9bu19u
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "No input provided in form step"
msgstr ""

#. js-lingui-id: MWAUHo
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No pattern provided in CUSTOM cron trigger"
msgstr ""

#. js-lingui-id: pOu4u/
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No schedule provided in cron trigger"
msgstr ""

#. js-lingui-id: cLUBlS
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: QwbSxD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No steps provided in workflow version"
msgstr ""

#. js-lingui-id: 6dP8sB
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No trigger type provided"
msgstr ""

#. js-lingui-id: KiJn9B
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Note"
msgstr "Nota"

#. js-lingui-id: GGlkb7
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note attachments"
msgstr "Anexos da Nota"

#. js-lingui-id: Q1Rz+6
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note body"
msgstr "Corpo da Nota"

#. js-lingui-id: Yp057F
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note record position"
msgstr "Posição do Registro da Nota"

#. js-lingui-id: spaO7l
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Target"
msgstr "Alvo da Nota"

#. js-lingui-id: mkchvJ
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note targets"
msgstr "Alvos da Nota"

#. js-lingui-id: tD4BxK
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Targets"
msgstr "Alvos da Nota"

#. js-lingui-id: jDThel
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note title"
msgstr "Título da Nota"

#. js-lingui-id: 1DBGsz
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes"
msgstr "Notas"

#. js-lingui-id: Ne73P/
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes tied to the {label}"
msgstr "Notas Vinculadas ao {label}"

#. js-lingui-id: fXBE74
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Notes tied to the company"
msgstr "Notas Vinculadas à Empresa"

#. js-lingui-id: iQ5AH6
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Notes tied to the contact"
msgstr "Notas Vinculadas ao Contato"

#. js-lingui-id: 0bi/Eh
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Notes tied to the opportunity"
msgstr "Notas Vinculadas à Oportunidade"

#. js-lingui-id: B2Y6QU
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget company"
msgstr "Empresa Alvo da Nota"

#. js-lingui-id: /mH0jo
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget note"
msgstr "Nota Alvo da Nota"

#. js-lingui-id: DTs4tO
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget opportunity"
msgstr "Oportunidade Alvo da Nota"

#. js-lingui-id: gBwbnk
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget person"
msgstr "Pessoa Alvo da Nota"

#. js-lingui-id: ioJFzx
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Number of employees in the company"
msgstr "Número de Funcionários na Empresa"

#. js-lingui-id: ECRKYR
#: src/engine/metadata-modules/utils/validate-no-other-object-with-same-name-exists-or-throw.util.ts
msgid "Object already exists"
msgstr ""

#. js-lingui-id: hhe7Ce
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Object id"
#~ msgstr "ID do Objeto"

#. js-lingui-id: dnPgTI
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object metadata id"
#~ msgstr "ID de Metadados do Objeto"

#. js-lingui-id: T/nPf5
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Object Metadata Id"
msgstr "ID de Metadados do Objeto"

#. js-lingui-id: afsWF6
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object name"
#~ msgstr "Nome do Objeto"

#. js-lingui-id: r/A6pA
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Open Record In"
msgstr "Abrir registro em"

#. js-lingui-id: Fzfj4N
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Operand"
msgstr "Operando"

#. js-lingui-id: FZg3wM
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operation"
msgstr "Operação"

#. js-lingui-id: B1MDds
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operations"
msgstr "Operações"

#. js-lingui-id: 4MyDFl
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities"
msgstr "Oportunidades"

#. js-lingui-id: 5QgKbT
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities linked to the company."
msgstr "Oportunidades Ligadas à Empresa."

#. js-lingui-id: SV/iis
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Opportunity"
msgstr "Oportunidade"

#. js-lingui-id: WnMlKn
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity amount"
msgstr "Valor da Oportunidade"

#. js-lingui-id: aj3fnv
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity close date"
msgstr "Data de Fechamento da Oportunidade"

#. js-lingui-id: 3NYczb
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity company"
msgstr "Empresa da Oportunidade"

#. js-lingui-id: as45IN
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity point of contact"
msgstr "Ponto de Contato da Oportunidade"

#. js-lingui-id: 5Nu7Uw
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity probability"
msgstr "Probabilidade da Oportunidade"

#. js-lingui-id: Q1dzBp
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity record position"
msgstr "Posição do Registro da Oportunidade"

#. js-lingui-id: 5ugYS3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity stage"
msgstr "Estágio da Oportunidade"

#. js-lingui-id: //eyK1
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label \"{sanitizedLabel}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: mTFS0z
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label exceeds 63 characters"
msgstr ""

#. js-lingui-id: +E9N4m
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label is required"
msgstr ""

#. js-lingui-id: nQqNzE
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value \"{sanitizedValue}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: 5m/1Hh
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value exceeds 63 characters"
msgstr ""

#. js-lingui-id: /wsRPd
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value is required"
msgstr ""

#. js-lingui-id: eLggyd
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Optional aggregate operation"
msgstr "Operação Agregada Opcional"

#. js-lingui-id: kdClJ/
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Optional secret used to compute the HMAC signature for webhook payloads. This secret is shared between Twenty and the webhook consumer to authenticate webhook requests."
msgstr "Segredo opcional usado para calcular a assinatura HMAC para cargas de webhook. Esse segredo é compartilhado entre o Twenty e o consumidor do webhook para autenticar solicitações de webhook."

#. js-lingui-id: gh06VD
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Output"
msgstr "Saída"

#. js-lingui-id: pDUbN1
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group"
msgstr "Grupo de Filtros da Visualização Principal"

#. js-lingui-id: YKSmIP
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group Id"
msgstr "ID do Grupo de Filtros da Visualização Principal"

#. js-lingui-id: wT0H4O
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Password too weak"
msgstr ""

#. js-lingui-id: 1wdjme
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People"
msgstr "Pessoas"

#. js-lingui-id: E1zc7W
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People linked to the company."
msgstr "Pessoas Ligadas à Empresa."

#. js-lingui-id: OZdaTZ
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Person"
msgstr "Pessoa"

#. js-lingui-id: c3Qq6o
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Person record Position"
msgstr "Posição do Registro da Pessoa"

#. js-lingui-id: zmwvG2
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phone"
msgstr "Telefone"

#. js-lingui-id: m2ivgq
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phones"
msgstr "Telefones"

#. js-lingui-id: P04j61
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Point of Contact"
msgstr "Ponto de Contato"

#. js-lingui-id: p/78dY
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Position"
msgstr "Posição"

#. js-lingui-id: HH1bMC
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in the parent view filter group"
msgstr "Posição no grupo de filtros pai"

#. js-lingui-id: CvOSME
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Position in the view filter group"
msgstr "Posição no grupo de filtros de visualização"

#. js-lingui-id: h8PGuF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in view filter group"
msgstr "Posição no grupo de filtros de visualização"

#. js-lingui-id: TTHIbk
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred color scheme"
msgstr "Esquema de cores preferido"

#. js-lingui-id: 5v4qYi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred language"
msgstr "Idioma preferido"

#. js-lingui-id: WJIL29
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Probability"
msgstr "Probabilidade"

#. js-lingui-id: dtGxRz
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred calling code are conflicting"
msgstr ""

#. js-lingui-id: Vq/afT
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred country code are conflicting"
msgstr ""

#. js-lingui-id: HZ45ox
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided country code and calling code are conflicting"
msgstr ""

#. js-lingui-id: A4Ohxb
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided phone number is invalid {number}"
msgstr ""

#. js-lingui-id: CrfRPa
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "provider"
msgstr "provedor"

#. js-lingui-id: YfbwOB
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Received At"
msgstr "Recebido em"

#. js-lingui-id: 4Tvtbu
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Record id"
#~ msgstr "ID do registro"

#. js-lingui-id: 9gXJw8
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Recurring Event ID"
msgstr "ID do evento recorrente"

#. js-lingui-id: 2rvMKg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Refresh Token"
msgstr "Token de atualização"

#. js-lingui-id: 2LpFdR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Related user email address"
msgstr "Endereço de e-mail do usuário relacionado"

#. js-lingui-id: g87L9j
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Relations"
msgstr "Relações"

#. js-lingui-id: pCsIQQ
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Request has expired, please try again."
msgstr ""

#. js-lingui-id: rnCndp
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Response Status"
msgstr "Status da resposta"

#. js-lingui-id: MCWKAU
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Revocation date"
msgstr "Data de revogação"

#. js-lingui-id: GDvlUT
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Role"
msgstr "Função"

#. js-lingui-id: Tpm2G9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Runs"
msgstr "Execuções"

#. js-lingui-id: N/rFzD
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Scopes"
msgstr "Escopos"

#. js-lingui-id: PdVIJC
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Search vector"
msgstr "Vetor de busca"

#. js-lingui-id: 8VEDbV
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Secret"
msgstr "Segredo"

#. js-lingui-id: Tz0i8g
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Settings"
msgstr ""

#. js-lingui-id: Cj2Gtd
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Size"
msgstr "Tamanho"

#. js-lingui-id: 3PRxO3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Stage"
msgstr "Fase"

#. js-lingui-id: D3iCkb
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Start Date"
msgstr "Data de início"

#. js-lingui-id: RS0o7b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State"
msgstr ""

#. js-lingui-id: nY8GL/
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State of the workflow run"
msgstr ""

#. js-lingui-id: uAQUqI
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Status"
msgstr "Status"

#. js-lingui-id: Db4W3/
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Statuses"
msgstr "Status"

#. js-lingui-id: S7DRR+
#: src/modules/workflow/common/utils/assert-workflow-statuses-not-set.ts
msgid "Statuses cannot be set manually."
msgstr ""

#. js-lingui-id: u+rv4L
#: src/modules/workflow/workflow-builder/workflow-step/workflow-version-step.workspace-service.ts
msgid "Step is not a form"
msgstr ""

#. js-lingui-id: YFMPch
#: src/engine/metadata-modules/utils/validate-metadata-name-start-with-lowercase-letter-and-contain-digits-nor-letters.utils.ts
msgid "String \"{name}\" is not valid: must start with lowercase letter and contain only alphanumeric letters"
msgstr ""

#. js-lingui-id: g4jxXh
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Sub field name"
msgstr ""

#. js-lingui-id: UJmAAK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Subject"
msgstr "Assunto"

#. js-lingui-id: oyJYg7
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor"
msgstr "Cursor de sincronização"

#. js-lingui-id: awvBUx
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor. Used for syncing events from the calendar provider"
msgstr "Cursor de sincronização. Usado para sincronizar eventos do provedor de calendário"

#. js-lingui-id: dNAbG6
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage"
msgstr "Fase de sincronização"

#. js-lingui-id: aqNjQE
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage started at"
msgstr "Fase de sincronização iniciada em"

#. js-lingui-id: bRUdLR
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync status"
msgstr "Status de sincronização"

#. js-lingui-id: 5y3Wbw
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Target object metadata not found for {connectFieldName}"
msgstr ""

#. js-lingui-id: 4SHJe4
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Target Url"
msgstr "URL de destino"

#. js-lingui-id: Q3P/4s
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Task"
msgstr "Tarefa"

#. js-lingui-id: kS+Ym6
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task assignee"
msgstr "Responsável pela tarefa"

#. js-lingui-id: 7fYQ6E
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task attachments"
msgstr "Anexos da tarefa"

#. js-lingui-id: X8fs74
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task body"
msgstr "Corpo da tarefa"

#. js-lingui-id: EPxYHS
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task due date"
msgstr "Data de vencimento da tarefa"

#. js-lingui-id: fUw1j+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task record position"
msgstr "Posição do registro da tarefa"

#. js-lingui-id: I6+0ph
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task status"
msgstr "Status da tarefa"

#. js-lingui-id: WSiiWf
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Target"
msgstr "Meta da tarefa"

#. js-lingui-id: khGQLP
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task targets"
msgstr "Metas da tarefa"

#. js-lingui-id: 836FiO
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Targets"
msgstr "Metas da tarefa"

#. js-lingui-id: R+s8S+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task title"
msgstr "Título da tarefa"

#. js-lingui-id: GtycJ/
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks"
msgstr "Tarefas"

#. js-lingui-id: HlDeG3
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Tasks assigned to the workspace member"
msgstr "Tarefas atribuídas ao membro do espaço de trabalho"

#. js-lingui-id: Ca/n4T
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks tied to the {label}"
msgstr "Tarefas vinculadas ao {label}"

#. js-lingui-id: M4rBti
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Tasks tied to the company"
msgstr "Tarefas vinculadas à empresa"

#. js-lingui-id: /VaiDW
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Tasks tied to the contact"
msgstr "Tarefas vinculadas ao contato"

#. js-lingui-id: 1TfX9U
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Tasks tied to the opportunity"
msgstr "Tarefas vinculadas à oportunidade"

#. js-lingui-id: pP0Dt9
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget company"
msgstr "Empresa TaskTarget"

#. js-lingui-id: UJ2aPi
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget opportunity"
msgstr "Oportunidade TaskTarget"

#. js-lingui-id: I1MiSs
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget person"
msgstr "Pessoa TaskTarget"

#. js-lingui-id: pciKLT
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget task"
msgstr "Tarefa TaskTarget"

#. js-lingui-id: xeiujy
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Text"
msgstr "Texto"

#. js-lingui-id: 6PJbR2
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account handle (email, username, phone number, etc.)"
msgstr "O identificador da conta (e-mail, nome de usuário, número de telefone, etc.)"

#. js-lingui-id: zUXOAB
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account provider"
msgstr "O provedor da conta"

#. js-lingui-id: qnNFrW
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Linkedin account"
msgstr "A conta da empresa no Linkedin"

#. js-lingui-id: N31Pso
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company name"
msgstr "O nome da empresa"

#. js-lingui-id: BHFCqB
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Twitter/X account"
msgstr "A conta da empresa no Twitter/X"

#. js-lingui-id: OBmU0K
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company website URL. We use this url to fetch the company icon"
msgstr "O URL do site da empresa. Usamos esse URL para buscar o ícone da empresa"

#. js-lingui-id: zGBDEH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "The creator of the record"
msgstr "O criador do registro"

#. js-lingui-id: bMyVOx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The current statuses of the workflow versions"
msgstr "Os status atuais das versões do fluxo de trabalho"

#. js-lingui-id: 0bo4Q0
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "The date the message was received"
msgstr "A data em que a mensagem foi recebida"

#. js-lingui-id: 8h4mhq
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "The executor of the workflow"
msgstr "O executor do fluxo de trabalho"

#. js-lingui-id: W3raza
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "The folder this favorite belongs to"
msgstr "A pasta à qual esse favorito pertence"

#. js-lingui-id: EJUSll
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "The initial version of a workflow can not be deleted"
msgstr ""

#. js-lingui-id: DbWmKZ
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "The opportunity name"
msgstr "O nome da oportunidade"

#. js-lingui-id: 5N6QtE
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger settings"
msgstr ""

#. js-lingui-id: SpKbfT
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger type"
msgstr ""

#. js-lingui-id: 7mPrpl
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "The workflow event listener name"
#~ msgstr "O nome do ouvinte do evento de fluxo de trabalho"

#. js-lingui-id: od0omS
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow last published version id"
msgstr "O ID da última versão publicada do fluxo de trabalho"

#. js-lingui-id: /EdWx6
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow name"
msgstr "O nome do fluxo de trabalho"

#. js-lingui-id: EhAsND
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version name"
msgstr "O nome da versão do fluxo de trabalho"

#. js-lingui-id: dhx13p
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version status"
msgstr "O status da versão do fluxo de trabalho"

#. js-lingui-id: yIUmAs
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-reserved-keyword.ts
#: src/engine/metadata-modules/utils/validate-field-name-availability.utils.ts
msgid "This name is not available."
msgstr ""

#. js-lingui-id: Zl0BJl
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread External Id"
msgstr "ID externo da thread"

#. js-lingui-id: RSSbWN
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread id from the messaging provider"
msgstr "ID da thread do provedor de mensagens"

#. js-lingui-id: sS8v5K
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Throttle Failure Count"
msgstr "Contagem de falhas de limitação"

#. js-lingui-id: n9nSNJ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time format"
msgstr "Formato de hora"

#. js-lingui-id: Mz2JN2
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time zone"
msgstr "Fuso horário"

#. js-lingui-id: az1boY
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities"
msgstr "Atividades da linha do tempo"

#. js-lingui-id: fqKMpF
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Timeline Activities linked to the company"
msgstr "Atividades da linha do tempo vinculadas à empresa"

#. js-lingui-id: 4/UzU5
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Timeline Activities linked to the note."
msgstr "Atividades da linha do tempo vinculadas à nota."

#. js-lingui-id: p6feIz
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Timeline Activities linked to the opportunity."
msgstr "Atividades da linha do tempo vinculadas à oportunidade."

#. js-lingui-id: yvPwuF
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Timeline activities linked to the run"
msgstr "Atividades da linha do tempo vinculadas à execução"

#. js-lingui-id: q96UvB
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Timeline Activities linked to the task."
msgstr "Atividades da linha do tempo vinculadas à tarefa."

#. js-lingui-id: N9HMa/
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Timeline activities linked to the version"
msgstr "Atividades da linha do tempo vinculadas à versão"

#. js-lingui-id: B1CYKX
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Timeline activities linked to the workflow"
msgstr "Atividades da linha do tempo vinculadas ao fluxo de trabalho"

#. js-lingui-id: G0UmtQ
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities tied to the {label}"
msgstr "Atividades da linha do tempo vinculadas ao {label}"

#. js-lingui-id: K/kU4E
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Timeline Activity"
msgstr "Atividade da linha do tempo"

#. js-lingui-id: MHrjPM
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Title"
msgstr "Título"

#. js-lingui-id: +zy2Nq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Type"
msgstr "Tipo"

#. js-lingui-id: EBZdX5
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Unsupported cron schedule type"
msgstr ""

#. js-lingui-id: 0gY5lO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Update DateTime"
msgstr "Atualizar DataHora"

#. js-lingui-id: l9dlVi
#: src/engine/core-modules/auth/strategies/jwt.auth.strategy.ts
msgid "User does not have access to this workspace"
msgstr ""

#. js-lingui-id: YFciqL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Email"
msgstr "E-mail do usuário"

#. js-lingui-id: d1BTW8
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Id"
msgstr "ID do usuário"

#. js-lingui-id: B43Kks
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "User is not part of the workspace"
msgstr ""

#. js-lingui-id: 4juE7s
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User time zone"
msgstr "Fuso horário do usuário"

#. js-lingui-id: 43zCwQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred date format"
msgstr "Formato de data preferido do usuário"

#. js-lingui-id: kJuoKm
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred time format"
msgstr "Formato de hora preferido do usuário"

#. js-lingui-id: wMHvYH
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Value"
msgstr "Valor"

#. js-lingui-id: 7aatUy
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version status"
msgstr "Status da versão"

#. js-lingui-id: CdQeU7
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version steps"
msgstr "Etapas da versão"

#. js-lingui-id: PGMPIi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version trigger"
msgstr "Gatilho da versão"

#. js-lingui-id: IYNSdp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Versions"
msgstr "Versões"

#. js-lingui-id: jpctdh
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "View"
msgstr "Visualização"

#. js-lingui-id: cZPDyy
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field"
msgstr "Campo de visualização"

#. js-lingui-id: 6jpoH4
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field position"
msgstr "Posição do campo de visualização"

#. js-lingui-id: Ju6gri
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field related view"
msgstr "Visualização relacionada ao campo de visualização"

#. js-lingui-id: tvTr2y
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field size"
msgstr "Tamanho do campo de visualização"

#. js-lingui-id: BkII8t
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field target field"
msgstr "Campo de destino do campo de visualização"

#. js-lingui-id: Gd/LzL
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field visibility"
msgstr "Visibilidade do campo de visualização"

#. js-lingui-id: GUFYyq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Fields"
msgstr "Campos de visualização"

#. js-lingui-id: JRtI7Y
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter"
msgstr "Filtro de visualização"

#. js-lingui-id: L2gQ5q
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Display Value"
msgstr "Valor de exibição do filtro de visualização"

#. js-lingui-id: l9/6pD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Group"
msgstr "Grupo de filtros de visualização"

#. js-lingui-id: Mbosm8
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Group Id"
msgstr "ID do grupo de filtros de visualização"

#. js-lingui-id: /aP3iG
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Groups"
msgstr "Grupos de filtros de visualização"

#. js-lingui-id: 4MG8+B
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter operand"
msgstr "Operando do filtro de visualização"

#. js-lingui-id: OrUkUF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter related view"
msgstr "Visualização relacionada ao filtro de visualização"

#. js-lingui-id: TyXOtD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter target field"
msgstr "Campo de destino do filtro de visualização"

#. js-lingui-id: 3KzkxN
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter value"
msgstr "Valor do filtro de visualização"

#. js-lingui-id: vj5JsR
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filters"
msgstr "Filtros de visualização"

#. js-lingui-id: ziEP12
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group"
msgstr "Grupo de visualização"

#. js-lingui-id: uQ3c2q
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group related view"
msgstr "Visualização relacionada ao grupo de visualização"

#. js-lingui-id: EFlLpQ
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group target field"
msgstr "Campo de destino do grupo de visualização"

#. js-lingui-id: b1Vc+l
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group visibility"
msgstr "Visibilidade do grupo de visualização"

#. js-lingui-id: V4nZs/
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Groups"
msgstr "Grupos de visualização"

#. js-lingui-id: qXlovu
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View icon"
msgstr "Ícone de visualização"

#. js-lingui-id: cd/+ZD
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View Kanban column field"
msgstr "Campo de coluna Kanban de visualização"

#. js-lingui-id: vdfZ+A
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View key"
msgstr "Chave de visualização"

#. js-lingui-id: oOljSE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View name"
msgstr "Nome da visualização"

#. js-lingui-id: rhO8zp
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View position"
msgstr "Posição da visualização"

#. js-lingui-id: EUjpwJ
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort"
msgstr "Ordenação de visualização"

#. js-lingui-id: 6ZUale
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort direction"
msgstr "Direção de ordenação de visualização"

#. js-lingui-id: /rCPqN
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort related view"
msgstr "Visualização relacionada à ordenação de visualização"

#. js-lingui-id: +du2wy
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort target field"
msgstr "Campo de destino da ordenação de visualização"

#. js-lingui-id: UsdY3K
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sorts"
msgstr "Ordenações de visualização"

#. js-lingui-id: clWwIZ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View target object"
msgstr "Objeto de destino da visualização"

#. js-lingui-id: bJAIqT
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View type"
msgstr "Tipo de visualização"

#. js-lingui-id: 1I6UoR
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Views"
msgstr "Visualizações"

#. js-lingui-id: 2q/Q7x
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Visibility"
msgstr "Visibilidade"

#. js-lingui-id: oh8+os
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Visible"
msgstr "Visível"

#. js-lingui-id: TRDppN
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook"
msgstr "Webhook"

#. js-lingui-id: fyB2Wp
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operation"
msgstr "Operação de webhook"

#. js-lingui-id: gONLmX
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operations"
msgstr "Operações de webhook"

#. js-lingui-id: cPoSTF
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook target url"
msgstr "URL de destino do webhook"

#. js-lingui-id: v1kQyJ
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhooks"
msgstr "Webhooks"

#. js-lingui-id: TVkCrJ
#: src/engine/core-modules/email-verification/services/email-verification.service.ts
msgid "Welcome to Twenty: Please Confirm Your Email"
msgstr "Bem-vindo ao Twenty: Por favor, confirme seu e-mail"

#. js-lingui-id: bLt/0J
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Workflow"
msgstr "Workflow"

#. js-lingui-id: hdtWQn
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow automated triggers linked to the workflow."
msgstr ""

#. js-lingui-id: E03XpH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Workflow event listeners linked to the workflow."
#~ msgstr "Ouvintes de eventos do Workflow vinculados ao Workflow."

#. js-lingui-id: vwSkSW
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow linked to the run."
msgstr "Workflow vinculado à execução."

#. js-lingui-id: y9tnFx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow record position"
msgstr "Posição do registro do Workflow"

#. js-lingui-id: 5vIcqC
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Workflow Run"
msgstr "Execução do Workflow"

#. js-lingui-id: 3Iz+qz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run ended at"
msgstr "Execução do Workflow terminou em"

#. js-lingui-id: IUaK6s
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run position"
msgstr "Posição da execução do Workflow"

#. js-lingui-id: zaN7tH
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run started at"
msgstr "Execução do Workflow começou em"

#. js-lingui-id: 1TU2A8
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run status"
msgstr "Status da execução do Workflow"

#. js-lingui-id: u6DF/V
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow Runs"
msgstr "Execuções do Workflow"

#. js-lingui-id: 9nOy7k
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow runs linked to the version."
msgstr "Execuções do Workflow vinculadas à versão."

#. js-lingui-id: c37F3j
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow runs linked to the workflow."
msgstr "Execuções do Workflow vinculadas ao Workflow."

#. js-lingui-id: lTXctu
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version"
msgstr "Versão do Workflow"

#. js-lingui-id: +wYPET
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Version"
msgstr "Versão do Workflow"

#. js-lingui-id: n4/IOE
#: src/modules/workflow/common/utils/assert-workflow-version-has-steps.ts
msgid "Workflow version does not contain at least one step"
msgstr ""

#. js-lingui-id: k4DPlQ
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/common/utils/assert-workflow-version-trigger-is-defined.util.ts
msgid "Workflow version does not contain trigger"
msgstr ""

#. js-lingui-id: bqFL4g
#: src/modules/workflow/common/utils/assert-workflow-version-is-draft.util.ts
msgid "Workflow version is not in draft status"
msgstr ""

#. js-lingui-id: CocTJJ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version linked to the run."
msgstr "Versão do Workflow vinculada à execução."

#. js-lingui-id: 9l+pJT
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow version position"
msgstr "Posição da versão do Workflow"

#. js-lingui-id: OCyhkn
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Versions"
msgstr "Versões do Workflow"

#. js-lingui-id: 018fP9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow versions linked to the workflow."
msgstr "Versões do Workflow vinculadas ao Workflow."

#. js-lingui-id: s7p+TL
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger"
msgstr ""

#. js-lingui-id: 96bCgZ
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger workflow"
msgstr ""

#. js-lingui-id: 5z92T9
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTriggers"
msgstr ""

#. js-lingui-id: ENOy6I
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener"
#~ msgstr "Listener de eventos do Workflow"

#. js-lingui-id: HN90FO
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener workflow"
#~ msgstr "Workflow do WorkflowEventListener"

#. js-lingui-id: 3JA9se
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListeners"
#~ msgstr "Listeners de eventos do Workflow"

#. js-lingui-id: woYYQq
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflows"
msgstr "Workflows"

#. js-lingui-id: urCUgs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "WorkflowVersion"
msgstr "Versão do Workflow"

#. js-lingui-id: b4kire
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "WorkflowVersion workflow"
msgstr "Workflow da Versão do Workflow"

#. js-lingui-id: rklt6M
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Workspace is not ready to welcome new members"
msgstr ""

#. js-lingui-id: CbGxon
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Workspace member"
msgstr "Membro do Workspace"

#. js-lingui-id: qc38qR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Workspace Member"
msgstr "Membro do Workspace"

#. js-lingui-id: R1S9pO
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member avatar"
msgstr "Avatar do membro do Workspace"

#. js-lingui-id: 5VCX7o
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member name"
msgstr "Nome do membro do Workspace"

#. js-lingui-id: NiUpuN
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member position"
msgstr "Cargo do membro do Workspace"

#. js-lingui-id: YCAEr+
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace Members"
msgstr "Membros do Workspace"

#. js-lingui-id: EtzFC0
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "WorkspaceMember"
msgstr "Membro do Workspace"

#. js-lingui-id: fV2Bu6
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Wrong password"
msgstr ""

#. js-lingui-id: 0gv+T2
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "X"
msgstr "X"

#. js-lingui-id: z+7x/s
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "You must be authenticated to perform this action."
msgstr ""

#. js-lingui-id: bTyBrW
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Your Password Has Been Successfully Changed"
msgstr "Sua senha foi alterada com sucesso"

#. js-lingui-id: vPccnr
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Your team member responsible for managing the company account"
msgstr "Seu membro da equipe responsável por gerenciar a conta da empresa"

#. js-lingui-id: ej/tZF
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "Your workspace has been updated with a new data model. Please refresh the page."
msgstr ""
