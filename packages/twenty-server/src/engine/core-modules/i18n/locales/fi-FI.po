msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-29 18:14+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: fi\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Finnish\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: fi\n"
"X-Crowdin-File: /packages/twenty-server/src/engine/core-modules/i18n/locales/en.po\n"
"X-Crowdin-File-ID: 31\n"

#. js-lingui-id: Qyrd7v
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "(System) View Fields"
msgstr "(Järjestelmä) Näytä Kentät"

#. js-lingui-id: 9Y3fTB
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "(System) View Filter Groups"
msgstr "(Järjestelmä) Näytä Suodinryhmät"

#. js-lingui-id: TB2jLV
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "(System) View Filters"
msgstr "(Järjestelmä) Näytä Suodattimet"

#. js-lingui-id: Y7M7Ro
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "(System) View Groups"
msgstr "(Järjestelmä) Näytä Ryhmät"

#. js-lingui-id: 9vliLw
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "(System) View Sorts"
msgstr "(Järjestelmä) Näytä Lajittelut"

#. js-lingui-id: 5B59WE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "(System) Views"
msgstr "(Järjestelmä) Näkymät"

#. js-lingui-id: Q0ISF1
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "{fieldName} and {fieldName}Id cannot be both provided."
msgstr ""

#. js-lingui-id: 8haj+G
#: src/engine/metadata-modules/utils/validate-metadata-name-is-camel-case.utils.ts
msgid "{name} should be in camelCase"
msgstr ""

#. js-lingui-id: kZR6+h
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "A company"
msgstr "Yritys"

#. js-lingui-id: +aeifv
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "A connected account"
msgstr "Liitetty tili"

#. js-lingui-id: HCoswz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "A favorite that can be accessed from the left menu"
msgstr "Suosikki, joka voidaan avata vasemmasta valikosta"

#. js-lingui-id: 6w8bHl
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "A Folder of favorites"
msgstr "Suosikkikansio"

#. js-lingui-id: sSGYmf
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "A group of related messages (e.g. email thread, chat thread)"
msgstr "Aiheeseen liittyvien viestien ryhmä (esim. sähköpostiketju, keskusteluketju)"

#. js-lingui-id: vZj1Xc
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "A message sent or received through a messaging channel (email, chat, etc.)"
msgstr "Viestintäkanavan kautta lähetetty tai vastaanotettu viesti (sähköposti, chat jne.)"

#. js-lingui-id: bufuBA
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "A note"
msgstr "Muistiinpano"

#. js-lingui-id: 6kUkZW
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "A note target"
msgstr "Muistiinpanon kohde"

#. js-lingui-id: Io42ej
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "A person"
msgstr "Henkilö"

#. js-lingui-id: Q2De+v
#: src/engine/metadata-modules/role/role.service.ts
msgid "A role with this label already exists."
msgstr ""

#. js-lingui-id: mkFXEH
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "A task"
msgstr "Tehtävä"

#. js-lingui-id: hk2NzW
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "A task target"
msgstr "Tehtävän kohde"

#. js-lingui-id: HTSJFW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "A webhook"
msgstr "Webhook"

#. js-lingui-id: ZIN9Ga
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "A workflow"
msgstr "Työnkulku"

#. js-lingui-id: YwBCp8
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "A workflow automated trigger"
msgstr ""

#. js-lingui-id: juBVjt
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "A workflow event listener"
#~ msgstr "Työnkulun tapahtumankuuntelija"

#. js-lingui-id: 1+xDbI
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "A workflow run"
msgstr "Työnkulun suoritus"

#. js-lingui-id: N0g7rp
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "A workflow version"
msgstr "Työnkulkujen versio"

#. js-lingui-id: HpZ/I5
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "A workspace member"
msgstr "Toimialueen jäsen"

#. js-lingui-id: GDKKxT
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Access Token"
msgstr "Pääsynhallintatokeni"

#. js-lingui-id: pd81Qb
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Account Owner"
msgstr "Tilin omistaja"

#. js-lingui-id: loqL/f
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account owner for companies"
msgstr "Yrityksen tilin omistaja"

#. js-lingui-id: HZosRi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account Owner For Companies"
msgstr "Yrityksen Tilin Omistaja"

#. js-lingui-id: 48AxkT
#: src/engine/workspace-manager/workspace-cleaner/services/cleaner.workspace-service.ts
msgid "Action needed to prevent workspace deletion"
msgstr "Toimia tarvitaan työtilan poistamisen estämiseksi"

#. js-lingui-id: j0DfGR
#: src/engine/core-modules/auth/services/reset-password.service.ts
msgid "Action Needed to Reset Password"
msgstr "Toimia tarvitaan salasanan nollaamiseksi"

#. js-lingui-id: Du6bPw
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address"
msgstr "Osoite"

#. js-lingui-id: JiOJxf
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address (deprecated) "
msgstr "Osoite (vanhentunut) "

#. js-lingui-id: Knl3c9
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company"
msgstr "Yrityksen osoite"

#. js-lingui-id: zJhwjv
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company - deprecated in favor of new address field"
msgstr "Yrityksen osoite - korvattu uudella osoitekentällä"

#. js-lingui-id: ZfVqbP
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Aggregate operation"
msgstr "Kokoomatoiminto"

#. js-lingui-id: W58PBh
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Aggregated / filtered event to be displayed on the timeline"
msgstr "Ajajanäkymässä näytettävä yhdistetty/suodatettu tapahtuma"

#. js-lingui-id: hehnjM
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Amount"
msgstr "Määrä"

#. js-lingui-id: qeHcQj
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "An API key"
msgstr "API-avain"

#. js-lingui-id: MjyFvC
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "An attachment"
msgstr "Liite"

#. js-lingui-id: +bL++X
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "An audit log of actions performed in the system"
#~ msgstr "Järjestelmässä suoritettujen toimintojen auditointiloki"

#. js-lingui-id: XyOToQ
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "An error occurred."
msgstr ""

#. js-lingui-id: muVHgL
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "An event related to user behavior"
#~ msgstr "Käyttäjän käyttäytymiseen liittyvä tapahtuma"

#. js-lingui-id: bZq8rL
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "An opportunity"
msgstr "Mahdollisuus"

#. js-lingui-id: JKWicb
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Annual Recurring Revenue: The actual or estimated annual revenue of the company"
msgstr "Toistuva vuosittainen liikevaihto: Yrityksen todellinen tai arvioitu vuosittainen liikevaihto"

#. js-lingui-id: yRnk5W
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Key"
msgstr "API-avain"

#. js-lingui-id: FfSJ1Y
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Keys"
msgstr "API-avaimet"

#. js-lingui-id: puNs/l
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey expiration date"
msgstr "API-avaimen vanhentumispäivämäärä"

#. js-lingui-id: YHiNxr
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey name"
msgstr "API-avaimen nimi"

#. js-lingui-id: xrndTt
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey revocation date"
msgstr "API-avaimen mitätöimispäivämäärä"

#. js-lingui-id: DGW5iN
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain does not match email domain"
msgstr ""

#. js-lingui-id: BKvuAq
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain has already been validated"
msgstr ""

#. js-lingui-id: 3EiOLz
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ARR"
msgstr "ARR"

#. js-lingui-id: Ek7xGj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Assigned tasks"
msgstr "Määrätyt tehtävät"

#. js-lingui-id: ojKCLU
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Assignee"
msgstr "Tehtävään määrätty"

#. js-lingui-id: Max2GU
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Associated User Id"
msgstr "Liitetyn käyttäjän tunnus"

#. js-lingui-id: UY1vmE
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment"
msgstr "Liite"

#. js-lingui-id: JAefBH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment author"
msgstr "Liitteen luoja"

#. js-lingui-id: bIaesZ
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment company"
msgstr "Liitteen yritys"

#. js-lingui-id: gfGYcl
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment full path"
msgstr "Liitteen täydellinen polku"

#. js-lingui-id: wjocwa
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment name"
msgstr "Liitteen nimi"

#. js-lingui-id: FWlOXr
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment note"
msgstr "Liitteen muistiinpano"

#. js-lingui-id: YASWpH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment opportunity"
msgstr "Liitteen mahdollisuus"

#. js-lingui-id: P38yED
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment person"
msgstr "Liitteen henkilö"

#. js-lingui-id: Tx1DxS
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment task"
msgstr "Liitteen tehtävä"

#. js-lingui-id: 4qqxMt
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment type"
msgstr "Liitteen tyyppi"

#. js-lingui-id: w/Sphq
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments"
msgstr "Liitteet"

#. js-lingui-id: 2tQOVc
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Attachments created by the workspace member"
msgstr "Työtilan jäsenen luomat liitteet"

#. js-lingui-id: iVnA89
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Attachments linked to the company"
msgstr "Yritykseen liitetyt liitteet"

#. js-lingui-id: MuTXtT
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Attachments linked to the contact."
msgstr "Yhteystietoihin liitetyt liitteet."

#. js-lingui-id: kw0mLu
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Attachments linked to the opportunity"
msgstr "Mahdollisuuteen liitetyt liitteet"

#. js-lingui-id: fCbqr7
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments tied to the {label}"
msgstr "{label}-kohteeseen liitetyt liitteet"

#. js-lingui-id: ilRCh1
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Log"
#~ msgstr "Tarkastusloki"

#. js-lingui-id: EPEFrH
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Logs"
#~ msgstr "Tarkastuslokit"

#. js-lingui-id: RqCC/0
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#~ msgid "Audit Logs linked to the workspace member"
#~ msgstr "Työtilan jäsenen tarkastuslokit"

#. js-lingui-id: cNBqH+
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Auth failed at"
msgstr "Authentikointi epäonnistui kello"

#. js-lingui-id: LB4qX/
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Authentication is not enabled with this provider."
msgstr ""

#. js-lingui-id: VbeIOx
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Author"
msgstr "Tekijä"

#. js-lingui-id: XJONK6
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Authored attachments"
msgstr "Tekijän luomat liitteet"

#. js-lingui-id: cvSznK
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Automated Trigger Type"
msgstr ""

#. js-lingui-id: 2mtBXs
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Automated Triggers"
msgstr ""

#. js-lingui-id: RpExX0
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Automatically create People records when receiving or sending emails"
msgstr "Luo automaattisesti henkilötietueita viestejä vastaanottaessa tai lähettäessä"

#. js-lingui-id: lXxdId
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Automatically create records for people you participated with in an event."
msgstr "Luo automaattisesti tietueita tapahtumissa osallistuneista henkilöistä."

#. js-lingui-id: kfcRb0
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Avatar"
msgstr "Hahmo"

#. js-lingui-id: S/mJUR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Avatar Url"
msgstr "Hahmon URL-osoite"

#. js-lingui-id: 20B9kW
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Event"
#~ msgstr "Käyttäytymistapahtuma"

#. js-lingui-id: Jeh/Q/
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Events"
#~ msgstr "Käyttäytymistapahtumat"

#. js-lingui-id: K1172m
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklist"
msgstr "Estolista"

#. js-lingui-id: Tv2hxv
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Blocklisted handles"
msgstr "Estolistatut kahvat"

#. js-lingui-id: L5JhJe
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklists"
msgstr "Estolistat"

#. js-lingui-id: bGQplw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body"
msgstr "Sisältö"

#. js-lingui-id: Nl8eMw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body (deprecated)"
msgstr "Ruumis (vanhentunut)"

#. js-lingui-id: 8mVqF7
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Cached record name"
msgstr "Välimuistitallennetun nimen"

#. js-lingui-id: Nh6GTX
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channel"
msgstr "Kalenterikanava"

#. js-lingui-id: jfNQ0m
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Association"
msgstr "Kalenterikanavan tapahtumayhdistys"

#. js-lingui-id: kYNT3F
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Associations"
msgstr "Kalenterikanavan tapahtumayhdistykset"

#. js-lingui-id: Znix/S
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channels"
msgstr "Kalenterikanavat"

#. js-lingui-id: bRk+FR
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar event"
msgstr "Kalenteritapahtuma"

#. js-lingui-id: N2kMfO
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participant"
msgstr "Kalenteritapahtuman osallistuja"

#. js-lingui-id: AWDqkQ
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participants"
msgstr "Kalenteritapahtuman osallistujat"

#. js-lingui-id: ZI2UyM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Calendar Event Participants"
msgstr "Kalenteritapahtuman osallistujat"

#. js-lingui-id: X9A2xC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar events"
msgstr "Kalenteritapahtumat"

#. js-lingui-id: vxbVwc
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Cannot activate non-draft or non-last-published version"
msgstr ""

#. js-lingui-id: LIPsWu
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create multiple draft versions for the same workflow"
msgstr ""

#. js-lingui-id: RggZr4
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create workflow version with status other than draft"
msgstr ""

#. js-lingui-id: AQyr9X
#: src/engine/metadata-modules/field-metadata/services/field-metadata.service.ts
msgid "Cannot delete, please update the label identifier field first"
msgstr ""

#. js-lingui-id: FrNWt0
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot disable non-active workflow version"
msgstr ""

#. js-lingui-id: IDMgr/
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot have more than one active workflow version"
msgstr ""

#. js-lingui-id: 4DKo3U
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot update workflow version status manually"
msgstr ""

#. js-lingui-id: 4IVK41
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Channel ID"
msgstr "Kanavan tunniste"

#. js-lingui-id: Ubg/B+
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Channel Type"
msgstr "Kanavatyyppi"

#. js-lingui-id: 3wV73y
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "City"
msgstr "Kaupunki"

#. js-lingui-id: NRF7pg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Close date"
msgstr "Päättymispäivämäärä"

#. js-lingui-id: 96YB1a
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Color Scheme"
msgstr "Värimaailma"

#. js-lingui-id: 07OSD1
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Compact View"
msgstr "Tiivis näkymä"

#. js-lingui-id: s2QZS6
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Companies"
msgstr "Yritykset"

#. js-lingui-id: 7i8j3G
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Company"
msgstr "Yritys"

#. js-lingui-id: yA1de7
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Company record position"
msgstr "Yritysmuistion sijainti"

#. js-lingui-id: AgktVC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Conference Solution"
msgstr "Videoneuvotteluratkaisu"

#. js-lingui-id: 1fFL4B
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Connect is not allowed for {connectFieldName} on {objectMetadataNameSingular}"
msgstr ""

#. js-lingui-id: PQ1Dw2
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Connected Account"
msgstr "Liitetty tili"

#. js-lingui-id: 9TzudL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Connected accounts"
msgstr "Liitetyt tilit"

#. js-lingui-id: AMDUqA
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Connected Accounts"
msgstr "Liitetyt tilit"

#. js-lingui-id: bA6O5B
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Contact auto creation policy"
msgstr "Ota yhteyttä automaattisen luonnin käytäntöön"

#. js-lingui-id: RnsmQs
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s avatar"
msgstr "Kontaktin avatar"

#. js-lingui-id: pL+pqi
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s city"
msgstr "Kontaktin kaupunki"

#. js-lingui-id: VnWMlz
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s company"
msgstr "Kontaktin yritys"

#. js-lingui-id: ITlFIB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Emails"
msgstr "Kontaktin sähköpostit"

#. js-lingui-id: GuRtLY
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s job title"
msgstr "Kontaktin työnimike"

#. js-lingui-id: QrCvRQ
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Linkedin account"
msgstr "Kontaktin LinkedIn-tili"

#. js-lingui-id: 6xPSVt
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s name"
msgstr "Kontaktin nimi"

#. js-lingui-id: Y37CZ4
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone number"
msgstr "Kontaktin puhelinnumero"

#. js-lingui-id: zsW3gg
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone numbers"
msgstr "Kontaktin puhelinnumerot"

#. js-lingui-id: uuZ00G
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s X/Twitter account"
msgstr "Kontaktin X/Twitter-tili"

#. js-lingui-id: M73whl
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Context"
msgstr "Konteksti"

#. js-lingui-id: NCIYDF
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Created by"
msgstr "Luoja"

#. js-lingui-id: wPvFAD
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Creation date"
msgstr "Luontipäivämäärä"

#. js-lingui-id: CJXWmO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Creation DateTime"
msgstr "Luontihetken aika"

#. js-lingui-id: umhXiy
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Cron pattern '{pattern}' is invalid"
msgstr ""

#. js-lingui-id: Mikszu
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Custom Connection Parameters"
msgstr ""

#. js-lingui-id: Lhd0oQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Date format"
msgstr "Päiväysmuoto"

#. js-lingui-id: 1lL5Iu
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Date when the record was deleted"
msgstr "Merkinnän poistopäivämäärä"

#. js-lingui-id: QN9ahV
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Deleted at"
msgstr "Poistettu"

#. js-lingui-id: U1bSSI
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Describes if the view is in compact mode"
msgstr "Kuvaa onko näkymä tiiviissä tilassa"

#. js-lingui-id: Nu4oKW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Description"
msgstr "Kuvaus"

#. js-lingui-id: MRB7nI
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Direction"
msgstr "Suunta"

#. js-lingui-id: 0gS7M5
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Display Name"
msgstr "Näyttönimi"

#. js-lingui-id: y7HJTU
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Display the records in a side panel or in a record page"
msgstr "Näytä tietueet sivupaneelissa tai tietuesivulla"

#. js-lingui-id: 0H/D9K
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Display Value"
msgstr "Näyttöarvo"

#. js-lingui-id: wMncXq
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Domain Name"
msgstr "Verkkotunnus"

#. js-lingui-id: YOowcq
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Due Date"
msgstr "Eräpäivä"

#. js-lingui-id: BBXuRb
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email already verified."
msgstr ""

#. js-lingui-id: xw6iqb
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Email is not verified."
msgstr ""

#. js-lingui-id: ZsZeV2
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Email is required"
msgstr ""

#. js-lingui-id: y8LSMh
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email verification not required."
msgstr ""

#. js-lingui-id: BXEcos
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Emails"
msgstr "Sähköpostit"

#. js-lingui-id: gqv5ZL
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Employees"
msgstr "Työntekijät"

#. js-lingui-id: VFv2ZC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "End Date"
msgstr "Päättymispäivä"

#. js-lingui-id: k//6Xs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event company"
msgstr "Tapahtuman yritys"

#. js-lingui-id: FJ7QI4
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event context"
#~ msgstr "Tapahtuman yhteys"

#. js-lingui-id: kJDmsI
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event details"
msgstr "Tapahtuman tiedot"

#. js-lingui-id: 0JhmlM
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event external ID"
msgstr "Tapahtuman ulkoinen ID"

#. js-lingui-id: aZJLAR
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event ID"
msgstr "Tapahtuman ID"

#. js-lingui-id: 81maJp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Event Listeners"
#~ msgstr "Tapahtumakuuntelijat"

#. js-lingui-id: PYs3rP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event name"
msgstr "Tapahtuman nimi"

#. js-lingui-id: evIGwh
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event name/type"
#~ msgstr "Tapahtuman nimi\\/tyyppi"

#. js-lingui-id: vv99Wu
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event note"
msgstr "Tapahtuman muistio"

#. js-lingui-id: eSt759
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event opportunity"
msgstr "Tapahtuman mahdollisuus"

#. js-lingui-id: aicVfT
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Event Participants"
msgstr "Tapahtuman osallistujat"

#. js-lingui-id: 0mp45a
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event person"
msgstr "Tapahtuman henkilö"

#. js-lingui-id: mMq0Wy
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event task"
msgstr "Tapahtumatehtävä"

#. js-lingui-id: dCV1dS
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow"
msgstr "Tapahtuman työnkulku"

#. js-lingui-id: W84pl6
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow run"
msgstr "Tapahtuman työnkulun suoritus"

#. js-lingui-id: vUpps9
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow version"
msgstr "Tapahtuman työnkulun versio"

#. js-lingui-id: LxOGsB
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workspace member"
msgstr "Tapahtuman työtilan jäsen"

#. js-lingui-id: tst44n
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events"
msgstr "Tapahtumat"

#. js-lingui-id: fHL+iH
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events linked to the person"
msgstr "Henkilöön liittyvät tapahtumat"

#. js-lingui-id: 3/O8MM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Events linked to the workspace member"
msgstr "Työtilan jäseniin liittyvät tapahtumat"

#. js-lingui-id: QQlMid
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude group emails"
msgstr "Jätä ryhmäsähköpostit pois"

#. js-lingui-id: kenYGr
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude non professional emails"
msgstr "Jätä ei-ammattimaiset sähköpostit pois"

#. js-lingui-id: Lo5U0b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Executed by"
msgstr "Suorittanut"

#. js-lingui-id: AiFXmG
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Expected the same constraint fields to be used consistently across all operations for {connectFieldName}."
msgstr ""

#. js-lingui-id: sZg7s1
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Expiration date"
msgstr "Vanhentumispäivä"

#. js-lingui-id: 6Ki4Pv
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite"
msgstr "Suosikki"

#. js-lingui-id: aKUOPp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite company"
msgstr "Suosikkiyritys"

#. js-lingui-id: TDlZ/o
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite Folder"
msgstr "Suosikkikansio"

#. js-lingui-id: WDVfUH
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite folder position"
msgstr "Suosikkikansion sijainti"

#. js-lingui-id: SStz54
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite Folders"
msgstr "Suosikkikansiot"

#. js-lingui-id: dz/fFp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite note"
msgstr "Suosikkimuistiinpano"

#. js-lingui-id: RRr9Bp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite opportunity"
msgstr "Suosikkitilaisuus"

#. js-lingui-id: RM6B6V
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite person"
msgstr "Suosikkihenkilö"

#. js-lingui-id: oVA9vM
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite position"
msgstr "Suosikkipositio"

#. js-lingui-id: OaQVQH
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite task"
msgstr "Suosikkitaski"

#. js-lingui-id: VkWV1s
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite view"
msgstr "Suosikkinäkymä"

#. js-lingui-id: l+lx2f
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow"
msgstr "Suosikkiprosessi"

#. js-lingui-id: BPBnux
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow run"
msgstr "Suosikkityönkulku"

#. js-lingui-id: 5X6Vlz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow version"
msgstr "Suosikkiprosessin versio"

#. js-lingui-id: FDpezg
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workspace member"
msgstr "Suosikityötilan jäsen"

#. js-lingui-id: X9kySA
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites"
msgstr "Suosikit"

#. js-lingui-id: 0CzeFL
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorites in this folder"
msgstr "Tässä kansiossa olevat suosikit"

#. js-lingui-id: zQponA
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Favorites linked to the company"
msgstr "Yritykseen linkitetyt suosikit"

#. js-lingui-id: VaKLrB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Favorites linked to the contact"
msgstr "Kontaktiin linkitetyt suosikit"

#. js-lingui-id: GOfcBt
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Favorites linked to the note"
msgstr "Muistiinpanoon linkitetyt suosikit"

#. js-lingui-id: 9zd8hg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Favorites linked to the opportunity"
msgstr "Tilaisuuteen linkitetyt suosikit"

#. js-lingui-id: L5ccWD
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Favorites linked to the task"
msgstr "Tehtävään linkitetyt suosikit"

#. js-lingui-id: R+1ib/
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Favorites linked to the view"
msgstr "Näkymään linkitetyt suosikit"

#. js-lingui-id: ee0tmj
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Favorites linked to the workflow"
msgstr "Prosessiin linkitetyt suosikit"

#. js-lingui-id: zar5jz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Favorites linked to the workflow run"
msgstr "Työnkulkutapahtumaan linkitetyt suosikit"

#. js-lingui-id: 499/sw
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Favorites linked to the workflow version"
msgstr "Prosessin versioon linkitetyt suosikit"

#. js-lingui-id: rgmtb4
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Favorites linked to the workspace member"
msgstr "Työtilan jäseniin linkitetyt suosikit"

#. js-lingui-id: GyXrE1
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites tied to the {label}"
msgstr "{label} kytketyt suosikit"

#. js-lingui-id: nSFFML
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Field Metadata Id"
msgstr "Kenttämetatiedon Id"

#. js-lingui-id: g+t8w9
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Field metadata used for aggregate operation"
msgstr "Käytetty metatietokenttä yhteenvetotoimintoon"

#. js-lingui-id: J4hC2m
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Field used for full-text search"
msgstr "Täydet tekstihaun kentät"

#. js-lingui-id: zuDgzc
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Field Value"
msgstr "Kentän Arvo"

#. js-lingui-id: 3oQ73E
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have a previous step"
#~ msgstr ""

#. js-lingui-id: Zllikb
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have only one previous step"
#~ msgstr ""

#. js-lingui-id: lEpDue
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder for Message Channel"
msgstr "Viestikanavan kansio"

#. js-lingui-id: cqQyPB
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder name"
msgstr "Kansion nimi"

#. js-lingui-id: kuN9I4
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have a defined label and type"
msgstr ""

#. js-lingui-id: QK3OvQ
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have unique names"
msgstr ""

#. js-lingui-id: KYP1t5
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action must have at least one field"
msgstr ""

#. js-lingui-id: oQA+cA
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Full path"
msgstr "Täysi polku"

#. js-lingui-id: UzpVUy
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Group by this field value"
msgstr "Ryhmittele tämän kentän arvon perusteella"

#. js-lingui-id: TkE8dW
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "handle"
msgstr "kahva"

#. js-lingui-id: Nf7oXL
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Handle"
msgstr "Kahva"

#. js-lingui-id: GvgxWx
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Handle Aliases"
msgstr "Kahva-aliakset"

#. js-lingui-id: VehAU2
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Header message Id"
msgstr "Viiten viestin Id"

#. js-lingui-id: NNJnBi
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "iCal UID"
msgstr "iCal-tunniste"

#. js-lingui-id: wwu18a
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Icon"
msgstr "Kuvake"

#. js-lingui-id: CiyiKN
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ICP"
msgstr "IPK"

#. js-lingui-id: dFb5Nt
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Id"
msgstr "Tunnus"

#. js-lingui-id: Ebc83S
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Ideal Customer Profile:  Indicates whether the company is the most suitable and valuable customer for you"
msgstr "Ideaalinen asiakasprofiili: Osoittaa, onko yritys sinulle sopivin ja arvokkain asiakas"

#. js-lingui-id: NrA0WZ
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "If the event is related to a particular object"
#~ msgstr "Jos tapahtuma liittyy tiettyyn objektiin"

#. js-lingui-id: 71VJzG
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-short.utils.ts
msgid "Input is too short: \"{name}\""
msgstr ""

#. js-lingui-id: X7+bC6
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid calling code {callingCode}"
msgstr ""

#. js-lingui-id: 1PtrY0
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid country code {countryCode}"
msgstr ""

#. js-lingui-id: 4EtFrA
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid day value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: MBpeQ6
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer between 0 and 23"
msgstr ""

#. js-lingui-id: 9xsjn9
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: FSunWx
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Invalid label: \"{label}\""
msgstr ""

#. js-lingui-id: iBBCnf
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer between 0 and 59"
msgstr ""

#. js-lingui-id: hYcFSd
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: ApP70c
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: sHcAMD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid trigger type for enabling workflow trigger"
msgstr ""

#. js-lingui-id: grHYXZ
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is canceled"
msgstr "On peruttu"

#. js-lingui-id: k+g9Uh
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Contact Auto Creation Enabled"
msgstr "Onko yhteystietojen automaattiluonti käytössä"

#. js-lingui-id: uBx1xd
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is Full Day"
msgstr "Onko koko päivän kestävä"

#. js-lingui-id: 3iAfL2
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Is Organizer"
msgstr "Onko järjestäjä"

#. js-lingui-id: Mqzqb8
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Sync Enabled"
msgstr "Onko synkronointi käytössä"

#. js-lingui-id: 27z+FV
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Job Title"
msgstr "Työnimike"

#. js-lingui-id: PviVyk
#: src/engine/core-modules/workspace-invitation/services/workspace-invitation.service.ts
msgid "Join your team on Twenty"
msgstr "Liity tiimiisi Twentyssä"

#. js-lingui-id: WNfJ8M
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "JSON object containing custom connection parameters"
msgstr ""

#. js-lingui-id: shbh25
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Json object to provide context (user, device, workspace, etc.)"
#~ msgstr "Json-objekti tarjoamaan kontekstin (käyttäjä, laite, työtila jne.)"

#. js-lingui-id: HgJ9jQ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Json object to provide output of the workflow run"
msgstr "Json-objekti tarjoamaan työprosessin suorituksen tuloksen"

#. js-lingui-id: AQuLwi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide steps"
msgstr "Json-objekti vaiheiden tarjoamiseen"

#. js-lingui-id: fOjmZ1
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide trigger"
msgstr "Json-objekti tarjoamaan laukaisijan"

#. js-lingui-id: TVc0/Q
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Json value for event details"
msgstr "Json-arvo tapahtuman yksityiskohtia varten"

#. js-lingui-id: qmarO2
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "kanbanfieldMetadataId"
msgstr "kanbanfieldMetadataId"

#. js-lingui-id: 7sMeHQ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Key"
msgstr "Avain"

#. js-lingui-id: w8Rv8T
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Label is required"
msgstr ""

#. js-lingui-id: 4EGmy6
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not be empty"
msgstr ""

#. js-lingui-id: k731jp
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not contain a comma"
msgstr ""

#. js-lingui-id: vXIe7J
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Language"
msgstr "Kieli"

#. js-lingui-id: WvYp6o
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Last published Version Id"
msgstr "Viimeksi julkaistun version Id"

#. js-lingui-id: Y60/DC
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Last sync cursor"
msgstr "Viimeisin synkronoinnin kursori"

#. js-lingui-id: Zdx9Qq
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Last sync date"
msgstr "Viimeisin synkronointipäivämäärä"

#. js-lingui-id: c7uqNg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Last sync history ID"
msgstr "Viimeisin synkronointihistorian ID"

#. js-lingui-id: tGwswq
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last time the record was changed"
msgstr "Viimeisin kerran kun tietuetta muutettiin"

#. js-lingui-id: o1zvNS
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last update"
msgstr "Viimeisin päivitys"

#. js-lingui-id: wR9USP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Object Metadata Id"
msgstr "Linkitetyn objektin metadata Id"

#. js-lingui-id: PV1Nm7
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Linked Opportunities"
msgstr "Linkitetyt mahdollisuudet"

#. js-lingui-id: 6YiMr4
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record cached name"
msgstr "Linkitetyn tietueen välimuistissa oleva nimi"

#. js-lingui-id: sgHAxx
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record id"
msgstr "Linkitetyn tietueen id"

#. js-lingui-id: uCA6be
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Linkedin"
msgstr "LinkedIn"

#. js-lingui-id: LeFv/R
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "List of opportunities for which that person is the point of contact"
msgstr "Luettelo mahdollisuuksista, joissa henkilö on yhteyshenkilönä"

#. js-lingui-id: wJijgU
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Location"
msgstr "Sijainti"

#. js-lingui-id: y+q8Lv
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical Operator"
msgstr "Looginen operaattori"

#. js-lingui-id: 6kQXsS
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical operator for the filter group"
msgstr "Suodinryhmän looginen operaattori"

#. js-lingui-id: EnfPm2
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Meet Link"
msgstr "Meet-linkki"

#. js-lingui-id: xDAtGP
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message"
msgstr "Viesti"

#. js-lingui-id: g+QGD6
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel"
msgstr "Viestikanava"

#. js-lingui-id: DzU4a3
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel Association"
msgstr "Viestikanavan yhdistys"

#. js-lingui-id: wd/R++
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Id"
msgstr "Viestikanavan tunnus"

#. js-lingui-id: disipM
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Association"
msgstr "Viestikanavan viestin yhdistys"

#. js-lingui-id: ijQY3P
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Associations"
msgstr "Viestikanavan viestien yhdistykset"

#. js-lingui-id: k7LXPQ
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Message Channels"
msgstr "Viestikanavat"

#. js-lingui-id: /4uGJc
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Direction"
msgstr "Viestin suunta"

#. js-lingui-id: fBw8fQ
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message External Id"
msgstr "Viestin ulkoinen tunnus"

#. js-lingui-id: CStLnc
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Message Folder"
msgstr "Viestikansio"

#. js-lingui-id: p4ANpY
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Folders"
msgstr "Viestikansiot"

#. js-lingui-id: 6icznk
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Id"
msgstr "Viestin tunnus"

#. js-lingui-id: 9FJSpK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message id from the message header"
msgstr "Viestin tunnus viestin otsikosta"

#. js-lingui-id: yaC1Aq
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message id from the messaging provider"
msgstr "Viestinpalvelun tarjoajan viestin tunnus"

#. js-lingui-id: IUmVwu
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participant"
msgstr "Viestiosallistuja"

#. js-lingui-id: FhIFx7
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participants"
msgstr "Viestiosallistujat"

#. js-lingui-id: IC5A8V
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Synced with a Message Channel"
msgstr "Viesti synkronoitu viestikanavan kanssa"

#. js-lingui-id: de2nM/
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Thread"
msgstr "Viestiketju"

#. js-lingui-id: km1jgD
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message Thread Id"
msgstr "Viestiketjun tunnus"

#. js-lingui-id: RD0ecC
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Threads"
msgstr "Viestiketjut"

#. js-lingui-id: t7TeQU
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages"
msgstr "Viestit"

#. js-lingui-id: WoWdku
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Messages from the channel."
msgstr "Viestit kanavalta."

#. js-lingui-id: rYPBO7
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages from the thread."
msgstr "Viestit ketjusta."

#. js-lingui-id: XcKQrV
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider access token"
msgstr "Viestipalvelun tarjoajan käyttöoikeustoken"

#. js-lingui-id: 80EvIk
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider refresh token"
msgstr "Viestipalvelun tarjoajan uudistustoken"

#. js-lingui-id: SzCMUQ
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Missing required fields: at least one unique constraint have to be fully populated for '{connectFieldName}'."
msgstr ""

#. js-lingui-id: 6YtxFj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Name"
msgstr "Nimi"

#. js-lingui-id: RGz1nY
#: src/engine/metadata-modules/field-metadata/services/field-metadata-validation.service.ts
msgid "Name is not available, it may be duplicating another field's name."
msgstr ""

#. js-lingui-id: 0MobB1
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-long.utils.ts
msgid "Name is too long: it exceeds the 63 characters limit."
msgstr ""

#. js-lingui-id: EEVPOx
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Name of the favorite folder"
msgstr "Suosikkikansion nimi"

#. js-lingui-id: csMjko
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Name of the workflow run"
msgstr "Työnkulun suorituksen nimi"

#. js-lingui-id: hQQZse
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No event name provided in database event trigger"
msgstr ""

#. js-lingui-id: 9bu19u
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "No input provided in form step"
msgstr ""

#. js-lingui-id: MWAUHo
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No pattern provided in CUSTOM cron trigger"
msgstr ""

#. js-lingui-id: pOu4u/
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No schedule provided in cron trigger"
msgstr ""

#. js-lingui-id: cLUBlS
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: QwbSxD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No steps provided in workflow version"
msgstr ""

#. js-lingui-id: 6dP8sB
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No trigger type provided"
msgstr ""

#. js-lingui-id: KiJn9B
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Note"
msgstr "Muistiinpano"

#. js-lingui-id: GGlkb7
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note attachments"
msgstr "Muistiinpanojen liitteet"

#. js-lingui-id: Q1Rz+6
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note body"
msgstr "Muistiinpanon sisältö"

#. js-lingui-id: Yp057F
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note record position"
msgstr "Muistiinpanon tietueen sijainti"

#. js-lingui-id: spaO7l
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Target"
msgstr "Muistiinpanon kohde"

#. js-lingui-id: mkchvJ
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note targets"
msgstr "Muistiinpanon kohteet"

#. js-lingui-id: tD4BxK
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Targets"
msgstr "Muistiinpanon kohteet"

#. js-lingui-id: jDThel
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note title"
msgstr "Muistiinpanon otsikko"

#. js-lingui-id: 1DBGsz
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes"
msgstr "Muistiinpanot"

#. js-lingui-id: Ne73P/
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes tied to the {label}"
msgstr "Muistiinpanot liittyen {label}"

#. js-lingui-id: fXBE74
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Notes tied to the company"
msgstr "Yritykseen liittyvät muistiinpanot"

#. js-lingui-id: iQ5AH6
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Notes tied to the contact"
msgstr "Yhteyshenkilöön liittyvät muistiinpanot"

#. js-lingui-id: 0bi/Eh
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Notes tied to the opportunity"
msgstr "Mahdollisuuteen liittyvät muistiinpanot"

#. js-lingui-id: B2Y6QU
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget company"
msgstr "Kohdeyritys"

#. js-lingui-id: /mH0jo
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget note"
msgstr "Kohdemuistiinpano"

#. js-lingui-id: DTs4tO
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget opportunity"
msgstr "Kohdemahdollisuus"

#. js-lingui-id: gBwbnk
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget person"
msgstr "Kohdehenkilö"

#. js-lingui-id: ioJFzx
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Number of employees in the company"
msgstr "Yrityksen työntekijöiden lukumäärä"

#. js-lingui-id: ECRKYR
#: src/engine/metadata-modules/utils/validate-no-other-object-with-same-name-exists-or-throw.util.ts
msgid "Object already exists"
msgstr ""

#. js-lingui-id: hhe7Ce
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Object id"
#~ msgstr "Kohde-id"

#. js-lingui-id: dnPgTI
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object metadata id"
#~ msgstr "Kohdemetadatatunniste"

#. js-lingui-id: T/nPf5
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Object Metadata Id"
msgstr "Kohdemetadatatunniste"

#. js-lingui-id: afsWF6
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object name"
#~ msgstr "Kohteen nimi"

#. js-lingui-id: r/A6pA
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Open Record In"
msgstr "Avaa tietue"

#. js-lingui-id: Fzfj4N
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Operand"
msgstr "Operaattori"

#. js-lingui-id: FZg3wM
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operation"
msgstr "Toimenpide"

#. js-lingui-id: B1MDds
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operations"
msgstr "Toimenpiteet"

#. js-lingui-id: 4MyDFl
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities"
msgstr "Mahdollisuudet"

#. js-lingui-id: 5QgKbT
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities linked to the company."
msgstr "Yritykseen liittyvät mahdollisuudet."

#. js-lingui-id: SV/iis
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Opportunity"
msgstr "Mahdollisuus"

#. js-lingui-id: WnMlKn
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity amount"
msgstr "Mahdollisuuden summa"

#. js-lingui-id: aj3fnv
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity close date"
msgstr "Mahdollisuuden päättymispäivä"

#. js-lingui-id: 3NYczb
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity company"
msgstr "Mahdollisuuden yritys"

#. js-lingui-id: as45IN
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity point of contact"
msgstr "Mahdollisuuden vastuuhenkilö"

#. js-lingui-id: 5Nu7Uw
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity probability"
msgstr "Mahdollisuuden todennäköisyys"

#. js-lingui-id: Q1dzBp
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity record position"
msgstr "Mahdollisuuden tietueen sijainti"

#. js-lingui-id: 5ugYS3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity stage"
msgstr "Mahdollisuuden vaihe"

#. js-lingui-id: //eyK1
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label \"{sanitizedLabel}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: mTFS0z
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label exceeds 63 characters"
msgstr ""

#. js-lingui-id: +E9N4m
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label is required"
msgstr ""

#. js-lingui-id: nQqNzE
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value \"{sanitizedValue}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: 5m/1Hh
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value exceeds 63 characters"
msgstr ""

#. js-lingui-id: /wsRPd
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value is required"
msgstr ""

#. js-lingui-id: eLggyd
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Optional aggregate operation"
msgstr "Vaihtoehtoinen aggregaatio-operaatio"

#. js-lingui-id: kdClJ/
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Optional secret used to compute the HMAC signature for webhook payloads. This secret is shared between Twenty and the webhook consumer to authenticate webhook requests."
msgstr "Vaihtoehtoinen salaisuus, jota käytetään HMAC-allekirjoituksen laskemiseen webhook-payloadille. Tämä salaisuus jaetaan Twenty ja webhookin kuluttajan välillä webhook-pyyntöjen todentamiseen."

#. js-lingui-id: gh06VD
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Output"
msgstr "Tulos"

#. js-lingui-id: pDUbN1
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group"
msgstr "Yläsuodattimien ryhmä"

#. js-lingui-id: YKSmIP
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group Id"
msgstr "Yläsuodattimien ryhmän tunniste"

#. js-lingui-id: wT0H4O
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Password too weak"
msgstr ""

#. js-lingui-id: 1wdjme
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People"
msgstr "Henkilöt"

#. js-lingui-id: E1zc7W
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People linked to the company."
msgstr "Yritykseen liittyvät henkilöt."

#. js-lingui-id: OZdaTZ
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Person"
msgstr "Henkilö"

#. js-lingui-id: c3Qq6o
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Person record Position"
msgstr "Henkilötietueen asema"

#. js-lingui-id: zmwvG2
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phone"
msgstr "Puhelin"

#. js-lingui-id: m2ivgq
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phones"
msgstr "Puhelimet"

#. js-lingui-id: P04j61
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Point of Contact"
msgstr "Yhteyshenkilö"

#. js-lingui-id: p/78dY
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Position"
msgstr "Asema"

#. js-lingui-id: HH1bMC
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in the parent view filter group"
msgstr "Asema ylemmän näkymäsuodinryhmän sisällä"

#. js-lingui-id: CvOSME
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Position in the view filter group"
msgstr "Asema näkymäsuodinryhmässä"

#. js-lingui-id: h8PGuF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in view filter group"
msgstr "Asema näkymäsuodinryhmässä"

#. js-lingui-id: TTHIbk
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred color scheme"
msgstr "Suosikkiväriteema"

#. js-lingui-id: 5v4qYi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred language"
msgstr "Suosikki kieli"

#. js-lingui-id: WJIL29
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Probability"
msgstr "Todennäköisyys"

#. js-lingui-id: dtGxRz
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred calling code are conflicting"
msgstr ""

#. js-lingui-id: Vq/afT
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred country code are conflicting"
msgstr ""

#. js-lingui-id: HZ45ox
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided country code and calling code are conflicting"
msgstr ""

#. js-lingui-id: A4Ohxb
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided phone number is invalid {number}"
msgstr ""

#. js-lingui-id: CrfRPa
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "provider"
msgstr "tarjoaja"

#. js-lingui-id: YfbwOB
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Received At"
msgstr "Vastaanotettu"

#. js-lingui-id: 4Tvtbu
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Record id"
#~ msgstr "Tietueen id"

#. js-lingui-id: 9gXJw8
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Recurring Event ID"
msgstr "Toistuvan tapahtuman ID"

#. js-lingui-id: 2rvMKg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Refresh Token"
msgstr "Virkistystoken"

#. js-lingui-id: 2LpFdR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Related user email address"
msgstr "Liittyvän käyttäjän sähköpostiosoite"

#. js-lingui-id: g87L9j
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Relations"
msgstr "Suhteet"

#. js-lingui-id: pCsIQQ
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Request has expired, please try again."
msgstr ""

#. js-lingui-id: rnCndp
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Response Status"
msgstr "Vastauksen tila"

#. js-lingui-id: MCWKAU
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Revocation date"
msgstr "Kumotun päivämäärä"

#. js-lingui-id: GDvlUT
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Role"
msgstr "Rooli"

#. js-lingui-id: Tpm2G9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Runs"
msgstr "Käynnistä"

#. js-lingui-id: N/rFzD
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Scopes"
msgstr "Toiminta-alat"

#. js-lingui-id: PdVIJC
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Search vector"
msgstr "Hakuvektori"

#. js-lingui-id: 8VEDbV
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Secret"
msgstr "Salaisuus"

#. js-lingui-id: Tz0i8g
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Settings"
msgstr ""

#. js-lingui-id: Cj2Gtd
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Size"
msgstr "Koko"

#. js-lingui-id: 3PRxO3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Stage"
msgstr "Vaihe"

#. js-lingui-id: D3iCkb
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Start Date"
msgstr "Aloituspäivä"

#. js-lingui-id: RS0o7b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State"
msgstr ""

#. js-lingui-id: nY8GL/
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State of the workflow run"
msgstr ""

#. js-lingui-id: uAQUqI
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Status"
msgstr "Tila"

#. js-lingui-id: Db4W3/
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Statuses"
msgstr "Tilat"

#. js-lingui-id: S7DRR+
#: src/modules/workflow/common/utils/assert-workflow-statuses-not-set.ts
msgid "Statuses cannot be set manually."
msgstr ""

#. js-lingui-id: u+rv4L
#: src/modules/workflow/workflow-builder/workflow-step/workflow-version-step.workspace-service.ts
msgid "Step is not a form"
msgstr ""

#. js-lingui-id: YFMPch
#: src/engine/metadata-modules/utils/validate-metadata-name-start-with-lowercase-letter-and-contain-digits-nor-letters.utils.ts
msgid "String \"{name}\" is not valid: must start with lowercase letter and contain only alphanumeric letters"
msgstr ""

#. js-lingui-id: g4jxXh
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Sub field name"
msgstr ""

#. js-lingui-id: UJmAAK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Subject"
msgstr "Aihe"

#. js-lingui-id: oyJYg7
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor"
msgstr "Synkronointikursori"

#. js-lingui-id: awvBUx
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor. Used for syncing events from the calendar provider"
msgstr "Synkronointikursori. Käytetään tapahtumien synkronointiin kalenterintarjoajalta"

#. js-lingui-id: dNAbG6
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage"
msgstr "Synkronointivaihe"

#. js-lingui-id: aqNjQE
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage started at"
msgstr "Synkronointivaihe alkoi"

#. js-lingui-id: bRUdLR
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync status"
msgstr "Synkronoinnin tila"

#. js-lingui-id: 5y3Wbw
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Target object metadata not found for {connectFieldName}"
msgstr ""

#. js-lingui-id: 4SHJe4
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Target Url"
msgstr "Kohde-URL"

#. js-lingui-id: Q3P/4s
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Task"
msgstr "Tehtävä"

#. js-lingui-id: kS+Ym6
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task assignee"
msgstr "Tehtävän vastuullinen"

#. js-lingui-id: 7fYQ6E
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task attachments"
msgstr "Tehtävän liitteet"

#. js-lingui-id: X8fs74
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task body"
msgstr "Tehtävän sisältö"

#. js-lingui-id: EPxYHS
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task due date"
msgstr "Tehtävän määräaika"

#. js-lingui-id: fUw1j+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task record position"
msgstr "Tehtävän tietueen sijainti"

#. js-lingui-id: I6+0ph
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task status"
msgstr "Tehtävän tila"

#. js-lingui-id: WSiiWf
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Target"
msgstr "Tehtävään kohdistuva kohde"

#. js-lingui-id: khGQLP
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task targets"
msgstr "Tehtäviin kohdistuvat kohteet"

#. js-lingui-id: 836FiO
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Targets"
msgstr "Tehtäväkohteet"

#. js-lingui-id: R+s8S+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task title"
msgstr "Tehtävän otsikko"

#. js-lingui-id: GtycJ/
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks"
msgstr "Tehtävät"

#. js-lingui-id: HlDeG3
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Tasks assigned to the workspace member"
msgstr "Työtilan jäsenelle määritetyt tehtävät"

#. js-lingui-id: Ca/n4T
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks tied to the {label}"
msgstr "Tehtävät liittyvät {label}-kohteeseen"

#. js-lingui-id: M4rBti
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Tasks tied to the company"
msgstr "Yritykseen liittyvät tehtävät"

#. js-lingui-id: /VaiDW
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Tasks tied to the contact"
msgstr "Yhteyshenkilöön liittyvät tehtävät"

#. js-lingui-id: 1TfX9U
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Tasks tied to the opportunity"
msgstr "Mahdollisuuteen liittyvät tehtävät"

#. js-lingui-id: pP0Dt9
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget company"
msgstr "Tehtävään kohdistuva yritys"

#. js-lingui-id: UJ2aPi
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget opportunity"
msgstr "Tehtävään kohdistuva mahdollisuus"

#. js-lingui-id: I1MiSs
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget person"
msgstr "Tehtävään kohdistuva henkilö"

#. js-lingui-id: pciKLT
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget task"
msgstr "Tehtävään kohdistuva tehtävä"

#. js-lingui-id: xeiujy
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Text"
msgstr "Teksti"

#. js-lingui-id: 6PJbR2
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account handle (email, username, phone number, etc.)"
msgstr "Käyttäjätunnus (sähköposti, käyttäjätunnus, puhelinnumero jne.)"

#. js-lingui-id: zUXOAB
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account provider"
msgstr "Tilin tarjoaja"

#. js-lingui-id: qnNFrW
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Linkedin account"
msgstr "Yrityksen Linkedin-tili"

#. js-lingui-id: N31Pso
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company name"
msgstr "Yrityksen nimi"

#. js-lingui-id: BHFCqB
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Twitter/X account"
msgstr "Yrityksen Twitter/X -tili"

#. js-lingui-id: OBmU0K
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company website URL. We use this url to fetch the company icon"
msgstr "Yrityksen verkkosivuston URL. Käytämme tätä URL:ää yrityksen kuvakkeen hakuun"

#. js-lingui-id: zGBDEH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "The creator of the record"
msgstr "Tietueen luoja"

#. js-lingui-id: bMyVOx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The current statuses of the workflow versions"
msgstr "Työnkulkujen versioiden nykyiset tilat"

#. js-lingui-id: 0bo4Q0
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "The date the message was received"
msgstr "Viestin vastaanottopäivä"

#. js-lingui-id: 8h4mhq
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "The executor of the workflow"
msgstr "Työnkulun suorittaja"

#. js-lingui-id: W3raza
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "The folder this favorite belongs to"
msgstr "Kansio, johon tämä suosikki kuuluu"

#. js-lingui-id: EJUSll
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "The initial version of a workflow can not be deleted"
msgstr ""

#. js-lingui-id: DbWmKZ
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "The opportunity name"
msgstr "Mahdollisuuden nimi"

#. js-lingui-id: 5N6QtE
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger settings"
msgstr ""

#. js-lingui-id: SpKbfT
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger type"
msgstr ""

#. js-lingui-id: 7mPrpl
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "The workflow event listener name"
#~ msgstr "Työnkulun tapahtumakuuntelijan nimi"

#. js-lingui-id: od0omS
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow last published version id"
msgstr "Työnkulun viimeksi julkaistun version tunnus"

#. js-lingui-id: /EdWx6
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow name"
msgstr "Työnkulun nimi"

#. js-lingui-id: EhAsND
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version name"
msgstr "Työnkulun version nimi"

#. js-lingui-id: dhx13p
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version status"
msgstr "Työnkulun version tila"

#. js-lingui-id: yIUmAs
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-reserved-keyword.ts
#: src/engine/metadata-modules/utils/validate-field-name-availability.utils.ts
msgid "This name is not available."
msgstr ""

#. js-lingui-id: Zl0BJl
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread External Id"
msgstr "Viestiketjun ulkoinen tunnus"

#. js-lingui-id: RSSbWN
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread id from the messaging provider"
msgstr "Viestipalvelun ketjun tunnus"

#. js-lingui-id: sS8v5K
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Throttle Failure Count"
msgstr "Rajoitusvirheiden määrä"

#. js-lingui-id: n9nSNJ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time format"
msgstr "Aikamuoto"

#. js-lingui-id: Mz2JN2
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time zone"
msgstr "Aikavyöhyke"

#. js-lingui-id: az1boY
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities"
msgstr "Aikajanatapahtumat"

#. js-lingui-id: fqKMpF
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Timeline Activities linked to the company"
msgstr "Yritykseen liitetyt aikajanatapahtumat"

#. js-lingui-id: 4/UzU5
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Timeline Activities linked to the note."
msgstr "Huomioon liitetyt aikajanatapahtumat."

#. js-lingui-id: p6feIz
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Timeline Activities linked to the opportunity."
msgstr "Mahdollisuuteen liitetyt aikajanatapahtumat."

#. js-lingui-id: yvPwuF
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Timeline activities linked to the run"
msgstr "Suoritukseen liitetyt aikajanatapahtumat"

#. js-lingui-id: q96UvB
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Timeline Activities linked to the task."
msgstr "Tehtävään liitetyt aikajanatapahtumat."

#. js-lingui-id: N9HMa/
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Timeline activities linked to the version"
msgstr "Versioon liitetyt aikajanatapahtumat"

#. js-lingui-id: B1CYKX
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Timeline activities linked to the workflow"
msgstr "Työnkulkuun liitetyt aikajanatapahtumat"

#. js-lingui-id: G0UmtQ
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities tied to the {label}"
msgstr "{label} -tunnisteeseen sidotut aikajanatapahtumat"

#. js-lingui-id: K/kU4E
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Timeline Activity"
msgstr "Aikajanatapahtuma"

#. js-lingui-id: MHrjPM
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Title"
msgstr "Otsikko"

#. js-lingui-id: +zy2Nq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Type"
msgstr "Tyyppi"

#. js-lingui-id: EBZdX5
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Unsupported cron schedule type"
msgstr ""

#. js-lingui-id: 0gY5lO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Update DateTime"
msgstr "Päivityksen päivämäärä ja aika"

#. js-lingui-id: l9dlVi
#: src/engine/core-modules/auth/strategies/jwt.auth.strategy.ts
msgid "User does not have access to this workspace"
msgstr ""

#. js-lingui-id: YFciqL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Email"
msgstr "Käyttäjän sähköposti"

#. js-lingui-id: d1BTW8
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Id"
msgstr "Käyttäjän tunnus"

#. js-lingui-id: B43Kks
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "User is not part of the workspace"
msgstr ""

#. js-lingui-id: 4juE7s
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User time zone"
msgstr "Käyttäjän aikavyöhyke"

#. js-lingui-id: 43zCwQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred date format"
msgstr "Käyttäjän suosima päivämäärämuoto"

#. js-lingui-id: kJuoKm
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred time format"
msgstr "Käyttäjän suosima aikamuoto"

#. js-lingui-id: wMHvYH
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Value"
msgstr "Arvo"

#. js-lingui-id: 7aatUy
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version status"
msgstr "Version tila"

#. js-lingui-id: CdQeU7
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version steps"
msgstr "Version vaiheet"

#. js-lingui-id: PGMPIi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version trigger"
msgstr "Version laukaisin"

#. js-lingui-id: IYNSdp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Versions"
msgstr "Versiot"

#. js-lingui-id: jpctdh
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "View"
msgstr "Näkymä"

#. js-lingui-id: cZPDyy
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field"
msgstr "Näkymän kenttä"

#. js-lingui-id: 6jpoH4
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field position"
msgstr "Näkymän kentän sijainti"

#. js-lingui-id: Ju6gri
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field related view"
msgstr "Katso kenttään liittyvä näkymä"

#. js-lingui-id: tvTr2y
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field size"
msgstr "Kentän koko"

#. js-lingui-id: BkII8t
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field target field"
msgstr "Kohdekenttä"

#. js-lingui-id: Gd/LzL
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field visibility"
msgstr "Kentän näkyvyys"

#. js-lingui-id: GUFYyq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Fields"
msgstr "Näkymäkentät"

#. js-lingui-id: JRtI7Y
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter"
msgstr "Näkymän suodatin"

#. js-lingui-id: L2gQ5q
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Display Value"
msgstr "Suotimen näyttöarvo"

#. js-lingui-id: l9/6pD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Group"
msgstr "Suodinryhmä"

#. js-lingui-id: Mbosm8
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Group Id"
msgstr "Suodinryhmän tunnus"

#. js-lingui-id: /aP3iG
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Groups"
msgstr "Suodinryhmät"

#. js-lingui-id: 4MG8+B
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter operand"
msgstr "Suotimen operandi"

#. js-lingui-id: OrUkUF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter related view"
msgstr "Suotimeen liittyvä näkymä"

#. js-lingui-id: TyXOtD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter target field"
msgstr "Suotimen kohdekenttä"

#. js-lingui-id: 3KzkxN
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter value"
msgstr "Suotimen arvo"

#. js-lingui-id: vj5JsR
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filters"
msgstr "Näkymän suotimet"

#. js-lingui-id: ziEP12
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group"
msgstr "Näkymäryhmä"

#. js-lingui-id: uQ3c2q
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group related view"
msgstr "Ryhmään liittyvä näkymä"

#. js-lingui-id: EFlLpQ
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group target field"
msgstr "Ryhmän kohdekenttä"

#. js-lingui-id: b1Vc+l
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group visibility"
msgstr "Ryhmän näkyvyys"

#. js-lingui-id: V4nZs/
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Groups"
msgstr "Näkymäryhmät"

#. js-lingui-id: qXlovu
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View icon"
msgstr "Näkymän ikoni"

#. js-lingui-id: cd/+ZD
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View Kanban column field"
msgstr "Kanban-näkymän sarakkeen kenttä"

#. js-lingui-id: vdfZ+A
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View key"
msgstr "Näkymän avain"

#. js-lingui-id: oOljSE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View name"
msgstr "Näkymän nimi"

#. js-lingui-id: rhO8zp
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View position"
msgstr "Näkymän sijoitus"

#. js-lingui-id: EUjpwJ
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort"
msgstr "Näkymän lajittelu"

#. js-lingui-id: 6ZUale
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort direction"
msgstr "Lajittelun suunta"

#. js-lingui-id: /rCPqN
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort related view"
msgstr "Lajitteluun liittyvä näkymä"

#. js-lingui-id: +du2wy
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort target field"
msgstr "Lajittelun kohdekenttä"

#. js-lingui-id: UsdY3K
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sorts"
msgstr "Näkymän lajittelut"

#. js-lingui-id: clWwIZ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View target object"
msgstr "Kohteen näkymä"

#. js-lingui-id: bJAIqT
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View type"
msgstr "Näkymän tyyppi"

#. js-lingui-id: 1I6UoR
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Views"
msgstr "Näkymät"

#. js-lingui-id: 2q/Q7x
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Visibility"
msgstr "Näkyvyys"

#. js-lingui-id: oh8+os
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Visible"
msgstr "Näkyy"

#. js-lingui-id: TRDppN
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook"
msgstr "Web-koukku"

#. js-lingui-id: fyB2Wp
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operation"
msgstr "Webhook toiminto"

#. js-lingui-id: gONLmX
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operations"
msgstr "Webhook toiminnot"

#. js-lingui-id: cPoSTF
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook target url"
msgstr "Web-koukun kohde-URL"

#. js-lingui-id: v1kQyJ
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhooks"
msgstr "Web-koukut"

#. js-lingui-id: TVkCrJ
#: src/engine/core-modules/email-verification/services/email-verification.service.ts
msgid "Welcome to Twenty: Please Confirm Your Email"
msgstr "Tervetuloa Twentyyn: Vahvista sähköpostisi"

#. js-lingui-id: bLt/0J
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Workflow"
msgstr "Työnkulku"

#. js-lingui-id: hdtWQn
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow automated triggers linked to the workflow."
msgstr ""

#. js-lingui-id: E03XpH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Workflow event listeners linked to the workflow."
#~ msgstr "Työnkulun tapahtumakuuntelijat liitetty työnkulkuun."

#. js-lingui-id: vwSkSW
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow linked to the run."
msgstr "Työnkulku liitetty suoritukseen."

#. js-lingui-id: y9tnFx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow record position"
msgstr "Työnkulun tietueen sijainti"

#. js-lingui-id: 5vIcqC
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Workflow Run"
msgstr "Työnkulun suoritus"

#. js-lingui-id: 3Iz+qz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run ended at"
msgstr "Työnkulun suoritus päättynyt"

#. js-lingui-id: IUaK6s
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run position"
msgstr "Työnkulun suorituksen sijainti"

#. js-lingui-id: zaN7tH
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run started at"
msgstr "Työnkulun suoritus alkanut"

#. js-lingui-id: 1TU2A8
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run status"
msgstr "Työnkulun suorituksen tila"

#. js-lingui-id: u6DF/V
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow Runs"
msgstr "Työnkulkujen suoritukset"

#. js-lingui-id: 9nOy7k
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow runs linked to the version."
msgstr "Versioon liitetyt työnkulkujen suoritukset."

#. js-lingui-id: c37F3j
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow runs linked to the workflow."
msgstr "Työnkulkuun liitetyt työnkulkujen suoritukset."

#. js-lingui-id: lTXctu
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version"
msgstr "Työnkulun versio"

#. js-lingui-id: +wYPET
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Version"
msgstr "Työnkulun versio"

#. js-lingui-id: n4/IOE
#: src/modules/workflow/common/utils/assert-workflow-version-has-steps.ts
msgid "Workflow version does not contain at least one step"
msgstr ""

#. js-lingui-id: k4DPlQ
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/common/utils/assert-workflow-version-trigger-is-defined.util.ts
msgid "Workflow version does not contain trigger"
msgstr ""

#. js-lingui-id: bqFL4g
#: src/modules/workflow/common/utils/assert-workflow-version-is-draft.util.ts
msgid "Workflow version is not in draft status"
msgstr ""

#. js-lingui-id: CocTJJ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version linked to the run."
msgstr "Työnkulun versio liitetty suoritukseen."

#. js-lingui-id: 9l+pJT
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow version position"
msgstr "Työnkulun version sijainti"

#. js-lingui-id: OCyhkn
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Versions"
msgstr "Työnkulun versiot"

#. js-lingui-id: 018fP9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow versions linked to the workflow."
msgstr "Työnkulkuun liitetyt versiot."

#. js-lingui-id: s7p+TL
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger"
msgstr ""

#. js-lingui-id: 96bCgZ
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger workflow"
msgstr ""

#. js-lingui-id: 5z92T9
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTriggers"
msgstr ""

#. js-lingui-id: ENOy6I
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener"
#~ msgstr "TyönkulunTapahtumakuuntelija"

#. js-lingui-id: HN90FO
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener workflow"
#~ msgstr "TyönkulunTapahtumakuuntelijan työnkulku"

#. js-lingui-id: 3JA9se
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListeners"
#~ msgstr "TyönkulunTapahtumakuuntelijat"

#. js-lingui-id: woYYQq
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflows"
msgstr "Työnkulut"

#. js-lingui-id: urCUgs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "WorkflowVersion"
msgstr "TyönkulunVersio"

#. js-lingui-id: b4kire
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "WorkflowVersion workflow"
msgstr "TyönkulunVersion työnkulku"

#. js-lingui-id: rklt6M
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Workspace is not ready to welcome new members"
msgstr ""

#. js-lingui-id: CbGxon
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Workspace member"
msgstr "Työtilan jäsen"

#. js-lingui-id: qc38qR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Workspace Member"
msgstr "Työtilan jäsen"

#. js-lingui-id: R1S9pO
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member avatar"
msgstr "Työtilan jäsenen avatar"

#. js-lingui-id: 5VCX7o
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member name"
msgstr "Työtilan jäsenen nimi"

#. js-lingui-id: NiUpuN
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member position"
msgstr "Työtilan jäsenen asema"

#. js-lingui-id: YCAEr+
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace Members"
msgstr "Työtilan jäsenet"

#. js-lingui-id: EtzFC0
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "WorkspaceMember"
msgstr "TyötilanJäsen"

#. js-lingui-id: fV2Bu6
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Wrong password"
msgstr ""

#. js-lingui-id: 0gv+T2
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "X"
msgstr ""

#. js-lingui-id: z+7x/s
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "You must be authenticated to perform this action."
msgstr ""

#. js-lingui-id: bTyBrW
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Your Password Has Been Successfully Changed"
msgstr "Salasanasi on vaihdettu onnistuneesti"

#. js-lingui-id: vPccnr
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Your team member responsible for managing the company account"
msgstr "Tiimin jäsen vastuussa yritystilin hallinnasta"

#. js-lingui-id: ej/tZF
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "Your workspace has been updated with a new data model. Please refresh the page."
msgstr ""
