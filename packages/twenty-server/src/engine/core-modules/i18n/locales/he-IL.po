msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-29 18:14+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: he\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Hebrew\n"
"Plural-Forms: nplurals=4; plural=n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3;\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: he\n"
"X-Crowdin-File: /packages/twenty-server/src/engine/core-modules/i18n/locales/en.po\n"
"X-Crowdin-File-ID: 31\n"

#. js-lingui-id: Qyrd7v
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "(System) View Fields"
msgstr "(מערכת) שדות תצוגה"

#. js-lingui-id: 9Y3fTB
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "(System) View Filter Groups"
msgstr "(מערכת) קבוצות סינון לתצוגה"

#. js-lingui-id: TB2jLV
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "(System) View Filters"
msgstr "(מערכת) מסנני תצוגה"

#. js-lingui-id: Y7M7Ro
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "(System) View Groups"
msgstr "(מערכת) קבוצות תצוגה"

#. js-lingui-id: 9vliLw
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "(System) View Sorts"
msgstr "(מערכת) מיוני תצוגה"

#. js-lingui-id: 5B59WE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "(System) Views"
msgstr "(מערכת) תצוגות"

#. js-lingui-id: Q0ISF1
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "{fieldName} and {fieldName}Id cannot be both provided."
msgstr ""

#. js-lingui-id: 8haj+G
#: src/engine/metadata-modules/utils/validate-metadata-name-is-camel-case.utils.ts
msgid "{name} should be in camelCase"
msgstr ""

#. js-lingui-id: kZR6+h
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "A company"
msgstr "חברה"

#. js-lingui-id: +aeifv
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "A connected account"
msgstr "חשבון מחובר"

#. js-lingui-id: HCoswz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "A favorite that can be accessed from the left menu"
msgstr "מועדף שניתן לגשת אליו מהתפריט השמאלי"

#. js-lingui-id: 6w8bHl
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "A Folder of favorites"
msgstr "תיקיית מועדפים"

#. js-lingui-id: sSGYmf
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "A group of related messages (e.g. email thread, chat thread)"
msgstr "קבוצה של הודעות קשורות (לדוגמא, שרשור אימייל, שרשור צ'אט)"

#. js-lingui-id: vZj1Xc
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "A message sent or received through a messaging channel (email, chat, etc.)"
msgstr "הודעה שנשלחה או נתקבלה דרך ערוץ ההודעות (אימייל, צ'אט, וכו')"

#. js-lingui-id: bufuBA
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "A note"
msgstr "הערה"

#. js-lingui-id: 6kUkZW
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "A note target"
msgstr "מטרת הערה"

#. js-lingui-id: Io42ej
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "A person"
msgstr "אדם"

#. js-lingui-id: Q2De+v
#: src/engine/metadata-modules/role/role.service.ts
msgid "A role with this label already exists."
msgstr ""

#. js-lingui-id: mkFXEH
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "A task"
msgstr "משימה"

#. js-lingui-id: hk2NzW
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "A task target"
msgstr "מטרת משימה"

#. js-lingui-id: HTSJFW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "A webhook"
msgstr "Webhook"

#. js-lingui-id: ZIN9Ga
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "A workflow"
msgstr "Workflow"

#. js-lingui-id: YwBCp8
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "A workflow automated trigger"
msgstr ""

#. js-lingui-id: juBVjt
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "A workflow event listener"
#~ msgstr "מאזין לאירועי Workflow"

#. js-lingui-id: 1+xDbI
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "A workflow run"
msgstr "הפעלת Workflow"

#. js-lingui-id: N0g7rp
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "A workflow version"
msgstr "גרסת Workflow"

#. js-lingui-id: HpZ/I5
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "A workspace member"
msgstr "חבר בסביבת עבודה"

#. js-lingui-id: GDKKxT
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Access Token"
msgstr "אסימון גישה"

#. js-lingui-id: pd81Qb
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Account Owner"
msgstr "בעלים של חשבון"

#. js-lingui-id: loqL/f
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account owner for companies"
msgstr "בעלים של חשבון עבור חברות"

#. js-lingui-id: HZosRi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Account Owner For Companies"
msgstr "בעלים של חשבון עבור חברות"

#. js-lingui-id: 48AxkT
#: src/engine/workspace-manager/workspace-cleaner/services/cleaner.workspace-service.ts
msgid "Action needed to prevent workspace deletion"
msgstr "יש צורך בפעולה כדי למנוע מחיקת סביבת העבודה"

#. js-lingui-id: j0DfGR
#: src/engine/core-modules/auth/services/reset-password.service.ts
msgid "Action Needed to Reset Password"
msgstr "יש צורך בפעולה כדי לאפס סיסמה"

#. js-lingui-id: Du6bPw
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address"
msgstr "כתובת"

#. js-lingui-id: JiOJxf
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address (deprecated) "
msgstr "כתובת (מיושן) "

#. js-lingui-id: Knl3c9
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company"
msgstr "כתובת של החברה"

#. js-lingui-id: zJhwjv
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Address of the company - deprecated in favor of new address field"
msgstr "כתובת של החברה - מיושן לטובת שדה כתובת חדש"

#. js-lingui-id: ZfVqbP
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Aggregate operation"
msgstr "פעולת אגרגציה"

#. js-lingui-id: W58PBh
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Aggregated / filtered event to be displayed on the timeline"
msgstr "אירוע מצטבר / מסונן שיוצג בציר הזמן"

#. js-lingui-id: hehnjM
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Amount"
msgstr "סכום"

#. js-lingui-id: qeHcQj
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "An API key"
msgstr "מפתח API"

#. js-lingui-id: MjyFvC
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "An attachment"
msgstr "קובץ מצורף"

#. js-lingui-id: +bL++X
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "An audit log of actions performed in the system"
#~ msgstr "יומן רישום של פעולות שבוצעו במערכת"

#. js-lingui-id: XyOToQ
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/utils/generate-graphql-error-from-error.util.ts
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "An error occurred."
msgstr ""

#. js-lingui-id: muVHgL
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "An event related to user behavior"
#~ msgstr "אירוע שקשור להתנהגות משתמש"

#. js-lingui-id: bZq8rL
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "An opportunity"
msgstr "הזדמנות"

#. js-lingui-id: JKWicb
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Annual Recurring Revenue: The actual or estimated annual revenue of the company"
msgstr "הכנסה חוזרת שנתית: ההכנסה השנתית הממשית או המשוערת של החברה"

#. js-lingui-id: yRnk5W
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Key"
msgstr "מפתח API"

#. js-lingui-id: FfSJ1Y
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "API Keys"
msgstr "מפתחות API"

#. js-lingui-id: puNs/l
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey expiration date"
msgstr "תאריך תפוגת מפתח ה-API"

#. js-lingui-id: YHiNxr
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey name"
msgstr "שם מפתח ה-API"

#. js-lingui-id: xrndTt
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "ApiKey revocation date"
msgstr "תאריך ביטול מפתח ה-API"

#. js-lingui-id: DGW5iN
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain does not match email domain"
msgstr ""

#. js-lingui-id: BKvuAq
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
#: src/engine/core-modules/approved-access-domain/services/approved-access-domain.service.ts
msgid "Approved access domain has already been validated"
msgstr ""

#. js-lingui-id: 3EiOLz
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ARR"
msgstr "הכנסה מחזורית"

#. js-lingui-id: Ek7xGj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Assigned tasks"
msgstr "משימות שהוקצו"

#. js-lingui-id: ojKCLU
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Assignee"
msgstr "המוקצה"

#. js-lingui-id: Max2GU
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Associated User Id"
msgstr "מזהה המשתמש המקושר"

#. js-lingui-id: UY1vmE
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment"
msgstr "קובץ מצורף"

#. js-lingui-id: JAefBH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment author"
msgstr "מחבר הקובץ המצורף"

#. js-lingui-id: bIaesZ
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment company"
msgstr "חברת הקובץ המצורף"

#. js-lingui-id: gfGYcl
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment full path"
msgstr "נתיב מלא של הקובץ המצורף"

#. js-lingui-id: wjocwa
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment name"
msgstr "שם הקובץ המצורף"

#. js-lingui-id: FWlOXr
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment note"
msgstr "הערה לקובץ המצורף"

#. js-lingui-id: YASWpH
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment opportunity"
msgstr "הזדמנות הקובץ המצורף"

#. js-lingui-id: P38yED
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment person"
msgstr "אדם הקובץ המצורף"

#. js-lingui-id: Tx1DxS
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment task"
msgstr "משימת הקובץ המצורף"

#. js-lingui-id: 4qqxMt
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Attachment type"
msgstr "סוג הקובץ המצורף"

#. js-lingui-id: w/Sphq
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments"
msgstr "קבצים מצורפים"

#. js-lingui-id: 2tQOVc
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Attachments created by the workspace member"
msgstr "קבצים מצורפים שנוצרו על ידי חבר במרחב העבודה"

#. js-lingui-id: iVnA89
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Attachments linked to the company"
msgstr "קבצים מצורפים המקושרים לחברה"

#. js-lingui-id: MuTXtT
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Attachments linked to the contact."
msgstr "קבצים מצורפים המקושרים לאיש קשר."

#. js-lingui-id: kw0mLu
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Attachments linked to the opportunity"
msgstr "קבצים מצורפים המקושרים להזדמנות"

#. js-lingui-id: fCbqr7
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Attachments tied to the {label}"
msgstr "קבצים מצורפים אשר קשורים ל{label}"

#. js-lingui-id: ilRCh1
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Log"
#~ msgstr "יומן ביקורת"

#. js-lingui-id: EPEFrH
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Audit Logs"
#~ msgstr "יומני ביקורת"

#. js-lingui-id: RqCC/0
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#~ msgid "Audit Logs linked to the workspace member"
#~ msgstr "יומני ביקורת המקושרים לחבר מרחב העבודה"

#. js-lingui-id: cNBqH+
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Auth failed at"
msgstr "אימות נכשל ב"

#. js-lingui-id: LB4qX/
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Authentication is not enabled with this provider."
msgstr ""

#. js-lingui-id: VbeIOx
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Author"
msgstr "מחבר"

#. js-lingui-id: XJONK6
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Authored attachments"
msgstr "קבצים מצורפים שנכתבו"

#. js-lingui-id: cvSznK
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Automated Trigger Type"
msgstr ""

#. js-lingui-id: 2mtBXs
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Automated Triggers"
msgstr ""

#. js-lingui-id: RpExX0
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Automatically create People records when receiving or sending emails"
msgstr "יצירה אוטומטית של רשומות אנשים בעת קבלה או שליחת אימיילים"

#. js-lingui-id: lXxdId
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Automatically create records for people you participated with in an event."
msgstr "יצירה אוטומטית של רשומות עבור אנשים שהשתתפת איתם באירוע."

#. js-lingui-id: kfcRb0
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Avatar"
msgstr "אווטאר"

#. js-lingui-id: S/mJUR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Avatar Url"
msgstr "כתובת אתר של אווטאר"

#. js-lingui-id: 20B9kW
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Event"
#~ msgstr "אירוע התנהגותי"

#. js-lingui-id: Jeh/Q/
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Behavioral Events"
#~ msgstr "אירועים התנהגותיים"

#. js-lingui-id: K1172m
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklist"
msgstr "רשימת חסימה"

#. js-lingui-id: Tv2hxv
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Blocklisted handles"
msgstr "מזהים שנחסמו"

#. js-lingui-id: L5JhJe
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Blocklists"
msgstr "רשימות חסימה"

#. js-lingui-id: bGQplw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body"
msgstr "גוף"

#. js-lingui-id: Nl8eMw
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Body (deprecated)"
msgstr "גוף (מיושן)"

#. js-lingui-id: 8mVqF7
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Cached record name"
msgstr "שם הרישום המטמון"

#. js-lingui-id: Nh6GTX
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channel"
msgstr "ערוץ הלוח שנה"

#. js-lingui-id: jfNQ0m
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Association"
msgstr "האגוד של ערוץ הלוח שנה"

#. js-lingui-id: kYNT3F
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Calendar Channel Event Associations"
msgstr "האגודות של אירועי ערוץ הלוח שנה"

#. js-lingui-id: Znix/S
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Calendar Channels"
msgstr "\\"

#. js-lingui-id: bRk+FR
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar event"
msgstr "\\"

#. js-lingui-id: N2kMfO
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participant"
msgstr "\\"

#. js-lingui-id: AWDqkQ
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Calendar event participants"
msgstr "\\"

#. js-lingui-id: ZI2UyM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Calendar Event Participants"
msgstr "\\"

#. js-lingui-id: X9A2xC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Calendar events"
msgstr "\\"

#. js-lingui-id: vxbVwc
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Cannot activate non-draft or non-last-published version"
msgstr ""

#. js-lingui-id: LIPsWu
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create multiple draft versions for the same workflow"
msgstr ""

#. js-lingui-id: RggZr4
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot create workflow version with status other than draft"
msgstr ""

#. js-lingui-id: AQyr9X
#: src/engine/metadata-modules/field-metadata/services/field-metadata.service.ts
msgid "Cannot delete, please update the label identifier field first"
msgstr ""

#. js-lingui-id: FrNWt0
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot disable non-active workflow version"
msgstr ""

#. js-lingui-id: IDMgr/
#: src/modules/workflow/workflow-trigger/workspace-services/workflow-trigger.workspace-service.ts
msgid "Cannot have more than one active workflow version"
msgstr ""

#. js-lingui-id: 4DKo3U
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "Cannot update workflow version status manually"
msgstr ""

#. js-lingui-id: 4IVK41
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Channel ID"
msgstr "\\"

#. js-lingui-id: Ubg/B+
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Channel Type"
msgstr "\\"

#. js-lingui-id: 3wV73y
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "City"
msgstr "\\"

#. js-lingui-id: NRF7pg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Close date"
msgstr "\\"

#. js-lingui-id: 96YB1a
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Color Scheme"
msgstr "\\"

#. js-lingui-id: 07OSD1
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Compact View"
msgstr "\\"

#. js-lingui-id: s2QZS6
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Companies"
msgstr "\\"

#. js-lingui-id: 7i8j3G
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Company"
msgstr "\\"

#. js-lingui-id: yA1de7
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Company record position"
msgstr "\\"

#. js-lingui-id: AgktVC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Conference Solution"
msgstr "\\"

#. js-lingui-id: 1fFL4B
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Connect is not allowed for {connectFieldName} on {objectMetadataNameSingular}"
msgstr ""

#. js-lingui-id: PQ1Dw2
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Connected Account"
msgstr "\\"

#. js-lingui-id: 9TzudL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Connected accounts"
msgstr "\\"

#. js-lingui-id: AMDUqA
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Connected Accounts"
msgstr "\\"

#. js-lingui-id: bA6O5B
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Contact auto creation policy"
msgstr "\\"

#. js-lingui-id: RnsmQs
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s avatar"
msgstr "\\"

#. js-lingui-id: pL+pqi
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s city"
msgstr "\\"

#. js-lingui-id: VnWMlz
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s company"
msgstr "\\"

#. js-lingui-id: ITlFIB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Emails"
msgstr "\\"

#. js-lingui-id: GuRtLY
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s job title"
msgstr "\\"

#. js-lingui-id: QrCvRQ
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s Linkedin account"
msgstr "\\"

#. js-lingui-id: 6xPSVt
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s name"
msgstr "\\"

#. js-lingui-id: Y37CZ4
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone number"
msgstr "\\"

#. js-lingui-id: zsW3gg
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s phone numbers"
msgstr "\\"

#. js-lingui-id: uuZ00G
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Contact’s X/Twitter account"
msgstr "\\"

#. js-lingui-id: M73whl
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Context"
msgstr "הקשר"

#. js-lingui-id: NCIYDF
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Created by"
msgstr "\\"

#. js-lingui-id: wPvFAD
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Creation date"
msgstr "\\"

#. js-lingui-id: CJXWmO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Creation DateTime"
msgstr "\\"

#. js-lingui-id: umhXiy
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Cron pattern '{pattern}' is invalid"
msgstr ""

#. js-lingui-id: Mikszu
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Custom Connection Parameters"
msgstr ""

#. js-lingui-id: Lhd0oQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Date format"
msgstr "\\"

#. js-lingui-id: 1lL5Iu
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Date when the record was deleted"
msgstr "\\"

#. js-lingui-id: QN9ahV
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Deleted at"
msgstr "\\"

#. js-lingui-id: U1bSSI
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Describes if the view is in compact mode"
msgstr "מתאר אם התצוגה נמצאת במצב קומפקטי"

#. js-lingui-id: Nu4oKW
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Description"
msgstr "תיאור"

#. js-lingui-id: MRB7nI
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Direction"
msgstr "כיוון"

#. js-lingui-id: 0gS7M5
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Display Name"
msgstr "שם תצוגה"

#. js-lingui-id: y7HJTU
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Display the records in a side panel or in a record page"
msgstr "הצג את הרשומות בפאנל צד או בדף רשומה"

#. js-lingui-id: 0H/D9K
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Display Value"
msgstr "ערך תצוגה"

#. js-lingui-id: wMncXq
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Domain Name"
msgstr "שם דומיין"

#. js-lingui-id: YOowcq
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Due Date"
msgstr "תאריך יעד"

#. js-lingui-id: BBXuRb
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email already verified."
msgstr ""

#. js-lingui-id: xw6iqb
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "Email is not verified."
msgstr ""

#. js-lingui-id: ZsZeV2
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Email is required"
msgstr ""

#. js-lingui-id: y8LSMh
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Email verification not required."
msgstr ""

#. js-lingui-id: BXEcos
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Emails"
msgstr "אימיילים"

#. js-lingui-id: gqv5ZL
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Employees"
msgstr "עובדים"

#. js-lingui-id: VFv2ZC
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "End Date"
msgstr "תאריך סיום"

#. js-lingui-id: k//6Xs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event company"
msgstr "חברה באירוע"

#. js-lingui-id: FJ7QI4
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event context"
#~ msgstr "הקשר אירוע"

#. js-lingui-id: kJDmsI
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event details"
msgstr "פרטי אירוע"

#. js-lingui-id: 0JhmlM
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event external ID"
msgstr "מזהה חיצוני של אירוע"

#. js-lingui-id: aZJLAR
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Event ID"
msgstr "מזהה אירוע"

#. js-lingui-id: 81maJp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Event Listeners"
#~ msgstr "מאזיני אירועים"

#. js-lingui-id: PYs3rP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event name"
msgstr "שם אירוע"

#. js-lingui-id: evIGwh
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Event name/type"
#~ msgstr "שם/סוג אירוע"

#. js-lingui-id: vv99Wu
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event note"
msgstr "הערה לאירוע"

#. js-lingui-id: eSt759
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event opportunity"
msgstr "הזדמנות באירוע"

#. js-lingui-id: aicVfT
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Event Participants"
msgstr "משתתפי אירוע"

#. js-lingui-id: 0mp45a
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event person"
msgstr "אדם באירוע"

#. js-lingui-id: mMq0Wy
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event task"
msgstr "משימה באירוע"

#. js-lingui-id: dCV1dS
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow"
msgstr "תהליך עבודה באירוע"

#. js-lingui-id: W84pl6
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow run"
msgstr "הפעלת תהליך עבודה באירוע"

#. js-lingui-id: vUpps9
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workflow version"
msgstr "גרסת תהליך עבודה של אירוע"

#. js-lingui-id: LxOGsB
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Event workspace member"
msgstr "חבר חלל עבודה באירוע"

#. js-lingui-id: tst44n
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events"
msgstr "אירועים"

#. js-lingui-id: fHL+iH
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Events linked to the person"
msgstr "אירועים הקשורים לאדם"

#. js-lingui-id: 3/O8MM
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Events linked to the workspace member"
msgstr "אירועים הקשורים לחבר חלל עבודה"

#. js-lingui-id: QQlMid
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude group emails"
msgstr "התעלם מכתובות דוא\"ל קבוצתיות"

#. js-lingui-id: kenYGr
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Exclude non professional emails"
msgstr "התעלם מכתובות דוא\"ל לא מקצועיות"

#. js-lingui-id: Lo5U0b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Executed by"
msgstr "נתבע על ידי"

#. js-lingui-id: AiFXmG
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Expected the same constraint fields to be used consistently across all operations for {connectFieldName}."
msgstr ""

#. js-lingui-id: sZg7s1
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Expiration date"
msgstr "\"תאריך תפוגה\""

#. js-lingui-id: 6Ki4Pv
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite"
msgstr "מועדף"

#. js-lingui-id: aKUOPp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite company"
msgstr "חברה מועדפת"

#. js-lingui-id: TDlZ/o
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite Folder"
msgstr "תיקייה מועדפת"

#. js-lingui-id: WDVfUH
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite folder position"
msgstr "מיקום תיקייה מועדפת"

#. js-lingui-id: SStz54
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorite Folders"
msgstr "תיקיות מועדפות"

#. js-lingui-id: dz/fFp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite note"
msgstr "הערה מועדפת"

#. js-lingui-id: RRr9Bp
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite opportunity"
msgstr "הזדמנות מועדפת"

#. js-lingui-id: RM6B6V
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite person"
msgstr "איש קשר מועדף"

#. js-lingui-id: oVA9vM
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite position"
msgstr "תפקיד מועדף"

#. js-lingui-id: OaQVQH
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite task"
msgstr "משימה מועדפת"

#. js-lingui-id: VkWV1s
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite view"
msgstr "תצוגה מועדפת"

#. js-lingui-id: l+lx2f
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow"
msgstr "תהליך עבודה מועדף"

#. js-lingui-id: BPBnux
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow run"
msgstr "הרצת תהליך עבודה מועדפת"

#. js-lingui-id: 5X6Vlz
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workflow version"
msgstr "גרסת תהליך עבודה מועדפת"

#. js-lingui-id: FDpezg
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Favorite workspace member"
msgstr "חבר בסביבת העבודה המועדף"

#. js-lingui-id: X9kySA
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites"
msgstr "מועדפים"

#. js-lingui-id: 0CzeFL
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Favorites in this folder"
msgstr "מועדפים בתיקיה זו"

#. js-lingui-id: zQponA
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Favorites linked to the company"
msgstr "מועדפים הקשורים לחברה"

#. js-lingui-id: VaKLrB
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Favorites linked to the contact"
msgstr "מועדפים הקשורים לאיש קשר"

#. js-lingui-id: GOfcBt
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Favorites linked to the note"
msgstr "מועדפים הקשורים להערה"

#. js-lingui-id: 9zd8hg
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Favorites linked to the opportunity"
msgstr "מועדפים הקשורים להזדמנות"

#. js-lingui-id: L5ccWD
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Favorites linked to the task"
msgstr "מועדפים הקשורים למשימה"

#. js-lingui-id: R+1ib/
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Favorites linked to the view"
msgstr "מועדפים הקשורים לתצוגה"

#. js-lingui-id: ee0tmj
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Favorites linked to the workflow"
msgstr "מועדפים הקשורים לתהליך עבודה"

#. js-lingui-id: zar5jz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Favorites linked to the workflow run"
msgstr "מועדפים הקשורים להרצת תהליך עבודה"

#. js-lingui-id: 499/sw
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Favorites linked to the workflow version"
msgstr "מועדפים הקשורים לגרסת תהליך עבודה"

#. js-lingui-id: rgmtb4
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Favorites linked to the workspace member"
msgstr "מועדפים הקשורים לחבר בסביבת העבודה"

#. js-lingui-id: GyXrE1
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Favorites tied to the {label}"
msgstr "מועדפים המקושרים ל-{label}"

#. js-lingui-id: nSFFML
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Field Metadata Id"
msgstr "מזהה המטא-דאטה של השדה"

#. js-lingui-id: g+t8w9
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Field metadata used for aggregate operation"
msgstr "השדה שמשמש לפעולת אגריגציה"

#. js-lingui-id: J4hC2m
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Field used for full-text search"
msgstr "שדה המשמש לחיפוש טקסט מלא"

#. js-lingui-id: zuDgzc
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Field Value"
msgstr "ערך השדה"

#. js-lingui-id: 3oQ73E
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have a previous step"
#~ msgstr ""

#. js-lingui-id: Zllikb
#: src/modules/workflow/workflow-executor/workflow-actions/filter/utils/get-previous-step-output.util.ts
#~ msgid "Filter action must have only one previous step"
#~ msgstr ""

#. js-lingui-id: lEpDue
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder for Message Channel"
msgstr "תיקיה עבור ערוץ הודעות"

#. js-lingui-id: cqQyPB
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Folder name"
msgstr "שם התיקיה"

#. js-lingui-id: kuN9I4
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have a defined label and type"
msgstr ""

#. js-lingui-id: QK3OvQ
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action fields must have unique names"
msgstr ""

#. js-lingui-id: KYP1t5
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "Form action must have at least one field"
msgstr ""

#. js-lingui-id: oQA+cA
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Full path"
msgstr "נתיב מלא"

#. js-lingui-id: UzpVUy
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "Group by this field value"
msgstr "לקבץ על פי ערך שדה זה"

#. js-lingui-id: TkE8dW
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "handle"
msgstr "ידית"

#. js-lingui-id: Nf7oXL
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "Handle"
msgstr "ידית"

#. js-lingui-id: GvgxWx
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Handle Aliases"
msgstr "כינויים לידית"

#. js-lingui-id: VehAU2
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Header message Id"
msgstr "מזהה הודעה ראשית"

#. js-lingui-id: NNJnBi
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "iCal UID"
msgstr "מזהה iCal"

#. js-lingui-id: wwu18a
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Icon"
msgstr "סמל"

#. js-lingui-id: CiyiKN
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "ICP"
msgstr "מספר זיהוי מוסדי לאינטרנט"

#. js-lingui-id: dFb5Nt
#: src/engine/twenty-orm/base.workspace-entity.ts
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Id"
msgstr "מזהה"

#. js-lingui-id: Ebc83S
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Ideal Customer Profile:  Indicates whether the company is the most suitable and valuable customer for you"
msgstr "פרופיל לקוח אידיאלי: מציין האם החברה היא הלקוח המתאים והשווה ביותר עבורך"

#. js-lingui-id: NrA0WZ
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "If the event is related to a particular object"
#~ msgstr "אם האירוע קשור לאובייקט מסוים"

#. js-lingui-id: 71VJzG
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-short.utils.ts
msgid "Input is too short: \"{name}\""
msgstr ""

#. js-lingui-id: X7+bC6
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid calling code {callingCode}"
msgstr ""

#. js-lingui-id: 1PtrY0
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Invalid country code {countryCode}"
msgstr ""

#. js-lingui-id: 4EtFrA
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid day value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: MBpeQ6
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer between 0 and 23"
msgstr ""

#. js-lingui-id: 9xsjn9
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid hour value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: FSunWx
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Invalid label: \"{label}\""
msgstr ""

#. js-lingui-id: iBBCnf
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer between 0 and 59"
msgstr ""

#. js-lingui-id: hYcFSd
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid minute value. Should be integer greater than 1"
msgstr ""

#. js-lingui-id: ApP70c
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: sHcAMD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "Invalid trigger type for enabling workflow trigger"
msgstr ""

#. js-lingui-id: grHYXZ
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is canceled"
msgstr "מבוטל"

#. js-lingui-id: k+g9Uh
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Contact Auto Creation Enabled"
msgstr "יצירת איש קשר אוטומטית פעילה"

#. js-lingui-id: uBx1xd
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Is Full Day"
msgstr "האירוע ממשך יום שלם"

#. js-lingui-id: 3iAfL2
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Is Organizer"
msgstr "מארגן האירוע"

#. js-lingui-id: Mqzqb8
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Is Sync Enabled"
msgstr "סנכרון מופעל"

#. js-lingui-id: 27z+FV
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Job Title"
msgstr "תיאור משרה"

#. js-lingui-id: PviVyk
#: src/engine/core-modules/workspace-invitation/services/workspace-invitation.service.ts
msgid "Join your team on Twenty"
msgstr "הצטרף לצוות שלך ב-Twenty"

#. js-lingui-id: WNfJ8M
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "JSON object containing custom connection parameters"
msgstr ""

#. js-lingui-id: shbh25
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Json object to provide context (user, device, workspace, etc.)"
#~ msgstr "אובייקט Json לספק הקשר (משתמש, מכשיר, סביבת עבודה וכו')"

#. js-lingui-id: HgJ9jQ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Json object to provide output of the workflow run"
msgstr "עצם Json לספק פלט של הרצת ה-Workflows"

#. js-lingui-id: AQuLwi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide steps"
msgstr "עצם Json לספק שלבים"

#. js-lingui-id: fOjmZ1
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Json object to provide trigger"
msgstr "עצם Json לספק טריגר"

#. js-lingui-id: TVc0/Q
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Json value for event details"
msgstr "ערך Json לפרטי אירוע"

#. js-lingui-id: qmarO2
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "kanbanfieldMetadataId"
msgstr "מזהה Metadata של שדה קנבן"

#. js-lingui-id: 7sMeHQ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Key"
msgstr "מפתח"

#. js-lingui-id: w8Rv8T
#: src/engine/metadata-modules/utils/compute-metadata-name-from-label.util.ts
msgid "Label is required"
msgstr ""

#. js-lingui-id: 4EGmy6
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not be empty"
msgstr ""

#. js-lingui-id: k731jp
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Label must not contain a comma"
msgstr ""

#. js-lingui-id: vXIe7J
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Language"
msgstr "שפה"

#. js-lingui-id: WvYp6o
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Last published Version Id"
msgstr "מזהה של גרסת הפרסום האחרונה"

#. js-lingui-id: Y60/DC
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Last sync cursor"
msgstr "סמן סנכרון אחרון"

#. js-lingui-id: Zdx9Qq
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Last sync date"
msgstr "תאריך סנכרון אחרון"

#. js-lingui-id: c7uqNg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Last sync history ID"
msgstr "מזהה היסטוריית סנכרון אחרון"

#. js-lingui-id: tGwswq
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last time the record was changed"
msgstr "הפעם האחרונה בה הרשומה הוחלפה"

#. js-lingui-id: o1zvNS
#: src/engine/twenty-orm/base.workspace-entity.ts
msgid "Last update"
msgstr "עדכון אחרון"

#. js-lingui-id: wR9USP
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Object Metadata Id"
msgstr "מזהה Metadata של אובייקט מקושר"

#. js-lingui-id: PV1Nm7
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Linked Opportunities"
msgstr "הזדמנויות מקושרות"

#. js-lingui-id: 6YiMr4
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record cached name"
msgstr "שם מטמון של רשומה מקושרת"

#. js-lingui-id: sgHAxx
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Linked Record id"
msgstr "מזהה רשומה מקושרת"

#. js-lingui-id: uCA6be
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Linkedin"
msgstr "לינקדין"

#. js-lingui-id: LeFv/R
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "List of opportunities for which that person is the point of contact"
msgstr "רשימת הזדמנויות שבהן האדם הזה הוא איש הקשר"

#. js-lingui-id: wJijgU
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Location"
msgstr "מיקום"

#. js-lingui-id: y+q8Lv
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical Operator"
msgstr "אופרטור לוגי"

#. js-lingui-id: 6kQXsS
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Logical operator for the filter group"
msgstr "אופרטור לוגי לקבוצת הפילטר"

#. js-lingui-id: EnfPm2
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Meet Link"
msgstr "לינק לפגישה"

#. js-lingui-id: xDAtGP
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message"
msgstr "\\"

#. js-lingui-id: g+QGD6
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel"
msgstr "\\"

#. js-lingui-id: DzU4a3
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Channel Association"
msgstr "\\"

#. js-lingui-id: wd/R++
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Id"
msgstr "\\"

#. js-lingui-id: disipM
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Association"
msgstr "\\"

#. js-lingui-id: ijQY3P
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Channel Message Associations"
msgstr "\\"

#. js-lingui-id: k7LXPQ
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Message Channels"
msgstr "\\"

#. js-lingui-id: /4uGJc
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Direction"
msgstr "\\"

#. js-lingui-id: fBw8fQ
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message External Id"
msgstr "\\"

#. js-lingui-id: CStLnc
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
msgid "Message Folder"
msgstr "\\"

#. js-lingui-id: p4ANpY
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Message Folders"
msgstr "\\"

#. js-lingui-id: 6icznk
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Id"
msgstr "\\"

#. js-lingui-id: 9FJSpK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message id from the message header"
msgstr "\\"

#. js-lingui-id: yaC1Aq
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message id from the messaging provider"
msgstr "\\"

#. js-lingui-id: IUmVwu
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participant"
msgstr "\\"

#. js-lingui-id: FhIFx7
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Message Participants"
msgstr "\\"

#. js-lingui-id: IC5A8V
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Message Synced with a Message Channel"
msgstr "\\"

#. js-lingui-id: de2nM/
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Thread"
msgstr "\\"

#. js-lingui-id: km1jgD
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Message Thread Id"
msgstr "\\"

#. js-lingui-id: RD0ecC
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Message Threads"
msgstr "\\"

#. js-lingui-id: t7TeQU
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages"
msgstr "הודעות"

#. js-lingui-id: WoWdku
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
msgid "Messages from the channel."
msgstr "הודעות מהערוץ."

#. js-lingui-id: rYPBO7
#: src/modules/messaging/common/standard-objects/message-thread.workspace-entity.ts
msgid "Messages from the thread."
msgstr "הודעות מהשרשור."

#. js-lingui-id: XcKQrV
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider access token"
msgstr "\\"

#. js-lingui-id: 80EvIk
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Messaging provider refresh token"
msgstr "\\"

#. js-lingui-id: SzCMUQ
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Missing required fields: at least one unique constraint have to be fully populated for '{connectFieldName}'."
msgstr ""

#. js-lingui-id: 6YtxFj
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Name"
msgstr "\\"

#. js-lingui-id: RGz1nY
#: src/engine/metadata-modules/field-metadata/services/field-metadata-validation.service.ts
msgid "Name is not available, it may be duplicating another field's name."
msgstr ""

#. js-lingui-id: 0MobB1
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-too-long.utils.ts
msgid "Name is too long: it exceeds the 63 characters limit."
msgstr ""

#. js-lingui-id: EEVPOx
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
msgid "Name of the favorite folder"
msgstr "\\"

#. js-lingui-id: csMjko
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Name of the workflow run"
msgstr "\\"

#. js-lingui-id: hQQZse
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No event name provided in database event trigger"
msgstr ""

#. js-lingui-id: 9bu19u
#: src/modules/workflow/workflow-trigger/utils/assert-form-step-is-valid.util.ts
msgid "No input provided in form step"
msgstr ""

#. js-lingui-id: MWAUHo
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No pattern provided in CUSTOM cron trigger"
msgstr ""

#. js-lingui-id: pOu4u/
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No schedule provided in cron trigger"
msgstr ""

#. js-lingui-id: cLUBlS
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No setting type provided in cron trigger"
msgstr ""

#. js-lingui-id: QwbSxD
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No steps provided in workflow version"
msgstr ""

#. js-lingui-id: 6dP8sB
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
msgid "No trigger type provided"
msgstr ""

#. js-lingui-id: KiJn9B
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Note"
msgstr "\\"

#. js-lingui-id: GGlkb7
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note attachments"
msgstr "\\"

#. js-lingui-id: Q1Rz+6
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note body"
msgstr "\\"

#. js-lingui-id: Yp057F
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note record position"
msgstr "מיקום רישום ההערה"

#. js-lingui-id: spaO7l
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Target"
msgstr "מטרת ההערה"

#. js-lingui-id: mkchvJ
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note targets"
msgstr "יעדי ההערה"

#. js-lingui-id: tD4BxK
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "Note Targets"
msgstr "יעדים להערה"

#. js-lingui-id: jDThel
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Note title"
msgstr "כותרת ההערה"

#. js-lingui-id: 1DBGsz
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes"
msgstr "הערות"

#. js-lingui-id: Ne73P/
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Notes tied to the {label}"
msgstr "הערות הקשורות ל{label}"

#. js-lingui-id: fXBE74
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Notes tied to the company"
msgstr "הערות הקשורות לחברה"

#. js-lingui-id: iQ5AH6
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Notes tied to the contact"
msgstr "הערות הקשורות לאיש קשר"

#. js-lingui-id: 0bi/Eh
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Notes tied to the opportunity"
msgstr "הערות הקשורות להזדמנות"

#. js-lingui-id: B2Y6QU
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget company"
msgstr "NoteTarget חברה"

#. js-lingui-id: /mH0jo
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget note"
msgstr "NoteTarget הערה"

#. js-lingui-id: DTs4tO
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget opportunity"
msgstr "NoteTarget הזדמנות"

#. js-lingui-id: gBwbnk
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
msgid "NoteTarget person"
msgstr "NoteTarget אדם"

#. js-lingui-id: ioJFzx
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Number of employees in the company"
msgstr "מספר העובדים בחברה"

#. js-lingui-id: ECRKYR
#: src/engine/metadata-modules/utils/validate-no-other-object-with-same-name-exists-or-throw.util.ts
msgid "Object already exists"
msgstr ""

#. js-lingui-id: hhe7Ce
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#~ msgid "Object id"
#~ msgstr "מזהה האובייקט"

#. js-lingui-id: dnPgTI
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object metadata id"
#~ msgstr "מזהה מטא-נתונים של אובייקט"

#. js-lingui-id: T/nPf5
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Object Metadata Id"
msgstr "מזהה מטא-נתונים של אובייקט"

#. js-lingui-id: afsWF6
#: src/modules/timeline/standard-objects/behavioral-event.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Object name"
#~ msgstr "שם האובייקט"

#. js-lingui-id: r/A6pA
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Open Record In"
msgstr "פתח רשומה ב"

#. js-lingui-id: Fzfj4N
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Operand"
msgstr "אופרנד"

#. js-lingui-id: FZg3wM
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operation"
msgstr "פעולה"

#. js-lingui-id: B1MDds
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Operations"
msgstr "פעולות"

#. js-lingui-id: 4MyDFl
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities"
msgstr "הזדמנויות"

#. js-lingui-id: 5QgKbT
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Opportunities linked to the company."
msgstr "הזדמנויות הקשורות לחברה."

#. js-lingui-id: SV/iis
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Opportunity"
msgstr "הזדמנות"

#. js-lingui-id: WnMlKn
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity amount"
msgstr "סכום ההזדמנות"

#. js-lingui-id: aj3fnv
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity close date"
msgstr "תאריך סגירת ההזדמנות"

#. js-lingui-id: 3NYczb
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity company"
msgstr "חברה של ההזדמנות"

#. js-lingui-id: as45IN
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity point of contact"
msgstr "איש קשר מרכזי של ההזדמנות"

#. js-lingui-id: 5Nu7Uw
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity probability"
msgstr "הסיכוי של ההזדמנות"

#. js-lingui-id: Q1dzBp
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity record position"
msgstr "מיקום רישום ההזדמנות"

#. js-lingui-id: 5ugYS3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Opportunity stage"
msgstr "שלב ההזדמנות"

#. js-lingui-id: //eyK1
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label \"{sanitizedLabel}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: mTFS0z
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label exceeds 63 characters"
msgstr ""

#. js-lingui-id: +E9N4m
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option label is required"
msgstr ""

#. js-lingui-id: nQqNzE
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value \"{sanitizedValue}\" is beneath 1 character"
msgstr ""

#. js-lingui-id: 5m/1Hh
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value exceeds 63 characters"
msgstr ""

#. js-lingui-id: /wsRPd
#: src/engine/metadata-modules/field-metadata/services/field-metadata-enum-validation.service.ts
msgid "Option value is required"
msgstr ""

#. js-lingui-id: eLggyd
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Optional aggregate operation"
msgstr "אגרגציה אופציונלית של פעולה"

#. js-lingui-id: kdClJ/
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Optional secret used to compute the HMAC signature for webhook payloads. This secret is shared between Twenty and the webhook consumer to authenticate webhook requests."
msgstr "סוד אופציונלי המשמש לחישוב חתימת HMAC עבור משאבות webhook. סוד זה מתואם בין Twenty וצרכן ה-webhook כדי לאמת בקשות webhook."

#. js-lingui-id: gh06VD
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Output"
msgstr "פלט"

#. js-lingui-id: pDUbN1
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group"
msgstr "קבוצת סינון תצוגת אב"

#. js-lingui-id: YKSmIP
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Parent View Filter Group Id"
msgstr "מזהה קבוצת סינון תצוגת אב"

#. js-lingui-id: wT0H4O
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Password too weak"
msgstr ""

#. js-lingui-id: 1wdjme
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People"
msgstr "אנשים"

#. js-lingui-id: E1zc7W
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "People linked to the company."
msgstr "אנשים הקשורים לחברה."

#. js-lingui-id: OZdaTZ
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/note/standard-objects/note-target.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Person"
msgstr "אדם"

#. js-lingui-id: c3Qq6o
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Person record Position"
msgstr "תיק אישי תפקיד"

#. js-lingui-id: zmwvG2
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phone"
msgstr "טלפון"

#. js-lingui-id: m2ivgq
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Phones"
msgstr "טלפונים"

#. js-lingui-id: P04j61
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Point of Contact"
msgstr "איש קשר"

#. js-lingui-id: p/78dY
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/favorite-folder/standard-objects/favorite-folder.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Position"
msgstr "תפקיד"

#. js-lingui-id: HH1bMC
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in the parent view filter group"
msgstr "מיקום בקבוצת מסנני הצגה עליונים"

#. js-lingui-id: CvOSME
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Position in the view filter group"
msgstr "מיקום בקבוצת מסנני ההצגה"

#. js-lingui-id: h8PGuF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "Position in view filter group"
msgstr "מיקום בקבוצת מסנני ההצגה"

#. js-lingui-id: TTHIbk
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred color scheme"
msgstr "''"

#. js-lingui-id: 5v4qYi
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Preferred language"
msgstr "שפה מועדפת"

#. js-lingui-id: WJIL29
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Probability"
msgstr "סבירות"

#. js-lingui-id: dtGxRz
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred calling code are conflicting"
msgstr ""

#. js-lingui-id: Vq/afT
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided and inferred country code are conflicting"
msgstr ""

#. js-lingui-id: HZ45ox
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided country code and calling code are conflicting"
msgstr ""

#. js-lingui-id: A4Ohxb
#: src/engine/core-modules/record-transformer/utils/transform-phones-value.util.ts
msgid "Provided phone number is invalid {number}"
msgstr ""

#. js-lingui-id: CrfRPa
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "provider"
msgstr "ספק"

#. js-lingui-id: YfbwOB
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Received At"
msgstr "תאריך קבלה"

#. js-lingui-id: 4Tvtbu
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#: src/modules/timeline/standard-objects/audit-log.workspace-entity.ts
#~ msgid "Record id"
#~ msgstr "מזהה רישום"

#. js-lingui-id: 9gXJw8
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel-event-association.workspace-entity.ts
msgid "Recurring Event ID"
msgstr "מזהה אירוע חוזר"

#. js-lingui-id: 2rvMKg
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Refresh Token"
msgstr "אסימון רענון"

#. js-lingui-id: 2LpFdR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Related user email address"
msgstr "כתובת דוא\"ל של המשתמש הקשור"

#. js-lingui-id: g87L9j
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Relations"
msgstr "יחסים"

#. js-lingui-id: pCsIQQ
#: src/engine/core-modules/email-verification/email-verification-exception-filter.util.ts
msgid "Request has expired, please try again."
msgstr ""

#. js-lingui-id: rnCndp
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Response Status"
msgstr "מצב תגובה"

#. js-lingui-id: MCWKAU
#: src/modules/api-key/standard-objects/api-key.workspace-entity.ts
msgid "Revocation date"
msgstr "תאריך ביטול"

#. js-lingui-id: GDvlUT
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Role"
msgstr "תפקיד"

#. js-lingui-id: Tpm2G9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Runs"
msgstr "ריצות"

#. js-lingui-id: N/rFzD
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "Scopes"
msgstr "טווחי הרשאה"

#. js-lingui-id: PdVIJC
#: src/engine/metadata-modules/constants/search-vector-field.constants.ts
msgid "Search vector"
msgstr "וקטור חיפוש"

#. js-lingui-id: 8VEDbV
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Secret"
msgstr "סוד"

#. js-lingui-id: Tz0i8g
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "Settings"
msgstr ""

#. js-lingui-id: Cj2Gtd
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Size"
msgstr "גודל"

#. js-lingui-id: 3PRxO3
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Stage"
msgstr "שלב"

#. js-lingui-id: D3iCkb
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Start Date"
msgstr "תאריך התחלה"

#. js-lingui-id: RS0o7b
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State"
msgstr ""

#. js-lingui-id: nY8GL/
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "State of the workflow run"
msgstr ""

#. js-lingui-id: uAQUqI
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Status"
msgstr "מצב"

#. js-lingui-id: Db4W3/
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Statuses"
msgstr "מצבים"

#. js-lingui-id: S7DRR+
#: src/modules/workflow/common/utils/assert-workflow-statuses-not-set.ts
msgid "Statuses cannot be set manually."
msgstr ""

#. js-lingui-id: u+rv4L
#: src/modules/workflow/workflow-builder/workflow-step/workflow-version-step.workspace-service.ts
msgid "Step is not a form"
msgstr ""

#. js-lingui-id: YFMPch
#: src/engine/metadata-modules/utils/validate-metadata-name-start-with-lowercase-letter-and-contain-digits-nor-letters.utils.ts
msgid "String \"{name}\" is not valid: must start with lowercase letter and contain only alphanumeric letters"
msgstr ""

#. js-lingui-id: g4jxXh
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Sub field name"
msgstr ""

#. js-lingui-id: UJmAAK
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Subject"
msgstr "נושא"

#. js-lingui-id: oyJYg7
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-folder.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor"
msgstr "אינדיקטור סנכרון"

#. js-lingui-id: awvBUx
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync Cursor. Used for syncing events from the calendar provider"
msgstr "אינדיקטור סנכרון. משמש לסנכרון אירועים מספקי היומן"

#. js-lingui-id: dNAbG6
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage"
msgstr "שלב סינכרון"

#. js-lingui-id: aqNjQE
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync stage started at"
msgstr "שלב סינכרון התחיל ב"

#. js-lingui-id: bRUdLR
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Sync status"
msgstr "סטטוס סינכרון"

#. js-lingui-id: 5y3Wbw
#: src/engine/twenty-orm/utils/compute-relation-connect-query-configs.util.ts
msgid "Target object metadata not found for {connectFieldName}"
msgstr ""

#. js-lingui-id: 4SHJe4
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Target Url"
msgstr "כתובת יעד"

#. js-lingui-id: Q3P/4s
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Task"
msgstr "משימה"

#. js-lingui-id: kS+Ym6
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task assignee"
msgstr "ממונה על המשימה"

#. js-lingui-id: 7fYQ6E
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task attachments"
msgstr "קבצים מצורפים למשימה"

#. js-lingui-id: X8fs74
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task body"
msgstr "תוכן המשימה"

#. js-lingui-id: EPxYHS
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task due date"
msgstr "תאריך הסיום של המשימה"

#. js-lingui-id: fUw1j+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task record position"
msgstr "מקום התיעוד של המשימה"

#. js-lingui-id: I6+0ph
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task status"
msgstr "סטטוס המשימה"

#. js-lingui-id: WSiiWf
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Target"
msgstr "יעד משימה"

#. js-lingui-id: khGQLP
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task targets"
msgstr "יעדי המשימה"

#. js-lingui-id: 836FiO
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "Task Targets"
msgstr "יעדי המשימה"

#. js-lingui-id: R+s8S+
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Task title"
msgstr "כותרת המשימה"

#. js-lingui-id: GtycJ/
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks"
msgstr "משימות"

#. js-lingui-id: HlDeG3
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Tasks assigned to the workspace member"
msgstr "משימות שהוקצו לחבר במרחב העבודה"

#. js-lingui-id: Ca/n4T
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Tasks tied to the {label}"
msgstr "משימות שקשורות ל{label}"

#. js-lingui-id: M4rBti
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Tasks tied to the company"
msgstr "משימות שקשורות לחברה"

#. js-lingui-id: /VaiDW
#: src/modules/person/standard-objects/person.workspace-entity.ts
msgid "Tasks tied to the contact"
msgstr "משימות שקשורות לאיש קשר"

#. js-lingui-id: 1TfX9U
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Tasks tied to the opportunity"
msgstr "משימות שקשורות להזדמנות"

#. js-lingui-id: pP0Dt9
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget company"
msgstr "יעד משימת חברה"

#. js-lingui-id: UJ2aPi
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget opportunity"
msgstr "יעד משימת הזדמנות"

#. js-lingui-id: I1MiSs
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget person"
msgstr "יעד משימת אדם"

#. js-lingui-id: pciKLT
#: src/modules/task/standard-objects/task-target.workspace-entity.ts
msgid "TaskTarget task"
msgstr "יעד משימת משימה"

#. js-lingui-id: xeiujy
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "Text"
msgstr "טקסט"

#. js-lingui-id: 6PJbR2
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account handle (email, username, phone number, etc.)"
msgstr "מזהה החשבון (דוא\"ל, שם משתמש, מספר טלפון וכו')"

#. js-lingui-id: zUXOAB
#: src/modules/connected-account/standard-objects/connected-account.workspace-entity.ts
msgid "The account provider"
msgstr "ספק החשבון"

#. js-lingui-id: qnNFrW
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Linkedin account"
msgstr "חשבון הלינקדין של החברה"

#. js-lingui-id: N31Pso
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company name"
msgstr "שם החברה"

#. js-lingui-id: BHFCqB
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company Twitter/X account"
msgstr "חשבון ה-Twitter/X של החברה"

#. js-lingui-id: OBmU0K
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "The company website URL. We use this url to fetch the company icon"
msgstr "כתובת האתר של החברה. אנחנו משתמשים בכתובת זו כדי לאחזר את סמל החברה"

#. js-lingui-id: zGBDEH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "The creator of the record"
msgstr "יוצר הרשומה"

#. js-lingui-id: bMyVOx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The current statuses of the workflow versions"
msgstr "המצבים הנוכחיים של גרסאות הזרימות"

#. js-lingui-id: 0bo4Q0
#: src/modules/messaging/common/standard-objects/message.workspace-entity.ts
msgid "The date the message was received"
msgstr "תאריך קבלת ההודעה"

#. js-lingui-id: 8h4mhq
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "The executor of the workflow"
msgstr "ביצוע הזרימה"

#. js-lingui-id: W3raza
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "The folder this favorite belongs to"
msgstr "התיקייה של המועדפים שאליה הוא שייך"

#. js-lingui-id: EJUSll
#: src/modules/workflow/common/workspace-services/workflow-version-validation.workspace-service.ts
msgid "The initial version of a workflow can not be deleted"
msgstr ""

#. js-lingui-id: DbWmKZ
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "The opportunity name"
msgstr "שם ההזדמנות"

#. js-lingui-id: 5N6QtE
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger settings"
msgstr ""

#. js-lingui-id: SpKbfT
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "The workflow automated trigger type"
msgstr ""

#. js-lingui-id: 7mPrpl
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "The workflow event listener name"
#~ msgstr "שם מאזין לאירוע תהליך העבודה"

#. js-lingui-id: od0omS
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow last published version id"
msgstr "מזהה של הגרסה האחרונה שפורסמה של תהליך העבודה"

#. js-lingui-id: /EdWx6
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "The workflow name"
msgstr "שם תהליך העבודה"

#. js-lingui-id: EhAsND
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version name"
msgstr "שם גרסת תהליך העבודה"

#. js-lingui-id: dhx13p
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "The workflow version status"
msgstr "סטטוס גרסת תהליך העבודה"

#. js-lingui-id: yIUmAs
#: src/engine/metadata-modules/utils/validate-metadata-name-is-not-reserved-keyword.ts
#: src/engine/metadata-modules/utils/validate-field-name-availability.utils.ts
msgid "This name is not available."
msgstr ""

#. js-lingui-id: Zl0BJl
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread External Id"
msgstr "מזהה חיצוני של שיחה"

#. js-lingui-id: RSSbWN
#: src/modules/messaging/common/standard-objects/message-channel-message-association.workspace-entity.ts
msgid "Thread id from the messaging provider"
msgstr "מזהה שיחה מספק המסרים"

#. js-lingui-id: sS8v5K
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Throttle Failure Count"
msgstr "מספר ניסיונות כשל בדיקת מהירות"

#. js-lingui-id: n9nSNJ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time format"
msgstr "פורמט זמן"

#. js-lingui-id: Mz2JN2
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Time zone"
msgstr "אזור זמן"

#. js-lingui-id: az1boY
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities"
msgstr "פעילויות ציר זמנים"

#. js-lingui-id: fqKMpF
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Timeline Activities linked to the company"
msgstr "פעילויות ציר זמנים הקשורות לחברה"

#. js-lingui-id: 4/UzU5
#: src/modules/note/standard-objects/note.workspace-entity.ts
msgid "Timeline Activities linked to the note."
msgstr "פעילויות ציר זמנים הקשורות להערה."

#. js-lingui-id: p6feIz
#: src/modules/opportunity/standard-objects/opportunity.workspace-entity.ts
msgid "Timeline Activities linked to the opportunity."
msgstr "פעילויות ציר זמנים הקשורות להזדמנות."

#. js-lingui-id: yvPwuF
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Timeline activities linked to the run"
msgstr "פעילויות ציר זמנים הקשורות לריצה"

#. js-lingui-id: q96UvB
#: src/modules/task/standard-objects/task.workspace-entity.ts
msgid "Timeline Activities linked to the task."
msgstr "פעילויות ציר זמנים הקשורות למשימה."

#. js-lingui-id: N9HMa/
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Timeline activities linked to the version"
msgstr "פעילויות ציר זמנים הקשורות לגרסה"

#. js-lingui-id: B1CYKX
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Timeline activities linked to the workflow"
msgstr "פעילויות ציר זמנים הקשורות לתהליך עבודה"

#. js-lingui-id: G0UmtQ
#: src/engine/twenty-orm/custom.workspace-entity.ts
msgid "Timeline Activities tied to the {label}"
msgstr "פעילויות ציר זמנים הקשורות ל{label}"

#. js-lingui-id: K/kU4E
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Timeline Activity"
msgstr "פעילות בציר זמנים"

#. js-lingui-id: MHrjPM
#: src/modules/task/standard-objects/task.workspace-entity.ts
#: src/modules/note/standard-objects/note.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Title"
msgstr "כותרת"

#. js-lingui-id: +zy2Nq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/attachment/standard-objects/attachment.workspace-entity.ts
msgid "Type"
msgstr "סוג"

#. js-lingui-id: EBZdX5
#: src/modules/workflow/workflow-trigger/utils/compute-cron-pattern-from-schedule.ts
msgid "Unsupported cron schedule type"
msgstr ""

#. js-lingui-id: 0gY5lO
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event.workspace-entity.ts
msgid "Update DateTime"
msgstr "תאריך ושעה של עדכון"

#. js-lingui-id: l9dlVi
#: src/engine/core-modules/auth/strategies/jwt.auth.strategy.ts
msgid "User does not have access to this workspace"
msgstr ""

#. js-lingui-id: YFciqL
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Email"
msgstr "דואר אלקטרוני של משתמש"

#. js-lingui-id: d1BTW8
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User Id"
msgstr "מזהה משתמש"

#. js-lingui-id: B43Kks
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "User is not part of the workspace"
msgstr ""

#. js-lingui-id: 4juE7s
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User time zone"
msgstr "אזור זמן של המשתמש"

#. js-lingui-id: 43zCwQ
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred date format"
msgstr "פורמט התאריך המועדף על המשתמש"

#. js-lingui-id: kJuoKm
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "User's preferred time format"
msgstr "פורמט הזמן המועדף על המשתמש"

#. js-lingui-id: wMHvYH
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "Value"
msgstr "ערך"

#. js-lingui-id: 7aatUy
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version status"
msgstr "סטטוס הגרסה"

#. js-lingui-id: CdQeU7
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version steps"
msgstr "שלבי הגרסה"

#. js-lingui-id: PGMPIi
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Version trigger"
msgstr "מאפשר התראה של הגרסה"

#. js-lingui-id: IYNSdp
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Versions"
msgstr "גרסאות"

#. js-lingui-id: jpctdh
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "View"
msgstr "תצוגה"

#. js-lingui-id: cZPDyy
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field"
msgstr "שדה תצוגה"

#. js-lingui-id: 6jpoH4
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field position"
msgstr "מיקום שדה התצוגה"

#. js-lingui-id: Ju6gri
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field related view"
msgstr "\t"

#. js-lingui-id: tvTr2y
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field size"
msgstr "\t"

#. js-lingui-id: BkII8t
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field target field"
msgstr "\t"

#. js-lingui-id: Gd/LzL
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Field visibility"
msgstr "\t"

#. js-lingui-id: GUFYyq
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "View Fields"
msgstr "תצוגות שדה"

#. js-lingui-id: JRtI7Y
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter"
msgstr "סינון תצוגה"

#. js-lingui-id: L2gQ5q
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Display Value"
msgstr "ערך תצוגה של סינון"

#. js-lingui-id: l9/6pD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Group"
msgstr "קבוצת סינון תצוגה"

#. js-lingui-id: Mbosm8
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter Group Id"
msgstr "מזהה קבוצת סינון תצוגה"

#. js-lingui-id: /aP3iG
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter-group.workspace-entity.ts
msgid "View Filter Groups"
msgstr "קבוצות סינון תצוגה"

#. js-lingui-id: 4MG8+B
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter operand"
msgstr "אופרנד סינון תצוגה"

#. js-lingui-id: OrUkUF
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter related view"
msgstr "קשור לתצוגת סינון"

#. js-lingui-id: TyXOtD
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter target field"
msgstr "שדה יעד של סינון תצוגה"

#. js-lingui-id: 3KzkxN
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filter value"
msgstr "ערך סינון תצוגה"

#. js-lingui-id: vj5JsR
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-filter.workspace-entity.ts
msgid "View Filters"
msgstr "סינוני תצוגה"

#. js-lingui-id: ziEP12
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group"
msgstr "קבוצת תצוגה"

#. js-lingui-id: uQ3c2q
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group related view"
msgstr "קשור לתצוגת קבוצה"

#. js-lingui-id: EFlLpQ
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group target field"
msgstr "שדה יעד של קבוצת תצוגה"

#. js-lingui-id: b1Vc+l
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Group visibility"
msgstr "\t"

#. js-lingui-id: V4nZs/
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
msgid "View Groups"
msgstr "קבוצות תצוגה"

#. js-lingui-id: qXlovu
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View icon"
msgstr "\t"

#. js-lingui-id: cd/+ZD
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View Kanban column field"
msgstr "\t"

#. js-lingui-id: vdfZ+A
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View key"
msgstr "\t"

#. js-lingui-id: oOljSE
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View name"
msgstr "\t"

#. js-lingui-id: rhO8zp
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View position"
msgstr "\t"

#. js-lingui-id: EUjpwJ
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort"
msgstr "מיון תצוגה"

#. js-lingui-id: 6ZUale
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort direction"
msgstr "כיוון מיון תצוגה"

#. js-lingui-id: /rCPqN
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort related view"
msgstr "קשור למיון תצוגה"

#. js-lingui-id: +du2wy
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sort target field"
msgstr "שדה יעד של מיון תצוגה"

#. js-lingui-id: UsdY3K
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view.workspace-entity.ts
#: src/modules/view/standard-objects/view-sort.workspace-entity.ts
msgid "View Sorts"
msgstr "מיוני תצוגה"

#. js-lingui-id: clWwIZ
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View target object"
msgstr "אוביקט יעד של תצוגה"

#. js-lingui-id: bJAIqT
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "View type"
msgstr "סוג תצוגה"

#. js-lingui-id: 1I6UoR
#: src/modules/view/standard-objects/view.workspace-entity.ts
msgid "Views"
msgstr "תצוגות"

#. js-lingui-id: 2q/Q7x
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-channel.workspace-entity.ts
msgid "Visibility"
msgstr "\t"

#. js-lingui-id: oh8+os
#: src/modules/view/standard-objects/view-group.workspace-entity.ts
#: src/modules/view/standard-objects/view-field.workspace-entity.ts
msgid "Visible"
msgstr "נראה"

#. js-lingui-id: TRDppN
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook"
msgstr "וובהוק"

#. js-lingui-id: fyB2Wp
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operation"
msgstr "פעולת Webhook"

#. js-lingui-id: gONLmX
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook operations"
msgstr "פעולות Webhook"

#. js-lingui-id: cPoSTF
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhook target url"
msgstr "כתובת יעד של Webhook"

#. js-lingui-id: v1kQyJ
#: src/modules/webhook/standard-objects/webhook.workspace-entity.ts
msgid "Webhooks"
msgstr "וובהוקים"

#. js-lingui-id: TVkCrJ
#: src/engine/core-modules/email-verification/services/email-verification.service.ts
msgid "Welcome to Twenty: Please Confirm Your Email"
msgstr "ברוך הבא ל-Twenty: נא לאשר את האימייל שלך"

#. js-lingui-id: bLt/0J
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
msgid "Workflow"
msgstr "Workflows"

#. js-lingui-id: hdtWQn
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow automated triggers linked to the workflow."
msgstr ""

#. js-lingui-id: E03XpH
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
#~ msgid "Workflow event listeners linked to the workflow."
#~ msgstr "מאזיני אירועים בזרימת עבודה מקושרים לזרימת העבודה."

#. js-lingui-id: vwSkSW
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow linked to the run."
msgstr "זרימת עבודה מקושרת להרצה."

#. js-lingui-id: y9tnFx
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow record position"
msgstr "מיקום רישום זרימת עבודה"

#. js-lingui-id: 5vIcqC
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "Workflow Run"
msgstr "הרצת זרימת עבודה"

#. js-lingui-id: 3Iz+qz
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run ended at"
msgstr "הרצת זרימת עבודה הסתיימה ב"

#. js-lingui-id: IUaK6s
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run position"
msgstr "מיקום הרצת זרימת עבודה"

#. js-lingui-id: zaN7tH
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run started at"
msgstr "הרצת זרימת עבודה התחילה ב"

#. js-lingui-id: 1TU2A8
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow run status"
msgstr "סטטוס הרצת זרימת עבודה"

#. js-lingui-id: u6DF/V
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow Runs"
msgstr "הרצאות זרימת עבודה"

#. js-lingui-id: 9nOy7k
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow runs linked to the version."
msgstr "הרצאות זרימת עבודה מקושרות לגרסה."

#. js-lingui-id: c37F3j
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow runs linked to the workflow."
msgstr "הרצאות זרימת עבודה מקושרות לזרימת העבודה."

#. js-lingui-id: lTXctu
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version"
msgstr "גרסת זרימת עבודה"

#. js-lingui-id: +wYPET
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Version"
msgstr "גרסת זרימת עבודה"

#. js-lingui-id: n4/IOE
#: src/modules/workflow/common/utils/assert-workflow-version-has-steps.ts
msgid "Workflow version does not contain at least one step"
msgstr ""

#. js-lingui-id: k4DPlQ
#: src/modules/workflow/workflow-trigger/utils/assert-version-can-be-activated.util.ts
#: src/modules/workflow/common/utils/assert-workflow-version-trigger-is-defined.util.ts
msgid "Workflow version does not contain trigger"
msgstr ""

#. js-lingui-id: bqFL4g
#: src/modules/workflow/common/utils/assert-workflow-version-is-draft.util.ts
msgid "Workflow version is not in draft status"
msgstr ""

#. js-lingui-id: CocTJJ
#: src/modules/workflow/common/standard-objects/workflow-run.workspace-entity.ts
msgid "Workflow version linked to the run."
msgstr "גרסת זרימת עבודה מקושרת להרצה."

#. js-lingui-id: 9l+pJT
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow version position"
msgstr "מיקום גרסת זרימת עבודה"

#. js-lingui-id: OCyhkn
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "Workflow Versions"
msgstr "גרסאות זרימת עבודה"

#. js-lingui-id: 018fP9
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflow versions linked to the workflow."
msgstr "גרסאות זרימת עבודה מקושרות לזרימת העבודה."

#. js-lingui-id: s7p+TL
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger"
msgstr ""

#. js-lingui-id: 96bCgZ
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTrigger workflow"
msgstr ""

#. js-lingui-id: 5z92T9
#: src/modules/workflow/common/standard-objects/workflow-automated-trigger.workspace-entity.ts
msgid "WorkflowAutomatedTriggers"
msgstr ""

#. js-lingui-id: ENOy6I
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener"
#~ msgstr "מאזין לאירוע זרימת עבודה"

#. js-lingui-id: HN90FO
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListener workflow"
#~ msgstr "מאזיני אירוע בזרימת עבודה"

#. js-lingui-id: 3JA9se
#: src/modules/workflow/common/standard-objects/workflow-event-listener.workspace-entity.ts
#~ msgid "WorkflowEventListeners"
#~ msgstr "מאזיני אירוע זרימת עבודה"

#. js-lingui-id: woYYQq
#: src/modules/workflow/common/standard-objects/workflow.workspace-entity.ts
msgid "Workflows"
msgstr "זרימות עבודה"

#. js-lingui-id: urCUgs
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
msgid "WorkflowVersion"
msgstr "גרסת זרימת עבודה"

#. js-lingui-id: b4kire
#: src/modules/workflow/common/standard-objects/workflow-version.workspace-entity.ts
msgid "WorkflowVersion workflow"
msgstr "גרסת זרימת עבודה"

#. js-lingui-id: rklt6M
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
msgid "Workspace is not ready to welcome new members"
msgstr ""

#. js-lingui-id: CbGxon
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
msgid "Workspace member"
msgstr "חבר סביבת עבודה"

#. js-lingui-id: qc38qR
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
#: src/modules/timeline/standard-objects/timeline-activity.workspace-entity.ts
#: src/modules/messaging/common/standard-objects/message-participant.workspace-entity.ts
#: src/modules/favorite/standard-objects/favorite.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
#: src/modules/calendar/common/standard-objects/calendar-event-participant.workspace-entity.ts
msgid "Workspace Member"
msgstr "חבר סביבת עבודה"

#. js-lingui-id: R1S9pO
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member avatar"
msgstr "סמל חבר סביבת עבודה"

#. js-lingui-id: 5VCX7o
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member name"
msgstr "שם חבר סביבת עבודה"

#. js-lingui-id: NiUpuN
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace member position"
msgstr "מיקום חבר סביבת עבודה"

#. js-lingui-id: YCAEr+
#: src/modules/workspace-member/standard-objects/workspace-member.workspace-entity.ts
msgid "Workspace Members"
msgstr "חברי סביבת עבודה"

#. js-lingui-id: EtzFC0
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
#: src/modules/blocklist/standard-objects/blocklist.workspace-entity.ts
msgid "WorkspaceMember"
msgstr "חבר בסדנה"

#. js-lingui-id: fV2Bu6
#: src/engine/core-modules/auth/services/sign-in-up.service.ts
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Wrong password"
msgstr ""

#. js-lingui-id: 0gv+T2
#: src/modules/person/standard-objects/person.workspace-entity.ts
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "X"
msgstr "X"

#. js-lingui-id: z+7x/s
#: src/engine/core-modules/auth/utils/auth-graphql-api-exception-handler.util.ts
msgid "You must be authenticated to perform this action."
msgstr ""

#. js-lingui-id: bTyBrW
#: src/engine/core-modules/auth/services/auth.service.ts
msgid "Your Password Has Been Successfully Changed"
msgstr "הסיסמה שלך שונתה בהצלחה"

#. js-lingui-id: vPccnr
#: src/modules/company/standard-objects/company.workspace-entity.ts
msgid "Your team member responsible for managing the company account"
msgstr "חבר הצוות שלך אחראי על ניהול חשבון החברה"

#. js-lingui-id: ej/tZF
#: src/engine/core-modules/graphql/hooks/use-graphql-error-handler.hook.ts
msgid "Your workspace has been updated with a new data model. Please refresh the page."
msgstr ""
