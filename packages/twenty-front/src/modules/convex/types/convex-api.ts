/**
 * Type definitions for Convex API functions
 * These mirror the functions defined in the Convex backend
 */

// Base types
export type ConvexId<T extends string> = string & { __tableName: T };

export type ActorMetadata = {
  source: string;
  workspaceMemberId?: ConvexId<'workspaceMembers'>;
  name?: string;
};

export type LinksMetadata = {
  primaryLinkUrl?: string;
  primaryLinkLabel?: string;
  secondaryLinks?: Array<{
    url: string;
    label: string;
  }>;
};

export type CurrencyMetadata = {
  amountMicros?: number;
  currencyCode?: string;
};

export type AddressMetadata = {
  addressStreet1?: string;
  addressStreet2?: string;
  addressCity?: string;
  addressState?: string;
  addressCountry?: string;
  addressPostcode?: string;
};

// Company types
export type ConvexCompany = {
  _id: ConvexId<'companies'>;
  workspaceId: ConvexId<'workspaces'>;
  name: string;
  domainName?: LinksMetadata;
  employees?: number;
  linkedinLink?: LinksMetadata;
  xLink?: LinksMetadata;
  annualRecurringRevenue?: CurrencyMetadata;
  address?: AddressMetadata;
  idealCustomerProfile: boolean;
  position: number;
  createdBy: ActorMetadata;
  accountOwnerId?: ConvexId<'workspaceMembers'>;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number;
};

// Person types
export type ConvexPerson = {
  _id: ConvexId<'people'>;
  workspaceId: ConvexId<'workspaces'>;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  city?: string;
  avatarUrl?: string;
  position: number;
  createdBy: ActorMetadata;
  companyId?: ConvexId<'companies'>;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number;
};

// User types
export type ConvexUser = {
  _id: ConvexId<'users'>;
  firstName: string;
  lastName: string;
  email: string;
  defaultAvatarUrl?: string;
  isEmailVerified: boolean;
  disabled: boolean;
  passwordHash?: string;
  canImpersonate: boolean;
  canAccessFullAdminPanel: boolean;
  locale: string;
  onboardingStatus?: 'INCOMPLETE' | 'COMPLETE';
  createdAt: number;
  updatedAt: number;
  deletedAt?: number;
};

// Workspace types
export type ConvexWorkspace = {
  _id: ConvexId<'workspaces'>;
  displayName?: string;
  logo?: string;
  inviteHash?: string;
  allowImpersonation: boolean;
  isPublicInviteLinkEnabled: boolean;
  activationStatus: 'INACTIVE' | 'PENDING_CREATION' | 'ONGOING_CREATION' | 'ACTIVE';
  metadataVersion: number;
  databaseUrl: string;
  databaseSchema: string;
  subdomain: string;
  customDomain?: string;
  isGoogleAuthEnabled: boolean;
  isPasswordAuthEnabled: boolean;
  isMicrosoftAuthEnabled: boolean;
  isCustomDomainEnabled: boolean;
  defaultRoleId?: ConvexId<'roles'>;
  defaultAgentId?: ConvexId<'agents'>;
  version?: string;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number;
};

// API function types
export type ConvexAPI = {
  // Company functions
  'companies:getCompaniesByWorkspace': {
    args: {
      workspaceId: ConvexId<'workspaces'>;
      limit?: number;
      cursor?: string;
    };
    returns: ConvexCompany[];
  };
  
  'companies:getCompanyById': {
    args: {
      companyId: ConvexId<'companies'>;
      workspaceId: ConvexId<'workspaces'>;
    };
    returns: ConvexCompany | null;
  };
  
  'companies:createCompany': {
    args: {
      workspaceId: ConvexId<'workspaces'>;
      name: string;
      domainName?: LinksMetadata;
      employees?: number;
      linkedinLink?: LinksMetadata;
      xLink?: LinksMetadata;
      annualRecurringRevenue?: CurrencyMetadata;
      address?: AddressMetadata;
      idealCustomerProfile?: boolean;
      accountOwnerId?: ConvexId<'workspaceMembers'>;
      createdBy: ActorMetadata;
    };
    returns: ConvexId<'companies'>;
  };
  
  'companies:updateCompany': {
    args: {
      companyId: ConvexId<'companies'>;
      workspaceId: ConvexId<'workspaces'>;
      name?: string;
      domainName?: LinksMetadata;
      employees?: number;
      linkedinLink?: LinksMetadata;
      xLink?: LinksMetadata;
      annualRecurringRevenue?: CurrencyMetadata;
      address?: AddressMetadata;
      idealCustomerProfile?: boolean;
      accountOwnerId?: ConvexId<'workspaceMembers'>;
    };
    returns: ConvexId<'companies'>;
  };
  
  'companies:deleteCompany': {
    args: {
      companyId: ConvexId<'companies'>;
      workspaceId: ConvexId<'workspaces'>;
    };
    returns: ConvexId<'companies'>;
  };
  
  'companies:searchCompaniesByName': {
    args: {
      workspaceId: ConvexId<'workspaces'>;
      searchTerm: string;
      limit?: number;
    };
    returns: ConvexCompany[];
  };

  // People functions
  'people:getPeopleByWorkspace': {
    args: {
      workspaceId: ConvexId<'workspaces'>;
      limit?: number;
      cursor?: string;
    };
    returns: ConvexPerson[];
  };
  
  'people:getPersonById': {
    args: {
      personId: ConvexId<'people'>;
      workspaceId: ConvexId<'workspaces'>;
    };
    returns: ConvexPerson | null;
  };
  
  'people:createPerson': {
    args: {
      workspaceId: ConvexId<'workspaces'>;
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
      city?: string;
      avatarUrl?: string;
      companyId?: ConvexId<'companies'>;
      createdBy: ActorMetadata;
    };
    returns: ConvexId<'people'>;
  };
  
  'people:updatePerson': {
    args: {
      personId: ConvexId<'people'>;
      workspaceId: ConvexId<'workspaces'>;
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
      city?: string;
      avatarUrl?: string;
      companyId?: ConvexId<'companies'>;
    };
    returns: ConvexId<'people'>;
  };
  
  'people:deletePerson': {
    args: {
      personId: ConvexId<'people'>;
      workspaceId: ConvexId<'workspaces'>;
    };
    returns: ConvexId<'people'>;
  };
  
  'people:searchPeopleByEmail': {
    args: {
      workspaceId: ConvexId<'workspaces'>;
      email: string;
    };
    returns: ConvexPerson | null;
  };
  
  'people:getPeopleByCompany': {
    args: {
      companyId: ConvexId<'companies'>;
      workspaceId: ConvexId<'workspaces'>;
      limit?: number;
    };
    returns: ConvexPerson[];
  };
};
