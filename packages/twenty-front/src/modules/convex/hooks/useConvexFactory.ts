import { ConvexClient } from 'convex/browser';
import { useMemo } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { REACT_APP_CONVEX_URL } from '~/config';

export const useConvexFactory = () => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);

  const convexClient = useMemo(() => {
    // Only create Convex client if URL is configured and we have a workspace
    if (!REACT_APP_CONVEX_URL || !currentWorkspace) {
      return null;
    }

    try {
      const client = new ConvexClient(REACT_APP_CONVEX_URL);
      
      // Store workspace context for use in queries
      (client as any)._workspaceId = currentWorkspace.id;
      
      return client;
    } catch (error) {
      console.error('Failed to create Convex client:', error);
      return null;
    }
  }, [currentWorkspace]);

  return convexClient;
};
