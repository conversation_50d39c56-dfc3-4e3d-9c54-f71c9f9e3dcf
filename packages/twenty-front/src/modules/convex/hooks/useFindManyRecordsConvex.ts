import { useCallback, useMemo } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { useConvexQuery } from '@/convex/hooks/useConvexQuery';
import { REACT_APP_USE_CONVEX } from '~/config';

type FindManyRecordsOptions = {
  skip?: boolean;
  filter?: Record<string, any>;
  orderBy?: Record<string, any>;
  limit?: number;
  onCompleted?: (records: any[]) => void;
};

type ConvexQueryFunction = {
  (args: any): any;
};

/**
 * Hook that provides Apollo-like interface for finding many records using Convex
 * This maintains compatibility with existing Twenty frontend patterns
 */
export const useFindManyRecordsConvex = <T = any>(
  objectNameSingular: string,
  options: FindManyRecordsOptions = {}
) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  
  const {
    skip = false,
    filter = {},
    orderBy = {},
    limit = 50,
    onCompleted,
  } = options;

  // Map object names to Convex query functions
  const getConvexQuery = useCallback((objectName: string): ConvexQueryFunction | null => {
    // This would be dynamically imported based on the object name
    // For now, we'll return null and implement specific objects
    switch (objectName) {
      case 'company':
        // Would import from convex functions
        return null; // Will be implemented in specific hooks
      case 'person':
        return null; // Will be implemented in specific hooks
      default:
        return null;
    }
  }, []);

  const convexQuery = getConvexQuery(objectNameSingular);

  // Prepare query arguments
  const queryArgs = useMemo(() => {
    if (!currentWorkspace) return {};
    
    return {
      workspaceId: currentWorkspace.id,
      filter,
      orderBy,
      limit,
    };
  }, [currentWorkspace, filter, orderBy, limit]);

  // Use Convex query if available and enabled
  const { data, loading, error, refetch } = useConvexQuery(
    convexQuery as any,
    queryArgs,
    { 
      skip: skip || !convexQuery || !isConvexEnabled,
      enabled: isConvexEnabled && !!convexQuery && !!currentWorkspace,
    }
  );

  // Transform data to match Apollo format
  const records = useMemo(() => {
    if (!data) return [];
    
    // Handle different Convex response formats
    if (Array.isArray(data)) {
      return data;
    }
    
    // Handle paginated responses
    if (data.page) {
      return data.page;
    }
    
    return [];
  }, [data]);

  // Call onCompleted when data is available
  useMemo(() => {
    if (records.length > 0 && onCompleted) {
      onCompleted(records);
    }
  }, [records, onCompleted]);

  return {
    records: records as T[],
    loading,
    error,
    refetch,
    totalCount: records.length, // Convex doesn't provide total count by default
    hasNextPage: false, // Would need to be implemented based on pagination
  };
};

/**
 * Utility function to check if Convex should be used for a given object
 */
export const shouldUseConvex = (objectNameSingular: string): boolean => {
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  const supportedObjects = ['company', 'person']; // Add more as implemented
  
  return isConvexEnabled && supportedObjects.includes(objectNameSingular);
};
