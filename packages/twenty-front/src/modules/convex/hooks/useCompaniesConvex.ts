import { useCallback, useMemo } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { useConvexQuery } from '@/convex/hooks/useConvexQuery';
import { useConvexMutation } from '@/convex/hooks/useConvexMutation';
import { ConvexCompany, ConvexId, ActorMetadata, LinksMetadata, CurrencyMetadata, AddressMetadata } from '@/convex/types/convex-api';
import { Company } from '@/companies/types/Company';
import { REACT_APP_USE_CONVEX } from '~/config';

// Mock Convex API functions - these would be imported from generated Convex client
const mockConvexAPI = {
  'companies:getCompaniesByWorkspace': async (args: any) => [],
  'companies:getCompanyById': async (args: any) => null,
  'companies:createCompany': async (args: any) => 'mock-id',
  'companies:updateCompany': async (args: any) => 'mock-id',
  'companies:deleteCompany': async (args: any) => 'mock-id',
  'companies:searchCompaniesByName': async (args: any) => [],
};

/**
 * Transform Convex company to Twenty company format
 */
const transformConvexCompanyToTwenty = (convexCompany: ConvexCompany): Company => {
  return {
    __typename: 'Company',
    id: convexCompany._id,
    createdAt: new Date(convexCompany.createdAt).toISOString(),
    updatedAt: new Date(convexCompany.updatedAt).toISOString(),
    deletedAt: convexCompany.deletedAt ? new Date(convexCompany.deletedAt).toISOString() : null,
    name: convexCompany.name,
    domainName: convexCompany.domainName || {
      __typename: 'Links',
      primaryLinkUrl: '',
      primaryLinkLabel: '',
      secondaryLinks: [],
    },
    address: convexCompany.address ? {
      __typename: 'Address',
      addressLine1: convexCompany.address.addressStreet1 || '',
      addressLine2: convexCompany.address.addressStreet2 || '',
      addressCity: convexCompany.address.addressCity || '',
      addressState: convexCompany.address.addressState || '',
      addressPostcode: convexCompany.address.addressPostcode || '',
      addressCountry: convexCompany.address.addressCountry || '',
      addressLat: null,
      addressLng: null,
    } : {
      __typename: 'Address',
      addressLine1: '',
      addressLine2: '',
      addressCity: '',
      addressState: '',
      addressPostcode: '',
      addressCountry: '',
      addressLat: null,
      addressLng: null,
    },
    accountOwnerId: convexCompany.accountOwnerId || null,
    position: convexCompany.position,
    linkedinLink: convexCompany.linkedinLink || {
      __typename: 'Links',
      primaryLinkUrl: '',
      primaryLinkLabel: '',
    },
    xLink: convexCompany.xLink || {
      __typename: 'Links',
      primaryLinkUrl: '',
      primaryLinkLabel: '',
    },
    employees: convexCompany.employees,
    idealCustomerProfile: convexCompany.idealCustomerProfile,
    annualRecurringRevenue: convexCompany.annualRecurringRevenue ? {
      __typename: 'Currency',
      amountMicros: convexCompany.annualRecurringRevenue.amountMicros || 0,
      currencyCode: convexCompany.annualRecurringRevenue.currencyCode || 'USD',
    } : {
      __typename: 'Currency',
      amountMicros: 0,
      currencyCode: 'USD',
    },
  };
};

/**
 * Hook for fetching companies using Convex
 */
export const useFindManyCompaniesConvex = (options: {
  skip?: boolean;
  filter?: Record<string, any>;
  orderBy?: Record<string, any>;
  limit?: number;
  onCompleted?: (companies: Company[]) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  
  const { skip = false, limit = 50, onCompleted } = options;

  // Mock query for now - would use actual Convex query
  const { data, loading, error, refetch } = useConvexQuery(
    mockConvexAPI['companies:getCompaniesByWorkspace'] as any,
    {
      workspaceId: currentWorkspace?.id as ConvexId<'workspaces'>,
      limit,
    },
    { 
      skip: skip || !isConvexEnabled || !currentWorkspace,
      enabled: isConvexEnabled && !!currentWorkspace,
    }
  );

  const companies = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    return data.map(transformConvexCompanyToTwenty);
  }, [data]);

  // Call onCompleted when data is available
  useMemo(() => {
    if (companies.length > 0 && onCompleted) {
      onCompleted(companies);
    }
  }, [companies, onCompleted]);

  return {
    records: companies,
    loading,
    error,
    refetch,
    totalCount: companies.length,
    hasNextPage: false,
  };
};

/**
 * Hook for creating companies using Convex
 */
export const useCreateCompanyConvex = (options: {
  onCompleted?: (company: Company) => void;
  onError?: (error: Error) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  
  const [createCompanyMutation, { loading, error }] = useConvexMutation(
    mockConvexAPI['companies:createCompany'] as any,
    {
      onCompleted: options.onCompleted,
      onError: options.onError,
    }
  );

  const createCompany = useCallback(
    async (input: {
      name: string;
      domainName?: string;
      employees?: number;
      linkedinUrl?: string;
      xUrl?: string;
      annualRecurringRevenue?: number;
      address?: Partial<AddressMetadata>;
      idealCustomerProfile?: boolean;
      accountOwnerId?: string;
    }) => {
      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      const convexInput = {
        workspaceId: currentWorkspace.id as ConvexId<'workspaces'>,
        name: input.name,
        domainName: input.domainName ? {
          primaryLinkUrl: input.domainName,
          primaryLinkLabel: 'Website',
        } : undefined,
        employees: input.employees,
        linkedinLink: input.linkedinUrl ? {
          primaryLinkUrl: input.linkedinUrl,
          primaryLinkLabel: 'LinkedIn',
        } : undefined,
        xLink: input.xUrl ? {
          primaryLinkUrl: input.xUrl,
          primaryLinkLabel: 'X (Twitter)',
        } : undefined,
        annualRecurringRevenue: input.annualRecurringRevenue ? {
          amountMicros: input.annualRecurringRevenue * 1000000, // Convert to micros
          currencyCode: 'USD',
        } : undefined,
        address: input.address,
        idealCustomerProfile: input.idealCustomerProfile || false,
        accountOwnerId: input.accountOwnerId as ConvexId<'workspaceMembers'>,
        createdBy: {
          source: 'manual',
          name: 'Current User', // Would get from user context
        } as ActorMetadata,
      };

      return await createCompanyMutation(convexInput);
    },
    [createCompanyMutation, currentWorkspace]
  );

  return [createCompany, { loading, error }] as const;
};

/**
 * Hook for updating companies using Convex
 */
export const useUpdateCompanyConvex = (options: {
  onCompleted?: (company: Company) => void;
  onError?: (error: Error) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  
  const [updateCompanyMutation, { loading, error }] = useConvexMutation(
    mockConvexAPI['companies:updateCompany'] as any,
    {
      onCompleted: options.onCompleted,
      onError: options.onError,
    }
  );

  const updateCompany = useCallback(
    async (companyId: string, updates: Partial<{
      name: string;
      domainName: string;
      employees: number;
      linkedinUrl: string;
      xUrl: string;
      annualRecurringRevenue: number;
      address: Partial<AddressMetadata>;
      idealCustomerProfile: boolean;
      accountOwnerId: string;
    }>) => {
      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      const convexUpdates: any = {
        companyId: companyId as ConvexId<'companies'>,
        workspaceId: currentWorkspace.id as ConvexId<'workspaces'>,
      };

      if (updates.name !== undefined) convexUpdates.name = updates.name;
      if (updates.employees !== undefined) convexUpdates.employees = updates.employees;
      if (updates.idealCustomerProfile !== undefined) convexUpdates.idealCustomerProfile = updates.idealCustomerProfile;
      if (updates.accountOwnerId !== undefined) convexUpdates.accountOwnerId = updates.accountOwnerId as ConvexId<'workspaceMembers'>;
      
      if (updates.domainName !== undefined) {
        convexUpdates.domainName = {
          primaryLinkUrl: updates.domainName,
          primaryLinkLabel: 'Website',
        };
      }
      
      if (updates.linkedinUrl !== undefined) {
        convexUpdates.linkedinLink = {
          primaryLinkUrl: updates.linkedinUrl,
          primaryLinkLabel: 'LinkedIn',
        };
      }
      
      if (updates.xUrl !== undefined) {
        convexUpdates.xLink = {
          primaryLinkUrl: updates.xUrl,
          primaryLinkLabel: 'X (Twitter)',
        };
      }
      
      if (updates.annualRecurringRevenue !== undefined) {
        convexUpdates.annualRecurringRevenue = {
          amountMicros: updates.annualRecurringRevenue * 1000000,
          currencyCode: 'USD',
        };
      }
      
      if (updates.address !== undefined) {
        convexUpdates.address = updates.address;
      }

      return await updateCompanyMutation(convexUpdates);
    },
    [updateCompanyMutation, currentWorkspace]
  );

  return [updateCompany, { loading, error }] as const;
};

/**
 * Hook for deleting companies using Convex
 */
export const useDeleteCompanyConvex = (options: {
  onCompleted?: () => void;
  onError?: (error: Error) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  
  const [deleteCompanyMutation, { loading, error }] = useConvexMutation(
    mockConvexAPI['companies:deleteCompany'] as any,
    {
      onCompleted: options.onCompleted,
      onError: options.onError,
    }
  );

  const deleteCompany = useCallback(
    async (companyId: string) => {
      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      return await deleteCompanyMutation({
        companyId: companyId as ConvexId<'companies'>,
        workspaceId: currentWorkspace.id as ConvexId<'workspaces'>,
      });
    },
    [deleteCompanyMutation, currentWorkspace]
  );

  return [deleteCompany, { loading, error }] as const;
};
