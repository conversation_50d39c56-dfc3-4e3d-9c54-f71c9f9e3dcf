import { useMutation } from 'convex/react';
import { FunctionReference } from 'convex/server';
import { useCallback } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { REACT_APP_USE_CONVEX } from '~/config';

type ConvexMutationOptions = {
  onCompleted?: (data: any) => void;
  onError?: (error: Error) => void;
};

/**
 * Custom hook for Convex mutations that automatically includes workspace context
 * and provides Apollo-like interface for compatibility
 */
export const useConvexMutation = <Args extends Record<string, any>, ReturnType>(
  mutation: FunctionReference<'mutation', 'public', Args, ReturnType>,
  options: ConvexMutationOptions = {}
) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  const convexMutation = useMutation(mutation);
  
  const { onCompleted, onError } = options;

  const mutate = useCallback(
    async (args: Args) => {
      if (!isConvexEnabled) {
        throw new Error('Convex is not enabled');
      }

      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      try {
        // Automatically inject workspaceId if the mutation expects it
        const mutationArgs = { ...args };
        if ('workspaceId' in mutationArgs || mutation.toString().includes('workspaceId')) {
          mutationArgs.workspaceId = currentWorkspace.id;
        }

        const result = await convexMutation(mutationArgs);
        
        if (onCompleted) {
          onCompleted(result);
        }
        
        return result;
      } catch (error) {
        if (onError) {
          onError(error as Error);
        }
        throw error;
      }
    },
    [convexMutation, currentWorkspace, isConvexEnabled, mutation, onCompleted, onError]
  );

  return [
    mutate,
    {
      loading: false, // Convex mutations don't have loading state like Apollo
      error: null,    // Errors are thrown directly
    },
  ] as const;
};

// Re-export for convenience
export { useMutation as useConvexMutationDirect } from 'convex/react';
