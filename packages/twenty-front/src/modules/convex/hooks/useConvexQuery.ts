import { useQuery } from 'convex/react';
import { FunctionReference } from 'convex/server';
import { useMemo } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { REACT_APP_USE_CONVEX } from '~/config';

type ConvexQueryOptions = {
  skip?: boolean;
  enabled?: boolean;
};

/**
 * Custom hook for Convex queries that automatically includes workspace context
 * and provides fallback behavior when Convex is disabled
 */
export const useConvexQuery = <Args extends Record<string, any>, ReturnType>(
  query: FunctionReference<'query', 'public', Args, ReturnType>,
  args: Args,
  options: ConvexQueryOptions = {}
) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  
  const { skip = false, enabled = true } = options;
  
  // Automatically inject workspaceId if the query expects it and we have a workspace
  const queryArgs = useMemo(() => {
    if (!currentWorkspace) return args;
    
    // Check if the query expects a workspaceId parameter
    const argsWithWorkspace = { ...args };
    if ('workspaceId' in argsWithWorkspace || query.toString().includes('workspaceId')) {
      argsWithWorkspace.workspaceId = currentWorkspace.id;
    }
    
    return argsWithWorkspace;
  }, [args, currentWorkspace, query]);

  // Use Convex query if enabled and not skipped
  const shouldQuery = isConvexEnabled && enabled && !skip && currentWorkspace;
  
  const result = useQuery(
    shouldQuery ? query : undefined,
    shouldQuery ? queryArgs : undefined
  );

  return {
    data: result,
    loading: result === undefined && shouldQuery,
    error: null, // Convex handles errors differently
    refetch: () => {
      // Convex queries are reactive, so no explicit refetch needed
      // This is here for compatibility with Apollo patterns
    },
  };
};

// Re-export for convenience
export { useQuery as useConvexQueryDirect } from 'convex/react';
