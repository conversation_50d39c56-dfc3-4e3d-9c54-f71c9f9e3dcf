import { useCallback, useMemo } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { useConvexQuery } from '@/convex/hooks/useConvexQuery';
import { useConvexMutation } from '@/convex/hooks/useConvexMutation';
import { ConvexPerson, ConvexId, ActorMetadata } from '@/convex/types/convex-api';
import { Person } from '@/people/types/Person';
import { REACT_APP_USE_CONVEX } from '~/config';

// Mock Convex API functions - these would be imported from generated Convex client
const mockConvexAPI = {
  'people:getPeopleByWorkspace': async (args: any) => [],
  'people:getPersonById': async (args: any) => null,
  'people:createPerson': async (args: any) => 'mock-id',
  'people:updatePerson': async (args: any) => 'mock-id',
  'people:deletePerson': async (args: any) => 'mock-id',
  'people:searchPeopleByEmail': async (args: any) => null,
  'people:getPeopleByCompany': async (args: any) => [],
};

/**
 * Transform Convex person to Twenty person format
 */
const transformConvexPersonToTwenty = (convexPerson: ConvexPerson): Person => {
  return {
    __typename: 'Person',
    id: convexPerson._id,
    createdAt: new Date(convexPerson.createdAt).toISOString(),
    updatedAt: new Date(convexPerson.updatedAt).toISOString(),
    deletedAt: convexPerson.deletedAt ? new Date(convexPerson.deletedAt).toISOString() : null,
    name: {
      __typename: 'FullName',
      firstName: convexPerson.firstName || '',
      lastName: convexPerson.lastName || '',
    },
    avatarUrl: convexPerson.avatarUrl,
    jobTitle: '', // Not in Convex schema yet
    linkedinLink: {
      __typename: 'Links',
      primaryLinkUrl: '',
      primaryLinkLabel: '',
    },
    xLink: {
      __typename: 'Links',
      primaryLinkUrl: '',
      primaryLinkLabel: '',
    },
    city: convexPerson.city || '',
    email: convexPerson.email || '',
    phone: convexPerson.phone || '',
    companyId: convexPerson.companyId,
    position: convexPerson.position,
    links: {
      __typename: 'Links',
      primaryLinkUrl: '',
      primaryLinkLabel: '',
    },
  };
};

/**
 * Hook for fetching people using Convex
 */
export const useFindManyPeopleConvex = (options: {
  skip?: boolean;
  filter?: Record<string, any>;
  orderBy?: Record<string, any>;
  limit?: number;
  onCompleted?: (people: Person[]) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  
  const { skip = false, limit = 50, onCompleted } = options;

  // Mock query for now - would use actual Convex query
  const { data, loading, error, refetch } = useConvexQuery(
    mockConvexAPI['people:getPeopleByWorkspace'] as any,
    {
      workspaceId: currentWorkspace?.id as ConvexId<'workspaces'>,
      limit,
    },
    { 
      skip: skip || !isConvexEnabled || !currentWorkspace,
      enabled: isConvexEnabled && !!currentWorkspace,
    }
  );

  const people = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    return data.map(transformConvexPersonToTwenty);
  }, [data]);

  // Call onCompleted when data is available
  useMemo(() => {
    if (people.length > 0 && onCompleted) {
      onCompleted(people);
    }
  }, [people, onCompleted]);

  return {
    records: people,
    loading,
    error,
    refetch,
    totalCount: people.length,
    hasNextPage: false,
  };
};

/**
 * Hook for fetching people by company using Convex
 */
export const useFindPeopleByCompanyConvex = (
  companyId: string,
  options: {
    skip?: boolean;
    limit?: number;
    onCompleted?: (people: Person[]) => void;
  } = {}
) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  
  const { skip = false, limit = 50, onCompleted } = options;

  const { data, loading, error, refetch } = useConvexQuery(
    mockConvexAPI['people:getPeopleByCompany'] as any,
    {
      companyId: companyId as ConvexId<'companies'>,
      workspaceId: currentWorkspace?.id as ConvexId<'workspaces'>,
      limit,
    },
    { 
      skip: skip || !isConvexEnabled || !currentWorkspace || !companyId,
      enabled: isConvexEnabled && !!currentWorkspace && !!companyId,
    }
  );

  const people = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    return data.map(transformConvexPersonToTwenty);
  }, [data]);

  // Call onCompleted when data is available
  useMemo(() => {
    if (people.length > 0 && onCompleted) {
      onCompleted(people);
    }
  }, [people, onCompleted]);

  return {
    records: people,
    loading,
    error,
    refetch,
    totalCount: people.length,
    hasNextPage: false,
  };
};

/**
 * Hook for creating people using Convex
 */
export const useCreatePersonConvex = (options: {
  onCompleted?: (person: Person) => void;
  onError?: (error: Error) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  
  const [createPersonMutation, { loading, error }] = useConvexMutation(
    mockConvexAPI['people:createPerson'] as any,
    {
      onCompleted: options.onCompleted,
      onError: options.onError,
    }
  );

  const createPerson = useCallback(
    async (input: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
      city?: string;
      avatarUrl?: string;
      companyId?: string;
    }) => {
      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      const convexInput = {
        workspaceId: currentWorkspace.id as ConvexId<'workspaces'>,
        firstName: input.firstName,
        lastName: input.lastName,
        email: input.email,
        phone: input.phone,
        city: input.city,
        avatarUrl: input.avatarUrl,
        companyId: input.companyId as ConvexId<'companies'>,
        createdBy: {
          source: 'manual',
          name: 'Current User', // Would get from user context
        } as ActorMetadata,
      };

      return await createPersonMutation(convexInput);
    },
    [createPersonMutation, currentWorkspace]
  );

  return [createPerson, { loading, error }] as const;
};

/**
 * Hook for updating people using Convex
 */
export const useUpdatePersonConvex = (options: {
  onCompleted?: (person: Person) => void;
  onError?: (error: Error) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  
  const [updatePersonMutation, { loading, error }] = useConvexMutation(
    mockConvexAPI['people:updatePerson'] as any,
    {
      onCompleted: options.onCompleted,
      onError: options.onError,
    }
  );

  const updatePerson = useCallback(
    async (personId: string, updates: Partial<{
      firstName: string;
      lastName: string;
      email: string;
      phone: string;
      city: string;
      avatarUrl: string;
      companyId: string;
    }>) => {
      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      const convexUpdates: any = {
        personId: personId as ConvexId<'people'>,
        workspaceId: currentWorkspace.id as ConvexId<'workspaces'>,
      };

      if (updates.firstName !== undefined) convexUpdates.firstName = updates.firstName;
      if (updates.lastName !== undefined) convexUpdates.lastName = updates.lastName;
      if (updates.email !== undefined) convexUpdates.email = updates.email;
      if (updates.phone !== undefined) convexUpdates.phone = updates.phone;
      if (updates.city !== undefined) convexUpdates.city = updates.city;
      if (updates.avatarUrl !== undefined) convexUpdates.avatarUrl = updates.avatarUrl;
      if (updates.companyId !== undefined) convexUpdates.companyId = updates.companyId as ConvexId<'companies'>;

      return await updatePersonMutation(convexUpdates);
    },
    [updatePersonMutation, currentWorkspace]
  );

  return [updatePerson, { loading, error }] as const;
};

/**
 * Hook for deleting people using Convex
 */
export const useDeletePersonConvex = (options: {
  onCompleted?: () => void;
  onError?: (error: Error) => void;
} = {}) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  
  const [deletePersonMutation, { loading, error }] = useConvexMutation(
    mockConvexAPI['people:deletePerson'] as any,
    {
      onCompleted: options.onCompleted,
      onError: options.onError,
    }
  );

  const deletePerson = useCallback(
    async (personId: string) => {
      if (!currentWorkspace) {
        throw new Error('No workspace context available');
      }

      return await deletePersonMutation({
        personId: personId as ConvexId<'people'>,
        workspaceId: currentWorkspace.id as ConvexId<'workspaces'>,
      });
    },
    [deletePersonMutation, currentWorkspace]
  );

  return [deletePerson, { loading, error }] as const;
};

/**
 * Hook for searching people by email using Convex
 */
export const useSearchPersonByEmailConvex = (
  email: string,
  options: {
    skip?: boolean;
    onCompleted?: (person: Person | null) => void;
  } = {}
) => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  
  const { skip = false, onCompleted } = options;

  const { data, loading, error, refetch } = useConvexQuery(
    mockConvexAPI['people:searchPeopleByEmail'] as any,
    {
      workspaceId: currentWorkspace?.id as ConvexId<'workspaces'>,
      email,
    },
    { 
      skip: skip || !isConvexEnabled || !currentWorkspace || !email,
      enabled: isConvexEnabled && !!currentWorkspace && !!email,
    }
  );

  const person = useMemo(() => {
    if (!data) return null;
    return transformConvexPersonToTwenty(data);
  }, [data]);

  // Call onCompleted when data is available
  useMemo(() => {
    if (person && onCompleted) {
      onCompleted(person);
    }
  }, [person, onCompleted]);

  return {
    person,
    loading,
    error,
    refetch,
  };
};
