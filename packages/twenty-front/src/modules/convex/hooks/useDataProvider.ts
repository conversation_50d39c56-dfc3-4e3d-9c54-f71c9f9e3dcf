import { useMemo } from 'react';

import { useFindManyRecords } from '@/object-record/hooks/useFindManyRecords';
import { useFindManyCompaniesConvex } from '@/convex/hooks/useCompaniesConvex';
import { useFindManyPeopleConvex } from '@/convex/hooks/usePeopleConvex';
import { shouldUseConvex } from '@/convex/hooks/useFindManyRecordsConvex';
import { REACT_APP_USE_CONVEX } from '~/config';

type DataProviderOptions = {
  skip?: boolean;
  filter?: Record<string, any>;
  orderBy?: Record<string, any>;
  limit?: number;
  onCompleted?: (records: any[]) => void;
};

/**
 * Universal hook that automatically chooses between GraphQL and Convex
 * based on configuration and object type support
 */
export const useFindManyRecordsUniversal = <T = any>(
  objectNameSingular: string,
  recordGqlFields?: any,
  options: DataProviderOptions = {}
) => {
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  const useConvexForObject = shouldUseConvex(objectNameSingular);

  // GraphQL implementation (existing)
  const graphqlResult = useFindManyRecords<T>({
    skip: isConvexEnabled && useConvexForObject,
    objectNameSingular,
    recordGqlFields,
    ...options,
  });

  // Convex implementations
  const convexCompaniesResult = useFindManyCompaniesConvex({
    skip: !isConvexEnabled || !useConvexForObject || objectNameSingular !== 'company',
    ...options,
  });

  const convexPeopleResult = useFindManyPeopleConvex({
    skip: !isConvexEnabled || !useConvexForObject || objectNameSingular !== 'person',
    ...options,
  });

  // Return the appropriate result based on configuration
  return useMemo(() => {
    if (isConvexEnabled && useConvexForObject) {
      switch (objectNameSingular) {
        case 'company':
          return convexCompaniesResult;
        case 'person':
          return convexPeopleResult;
        default:
          // Fallback to GraphQL for unsupported objects
          return graphqlResult;
      }
    }
    
    // Default to GraphQL
    return graphqlResult;
  }, [
    isConvexEnabled,
    useConvexForObject,
    objectNameSingular,
    graphqlResult,
    convexCompaniesResult,
    convexPeopleResult,
  ]);
};

/**
 * Hook to check which data provider is being used
 */
export const useDataProviderInfo = (objectNameSingular: string) => {
  const isConvexEnabled = REACT_APP_USE_CONVEX === 'true';
  const useConvexForObject = shouldUseConvex(objectNameSingular);

  return useMemo(() => ({
    isConvexEnabled,
    useConvexForObject,
    provider: isConvexEnabled && useConvexForObject ? 'convex' : 'graphql',
    supportedInConvex: ['company', 'person'].includes(objectNameSingular),
  }), [isConvexEnabled, useConvexForObject, objectNameSingular]);
};

/**
 * Debug component to show which data provider is being used
 */
export const DataProviderDebugInfo = ({ objectNameSingular }: { objectNameSingular: string }) => {
  const info = useDataProviderInfo(objectNameSingular);
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: info.provider === 'convex' ? '#4CAF50' : '#2196F3',
      color: 'white',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 9999,
      boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
    }}>
      <div><strong>{objectNameSingular}</strong></div>
      <div>Provider: {info.provider.toUpperCase()}</div>
      <div>Convex Enabled: {info.isConvexEnabled ? 'Yes' : 'No'}</div>
      <div>Supported: {info.supportedInConvex ? 'Yes' : 'No'}</div>
    </div>
  );
};
