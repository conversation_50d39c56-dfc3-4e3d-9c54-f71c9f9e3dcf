// Components
export { ConvexProvider } from './components/ConvexProvider';
export { ConvexDemo } from './components/ConvexDemo';

// Hooks
export { useConvexFactory } from './hooks/useConvexFactory';
export { useConvexQuery } from './hooks/useConvexQuery';
export { useConvexMutation } from './hooks/useConvexMutation';
export { useFindManyRecordsConvex, shouldUseConvex } from './hooks/useFindManyRecordsConvex';
export { 
  useFindManyRecordsUniversal, 
  useDataProviderInfo, 
  DataProviderDebugInfo 
} from './hooks/useDataProvider';

// Company hooks
export {
  useFindManyCompaniesConvex,
  useCreateCompanyConvex,
  useUpdateCompanyConvex,
  useDeleteCompanyConvex,
} from './hooks/useCompaniesConvex';

// People hooks
export {
  useFindManyPeopleConvex,
  useFindPeopleByCompanyConvex,
  useCreatePersonConvex,
  useUpdatePersonConvex,
  useDeletePersonConvex,
  useSearchPersonByEmailConvex,
} from './hooks/usePeopleConvex';

// Types
export type {
  ConvexId,
  ActorMetadata,
  LinksMetadata,
  CurrencyMetadata,
  AddressMetadata,
  ConvexCompany,
  ConvexPerson,
  ConvexUser,
  ConvexWorkspace,
  ConvexAPI,
} from './types/convex-api';
