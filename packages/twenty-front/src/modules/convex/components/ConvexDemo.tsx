import { useState } from 'react';
import { useRecoilValue } from 'recoil';

import { currentWorkspaceState } from '@/auth/states/currentWorkspaceState';
import { useFindManyCompaniesConvex, useCreateCompanyConvex } from '@/convex/hooks/useCompaniesConvex';
import { useFindManyPeopleConvex, useCreatePersonConvex } from '@/convex/hooks/usePeopleConvex';
import { DataProviderDebugInfo, useDataProviderInfo } from '@/convex/hooks/useDataProvider';
import { REACT_APP_USE_CONVEX, REACT_APP_CONVEX_URL } from '~/config';

export const ConvexDemo = () => {
  const currentWorkspace = useRecoilValue(currentWorkspaceState);
  const [newCompanyName, setNewCompanyName] = useState('');
  const [newPersonName, setNewPersonName] = useState('');
  
  const companyInfo = useDataProviderInfo('company');
  const personInfo = useDataProviderInfo('person');

  // Companies
  const { 
    records: companies, 
    loading: companiesLoading, 
    error: companiesError 
  } = useFindManyCompaniesConvex({
    limit: 10,
  });

  const [createCompany, { loading: createCompanyLoading }] = useCreateCompanyConvex({
    onCompleted: () => {
      setNewCompanyName('');
      console.log('Company created successfully');
    },
    onError: (error) => {
      console.error('Failed to create company:', error);
    },
  });

  // People
  const { 
    records: people, 
    loading: peopleLoading, 
    error: peopleError 
  } = useFindManyPeopleConvex({
    limit: 10,
  });

  const [createPerson, { loading: createPersonLoading }] = useCreatePersonConvex({
    onCompleted: () => {
      setNewPersonName('');
      console.log('Person created successfully');
    },
    onError: (error) => {
      console.error('Failed to create person:', error);
    },
  });

  const handleCreateCompany = async () => {
    if (!newCompanyName.trim()) return;
    
    try {
      await createCompany({
        name: newCompanyName,
        idealCustomerProfile: false,
      });
    } catch (error) {
      console.error('Error creating company:', error);
    }
  };

  const handleCreatePerson = async () => {
    if (!newPersonName.trim()) return;
    
    const [firstName, ...lastNameParts] = newPersonName.split(' ');
    const lastName = lastNameParts.join(' ');
    
    try {
      await createPerson({
        firstName,
        lastName,
        email: `${firstName.toLowerCase()}@example.com`,
      });
    } catch (error) {
      console.error('Error creating person:', error);
    }
  };

  if (!currentWorkspace) {
    return (
      <div style={{ padding: '20px' }}>
        <h2>Convex Demo</h2>
        <p>Please select a workspace to test Convex integration.</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <DataProviderDebugInfo objectNameSingular="company" />
      
      <h2>🚀 Twenty Convex Integration Demo</h2>
      
      {/* Configuration Info */}
      <div style={{ 
        background: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>Configuration</h3>
        <p><strong>Convex Enabled:</strong> {REACT_APP_USE_CONVEX}</p>
        <p><strong>Convex URL:</strong> {REACT_APP_CONVEX_URL}</p>
        <p><strong>Current Workspace:</strong> {currentWorkspace.displayName} ({currentWorkspace.id})</p>
        <p><strong>Company Provider:</strong> {companyInfo.provider.toUpperCase()}</p>
        <p><strong>Person Provider:</strong> {personInfo.provider.toUpperCase()}</p>
      </div>

      {/* Companies Section */}
      <div style={{ marginBottom: '30px' }}>
        <h3>🏢 Companies</h3>
        
        {/* Create Company */}
        <div style={{ marginBottom: '15px' }}>
          <input
            type="text"
            value={newCompanyName}
            onChange={(e) => setNewCompanyName(e.target.value)}
            placeholder="Enter company name"
            style={{ 
              padding: '8px', 
              marginRight: '10px', 
              border: '1px solid #ddd',
              borderRadius: '4px',
              width: '200px'
            }}
          />
          <button
            onClick={handleCreateCompany}
            disabled={createCompanyLoading || !newCompanyName.trim()}
            style={{
              padding: '8px 16px',
              background: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            {createCompanyLoading ? 'Creating...' : 'Create Company'}
          </button>
        </div>

        {/* Companies List */}
        {companiesLoading && <p>Loading companies...</p>}
        {companiesError && <p style={{ color: 'red' }}>Error: {companiesError.message}</p>}
        
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '4px', 
          maxHeight: '200px', 
          overflow: 'auto' 
        }}>
          {companies.length === 0 ? (
            <p style={{ padding: '15px', margin: 0, color: '#666' }}>
              No companies found. Create one above to test Convex!
            </p>
          ) : (
            companies.map((company) => (
              <div 
                key={company.id} 
                style={{ 
                  padding: '10px', 
                  borderBottom: '1px solid #eee',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <div>
                  <strong>{company.name}</strong>
                  {company.employees && <span> ({company.employees} employees)</span>}
                </div>
                <small style={{ color: '#666' }}>
                  {new Date(company.createdAt).toLocaleDateString()}
                </small>
              </div>
            ))
          )}
        </div>
      </div>

      {/* People Section */}
      <div style={{ marginBottom: '30px' }}>
        <h3>👤 People</h3>
        
        {/* Create Person */}
        <div style={{ marginBottom: '15px' }}>
          <input
            type="text"
            value={newPersonName}
            onChange={(e) => setNewPersonName(e.target.value)}
            placeholder="Enter person name"
            style={{ 
              padding: '8px', 
              marginRight: '10px', 
              border: '1px solid #ddd',
              borderRadius: '4px',
              width: '200px'
            }}
          />
          <button
            onClick={handleCreatePerson}
            disabled={createPersonLoading || !newPersonName.trim()}
            style={{
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            {createPersonLoading ? 'Creating...' : 'Create Person'}
          </button>
        </div>

        {/* People List */}
        {peopleLoading && <p>Loading people...</p>}
        {peopleError && <p style={{ color: 'red' }}>Error: {peopleError.message}</p>}
        
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '4px', 
          maxHeight: '200px', 
          overflow: 'auto' 
        }}>
          {people.length === 0 ? (
            <p style={{ padding: '15px', margin: 0, color: '#666' }}>
              No people found. Create one above to test Convex!
            </p>
          ) : (
            people.map((person) => (
              <div 
                key={person.id} 
                style={{ 
                  padding: '10px', 
                  borderBottom: '1px solid #eee',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <div>
                  <strong>{person.name.firstName} {person.name.lastName}</strong>
                  {person.email && <span> ({person.email})</span>}
                  {person.city && <span> - {person.city}</span>}
                </div>
                <small style={{ color: '#666' }}>
                  {new Date(person.createdAt).toLocaleDateString()}
                </small>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Instructions */}
      <div style={{ 
        background: '#e7f3ff', 
        padding: '15px', 
        borderRadius: '8px',
        border: '1px solid #b3d9ff'
      }}>
        <h4>🧪 Testing Instructions</h4>
        <ol>
          <li>Set <code>REACT_APP_USE_CONVEX=true</code> in your environment to enable Convex</li>
          <li>Make sure the Convex backend is running (<code>npm run dev</code> in <code>packages/twenty-convex</code>)</li>
          <li>Create companies and people using the forms above</li>
          <li>Watch the debug info in the top-right corner to see which provider is being used</li>
          <li>Check the browser console for detailed logs</li>
        </ol>
        
        <p><strong>Note:</strong> This demo uses mock Convex functions. To see real data, you need to:</p>
        <ul>
          <li>Connect the frontend to the actual Convex functions</li>
          <li>Import the generated Convex API client</li>
          <li>Replace the mock functions with real Convex queries and mutations</li>
        </ul>
      </div>
    </div>
  );
};
