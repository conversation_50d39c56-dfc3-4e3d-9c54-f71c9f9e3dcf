import { ConvexProvider as ConvexProviderBase } from 'convex/react';
import { ReactNode } from 'react';

import { useConvexFactory } from '@/convex/hooks/useConvexFactory';

type ConvexProviderProps = {
  children: ReactNode;
};

export const ConvexProvider = ({ children }: ConvexProviderProps) => {
  const convexClient = useConvexFactory();

  if (!convexClient) {
    // Return children without Convex provider if client is not available
    // This allows graceful fallback to GraphQL
    return <>{children}</>;
  }

  return (
    <ConvexProviderBase client={convexClient}>
      {children}
    </ConvexProviderBase>
  );
};
