import { ConvexDemo } from '@/convex/components/ConvexDemo';
import { SubMenuTopBarContainer } from '@/ui/layout/page/components/SubMenuTopBarContainer';
import { SettingsPageContainer } from '@/settings/components/SettingsPageContainer';
import { getSettingsPath } from '@/settings/utils/getSettingsPath';
import { SettingsPath } from '@/types/SettingsPath';
import { H3Title } from 'twenty-ui/display';

export const ConvexDemoPage = () => {
  return (
    <SubMenuTopBarContainer
      title={<H3Title title="Convex Demo" />}
      links={[
        {
          children: 'Workspace',
          href: getSettingsPath(SettingsPath.Workspace),
        },
        { children: 'Convex Demo' },
      ]}
    >
      <SettingsPageContainer>
        <ConvexDemo />
      </SettingsPageContainer>
    </SubMenuTopBarContainer>
  );
};
