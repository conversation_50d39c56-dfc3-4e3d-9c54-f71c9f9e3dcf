{"name": "twenty-front", "private": true, "type": "module", "scripts": {"build": "NODE_ENV=production VITE_DISABLE_TYPESCRIPT_CHECKER=true VITE_DISABLE_ESLINT_CHECKER=true NODE_OPTIONS=--max-old-space-size=8192 npx vite build && sh ./scripts/inject-runtime-env.sh", "build:sourcemaps": "NODE_ENV=production VITE_BUILD_SOURCEMAP=true VITE_DISABLE_TYPESCRIPT_CHECKER=true VITE_DISABLE_ESLINT_CHECKER=true NODE_OPTIONS=--max-old-space-size=8192 npx vite build && sh ./scripts/inject-runtime-env.sh", "start:prod": "NODE_ENV=production npx serve -s build", "tsup": "npx tsup"}, "engines": {"node": "^22.12.0", "npm": "please-use-yarn", "yarn": "^4.0.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "msw": {"workerDirectory": "public"}, "dependencies": {"@blocknote/mantine": "^0.31.1", "@blocknote/react": "^0.31.1", "@blocknote/xl-docx-exporter": "^0.31.1", "@blocknote/xl-pdf-exporter": "^0.31.1", "@cyntler/react-doc-viewer": "^1.17.0", "@lingui/core": "^5.1.2", "@lingui/detect-locale": "^5.2.0", "@lingui/react": "^5.1.2", "@nivo/calendar": "^0.87.0", "@nivo/core": "^0.87.0", "@nivo/line": "^0.87.0", "@react-pdf/renderer": "^4.1.6", "@scalar/api-reference-react": "^0.4.36", "@tiptap/core": "^2.10.4", "@tiptap/extension-document": "^2.10.4", "@tiptap/extension-hard-break": "^2.10.4", "@tiptap/extension-history": "^2.10.4", "@tiptap/extension-paragraph": "^2.10.4", "@tiptap/extension-placeholder": "^2.10.4", "@tiptap/extension-text": "^2.10.4", "@tiptap/extension-text-style": "^2.10.4", "@tiptap/react": "^2.10.4", "@xyflow/react": "^12.4.2", "buffer": "^6.0.3", "convex": "^1.25.2", "docx": "^9.1.0", "file-saver": "^2.0.5", "transliteration": "^2.3.5", "twenty-shared": "workspace:*", "twenty-ui": "workspace:*"}, "devDependencies": {"@lingui/cli": "^5.1.2", "@lingui/swc-plugin": "^5.1.0", "@lingui/vite-plugin": "^5.1.2", "@types/file-saver": "^2", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/experimental-utils": "^5.62.0", "@typescript-eslint/parser": "6.21.0", "@typescript-eslint/utils": "6.21.0", "eslint": "^8.53.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-lingui": "^0.9.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-project-structure": "^3.9.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-unicorn": "^51.0.1", "eslint-plugin-unused-imports": "^3.0.0", "optionator": "^0.9.1", "rollup-plugin-visualizer": "^5.14.0"}}