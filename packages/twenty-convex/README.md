# Twenty Convex Backend

This package contains a **Convex-powered backend** for the Twenty CRM system, providing a modern, real-time alternative to the traditional PostgreSQL + TypeORM setup.

## 🚀 What is Convex?

[Convex](https://convex.dev) is a reactive, real-time database where:
- **Queries are TypeScript functions** running in the database
- **Automatic real-time updates** to connected clients
- **Document-relational model** (JSON documents with relationships)
- **Built-in authentication, file storage, and scheduling**
- **Strong transactional guarantees** with ACID compliance
- **No SQL needed** - everything is TypeScript

## 📁 Project Structure

```
packages/twenty-convex/
├── convex/
│   ├── schema.ts           # Database schema definitions
│   ├── users.ts           # User management functions
│   ├── workspaces.ts      # Workspace management functions
│   ├── companies.ts       # Company CRUD operations
│   ├── people.ts          # People CRUD operations
│   └── _generated/        # Auto-generated types
├── test-client.html       # Interactive web test client
├── test-script.js         # Automated test script
└── README.md             # This file
```

## 🛠️ Setup & Installation

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Convex CLI (installed globally)

### Installation
```bash
# Navigate to the convex package
cd packages/twenty-convex

# Install dependencies
npm install

# Start Convex development server
npm run dev
```

### Environment Configuration
The Convex URL is automatically configured in `.env.local` when you run `convex dev`.

## 🧪 Testing the Backend

### Option 1: Automated Test Script
```bash
npm test
```

This runs a comprehensive test suite that:
- ✅ Creates users, workspaces, companies, and people
- ✅ Tests all CRUD operations
- ✅ Validates multi-tenancy (workspace isolation)
- ✅ Tests search functionality
- ✅ Verifies relationships between entities

### Option 2: Interactive Web Client
Open `test-client.html` in your browser for a visual interface to:
- Create and manage entities
- See real-time updates
- Test the API interactively

## 📊 Schema Overview

### Core Entities

#### Users
- Global user accounts
- Authentication and profile data
- Can belong to multiple workspaces

#### Workspaces  
- Multi-tenant isolation
- Workspace-specific settings
- Subdomain and custom domain support

#### Companies (Workspace-scoped)
- CRM company records
- Rich metadata (domain, employees, revenue)
- Address and social links

#### People (Workspace-scoped)
- Individual contacts
- Associated with companies
- Contact information and metadata

### Multi-tenancy
All workspace-scoped entities include a `workspaceId` field for proper isolation. The schema enforces workspace boundaries through:
- Indexed queries by workspace
- Access control in mutations
- Soft deletion with workspace context

## 🔄 Real-time Features

Convex provides automatic real-time updates:

```typescript
// Subscribe to companies in a workspace
const companies = useQuery(api.companies.getCompaniesByWorkspace, {
  workspaceId: "workspace123"
});

// Automatically updates when data changes!
```

## 🔍 Key Functions

### User Management
- `createUser` - Create new user account
- `getUserByEmail` - Find user by email
- `getUserWorkspaces` - Get user's workspaces
- `addUserToWorkspace` - Add user to workspace

### Workspace Management  
- `createWorkspace` - Create new workspace
- `getWorkspaceBySubdomain` - Find by subdomain
- `getWorkspaceStats` - Get member/company/people counts
- `generateInviteHash` - Create invite links

### Company Operations
- `createCompany` - Create company record
- `getCompaniesByWorkspace` - List companies (paginated)
- `searchCompaniesByName` - Search functionality
- `updateCompany` - Update company data

### People Operations
- `createPerson` - Create person record
- `getPeopleByWorkspace` - List people (paginated)
- `searchPeopleByEmail` - Find by email
- `assignPersonToCompany` - Link person to company

## 🔐 Security & Access Control

- **Workspace Isolation**: All queries validate workspace access
- **Soft Deletion**: Records are marked as deleted, not removed
- **Input Validation**: All mutations validate required fields
- **Type Safety**: Full TypeScript support with generated types

## 🚀 Performance Features

- **Automatic Indexing**: Optimized queries with proper indexes
- **Pagination Support**: Cursor-based pagination for large datasets
- **Real-time Subscriptions**: Efficient change notifications
- **Caching**: Built-in query result caching

## 🔄 Migration from PostgreSQL

### Benefits of Convex Migration

1. **Real-time by Default**: No need for complex GraphQL subscriptions
2. **Type Safety**: End-to-end TypeScript without ORM complexity
3. **Simplified Architecture**: No database administration needed
4. **Better Developer Experience**: Hot reload, automatic migrations
5. **Built-in Features**: Authentication, file storage, scheduling

### Migration Strategy

1. **Parallel Development**: Run Convex alongside existing PostgreSQL
2. **Gradual Migration**: Move entities one by one
3. **Data Synchronization**: Sync data during transition period
4. **Client Updates**: Update frontend to use Convex queries
5. **Full Cutover**: Switch to Convex as primary database

## 📈 Next Steps

1. **Authentication Integration**: Connect with Twenty's auth system
2. **File Storage**: Implement file uploads with Convex storage
3. **Advanced Queries**: Add complex filtering and sorting
4. **Background Jobs**: Use Convex scheduling for async tasks
5. **Production Deployment**: Deploy to Convex production environment

## 🤝 Contributing

To add new entities or modify existing ones:

1. Update `schema.ts` with new table definitions
2. Create corresponding function files (e.g., `tasks.ts`)
3. Add proper indexes for query performance
4. Include workspace isolation for multi-tenancy
5. Add tests to the test script

## 📚 Resources

- [Convex Documentation](https://docs.convex.dev/)
- [Convex TypeScript Guide](https://docs.convex.dev/typescript)
- [Real-time Queries](https://docs.convex.dev/database/queries)
- [Mutations & Transactions](https://docs.convex.dev/database/mutations)

---

**Status**: ✅ **Proof of Concept Complete**

The Convex backend successfully demonstrates:
- Complete CRUD operations for core entities
- Multi-tenant workspace isolation  
- Real-time data synchronization
- Type-safe TypeScript API
- Scalable architecture ready for production

This implementation provides a solid foundation for migrating Twenty from PostgreSQL to Convex, offering improved developer experience and built-in real-time capabilities.
