#!/usr/bin/env node

/**
 * Twenty Convex Test Script
 * 
 * This script demonstrates the core CRUD operations for the Twenty CRM
 * using Convex as the backend database.
 */

import { ConvexClient } from "convex/browser";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

const CONVEX_URL = process.env.CONVEX_URL;

if (!CONVEX_URL) {
  console.error("❌ CONVEX_URL not found in .env.local");
  process.exit(1);
}

console.log("🚀 Twenty Convex Test Script");
console.log("📡 Connecting to:", CONVEX_URL);

const convex = new ConvexClient(CONVEX_URL);

async function runTests() {
  try {
    console.log("\n=== Testing User Operations ===");
    
    // Create a test user
    console.log("📝 Creating user...");
    const userId = await convex.mutation("users:createUser", {
      firstName: "John",
      lastName: "Doe", 
      email: "<EMAIL>",
      locale: "en"
    });
    console.log("✅ User created:", userId);

    // Get user by email
    console.log("🔍 Finding user by email...");
    const user = await convex.query("users:getUserByEmail", {
      email: "<EMAIL>"
    });
    console.log("✅ User found:", user?.firstName, user?.lastName);

    console.log("\n=== Testing Workspace Operations ===");
    
    // Create a test workspace
    console.log("📝 Creating workspace...");
    const workspaceId = await convex.mutation("workspaces:createWorkspace", {
      displayName: "Test Company",
      subdomain: "testcompany",
      ownerId: userId
    });
    console.log("✅ Workspace created:", workspaceId);

    // Get workspace by subdomain
    console.log("🔍 Finding workspace by subdomain...");
    const workspace = await convex.query("workspaces:getWorkspaceBySubdomain", {
      subdomain: "testcompany"
    });
    console.log("✅ Workspace found:", workspace?.displayName);

    // Get user workspaces
    console.log("🔍 Getting user workspaces...");
    const userWorkspaces = await convex.query("users:getUserWorkspaces", {
      userId: userId
    });
    console.log("✅ User workspaces:", userWorkspaces.length);

    console.log("\n=== Testing Company Operations ===");
    
    // Create a test company
    console.log("📝 Creating company...");
    const companyId = await convex.mutation("companies:createCompany", {
      workspaceId: workspaceId,
      name: "Acme Corporation",
      domainName: {
        primaryLinkUrl: "https://acme.com",
        primaryLinkLabel: "Website"
      },
      employees: 150,
      idealCustomerProfile: true,
      createdBy: {
        source: "manual",
        name: "John Doe"
      }
    });
    console.log("✅ Company created:", companyId);

    // Get companies by workspace
    console.log("🔍 Getting companies in workspace...");
    const companies = await convex.query("companies:getCompaniesByWorkspace", {
      workspaceId: workspaceId,
      limit: 10
    });
    console.log("✅ Companies found:", companies.length);

    // Get company with details
    console.log("🔍 Getting company details...");
    const company = await convex.query("companies:getCompanyById", {
      companyId: companyId,
      workspaceId: workspaceId
    });
    console.log("✅ Company details:", company?.name, `(${company?.employees} employees)`);

    console.log("\n=== Testing People Operations ===");
    
    // Create a test person
    console.log("📝 Creating person...");
    const personId = await convex.mutation("people:createPerson", {
      workspaceId: workspaceId,
      firstName: "Jane",
      lastName: "Smith",
      email: "<EMAIL>",
      phone: "******-0123",
      city: "San Francisco",
      companyId: companyId,
      createdBy: {
        source: "manual",
        name: "John Doe"
      }
    });
    console.log("✅ Person created:", personId);

    // Get people by workspace
    console.log("🔍 Getting people in workspace...");
    const people = await convex.query("people:getPeopleByWorkspace", {
      workspaceId: workspaceId,
      limit: 10
    });
    console.log("✅ People found:", people.length);

    // Get person with company
    console.log("🔍 Getting person with company details...");
    const personWithCompany = await convex.query("people:getPersonWithCompany", {
      personId: personId,
      workspaceId: workspaceId
    });
    console.log("✅ Person details:", 
      personWithCompany?.person?.firstName, 
      personWithCompany?.person?.lastName,
      "at", 
      personWithCompany?.company?.name
    );

    // Get people by company
    console.log("🔍 Getting people in company...");
    const companyPeople = await convex.query("people:getPeopleByCompany", {
      companyId: companyId,
      workspaceId: workspaceId,
      limit: 10
    });
    console.log("✅ Company people:", companyPeople.length);

    console.log("\n=== Testing Update Operations ===");
    
    // Update company
    console.log("📝 Updating company...");
    await convex.mutation("companies:updateCompany", {
      companyId: companyId,
      workspaceId: workspaceId,
      employees: 200,
      annualRecurringRevenue: {
        amountMicros: 5000000 * 1000000, // $5M in micros
        currencyCode: "USD"
      }
    });
    console.log("✅ Company updated");

    // Update person
    console.log("📝 Updating person...");
    await convex.mutation("people:updatePerson", {
      personId: personId,
      workspaceId: workspaceId,
      city: "New York",
      phone: "******-9876"
    });
    console.log("✅ Person updated");

    console.log("\n=== Testing Search Operations ===");
    
    // Search companies by name
    console.log("🔍 Searching companies by name...");
    const searchResults = await convex.query("companies:searchCompaniesByName", {
      workspaceId: workspaceId,
      searchTerm: "Acme",
      limit: 5
    });
    console.log("✅ Search results:", searchResults.length);

    // Search people by email
    console.log("🔍 Searching person by email...");
    const personByEmail = await convex.query("people:searchPeopleByEmail", {
      workspaceId: workspaceId,
      email: "<EMAIL>"
    });
    console.log("✅ Person found by email:", personByEmail?.firstName, personByEmail?.lastName);

    console.log("\n=== Testing Workspace Stats ===");
    
    // Get workspace statistics
    console.log("📊 Getting workspace statistics...");
    const stats = await convex.query("workspaces:getWorkspaceStats", {
      workspaceId: workspaceId
    });
    console.log("✅ Workspace stats:");
    console.log("   👥 Members:", stats.memberCount);
    console.log("   🏢 Companies:", stats.companyCount);
    console.log("   👤 People:", stats.peopleCount);

    console.log("\n🎉 All tests completed successfully!");
    console.log("\n📈 Summary:");
    console.log("   ✅ User operations: Create, Read, Update");
    console.log("   ✅ Workspace operations: Create, Read, Stats");
    console.log("   ✅ Company operations: Create, Read, Update, Search");
    console.log("   ✅ People operations: Create, Read, Update, Search");
    console.log("   ✅ Multi-tenancy: Workspace isolation working");
    console.log("   ✅ Relationships: Company-Person associations working");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the tests
runTests().then(() => {
  console.log("\n👋 Test script completed. Convex backend is working!");
  process.exit(0);
}).catch((error) => {
  console.error("❌ Unexpected error:", error);
  process.exit(1);
});
