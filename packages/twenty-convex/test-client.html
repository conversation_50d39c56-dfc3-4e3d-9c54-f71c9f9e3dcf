<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twenty Convex Test Client</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: 500;
        }
        .error {
            color: #dc3545;
            font-weight: 500;
        }
        .data-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background: #f8f9fa;
        }
        .data-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            background: white;
            border-radius: 4px;
        }
        .data-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .flex {
            display: flex;
            gap: 20px;
        }
        .flex > div {
            flex: 1;
        }
        #status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            font-weight: 500;
            z-index: 1000;
        }
        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div id="status" class="status-disconnected">Disconnected</div>
    
    <h1>Twenty Convex Test Client</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <p>This client demonstrates the Convex backend for Twenty CRM with real-time updates.</p>
        <button onclick="initializeConvex()">Connect to Convex</button>
        <div id="connection-status"></div>
    </div>

    <div class="flex">
        <div>
            <div class="container">
                <h2>Create User</h2>
                <div class="form-group">
                    <label>First Name:</label>
                    <input type="text" id="userFirstName" placeholder="John">
                </div>
                <div class="form-group">
                    <label>Last Name:</label>
                    <input type="text" id="userLastName" placeholder="Doe">
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="userEmail" placeholder="<EMAIL>">
                </div>
                <button onclick="createUser()">Create User</button>
                <div id="user-result"></div>
            </div>

            <div class="container">
                <h2>Create Workspace</h2>
                <div class="form-group">
                    <label>Display Name:</label>
                    <input type="text" id="workspaceDisplayName" placeholder="My Company">
                </div>
                <div class="form-group">
                    <label>Subdomain:</label>
                    <input type="text" id="workspaceSubdomain" placeholder="mycompany">
                </div>
                <div class="form-group">
                    <label>Owner User ID:</label>
                    <input type="text" id="workspaceOwnerId" placeholder="Select a user first">
                </div>
                <button onclick="createWorkspace()">Create Workspace</button>
                <div id="workspace-result"></div>
            </div>
        </div>

        <div>
            <div class="container">
                <h2>Create Company</h2>
                <div class="form-group">
                    <label>Workspace ID:</label>
                    <input type="text" id="companyWorkspaceId" placeholder="Select a workspace first">
                </div>
                <div class="form-group">
                    <label>Company Name:</label>
                    <input type="text" id="companyName" placeholder="Acme Corp">
                </div>
                <div class="form-group">
                    <label>Domain:</label>
                    <input type="text" id="companyDomain" placeholder="acme.com">
                </div>
                <div class="form-group">
                    <label>Employees:</label>
                    <input type="number" id="companyEmployees" placeholder="100">
                </div>
                <button onclick="createCompany()">Create Company</button>
                <div id="company-result"></div>
            </div>

            <div class="container">
                <h2>Create Person</h2>
                <div class="form-group">
                    <label>Workspace ID:</label>
                    <input type="text" id="personWorkspaceId" placeholder="Select a workspace first">
                </div>
                <div class="form-group">
                    <label>First Name:</label>
                    <input type="text" id="personFirstName" placeholder="Jane">
                </div>
                <div class="form-group">
                    <label>Last Name:</label>
                    <input type="text" id="personLastName" placeholder="Smith">
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="personEmail" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Company ID:</label>
                    <input type="text" id="personCompanyId" placeholder="Select a company first">
                </div>
                <button onclick="createPerson()">Create Person</button>
                <div id="person-result"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Live Data (Real-time Updates)</h2>
        <div class="flex">
            <div>
                <h3>Users</h3>
                <div id="users-list" class="data-list">
                    <p>Connect to Convex to see users...</p>
                </div>
            </div>
            <div>
                <h3>Workspaces</h3>
                <div id="workspaces-list" class="data-list">
                    <p>Connect to Convex to see workspaces...</p>
                </div>
            </div>
        </div>
        <div class="flex">
            <div>
                <h3>Companies</h3>
                <div id="companies-list" class="data-list">
                    <p>Select a workspace to see companies...</p>
                </div>
            </div>
            <div>
                <h3>People</h3>
                <div id="people-list" class="data-list">
                    <p>Select a workspace to see people...</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { ConvexClient } from "https://cdn.skypack.dev/convex/browser";
        
        let convex = null;
        let currentWorkspaceId = null;

        // Initialize Convex connection
        window.initializeConvex = async () => {
            try {
                // You'll need to replace this with your actual Convex URL
                const CONVEX_URL = "https://decisive-iguana-732.convex.cloud";
                convex = new ConvexClient(CONVEX_URL);
                
                document.getElementById('status').textContent = 'Connected';
                document.getElementById('status').className = 'status-connected';
                document.getElementById('connection-status').innerHTML = '<span class="success">✓ Connected to Convex</span>';
                
                // Set up real-time subscriptions
                setupSubscriptions();
                
            } catch (error) {
                console.error('Failed to connect to Convex:', error);
                document.getElementById('connection-status').innerHTML = `<span class="error">✗ Connection failed: ${error.message}</span>`;
            }
        };

        // Set up real-time subscriptions
        function setupSubscriptions() {
            // Subscribe to all users (for demo purposes)
            convex.watchQuery("users:getUserByEmail", { email: "<EMAIL>" })
                .subscribe(result => {
                    // This is just a placeholder - in real app you'd have a proper user list query
                    console.log('User subscription update:', result);
                });
        }

        // Create user
        window.createUser = async () => {
            if (!convex) {
                alert('Please connect to Convex first');
                return;
            }

            const firstName = document.getElementById('userFirstName').value;
            const lastName = document.getElementById('userLastName').value;
            const email = document.getElementById('userEmail').value;

            if (!firstName || !lastName || !email) {
                alert('Please fill in all user fields');
                return;
            }

            try {
                const userId = await convex.mutation("users:createUser", {
                    firstName,
                    lastName,
                    email,
                    locale: "en"
                });
                
                document.getElementById('user-result').innerHTML = `<span class="success">✓ User created with ID: ${userId}</span>`;
                document.getElementById('workspaceOwnerId').value = userId;
                
                // Clear form
                document.getElementById('userFirstName').value = '';
                document.getElementById('userLastName').value = '';
                document.getElementById('userEmail').value = '';
                
            } catch (error) {
                document.getElementById('user-result').innerHTML = `<span class="error">✗ Error: ${error.message}</span>`;
            }
        };

        // Create workspace
        window.createWorkspace = async () => {
            if (!convex) {
                alert('Please connect to Convex first');
                return;
            }

            const displayName = document.getElementById('workspaceDisplayName').value;
            const subdomain = document.getElementById('workspaceSubdomain').value;
            const ownerId = document.getElementById('workspaceOwnerId').value;

            if (!displayName || !subdomain || !ownerId) {
                alert('Please fill in all workspace fields');
                return;
            }

            try {
                const workspaceId = await convex.mutation("workspaces:createWorkspace", {
                    displayName,
                    subdomain,
                    ownerId
                });
                
                document.getElementById('workspace-result').innerHTML = `<span class="success">✓ Workspace created with ID: ${workspaceId}</span>`;
                document.getElementById('companyWorkspaceId').value = workspaceId;
                document.getElementById('personWorkspaceId').value = workspaceId;
                currentWorkspaceId = workspaceId;
                
                // Clear form
                document.getElementById('workspaceDisplayName').value = '';
                document.getElementById('workspaceSubdomain').value = '';
                
            } catch (error) {
                document.getElementById('workspace-result').innerHTML = `<span class="error">✗ Error: ${error.message}</span>`;
            }
        };

        // Create company
        window.createCompany = async () => {
            if (!convex) {
                alert('Please connect to Convex first');
                return;
            }

            const workspaceId = document.getElementById('companyWorkspaceId').value;
            const name = document.getElementById('companyName').value;
            const domain = document.getElementById('companyDomain').value;
            const employees = parseInt(document.getElementById('companyEmployees').value) || undefined;

            if (!workspaceId || !name) {
                alert('Please fill in workspace ID and company name');
                return;
            }

            try {
                const companyId = await convex.mutation("companies:createCompany", {
                    workspaceId,
                    name,
                    domainName: domain ? { primaryLinkUrl: `https://${domain}` } : undefined,
                    employees,
                    createdBy: {
                        source: "manual",
                        name: "Test User"
                    }
                });
                
                document.getElementById('company-result').innerHTML = `<span class="success">✓ Company created with ID: ${companyId}</span>`;
                document.getElementById('personCompanyId').value = companyId;
                
                // Clear form
                document.getElementById('companyName').value = '';
                document.getElementById('companyDomain').value = '';
                document.getElementById('companyEmployees').value = '';
                
            } catch (error) {
                document.getElementById('company-result').innerHTML = `<span class="error">✗ Error: ${error.message}</span>`;
            }
        };

        // Create person
        window.createPerson = async () => {
            if (!convex) {
                alert('Please connect to Convex first');
                return;
            }

            const workspaceId = document.getElementById('personWorkspaceId').value;
            const firstName = document.getElementById('personFirstName').value;
            const lastName = document.getElementById('personLastName').value;
            const email = document.getElementById('personEmail').value;
            const companyId = document.getElementById('personCompanyId').value || undefined;

            if (!workspaceId || !firstName || !lastName) {
                alert('Please fill in workspace ID, first name, and last name');
                return;
            }

            try {
                const personId = await convex.mutation("people:createPerson", {
                    workspaceId,
                    firstName,
                    lastName,
                    email,
                    companyId,
                    createdBy: {
                        source: "manual",
                        name: "Test User"
                    }
                });
                
                document.getElementById('person-result').innerHTML = `<span class="success">✓ Person created with ID: ${personId}</span>`;
                
                // Clear form
                document.getElementById('personFirstName').value = '';
                document.getElementById('personLastName').value = '';
                document.getElementById('personEmail').value = '';
                
            } catch (error) {
                document.getElementById('person-result').innerHTML = `<span class="error">✗ Error: ${error.message}</span>`;
            }
        };

        // Auto-connect on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('connection-status').innerHTML = '<p>Click "Connect to Convex" to start testing the Twenty Convex backend.</p>';
            }, 1000);
        });
    </script>
</body>
</html>
