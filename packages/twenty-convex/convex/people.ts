import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// People queries
export const getPeopleByWorkspace = query({
  args: {
    workspaceId: v.id("workspaces"),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    const baseQuery = ctx.db
      .query("people")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", args.workspaceId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .order("desc");

    if (args.cursor) {
      return await baseQuery.paginate({ cursor: args.cursor, numItems: limit });
    } else {
      return await baseQuery.take(limit);
    }
  },
});

export const getPersonById = query({
  args: { 
    personId: v.id("people"),
    workspaceId: v.id("workspaces")
  },
  handler: async (ctx, args) => {
    const person = await ctx.db.get(args.personId);
    
    if (!person || person.deletedAt || person.workspaceId !== args.workspaceId) {
      return null;
    }
    
    return person;
  },
});

export const getPersonWithCompany = query({
  args: { 
    personId: v.id("people"),
    workspaceId: v.id("workspaces")
  },
  handler: async (ctx, args) => {
    const person = await ctx.db.get(args.personId);
    
    if (!person || person.deletedAt || person.workspaceId !== args.workspaceId) {
      return null;
    }

    let company = null;
    if (person.companyId) {
      company = await ctx.db.get(person.companyId);
      if (company?.deletedAt) {
        company = null;
      }
    }
    
    return {
      person,
      company,
    };
  },
});

export const searchPeopleByName = query({
  args: { 
    workspaceId: v.id("workspaces"),
    searchTerm: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    
    return await ctx.db
      .query("people")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", args.workspaceId))
      .filter((q) => 
        q.and(
          q.eq(q.field("deletedAt"), undefined),
          q.or(
            q.gte(q.field("firstName"), args.searchTerm),
            q.lte(q.field("firstName"), args.searchTerm + "\uffff"),
            q.gte(q.field("lastName"), args.searchTerm),
            q.lte(q.field("lastName"), args.searchTerm + "\uffff")
          )
        )
      )
      .take(limit);
  },
});

export const searchPeopleByEmail = query({
  args: { 
    workspaceId: v.id("workspaces"),
    email: v.string()
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("people")
      .withIndex("by_workspace_email", (q) => 
        q.eq("workspaceId", args.workspaceId).eq("email", args.email.toLowerCase())
      )
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .first();
  },
});

export const getPeopleByCompany = query({
  args: { 
    companyId: v.id("companies"),
    workspaceId: v.id("workspaces"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    return await ctx.db
      .query("people")
      .withIndex("by_company", (q) => q.eq("companyId", args.companyId))
      .filter((q) => 
        q.and(
          q.eq(q.field("workspaceId"), args.workspaceId),
          q.eq(q.field("deletedAt"), undefined)
        )
      )
      .take(limit);
  },
});

// People mutations
export const createPerson = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    city: v.optional(v.string()),
    avatarUrl: v.optional(v.string()),
    companyId: v.optional(v.id("companies")),
    createdBy: v.object({
      source: v.string(),
      workspaceMemberId: v.optional(v.id("workspaceMembers")),
      name: v.optional(v.string())
    }),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Validate email uniqueness within workspace if provided
    if (args.email) {
      const existingPerson = await ctx.db
        .query("people")
        .withIndex("by_workspace_email", (q) => 
          q.eq("workspaceId", args.workspaceId).eq("email", args.email!.toLowerCase())
        )
        .filter((q) => q.eq(q.field("deletedAt"), undefined))
        .first();

      if (existingPerson) {
        throw new Error("Person with this email already exists in workspace");
      }
    }

    // Validate company belongs to workspace if provided
    if (args.companyId) {
      const company = await ctx.db.get(args.companyId);
      if (!company || company.workspaceId !== args.workspaceId || company.deletedAt) {
        throw new Error("Invalid company or company not found");
      }
    }

    // Get the highest position for ordering
    const lastPerson = await ctx.db
      .query("people")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", args.workspaceId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .order("desc")
      .first();

    const position = lastPerson ? lastPerson.position + 1 : 1;

    const personId = await ctx.db.insert("people", {
      workspaceId: args.workspaceId,
      firstName: args.firstName,
      lastName: args.lastName,
      email: args.email?.toLowerCase(),
      phone: args.phone,
      city: args.city,
      avatarUrl: args.avatarUrl,
      position,
      createdBy: args.createdBy,
      companyId: args.companyId,
      createdAt: now,
      updatedAt: now,
    });

    return personId;
  },
});

export const updatePerson = mutation({
  args: {
    personId: v.id("people"),
    workspaceId: v.id("workspaces"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    city: v.optional(v.string()),
    avatarUrl: v.optional(v.string()),
    companyId: v.optional(v.id("companies")),
  },
  handler: async (ctx, args) => {
    const { personId, workspaceId, ...updates } = args;
    const now = Date.now();

    // Verify person belongs to workspace
    const person = await ctx.db.get(personId);
    if (!person || person.workspaceId !== workspaceId || person.deletedAt) {
      throw new Error("Person not found or access denied");
    }

    // Validate email uniqueness within workspace if being updated
    if (updates.email) {
      const existingPerson = await ctx.db
        .query("people")
        .withIndex("by_workspace_email", (q) => 
          q.eq("workspaceId", workspaceId).eq("email", updates.email!.toLowerCase())
        )
        .filter((q) => q.eq(q.field("deletedAt"), undefined))
        .first();

      if (existingPerson && existingPerson._id !== personId) {
        throw new Error("Person with this email already exists in workspace");
      }
      updates.email = updates.email.toLowerCase();
    }

    // Validate company belongs to workspace if being updated
    if (updates.companyId) {
      const company = await ctx.db.get(updates.companyId);
      if (!company || company.workspaceId !== workspaceId || company.deletedAt) {
        throw new Error("Invalid company or company not found");
      }
    }

    await ctx.db.patch(personId, {
      ...updates,
      updatedAt: now,
    });

    return personId;
  },
});

export const deletePerson = mutation({
  args: {
    personId: v.id("people"),
    workspaceId: v.id("workspaces"),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Verify person belongs to workspace
    const person = await ctx.db.get(args.personId);
    if (!person || person.workspaceId !== args.workspaceId || person.deletedAt) {
      throw new Error("Person not found or access denied");
    }

    // Soft delete person
    await ctx.db.patch(args.personId, {
      deletedAt: now,
      updatedAt: now,
    });

    return args.personId;
  },
});

export const updatePersonPosition = mutation({
  args: {
    personId: v.id("people"),
    workspaceId: v.id("workspaces"),
    newPosition: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Verify person belongs to workspace
    const person = await ctx.db.get(args.personId);
    if (!person || person.workspaceId !== args.workspaceId || person.deletedAt) {
      throw new Error("Person not found or access denied");
    }

    await ctx.db.patch(args.personId, {
      position: args.newPosition,
      updatedAt: now,
    });

    return args.personId;
  },
});

export const assignPersonToCompany = mutation({
  args: {
    personId: v.id("people"),
    workspaceId: v.id("workspaces"),
    companyId: v.optional(v.id("companies")),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Verify person belongs to workspace
    const person = await ctx.db.get(args.personId);
    if (!person || person.workspaceId !== args.workspaceId || person.deletedAt) {
      throw new Error("Person not found or access denied");
    }

    // Validate company belongs to workspace if provided
    if (args.companyId) {
      const company = await ctx.db.get(args.companyId);
      if (!company || company.workspaceId !== args.workspaceId || company.deletedAt) {
        throw new Error("Invalid company or company not found");
      }
    }

    await ctx.db.patch(args.personId, {
      companyId: args.companyId,
      updatedAt: now,
    });

    return args.personId;
  },
});
