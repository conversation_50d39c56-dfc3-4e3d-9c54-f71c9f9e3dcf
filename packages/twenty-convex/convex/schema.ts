import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// Enums
const WorkspaceActivationStatus = v.union(
  v.literal("INACTIVE"),
  v.literal("PENDING_CREATION"), 
  v.literal("ONGOING_CREATION"),
  v.literal("ACTIVE")
);

const OnboardingStatus = v.union(
  v.literal("INCOMPLETE"),
  v.literal("COMPLETE")
);

// Common metadata types
const LinksMetadata = v.object({
  primaryLinkUrl: v.optional(v.string()),
  primaryLinkLabel: v.optional(v.string()),
  secondaryLinks: v.optional(v.array(v.object({
    url: v.string(),
    label: v.string()
  })))
});

const CurrencyMetadata = v.object({
  amountMicros: v.optional(v.number()),
  currencyCode: v.optional(v.string())
});

const AddressMetadata = v.object({
  addressStreet1: v.optional(v.string()),
  addressStreet2: v.optional(v.string()),
  addressCity: v.optional(v.string()),
  addressState: v.optional(v.string()),
  addressCountry: v.optional(v.string()),
  addressPostcode: v.optional(v.string())
});

const ActorMetadata = v.object({
  source: v.string(),
  workspaceMemberId: v.optional(v.id("workspaceMembers")),
  name: v.optional(v.string())
});

export default defineSchema({
  // Core entities
  users: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    defaultAvatarUrl: v.optional(v.string()),
    isEmailVerified: v.boolean(),
    disabled: v.boolean(),
    passwordHash: v.optional(v.string()),
    canImpersonate: v.boolean(),
    canAccessFullAdminPanel: v.boolean(),
    locale: v.string(),
    onboardingStatus: v.optional(OnboardingStatus),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_email", ["email"])
    .index("by_deleted", ["deletedAt"]),

  workspaces: defineTable({
    displayName: v.optional(v.string()),
    logo: v.optional(v.string()),
    inviteHash: v.optional(v.string()),
    allowImpersonation: v.boolean(),
    isPublicInviteLinkEnabled: v.boolean(),
    activationStatus: WorkspaceActivationStatus,
    metadataVersion: v.number(),
    databaseUrl: v.string(),
    databaseSchema: v.string(),
    subdomain: v.string(),
    customDomain: v.optional(v.string()),
    isGoogleAuthEnabled: v.boolean(),
    isPasswordAuthEnabled: v.boolean(),
    isMicrosoftAuthEnabled: v.boolean(),
    isCustomDomainEnabled: v.boolean(),
    defaultRoleId: v.optional(v.id("roles")),
    defaultAgentId: v.optional(v.id("agents")),
    version: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_subdomain", ["subdomain"])
    .index("by_custom_domain", ["customDomain"])
    .index("by_activation_status", ["activationStatus"])
    .index("by_deleted", ["deletedAt"]),

  userWorkspaces: defineTable({
    userId: v.id("users"),
    workspaceId: v.id("workspaces"),
    defaultAvatarUrl: v.optional(v.string()),
    locale: v.string(),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_user", ["userId"])
    .index("by_workspace", ["workspaceId"])
    .index("by_user_workspace", ["userId", "workspaceId"])
    .index("by_deleted", ["deletedAt"]),

  // Workspace-scoped entities (all include workspaceId for multi-tenancy)
  companies: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    domainName: v.optional(LinksMetadata),
    employees: v.optional(v.number()),
    linkedinLink: v.optional(LinksMetadata),
    xLink: v.optional(LinksMetadata),
    annualRecurringRevenue: v.optional(CurrencyMetadata),
    address: v.optional(AddressMetadata),
    idealCustomerProfile: v.boolean(),
    position: v.number(),
    createdBy: ActorMetadata,
    accountOwnerId: v.optional(v.id("workspaceMembers")),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_workspace_name", ["workspaceId", "name"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"])
    .index("by_account_owner", ["accountOwnerId"]),

  people: defineTable({
    workspaceId: v.id("workspaces"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    city: v.optional(v.string()),
    avatarUrl: v.optional(v.string()),
    position: v.number(),
    createdBy: ActorMetadata,
    companyId: v.optional(v.id("companies")),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_workspace_email", ["workspaceId", "email"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"])
    .index("by_company", ["companyId"]),

  workspaceMembers: defineTable({
    workspaceId: v.id("workspaces"),
    userId: v.id("users"),
    name: v.object({
      firstName: v.string(),
      lastName: v.string()
    }),
    colorScheme: v.string(),
    avatarUrl: v.optional(v.string()),
    locale: v.string(),
    timeZone: v.string(),
    dateFormat: v.string(),
    timeFormat: v.string(),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_user", ["userId"])
    .index("by_workspace_user", ["workspaceId", "userId"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"]),

  // Metadata system for dynamic entities
  objectMetadata: defineTable({
    workspaceId: v.id("workspaces"),
    standardId: v.optional(v.string()),
    nameSingular: v.string(),
    namePlural: v.string(),
    labelSingular: v.string(),
    labelPlural: v.string(),
    description: v.optional(v.string()),
    icon: v.optional(v.string()),
    targetTableName: v.string(),
    isCustom: v.boolean(),
    isRemote: v.boolean(),
    isActive: v.boolean(),
    isSystem: v.boolean(),
    isAuditLogged: v.boolean(),
    isSearchable: v.boolean(),
    shortcut: v.optional(v.string()),
    labelIdentifierFieldMetadataId: v.optional(v.id("fieldMetadata")),
    imageIdentifierFieldMetadataId: v.optional(v.id("fieldMetadata")),
    isLabelSyncedWithName: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_workspace_name_singular", ["workspaceId", "nameSingular"])
    .index("by_workspace_name_plural", ["workspaceId", "namePlural"])
    .index("by_workspace_active", ["workspaceId", "isActive"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"]),

  fieldMetadata: defineTable({
    workspaceId: v.id("workspaces"),
    objectMetadataId: v.id("objectMetadata"),
    standardId: v.optional(v.string()),
    name: v.string(),
    label: v.string(),
    description: v.optional(v.string()),
    type: v.string(),
    targetColumnMap: v.object({}), // JSON object
    defaultValue: v.any(),
    options: v.optional(v.any()),
    isCustom: v.boolean(),
    isActive: v.boolean(),
    isSystem: v.boolean(),
    isNullable: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_object", ["objectMetadataId"])
    .index("by_workspace_object", ["workspaceId", "objectMetadataId"])
    .index("by_workspace_name", ["workspaceId", "name"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"]),

  // Support entities
  roles: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    color: v.string(),
    position: v.number(),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"]),

  agents: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    label: v.string(),
    icon: v.optional(v.string()),
    description: v.optional(v.string()),
    prompt: v.string(),
    modelId: v.string(),
    responseFormat: v.optional(v.object({})),
    isCustom: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
    deletedAt: v.optional(v.number())
  })
    .index("by_workspace", ["workspaceId"])
    .index("by_workspace_deleted", ["workspaceId", "deletedAt"])
});
