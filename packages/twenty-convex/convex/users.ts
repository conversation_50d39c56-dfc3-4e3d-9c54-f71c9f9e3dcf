import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";

// User queries
export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  },
});

export const getUserByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email.toLowerCase()))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .first();
  },
});

export const getUserWorkspaces = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const userWorkspaces = await ctx.db
      .query("userWorkspaces")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .collect();

    // Get workspace details for each user workspace
    const workspaces = await Promise.all(
      userWorkspaces.map(async (uw) => {
        const workspace = await ctx.db.get(uw.workspaceId);
        return {
          userWorkspace: uw,
          workspace,
        };
      })
    );

    return workspaces.filter((w) => w.workspace && !w.workspace.deletedAt);
  },
});

// User mutations
export const createUser = mutation({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    passwordHash: v.optional(v.string()),
    defaultAvatarUrl: v.optional(v.string()),
    locale: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email.toLowerCase()))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .first();

    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    const userId = await ctx.db.insert("users", {
      firstName: args.firstName,
      lastName: args.lastName,
      email: args.email.toLowerCase(),
      passwordHash: args.passwordHash,
      defaultAvatarUrl: args.defaultAvatarUrl,
      isEmailVerified: false,
      disabled: false,
      canImpersonate: false,
      canAccessFullAdminPanel: false,
      locale: args.locale || "en",
      createdAt: now,
      updatedAt: now,
    });

    return userId;
  },
});

export const updateUser = mutation({
  args: {
    userId: v.id("users"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.optional(v.string()),
    defaultAvatarUrl: v.optional(v.string()),
    isEmailVerified: v.optional(v.boolean()),
    disabled: v.optional(v.boolean()),
    locale: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    const now = Date.now();

    // If email is being updated, check for conflicts
    if (updates.email) {
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", updates.email!.toLowerCase()))
        .filter((q) => q.eq(q.field("deletedAt"), undefined))
        .first();

      if (existingUser && existingUser._id !== userId) {
        throw new Error("User with this email already exists");
      }
      updates.email = updates.email.toLowerCase();
    }

    await ctx.db.patch(userId, {
      ...updates,
      updatedAt: now,
    });

    return userId;
  },
});

export const deleteUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.userId, {
      deletedAt: now,
      updatedAt: now,
    });

    // Also soft delete all user workspaces
    const userWorkspaces = await ctx.db
      .query("userWorkspaces")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .collect();

    await Promise.all(
      userWorkspaces.map((uw) =>
        ctx.db.patch(uw._id, {
          deletedAt: now,
          updatedAt: now,
        })
      )
    );

    return args.userId;
  },
});

export const verifyUserEmail = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.userId, {
      isEmailVerified: true,
      updatedAt: now,
    });

    return args.userId;
  },
});

export const setUserPassword = mutation({
  args: {
    userId: v.id("users"),
    passwordHash: v.string(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.userId, {
      passwordHash: args.passwordHash,
      updatedAt: now,
    });

    return args.userId;
  },
});

// User workspace management
export const addUserToWorkspace = mutation({
  args: {
    userId: v.id("users"),
    workspaceId: v.id("workspaces"),
    locale: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Check if user workspace already exists
    const existingUserWorkspace = await ctx.db
      .query("userWorkspaces")
      .withIndex("by_user_workspace", (q) => 
        q.eq("userId", args.userId).eq("workspaceId", args.workspaceId)
      )
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .first();

    if (existingUserWorkspace) {
      throw new Error("User is already a member of this workspace");
    }

    const userWorkspaceId = await ctx.db.insert("userWorkspaces", {
      userId: args.userId,
      workspaceId: args.workspaceId,
      locale: args.locale || "en",
      createdAt: now,
      updatedAt: now,
    });

    return userWorkspaceId;
  },
});

export const removeUserFromWorkspace = mutation({
  args: {
    userId: v.id("users"),
    workspaceId: v.id("workspaces"),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    const userWorkspace = await ctx.db
      .query("userWorkspaces")
      .withIndex("by_user_workspace", (q) => 
        q.eq("userId", args.userId).eq("workspaceId", args.workspaceId)
      )
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .first();

    if (!userWorkspace) {
      throw new Error("User is not a member of this workspace");
    }

    await ctx.db.patch(userWorkspace._id, {
      deletedAt: now,
      updatedAt: now,
    });

    return userWorkspace._id;
  },
});

// Helper function to get user with workspace context
export const getUserWithWorkspace = query({
  args: {
    userId: v.id("users"),
    workspaceId: v.id("workspaces"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user || user.deletedAt) {
      return null;
    }

    const userWorkspace = await ctx.db
      .query("userWorkspaces")
      .withIndex("by_user_workspace", (q) => 
        q.eq("userId", args.userId).eq("workspaceId", args.workspaceId)
      )
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .first();

    if (!userWorkspace) {
      return null;
    }

    const workspace = await ctx.db.get(args.workspaceId);
    if (!workspace || workspace.deletedAt) {
      return null;
    }

    return {
      user,
      userWorkspace,
      workspace,
    };
  },
});
