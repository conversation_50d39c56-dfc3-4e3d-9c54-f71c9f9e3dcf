import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Company queries
export const getCompaniesByWorkspace = query({
  args: {
    workspaceId: v.id("workspaces"),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    const baseQuery = ctx.db
      .query("companies")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", args.workspaceId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .order("desc");

    if (args.cursor) {
      return await baseQuery.paginate({ cursor: args.cursor, numItems: limit });
    } else {
      return await baseQuery.take(limit);
    }
  },
});

export const getCompanyById = query({
  args: { 
    companyId: v.id("companies"),
    workspaceId: v.id("workspaces")
  },
  handler: async (ctx, args) => {
    const company = await ctx.db.get(args.companyId);
    
    if (!company || company.deletedAt || company.workspaceId !== args.workspaceId) {
      return null;
    }
    
    return company;
  },
});

export const searchCompaniesByName = query({
  args: { 
    workspaceId: v.id("workspaces"),
    searchTerm: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    
    return await ctx.db
      .query("companies")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", args.workspaceId))
      .filter((q) => 
        q.and(
          q.eq(q.field("deletedAt"), undefined),
          q.or(
            q.gte(q.field("name"), args.searchTerm),
            q.lte(q.field("name"), args.searchTerm + "\uffff")
          )
        )
      )
      .take(limit);
  },
});

export const getCompanyWithPeople = query({
  args: { 
    companyId: v.id("companies"),
    workspaceId: v.id("workspaces")
  },
  handler: async (ctx, args) => {
    const company = await ctx.db.get(args.companyId);
    
    if (!company || company.deletedAt || company.workspaceId !== args.workspaceId) {
      return null;
    }

    const people = await ctx.db
      .query("people")
      .withIndex("by_company", (q) => q.eq("companyId", args.companyId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .collect();
    
    return {
      company,
      people,
    };
  },
});

// Company mutations
export const createCompany = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
    domainName: v.optional(v.object({
      primaryLinkUrl: v.optional(v.string()),
      primaryLinkLabel: v.optional(v.string()),
      secondaryLinks: v.optional(v.array(v.object({
        url: v.string(),
        label: v.string()
      })))
    })),
    employees: v.optional(v.number()),
    linkedinLink: v.optional(v.object({
      primaryLinkUrl: v.optional(v.string()),
      primaryLinkLabel: v.optional(v.string()),
    })),
    xLink: v.optional(v.object({
      primaryLinkUrl: v.optional(v.string()),
      primaryLinkLabel: v.optional(v.string()),
    })),
    annualRecurringRevenue: v.optional(v.object({
      amountMicros: v.optional(v.number()),
      currencyCode: v.optional(v.string())
    })),
    address: v.optional(v.object({
      addressStreet1: v.optional(v.string()),
      addressStreet2: v.optional(v.string()),
      addressCity: v.optional(v.string()),
      addressState: v.optional(v.string()),
      addressCountry: v.optional(v.string()),
      addressPostcode: v.optional(v.string())
    })),
    idealCustomerProfile: v.optional(v.boolean()),
    accountOwnerId: v.optional(v.id("workspaceMembers")),
    createdBy: v.object({
      source: v.string(),
      workspaceMemberId: v.optional(v.id("workspaceMembers")),
      name: v.optional(v.string())
    }),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Get the highest position for ordering
    const lastCompany = await ctx.db
      .query("companies")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", args.workspaceId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .order("desc")
      .first();

    const position = lastCompany ? lastCompany.position + 1 : 1;

    const companyId = await ctx.db.insert("companies", {
      workspaceId: args.workspaceId,
      name: args.name,
      domainName: args.domainName,
      employees: args.employees,
      linkedinLink: args.linkedinLink,
      xLink: args.xLink,
      annualRecurringRevenue: args.annualRecurringRevenue,
      address: args.address,
      idealCustomerProfile: args.idealCustomerProfile || false,
      position,
      createdBy: args.createdBy,
      accountOwnerId: args.accountOwnerId,
      createdAt: now,
      updatedAt: now,
    });

    return companyId;
  },
});

export const updateCompany = mutation({
  args: {
    companyId: v.id("companies"),
    workspaceId: v.id("workspaces"),
    name: v.optional(v.string()),
    domainName: v.optional(v.object({
      primaryLinkUrl: v.optional(v.string()),
      primaryLinkLabel: v.optional(v.string()),
      secondaryLinks: v.optional(v.array(v.object({
        url: v.string(),
        label: v.string()
      })))
    })),
    employees: v.optional(v.number()),
    linkedinLink: v.optional(v.object({
      primaryLinkUrl: v.optional(v.string()),
      primaryLinkLabel: v.optional(v.string()),
    })),
    xLink: v.optional(v.object({
      primaryLinkUrl: v.optional(v.string()),
      primaryLinkLabel: v.optional(v.string()),
    })),
    annualRecurringRevenue: v.optional(v.object({
      amountMicros: v.optional(v.number()),
      currencyCode: v.optional(v.string())
    })),
    address: v.optional(v.object({
      addressStreet1: v.optional(v.string()),
      addressStreet2: v.optional(v.string()),
      addressCity: v.optional(v.string()),
      addressState: v.optional(v.string()),
      addressCountry: v.optional(v.string()),
      addressPostcode: v.optional(v.string())
    })),
    idealCustomerProfile: v.optional(v.boolean()),
    accountOwnerId: v.optional(v.id("workspaceMembers")),
  },
  handler: async (ctx, args) => {
    const { companyId, workspaceId, ...updates } = args;
    const now = Date.now();

    // Verify company belongs to workspace
    const company = await ctx.db.get(companyId);
    if (!company || company.workspaceId !== workspaceId || company.deletedAt) {
      throw new Error("Company not found or access denied");
    }

    await ctx.db.patch(companyId, {
      ...updates,
      updatedAt: now,
    });

    return companyId;
  },
});

export const deleteCompany = mutation({
  args: {
    companyId: v.id("companies"),
    workspaceId: v.id("workspaces"),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Verify company belongs to workspace
    const company = await ctx.db.get(args.companyId);
    if (!company || company.workspaceId !== args.workspaceId || company.deletedAt) {
      throw new Error("Company not found or access denied");
    }

    // Soft delete company
    await ctx.db.patch(args.companyId, {
      deletedAt: now,
      updatedAt: now,
    });

    // Update people to remove company association
    const people = await ctx.db
      .query("people")
      .withIndex("by_company", (q) => q.eq("companyId", args.companyId))
      .filter((q) => q.eq(q.field("deletedAt"), undefined))
      .collect();

    await Promise.all(
      people.map((person) =>
        ctx.db.patch(person._id, {
          companyId: undefined,
          updatedAt: now,
        })
      )
    );

    return args.companyId;
  },
});

export const updateCompanyPosition = mutation({
  args: {
    companyId: v.id("companies"),
    workspaceId: v.id("workspaces"),
    newPosition: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Verify company belongs to workspace
    const company = await ctx.db.get(args.companyId);
    if (!company || company.workspaceId !== args.workspaceId || company.deletedAt) {
      throw new Error("Company not found or access denied");
    }

    await ctx.db.patch(args.companyId, {
      position: args.newPosition,
      updatedAt: now,
    });

    return args.companyId;
  },
});

export const getCompaniesByAccountOwner = query({
  args: { 
    workspaceId: v.id("workspaces"),
    accountOwnerId: v.id("workspaceMembers"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    return await ctx.db
      .query("companies")
      .withIndex("by_account_owner", (q) => q.eq("accountOwnerId", args.accountOwnerId))
      .filter((q) => 
        q.and(
          q.eq(q.field("workspaceId"), args.workspaceId),
          q.eq(q.field("deletedAt"), undefined)
        )
      )
      .take(limit);
  },
});
