{"name": "twenty-convex", "version": "0.2.1", "description": "Convex backend for Twenty CRM", "main": "index.js", "private": true, "license": "AGPL-3.0", "scripts": {"dev": "convex dev", "predev": "convex dev --until-success", "build": "convex deploy --prod", "test": "node test-script.js", "test:watch": "nodemon test-script.js"}, "keywords": ["convex", "twenty", "crm", "backend"], "author": "Twenty Team", "dependencies": {"convex": "^1.25.2"}, "devDependencies": {"typescript": "5.3.3", "@types/node": "^22.0.0"}}